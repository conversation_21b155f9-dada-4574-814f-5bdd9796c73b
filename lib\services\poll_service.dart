import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/poll.dart';

class PollService {
  final SupabaseClient _client = Supabase.instance.client;

  // دالة مساعدة لجلب بيانات المؤلف
  Future<Map<String, dynamic>?> _getAuthorProfile(String userId) async {
    try {
      print('🔍 جلب بيانات المؤلف للمستخدم: $userId');
      
      final response = await _client
          .from('profiles')
          .select('username, name, avatar_url, is_verified')
          .eq('id', userId)
          .maybeSingle();
      
      print('📋 بيانات المؤلف المحصل عليها: $response');
      return response;
    } catch (e) {
      print('❌ خطأ في جلب بيانات المؤلف: $e');
      return null;
    }
  }

  // دالة مساعدة لتحديد الاسم الحقيقي للمؤلف
  String _getAuthorName(Map<String, dynamic>? profile) {
    if (profile == null) return 'مستخدم';
    
    // أولوية للاسم
    if (profile['name'] != null && 
        profile['name'].toString().trim().isNotEmpty) {
      return profile['name'].toString().trim();
    } 
    // ثم اسم المستخدم
    else if (profile['username'] != null && 
             profile['username'].toString().trim().isNotEmpty) {
      return profile['username'].toString().trim();
    }
    
    return 'مستخدم';
  }

  // إنشاء تصويت جديد
  Future<String> createPoll(Poll poll) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // حساب تاريخ انتهاء الصلاحية
      DateTime? expiresAt;
      if (poll.duration.duration != null) {
        expiresAt = DateTime.now().add(poll.duration.duration!);
      }

      final pollData = {
        'user_id': userId,
        'question': poll.question,
        'type': poll.type.name,
        'category': poll.category.name,
        'duration': poll.duration.name,
        'allow_comments': poll.allowComments,
        'allow_revote': poll.allowRevote,
        'expires_at': expiresAt?.toIso8601String(),
      };

      final response = await _client
          .from('polls')
          .insert(pollData)
          .select()
          .single();

      final pollId = response['id'];

      // إضافة خيارات التصويت
      final optionsData = poll.options.asMap().entries.map((entry) {
        return {
          'poll_id': pollId,
          'text': entry.value.text,
          'option_order': entry.key,
        };
      }).toList();

      await _client
          .from('poll_options')
          .insert(optionsData);

      return pollId;
    } catch (e) {
      throw Exception('فشل في إنشاء التصويت: $e');
    }
  }

  // الحصول على التصويتات
  Future<List<Poll>> getPolls({
    PollCategory? category,
    String sortBy = 'created_at',
    bool ascending = false,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      var query = _client
          .from('polls')
          .select('''
            *,
            poll_options(*),
            profiles!polls_user_id_fkey(username, full_name, avatar_url, is_verified)
          ''')
          .eq('is_active', true);

      if (category != null) {
        query = query.eq('category', category.name);
      }

      final response = await query
          .order(sortBy, ascending: ascending)
          .range(offset, offset + limit - 1);

      List<Poll> polls = [];
      final currentUserId = _client.auth.currentUser?.id;

      for (var pollData in response as List) {
        // الحصول على تصويت المستخدم الحالي
        String? userVote;
        if (currentUserId != null) {
          try {
            final voteResponse = await _client
                .from('poll_votes')
                .select('option_id')
                .eq('poll_id', pollData['id'])
                .eq('user_id', currentUserId)
                .maybeSingle();
            
            userVote = voteResponse?['option_id'];
          } catch (e) {
            // تجاهل الخطأ
          }
        }

        // حساب إجمالي الأصوات والنسب المئوية
        final options = (pollData['poll_options'] as List)
            .map((option) => option as Map<String, dynamic>)
            .toList();

        final totalVotes = await _getTotalVotes(pollData['id']);
        
        final pollOptions = options.map((option) {
          final votes = option['votes'] ?? 0;
          final percentage = totalVotes > 0 ? (votes / totalVotes) * 100 : 0.0;
          
          return PollOption(
            id: option['id'],
            text: option['text'],
            votes: votes,
            percentage: percentage,
          );
        }).toList();

        // ترتيب الخيارات حسب الترتيب الأصلي
        pollOptions.sort((a, b) {
          final aOrder = options.firstWhere((o) => o['id'] == a.id)['option_order'] ?? 0;
          final bOrder = options.firstWhere((o) => o['id'] == b.id)['option_order'] ?? 0;
          return aOrder.compareTo(bOrder);
        });

        // جلب بيانات المؤلف بشكل منفصل
        final authorProfile = await _getAuthorProfile(pollData['user_id']);
        
        // Debug prints لمراقبة بيانات المؤلف
        print('👤 بيانات المؤلف للتصويت ${pollData['id']}:');
        print('   - User ID: ${pollData['user_id']}');
        print('   - Author Profile: $authorProfile');
        print('   - Name: ${authorProfile?['name']}');
        print('   - Username: ${authorProfile?['username']}');
        print('   - Is Verified: ${authorProfile?['is_verified']}');
        print('   - Author Name: ${_getAuthorName(authorProfile)}');
        
        polls.add(Poll(
          id: pollData['id'],
          userId: pollData['user_id'],
          question: pollData['question'],
          options: pollOptions,
          type: PollType.values.firstWhere(
            (t) => t.name == pollData['type'],
            orElse: () => PollType.public,
          ),
          category: PollCategory.values.firstWhere(
            (c) => c.name == pollData['category'],
            orElse: () => PollCategory.general,
          ),
          duration: PollDuration.values.firstWhere(
            (d) => d.name == pollData['duration'],
            orElse: () => PollDuration.unlimited,
          ),
          allowComments: pollData['allow_comments'] ?? true,
          allowRevote: pollData['allow_revote'] ?? false,
          isActive: pollData['is_active'] ?? true,
          totalVotes: totalVotes,
          createdAt: DateTime.parse(pollData['created_at']),
          expiresAt: pollData['expires_at'] != null 
              ? DateTime.parse(pollData['expires_at']) 
              : null,
          userVote: userVote,
          authorName: _getAuthorName(authorProfile),
          authorAvatar: authorProfile?['avatar_url'],
          isVerified: authorProfile?['is_verified'] ?? false,
        ));
      }

      return polls;
    } catch (e) {
      throw Exception('فشل في جلب التصويتات: $e');
    }
  }

  // الحصول على إجمالي الأصوات لتصويت معين
  Future<int> _getTotalVotes(String pollId) async {
    try {
      print('📊 حساب إجمالي الأصوات للتصويت: $pollId');
      
      final response = await _client
          .from('poll_votes')
          .select('id')
          .eq('poll_id', pollId);
      
      final totalVotes = (response as List).length;
      print('✅ إجمالي الأصوات: $totalVotes');
      
      return totalVotes;
    } catch (e) {
      print('❌ خطأ في حساب إجمالي الأصوات: $e');
      return 0;
    }
  }

  // التصويت على خيار
  Future<void> vote(String pollId, String optionId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      print('🗳️ بدء التصويت: Poll=$pollId, Option=$optionId, User=$userId');

      // التحقق من وجود تصويت سابق
      final existingVote = await _client
          .from('poll_votes')
          .select('id, option_id')
          .eq('poll_id', pollId)
          .eq('user_id', userId)
          .maybeSingle();

      if (existingVote != null) {
        print('🔄 تصويت سابق موجود: ${existingVote['option_id']}');
        
        // التحقق من إمكانية إعادة التصويت
        final poll = await _client
            .from('polls')
            .select('allow_revote')
            .eq('id', pollId)
            .single();

        if (!poll['allow_revote']) {
          throw Exception('لا يمكن تغيير التصويت');
        }

        // تحديث التصويت
        await _client
            .from('poll_votes')
            .update({'option_id': optionId})
            .eq('id', existingVote['id']);
            
        print('✅ تم تحديث التصويت من ${existingVote['option_id']} إلى $optionId');
        
        // تحديث عدد الأصوات للخيار القديم
        await _updateOptionVotes(existingVote['option_id']);
      } else {
        print('🆕 إضافة تصويت جديد');
        
        // إضافة تصويت جديد
        await _client
            .from('poll_votes')
            .insert({
              'poll_id': pollId,
              'option_id': optionId,
              'user_id': userId,
            });
      }

      // تحديث عدد الأصوات في جدول الخيارات
      await _updateOptionVotes(optionId);
      
      // تحديث جميع الأصوات للتأكد من الدقة
      await _updateAllPollVotes(pollId);
      
      print('✅ تم التصويت بنجاح');

    } catch (e) {
      print('❌ خطأ في التصويت: $e');
      throw Exception('فشل في التصويت: $e');
    }
  }

  // تحديث عدد الأصوات لخيار معين
  Future<void> _updateOptionVotes(String optionId) async {
    try {
      // حساب عدد الأصوات الفعلي من جدول poll_votes
      final votesCount = await _client
          .from('poll_votes')
          .select('id')
          .eq('option_id', optionId);

      final votesLength = (votesCount as List).length;
      
      // تحديث عدد الأصوات في جدول poll_options
      await _client
          .from('poll_options')
          .update({'votes': votesLength})
          .eq('id', optionId);
          
      print('✅ تم تحديث عدد الأصوات للخيار $optionId: $votesLength');
    } catch (e) {
      print('❌ خطأ في تحديث عدد الأصوات: $e');
    }
  }

  // تحديث جميع الأصوات في تصويت معين
  Future<void> _updateAllPollVotes(String pollId) async {
    try {
      // الحصول على جميع خيارات التصويت
      final options = await _client
          .from('poll_options')
          .select('id')
          .eq('poll_id', pollId);

      // تحديث عدد الأصوات لكل خيار
      for (final option in options as List) {
        await _updateOptionVotes(option['id']);
      }
      
      print('✅ تم تحديث جميع الأصوات للتصويت $pollId');
    } catch (e) {
      print('❌ خطأ في تحديث جميع الأصوات: $e');
    }
  }

  // الحصول على تصويت محدد
  Future<Poll?> getPoll(String pollId) async {
    try {
      final polls = await getPolls();
      return polls.firstWhere((poll) => poll.id == pollId);
    } catch (e) {
      return null;
    }
  }

  // الحصول على التصويتات الأكثر تفاعلاً
  Future<List<Poll>> getTrendingPolls({int limit = 10}) async {
    try {
      // الحصول على التصويتات مرتبة حسب إجمالي الأصوات
      final response = await _client
          .from('polls')
          .select('''
            *,
            poll_options(*),
            profiles!polls_user_id_fkey(username, full_name, avatar_url, is_verified)
          ''')
          .eq('is_active', true)
          .order('created_at', ascending: false)
          .limit(limit * 3); // جلب عدد أكبر للفلترة

      List<Poll> polls = [];
      final currentUserId = _client.auth.currentUser?.id;

      for (var pollData in response as List) {
        final totalVotes = await _getTotalVotes(pollData['id']);
        
        // تخطي التصويتات بدون أصوات
        if (totalVotes == 0) continue;

        String? userVote;
        if (currentUserId != null) {
          try {
            final voteResponse = await _client
                .from('poll_votes')
                .select('option_id')
                .eq('poll_id', pollData['id'])
                .eq('user_id', currentUserId)
                .maybeSingle();
            
            userVote = voteResponse?['option_id'];
          } catch (e) {
            // تجاهل الخطأ
          }
        }

        final options = (pollData['poll_options'] as List)
            .map((option) => option as Map<String, dynamic>)
            .toList();
        
        final pollOptions = options.map((option) {
          final votes = option['votes'] ?? 0;
          final percentage = totalVotes > 0 ? (votes / totalVotes) * 100 : 0.0;
          
          return PollOption(
            id: option['id'],
            text: option['text'],
            votes: votes,
            percentage: percentage,
          );
        }).toList();

        pollOptions.sort((a, b) {
          final aOrder = options.firstWhere((o) => o['id'] == a.id)['option_order'] ?? 0;
          final bOrder = options.firstWhere((o) => o['id'] == b.id)['option_order'] ?? 0;
          return aOrder.compareTo(bOrder);
        });

        // جلب بيانات المؤلف بشكل منفصل
        final authorProfile = await _getAuthorProfile(pollData['user_id']);
        
        // Debug prints لمراقبة بيانات المؤلف
        print('👤 بيانات المؤلف للتصويت الرائج ${pollData['id']}:');
        print('   - User ID: ${pollData['user_id']}');
        print('   - Author Profile: $authorProfile');
        print('   - Name: ${authorProfile?['name']}');
        print('   - Username: ${authorProfile?['username']}');
        print('   - Is Verified: ${authorProfile?['is_verified']}');
        print('   - Author Name: ${_getAuthorName(authorProfile)}');
        
        polls.add(Poll(
          id: pollData['id'],
          userId: pollData['user_id'],
          question: pollData['question'],
          options: pollOptions,
          type: PollType.values.firstWhere(
            (t) => t.name == pollData['type'],
            orElse: () => PollType.public,
          ),
          category: PollCategory.values.firstWhere(
            (c) => c.name == pollData['category'],
            orElse: () => PollCategory.general,
          ),
          duration: PollDuration.values.firstWhere(
            (d) => d.name == pollData['duration'],
            orElse: () => PollDuration.unlimited,
          ),
          allowComments: pollData['allow_comments'] ?? true,
          allowRevote: pollData['allow_revote'] ?? false,
          isActive: pollData['is_active'] ?? true,
          totalVotes: totalVotes,
          createdAt: DateTime.parse(pollData['created_at']),
          expiresAt: pollData['expires_at'] != null 
              ? DateTime.parse(pollData['expires_at']) 
              : null,
          userVote: userVote,
          authorName: _getAuthorName(authorProfile),
          authorAvatar: authorProfile?['avatar_url'],
          isVerified: authorProfile?['is_verified'] ?? false,
        ));
      }

      // ترتيب حسب إجمالي الأصوات
      polls.sort((a, b) => b.totalVotes.compareTo(a.totalVotes));
      
      return polls.take(limit).toList();
    } catch (e) {
      throw Exception('فشل في جلب التصويتات الرائجة: $e');
    }
  }

  // حذف تصويت
  Future<void> deletePoll(String pollId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('polls')
          .delete()
          .eq('id', pollId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في حذف التصويت: $e');
    }
  }

  // البحث في التصويتات
  Future<List<Poll>> searchPolls(String query) async {
    try {
      final response = await _client
          .from('polls')
          .select('''
            *,
            poll_options(*),
            profiles!polls_user_id_fkey(username, full_name, avatar_url, is_verified)
          ''')
          .eq('is_active', true)
          .ilike('question', '%$query%')
          .order('created_at', ascending: false)
          .limit(20);

      List<Poll> polls = [];
      final currentUserId = _client.auth.currentUser?.id;

      for (var pollData in response as List) {
        String? userVote;
        if (currentUserId != null) {
          try {
            final voteResponse = await _client
                .from('poll_votes')
                .select('option_id')
                .eq('poll_id', pollData['id'])
                .eq('user_id', currentUserId)
                .maybeSingle();
            
            userVote = voteResponse?['option_id'];
          } catch (e) {
            // تجاهل الخطأ
          }
        }

        final options = (pollData['poll_options'] as List)
            .map((option) => option as Map<String, dynamic>)
            .toList();

        final totalVotes = await _getTotalVotes(pollData['id']);
        
        final pollOptions = options.map((option) {
          final votes = option['votes'] ?? 0;
          final percentage = totalVotes > 0 ? (votes / totalVotes) * 100 : 0.0;
          
          return PollOption(
            id: option['id'],
            text: option['text'],
            votes: votes,
            percentage: percentage,
          );
        }).toList();

        pollOptions.sort((a, b) {
          final aOrder = options.firstWhere((o) => o['id'] == a.id)['option_order'] ?? 0;
          final bOrder = options.firstWhere((o) => o['id'] == b.id)['option_order'] ?? 0;
          return aOrder.compareTo(bOrder);
        });

        // جلب بيانات المؤلف بشكل منفصل
        final authorProfile = await _getAuthorProfile(pollData['user_id']);
        
        // Debug prints لمراقبة بيانات المؤلف
        print('👤 بيانات المؤلف للتصويت المبحوث ${pollData['id']}:');
        print('   - User ID: ${pollData['user_id']}');
        print('   - Author Profile: $authorProfile');
        print('   - Name: ${authorProfile?['name']}');
        print('   - Username: ${authorProfile?['username']}');
        print('   - Is Verified: ${authorProfile?['is_verified']}');
        print('   - Author Name: ${_getAuthorName(authorProfile)}');
        
        polls.add(Poll(
          id: pollData['id'],
          userId: pollData['user_id'],
          question: pollData['question'],
          options: pollOptions,
          type: PollType.values.firstWhere(
            (t) => t.name == pollData['type'],
            orElse: () => PollType.public,
          ),
          category: PollCategory.values.firstWhere(
            (c) => c.name == pollData['category'],
            orElse: () => PollCategory.general,
          ),
          duration: PollDuration.values.firstWhere(
            (d) => d.name == pollData['duration'],
            orElse: () => PollDuration.unlimited,
          ),
          allowComments: pollData['allow_comments'] ?? true,
          allowRevote: pollData['allow_revote'] ?? false,
          isActive: pollData['is_active'] ?? true,
          totalVotes: totalVotes,
          createdAt: DateTime.parse(pollData['created_at']),
          expiresAt: pollData['expires_at'] != null 
              ? DateTime.parse(pollData['expires_at']) 
              : null,
          userVote: userVote,
          authorName: _getAuthorName(authorProfile),
          authorAvatar: authorProfile?['avatar_url'],
          isVerified: authorProfile?['is_verified'] ?? false,
        ));
      }

      return polls;
    } catch (e) {
      throw Exception('فشل في البحث: $e');
    }
  }

  // إعادة التصويت (حذف التصويت الحالي)
  Future<void> revote(String pollId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      print('🔄 بدء إعادة التصويت: Poll=$pollId, User=$userId');

      // البحث عن التصويت الحالي للمستخدم
      final existingVote = await _client
          .from('poll_votes')
          .select('id, option_id')
          .eq('poll_id', pollId)
          .eq('user_id', userId)
          .maybeSingle();

      if (existingVote != null) {
        print('🗑️ حذف التصويت الحالي: ${existingVote['option_id']}');
        
        // حذف التصويت الحالي
        await _client
            .from('poll_votes')
            .delete()
            .eq('id', existingVote['id']);
        
        print('✅ تم حذف التصويت من قاعدة البيانات');
        
        // تحديث عدد الأصوات للخيار الذي تم حذف التصويت منه
        await _updateOptionVotes(existingVote['option_id']);
        
        // تحديث جميع الأصوات للتأكد من الدقة
        await _updateAllPollVotes(pollId);
        
        print('✅ تم تحديث جميع الأصوات بعد الحذف');
      } else {
        print('ℹ️ لا يوجد تصويت سابق لحذفه');
      }
      
      print('✅ تم إعادة التصويت بنجاح');
      
    } catch (e) {
      print('❌ خطأ في إعادة التصويت: $e');
      throw Exception('فشل في إعادة التصويت: $e');
    }
  }

  // تحديث حالة التصويت (نشط/غير نشط)
  Future<void> updatePollStatus(String pollId, bool isActive) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('polls')
          .update({'is_active': isActive})
          .eq('id', pollId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في تحديث حالة التصويت: $e');
    }
  }

  // تحديث إعدادات التصويت
  Future<void> updatePollSettings(String pollId, {
    bool? allowComments,
    bool? allowRevote,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final updates = <String, dynamic>{};
      if (allowComments != null) updates['allow_comments'] = allowComments;
      if (allowRevote != null) updates['allow_revote'] = allowRevote;

      if (updates.isNotEmpty) {
        await _client
            .from('polls')
            .update(updates)
            .eq('id', pollId)
            .eq('user_id', userId);
      }
    } catch (e) {
      throw Exception('فشل في تحديث إعدادات التصويت: $e');
    }
  }

  // الحصول على تصويت محدد (محدث)
  Future<Poll?> getPollById(String pollId) async {
    try {
      final response = await _client
          .from('polls')
          .select('''
            *,
            profiles!polls_user_id_fkey(full_name, avatar_url, is_verified),
            poll_options(*)
          ''')
          .eq('id', pollId)
          .eq('is_active', true)
          .single();

      final currentUserId = _client.auth.currentUser?.id;
      String? userVote;

      if (currentUserId != null) {
        try {
          final voteResponse = await _client
              .from('poll_votes')
              .select('option_id')
              .eq('poll_id', pollId)
              .eq('user_id', currentUserId)
              .maybeSingle();

          userVote = voteResponse?['option_id'];
        } catch (e) {
          // تجاهل الخطأ
        }
      }

      final options = (response['poll_options'] as List)
          .map((option) => option as Map<String, dynamic>)
          .toList();

      final totalVotes = await _getTotalVotes(pollId);

      final pollOptions = options.map((option) {
        final votes = option['votes'] ?? 0;
        final percentage = totalVotes > 0 ? (votes / totalVotes) * 100 : 0.0;

        return PollOption(
          id: option['id'],
          text: option['text'],
          votes: votes,
          percentage: percentage,
        );
      }).toList();

      pollOptions.sort((a, b) {
        final aOrder = options.firstWhere((o) => o['id'] == a.id)['option_order'] ?? 0;
        final bOrder = options.firstWhere((o) => o['id'] == b.id)['option_order'] ?? 0;
        return aOrder.compareTo(bOrder);
      });

      final profile = response['profiles'];

      return Poll(
        id: response['id'],
        userId: response['user_id'],
        question: response['question'],
        options: pollOptions,
        type: PollType.values.firstWhere(
          (t) => t.name == response['type'],
          orElse: () => PollType.public,
        ),
        category: PollCategory.values.firstWhere(
          (c) => c.name == response['category'],
          orElse: () => PollCategory.general,
        ),
        duration: PollDuration.values.firstWhere(
          (d) => d.name == response['duration'],
          orElse: () => PollDuration.unlimited,
        ),
        allowComments: response['allow_comments'] ?? true,
        allowRevote: response['allow_revote'] ?? false,
        isActive: response['is_active'] ?? true,
        totalVotes: totalVotes,
        createdAt: DateTime.parse(response['created_at']),
        expiresAt: response['expires_at'] != null
            ? DateTime.parse(response['expires_at'])
            : null,
        userVote: userVote,
        authorName: profile?['full_name'] ?? profile?['username'],
        authorAvatar: profile?['avatar_url'],
        isVerified: profile?['is_verified'] ?? false,
      );
    } catch (e) {
      throw Exception('فشل في جلب التصويت: $e');
    }
  }
}
