import 'package:flutter/material.dart';
import '../supabase_service.dart';

class CreateCommunityPage extends StatefulWidget {
  const CreateCommunityPage({super.key});

  @override
  State<CreateCommunityPage> createState() => _CreateCommunityPageState();
}

class _CreateCommunityPageState extends State<CreateCommunityPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descController = TextEditingController();
  final _categoryController = TextEditingController();
  bool _isPrivate = false;
  bool _saving = false;

  // إعدادات إضافية
  bool _allowMemberPosts = true;
  bool _requireApproval = false;
  bool _allowComments = true;
  bool _allowInvites = true;
  String _postPermission = 'members';
  String _joinType = 'open';

  @override
  void dispose() {
    _nameController.dispose();
    _descController.dispose();
    _categoryController.dispose();
    super.dispose();
  }

  Future<void> _save() async {
    if (!_formKey.currentState!.validate()) return;
    setState(() => _saving = true);
    try {
      await SupabaseService().createCommunity(
        name: _nameController.text.trim(),
        description: _descController.text.trim().isEmpty ? null : _descController.text.trim(),
        category: _categoryController.text.trim().isEmpty ? null : _categoryController.text.trim(),
        isPrivate: _isPrivate,
        allowMemberPosts: _allowMemberPosts,
        requireApproval: _requireApproval,
        allowComments: _allowComments,
        allowInvites: _allowInvites,
        postPermission: _postPermission,
        joinType: _joinType,
      );
      if (mounted) Navigator.pop(context, true);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعذَّر إنشاء المجتمع: $e')),
        );
      }
    }
    if (mounted) setState(() => _saving = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إنشاء مجتمع')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              const Text('المعلومات الأساسية', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المجتمع',
                  border: OutlineInputBorder(),
                ),
                validator: (v) => (v == null || v.trim().isEmpty) ? 'مطلوب' : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descController,
                decoration: const InputDecoration(
                  labelText: 'الوصف',
                  border: OutlineInputBorder(),
                ),
                minLines: 2,
                maxLines: 4,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _categoryController,
                decoration: const InputDecoration(
                  labelText: 'الفئة',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 24),
              const Text('إعدادات الخصوصية', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              Card(
                child: Column(
                  children: [
                    SwitchListTile(
                      value: _isPrivate,
                      title: const Text('مجتمع خاص'),
                      subtitle: const Text('يتطلب موافقة للانضمام'),
                      onChanged: (v) => setState(() => _isPrivate = v),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      title: const Text('نوع الانضمام'),
                      subtitle: Text(_getJoinTypeText(_joinType)),
                      trailing: DropdownButton<String>(
                        value: _joinType,
                        items: const [
                          DropdownMenuItem(value: 'open', child: Text('مفتوح')),
                          DropdownMenuItem(value: 'approval', child: Text('بموافقة')),
                          DropdownMenuItem(value: 'invite_only', child: Text('بدعوة فقط')),
                        ],
                        onChanged: (value) => setState(() => _joinType = value!),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              const Text('إعدادات المحتوى', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              const SizedBox(height: 16),
              Card(
                child: Column(
                  children: [
                    SwitchListTile(
                      value: _allowMemberPosts,
                      title: const Text('السماح للأعضاء بالنشر'),
                      subtitle: const Text('يمكن للأعضاء إنشاء منشورات جديدة'),
                      onChanged: (v) => setState(() => _allowMemberPosts = v),
                    ),
                    const Divider(height: 1),
                    SwitchListTile(
                      value: _requireApproval,
                      title: const Text('تتطلب موافقة على المنشورات'),
                      subtitle: const Text('يجب موافقة المدير على المنشورات قبل نشرها'),
                      onChanged: (v) => setState(() => _requireApproval = v),
                    ),
                    const Divider(height: 1),
                    SwitchListTile(
                      value: _allowComments,
                      title: const Text('السماح بالتعليقات'),
                      subtitle: const Text('يمكن للأعضاء التعليق على المنشورات'),
                      onChanged: (v) => setState(() => _allowComments = v),
                    ),
                    const Divider(height: 1),
                    SwitchListTile(
                      value: _allowInvites,
                      title: const Text('السماح بالدعوات'),
                      subtitle: const Text('يمكن للأعضاء دعوة أشخاص آخرين'),
                      onChanged: (v) => setState(() => _allowInvites = v),
                    ),
                    const Divider(height: 1),
                    ListTile(
                      title: const Text('صلاحيات النشر'),
                      subtitle: Text(_getPostPermissionText(_postPermission)),
                      trailing: DropdownButton<String>(
                        value: _postPermission,
                        items: const [
                          DropdownMenuItem(value: 'all', child: Text('الجميع')),
                          DropdownMenuItem(value: 'members', child: Text('الأعضاء فقط')),
                          DropdownMenuItem(value: 'admins', child: Text('المدراء فقط')),
                        ],
                        onChanged: (value) => setState(() => _postPermission = value!),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _saving ? null : _save,
                  child: _saving
                    ? const CircularProgressIndicator()
                    : const Text('إنشاء المجتمع', style: TextStyle(fontSize: 16)),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  String _getJoinTypeText(String type) {
    switch (type) {
      case 'open': return 'مفتوح للجميع';
      case 'approval': return 'يتطلب موافقة';
      case 'invite_only': return 'بدعوة فقط';
      default: return 'غير محدد';
    }
  }

  String _getPostPermissionText(String permission) {
    switch (permission) {
      case 'all': return 'الجميع';
      case 'members': return 'الأعضاء فقط';
      case 'admins': return 'المدراء فقط';
      default: return 'غير محدد';
    }
  }
} 