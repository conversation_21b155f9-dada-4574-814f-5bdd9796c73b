import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/charity_item.dart';
import '../services/charity_service.dart';
import '../widgets/charity_card.dart';
import 'edit_charity_item_page.dart';

class CharitySettingsPage extends StatefulWidget {
  const CharitySettingsPage({super.key});

  @override
  State<CharitySettingsPage> createState() => _CharitySettingsPageState();
}

class _CharitySettingsPageState extends State<CharitySettingsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final CharityService _charityService = CharityService();
  
  List<CharityItem> _myItems = [];
  List<CharityItem> _myInterests = [];
  
  bool _loading = true;
  bool _notificationsEnabled = true;
  bool _showPhoneNumber = true;
  bool _allowDirectContact = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    setState(() => _loading = true);
    try {
      final myItems = await _charityService.getUserCharityItems();
      final myInterests = await _charityService.getUserInterests();
      
      setState(() {
        _myItems = myItems;
        _myInterests = myInterests;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'إعدادات الصدقات',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.teal[600],
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.list_alt),
              text: 'منشوراتي',
            ),
            Tab(
              icon: Icon(Icons.favorite_outline),
              text: 'اهتماماتي',
            ),
            Tab(
              icon: Icon(Icons.settings),
              text: 'الإعدادات',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMyItemsTab(),
          _buildMyInterestsTab(),
          _buildSettingsTab(),
        ],
      ),
    );
  }

  Widget _buildMyItemsTab() {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_myItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لم تقم بإنشاء أي عناصر بعد',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.add),
              label: const Text('إضافة عنصر جديد'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.teal[600],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadUserData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _myItems.length,
        itemBuilder: (context, index) {
          final item = _myItems[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات العنصر
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _getTypeColor(item.type).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: _getTypeColor(item.type).withValues(alpha: 0.3)),
                        ),
                        child: Text(
                          item.type.arabicName,
                          style: TextStyle(
                            fontSize: 12,
                            color: _getTypeColor(item.type),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      const Spacer(),
                      if (item.isCompleted)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.green[100],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.check_circle, size: 14, color: Colors.green[600]),
                              const SizedBox(width: 4),
                              Text(
                                'مكتمل',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.green[600],
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // العنوان والوصف
                  Text(
                    item.title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    item.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // معلومات إضافية
                  Row(
                    children: [
                      Icon(Icons.location_on_outlined, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        item.city,
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                      const SizedBox(width: 16),
                      Icon(Icons.people_outline, size: 16, color: Colors.grey[600]),
                      const SizedBox(width: 4),
                      Text(
                        '${item.interestCount} مهتم',
                        style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                      ),
                      const Spacer(),
                      Text(
                        item.formattedTime,
                        style: TextStyle(fontSize: 12, color: Colors.grey[500]),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // أزرار الإجراءات
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _editItem(item),
                          icon: const Icon(Icons.edit, size: 16),
                          label: const Text('تعديل'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.blue[600],
                            side: BorderSide(color: Colors.blue[600]!),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: item.isCompleted ? null : () => _markAsCompleted(item),
                          icon: const Icon(Icons.check_circle, size: 16),
                          label: Text(item.isCompleted ? 'مكتمل' : 'إكمال'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.green[600],
                            side: BorderSide(color: Colors.green[600]!),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _deleteItem(item),
                          icon: const Icon(Icons.delete, size: 16),
                          label: const Text('حذف'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.red[600],
                            side: BorderSide(color: Colors.red[600]!),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMyInterestsTab() {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_myInterests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.favorite_border,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لم تبدِ اهتماماً بأي عناصر بعد',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadUserData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _myInterests.length,
        itemBuilder: (context, index) {
          return CharityCard(
            item: _myInterests[index],
            onTap: () => _showItemDetails(_myInterests[index]),
            onInterest: () => _removeInterest(_myInterests[index]),
          );
        },
      ),
    );
  }

  Widget _buildSettingsTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // إعدادات الإشعارات
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.notifications, color: Colors.teal[600]),
                    const SizedBox(width: 8),
                    const Text(
                      'الإشعارات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                SwitchListTile(
                  title: const Text('تفعيل الإشعارات'),
                  subtitle: const Text('استقبال إشعارات عند وجود اهتمام جديد'),
                  value: _notificationsEnabled,
                  onChanged: (value) {
                    setState(() => _notificationsEnabled = value);
                    _saveSettings();
                  },
                  activeColor: Colors.teal[600],
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // إعدادات الخصوصية
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.privacy_tip, color: Colors.teal[600]),
                    const SizedBox(width: 8),
                    const Text(
                      'الخصوصية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                SwitchListTile(
                  title: const Text('إظهار رقم الهاتف'),
                  subtitle: const Text('السماح للآخرين برؤية رقم هاتفك'),
                  value: _showPhoneNumber,
                  onChanged: (value) {
                    setState(() => _showPhoneNumber = value);
                    _saveSettings();
                  },
                  activeColor: Colors.teal[600],
                ),
                SwitchListTile(
                  title: const Text('السماح بالاتصال المباشر'),
                  subtitle: const Text('السماح للمهتمين بالاتصال بك مباشرة'),
                  value: _allowDirectContact,
                  onChanged: (value) {
                    setState(() => _allowDirectContact = value);
                    _saveSettings();
                  },
                  activeColor: Colors.teal[600],
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // إعدادات الحساب
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.account_circle, color: Colors.teal[600]),
                    const SizedBox(width: 8),
                    const Text(
                      'الحساب',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ListTile(
                  leading: const Icon(Icons.edit),
                  title: const Text('تعديل الملف الشخصي'),
                  subtitle: const Text('تحديث اسمك وصورتك الشخصية'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _editProfile,
                ),
                ListTile(
                  leading: const Icon(Icons.delete_forever, color: Colors.red),
                  title: const Text('حذف جميع بياناتي', style: TextStyle(color: Colors.red)),
                  subtitle: const Text('حذف جميع التبرعات والطلبات نهائياً'),
                  trailing: const Icon(Icons.arrow_forward_ios, color: Colors.red),
                  onTap: _deleteAllData,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Color _getTypeColor(CharityType type) {
    switch (type) {
      case CharityType.donation:
        return Colors.green;
      case CharityType.request:
        return Colors.blue;
      case CharityType.urgent:
        return Colors.red;
    }
  }

  void _editItem(CharityItem item) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditCharityItemPage(item: item),
      ),
    ).then((_) => _loadUserData());
  }

  void _markAsCompleted(CharityItem item) async {
    try {
      await _charityService.markAsCompleted(item.id);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم وضع علامة الإكمال بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
      _loadUserData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في الإكمال: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _deleteItem(CharityItem item) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف "${item.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _charityService.deleteCharityItem(item.id);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم الحذف بنجاح'),
                    backgroundColor: Colors.green,
                  ),
                );
                _loadUserData();
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('خطأ في الحذف: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _removeInterest(CharityItem item) async {
    try {
      await _charityService.removeInterest(item.id);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إلغاء الاهتمام بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
      _loadUserData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إلغاء الاهتمام: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showItemDetails(CharityItem item) {
    // عرض تفاصيل العنصر
  }

  void _saveSettings() {
    // حفظ الإعدادات في قاعدة البيانات أو التخزين المحلي
  }

  void _editProfile() {
    // فتح صفحة تعديل الملف الشخصي
  }

  void _deleteAllData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحذير!'),
        content: const Text(
          'هل أنت متأكد من حذف جميع بياناتك؟\n'
          'سيتم حذف جميع التبرعات والطلبات والاهتمامات نهائياً.\n'
          'هذا الإجراء لا يمكن التراجع عنه!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _charityService.deleteAllUserData();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم حذف جميع البيانات بنجاح'),
                    backgroundColor: Colors.green,
                  ),
                );
                _loadUserData();
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('خطأ في الحذف: $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: const Text('حذف نهائياً', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
