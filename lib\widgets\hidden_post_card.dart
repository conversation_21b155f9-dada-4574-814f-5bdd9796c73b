import 'package:flutter/material.dart';
import '../models/post.dart';
import 'post_hide_options_sheet.dart';

class HiddenPostCard extends StatelessWidget {
  final Post post;
  final VoidCallback onUndo;

  const HiddenPostCard({
    super.key,
    required this.post,
    required this.onUndo,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Icon and main message
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.visibility_off_rounded,
                    color: Colors.grey[600],
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'تم إخفاء المنشور',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[800],
                        ),
                      ),
                      Text(
                        _getHideReasonText(),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Benefits explanation
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[100]!),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline_rounded,
                    color: Colors.blue[600],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _getBenefitText(),
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.blue[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: onUndo,
                    icon: const Icon(Icons.undo_rounded, size: 18),
                    label: const Text('تراجع'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: Colors.blue[600],
                      side: BorderSide(color: Colors.blue[300]!),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: TextButton.icon(
                    onPressed: () => _showMoreOptions(context),
                    icon: const Icon(Icons.settings_rounded, size: 18),
                    label: const Text('خيارات أخرى'),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  String _getHideReasonText() {
    if (post.hideReason == null) return 'تم إخفاء هذا المنشور بناءً على طلبك';
    
    switch (post.hideReason!) {
      case 'notInterested':
        return 'لست مهتماً بهذا النوع من المحتوى';
      case 'spam':
        return 'تم تصنيفه كمحتوى مزعج';
      case 'inappropriate':
        return 'محتوى غير مناسب';
      case 'misleading':
        return 'معلومات مضللة';
      case 'repetitive':
        return 'محتوى متكرر';
      case 'hideUser':
        return 'تم إخفاء جميع منشورات ${post.userName}';
      case 'hideUserTemporary':
        return 'تم إخفاء منشورات ${post.userName} لمدة 30 يوماً';
      default:
        return 'تم إخفاء هذا المنشور';
    }
  }

  String _getBenefitText() {
    if (post.hideReason == null) return 'سيساعدنا هذا في تحسين المحتوى المعروض لك';
    
    switch (post.hideReason!) {
      case 'notInterested':
        return 'سنعرض عليك محتوى أقل شبهاً بهذا في المستقبل';
      case 'spam':
        return 'سنحمي حسابك من المحتوى المزعج والغير مرغوب فيه';
      case 'inappropriate':
        return 'سنقوم بمراجعة هذا النوع من المحتوى لضمان سلامة المجتمع';
      case 'misleading':
        return 'سنعمل على التحقق من صحة المعلومات المشابهة';
      case 'repetitive':
        return 'سنقلل من عرض المحتوى المتكرر في خلاصتك';
      case 'hideUser':
        return 'لن تظهر لك منشورات ${post.userName} في خلاصتك';
      case 'hideUserTemporary':
        return 'ستختفي منشورات ${post.userName} من خلاصتك لمدة 30 يوماً';
      default:
        return 'سيساعدنا هذا في تحسين تجربتك على المنصة';
    }
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.block_rounded),
              title: const Text('حظر المستخدم'),
              subtitle: Text('حظر ${post.userName} نهائياً'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement block user
              },
            ),
            ListTile(
              leading: const Icon(Icons.flag_rounded),
              title: const Text('الإبلاغ عن المنشور'),
              subtitle: const Text('إبلاغ عن محتوى مخالف'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement report post
              },
            ),
            ListTile(
              leading: const Icon(Icons.feedback_rounded),
              title: const Text('إرسال ملاحظات'),
              subtitle: const Text('أخبرنا برأيك حول هذا القرار'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement feedback
              },
            ),
          ],
        ),
      ),
    );
  }
}
