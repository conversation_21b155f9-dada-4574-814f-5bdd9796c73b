import 'package:flutter/material.dart';
import '../supabase_service.dart';
import '../widgets/profile_avatar.dart';
import 'profile_page.dart';

class InviteFollowersPage extends StatefulWidget {
  final String communityId;
  final String communityName;

  const InviteFollowersPage({
    super.key,
    required this.communityId,
    required this.communityName,
  });

  @override
  State<InviteFollowersPage> createState() => _InviteFollowersPageState();
}

class _InviteFollowersPageState extends State<InviteFollowersPage> {
  List<Map<String, dynamic>> _followers = [];
  List<Map<String, dynamic>> _following = [];
  List<Map<String, dynamic>> _allUsers = [];
  final Set<String> _invitedUsers = {};
  bool _loading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadUsers();
  }

  Future<void> _loadUsers() async {
    setState(() => _loading = true);
    try {
      // جلب المتابعين والمتابَعين
      final followers = await SupabaseService().fetchFollowersUsers();
      final following = await SupabaseService().fetchFollowingUsers();
      
      // دمج القوائم وإزالة التكرار
      final Map<String, Map<String, dynamic>> uniqueUsers = {};
      
      for (final user in followers) {
        uniqueUsers[user['id']] = {
          ...user,
          'relationship': 'follower', // يتابعني
        };
      }
      
      for (final user in following) {
        if (uniqueUsers.containsKey(user['id'])) {
          uniqueUsers[user['id']]!['relationship'] = 'mutual'; // متابعة متبادلة
        } else {
          uniqueUsers[user['id']] = {
            ...user,
            'relationship': 'following', // أتابعه
          };
        }
      }
      
      setState(() {
        _followers = followers;
        _following = following;
        _allUsers = uniqueUsers.values.toList();
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل المتابعين: $e')),
        );
      }
    }
  }

  List<Map<String, dynamic>> get _filteredUsers {
    if (_searchQuery.isEmpty) return _allUsers;
    return _allUsers.where((user) {
      final name = (user['name'] ?? '').toString().toLowerCase();
      final username = (user['username'] ?? '').toString().toLowerCase();
      final query = _searchQuery.toLowerCase();
      return name.contains(query) || username.contains(query);
    }).toList();
  }

  Future<void> _inviteUser(Map<String, dynamic> user) async {
    final userId = user['id'] as String;
    if (_invitedUsers.contains(userId)) return;

    try {
      await SupabaseService().inviteUserToCommunity(
        communityId: widget.communityId,
        userId: userId,
        communityName: widget.communityName,
      );
      
      setState(() {
        _invitedUsers.add(userId);
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم إرسال دعوة إلى ${user['name']}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل إرسال الدعوة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _getRelationshipText(String relationship) {
    switch (relationship) {
      case 'follower':
        return 'يتابعك';
      case 'following':
        return 'تتابعه';
      case 'mutual':
        return 'متابعة متبادلة';
      default:
        return '';
    }
  }

  Color _getRelationshipColor(String relationship) {
    switch (relationship) {
      case 'follower':
        return Colors.blue;
      case 'following':
        return Colors.green;
      case 'mutual':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  void _navigateToProfile(String userId, String username) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProfilePage(userId: userId, username: username),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('دعوة أصدقاء إلى ${widget.communityName}'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
      ),
      body: Column(
        children: [
          // شريط البحث
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              onChanged: (value) => setState(() => _searchQuery = value),
              decoration: InputDecoration(
                hintText: 'البحث عن متابع...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey[100],
              ),
            ),
          ),
          
          // إحصائيات سريعة
          if (!_loading)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('المتابعون', _followers.length, Colors.blue),
                  _buildStatItem('المتابَعون', _following.length, Colors.green),
                  _buildStatItem('المدعوون', _invitedUsers.length, Colors.purple),
                ],
              ),
            ),
          
          const SizedBox(height: 16),
          
          // قائمة المتابعين
          Expanded(
            child: _loading
                ? const Center(child: CircularProgressIndicator())
                : _filteredUsers.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
                            const SizedBox(height: 16),
                            Text(
                              _searchQuery.isEmpty 
                                  ? 'لا توجد متابعون للدعوة'
                                  : 'لا توجد نتائج للبحث',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      )
                    : ListView.builder(
                        itemCount: _filteredUsers.length,
                        itemBuilder: (context, index) {
                          final user = _filteredUsers[index];
                          final userId = user['id'] as String;
                          final isInvited = _invitedUsers.contains(userId);
                          
                          return Card(
                            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                            elevation: 2,
                            color: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: ListTile(
                              contentPadding: const EdgeInsets.all(12),
                              leading: GestureDetector(
                                onTap: () => _navigateToProfile(user['id'], user['username'] ?? ''),
                                child: ProfileAvatar(
                                  userId: user['id'],
                                  radius: 28,
                                ),
                              ),
                              title: GestureDetector(
                                onTap: () => _navigateToProfile(user['id'], user['username'] ?? ''),
                                child: Text(
                                  user['name'] ?? 'مستخدم',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (user['username'] != null)
                                    GestureDetector(
                                      onTap: () => _navigateToProfile(user['id'], user['username'] ?? ''),
                                      child: Text(
                                        '@${user['username']}',
                                        style: TextStyle(
                                          color: Colors.grey[600],
                                          fontSize: 14,
                                        ),
                                      ),
                                    ),
                                  const SizedBox(height: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                    decoration: BoxDecoration(
                                      color: _getRelationshipColor(user['relationship']),
                                      borderRadius: BorderRadius.circular(15),
                                    ),
                                    child: Text(
                                      _getRelationshipText(user['relationship']),
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 11,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              trailing: isInvited
                                  ? Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                      decoration: BoxDecoration(
                                        color: Colors.green,
                                        borderRadius: BorderRadius.circular(25),
                                      ),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: const [
                                          Icon(Icons.check, color: Colors.white, size: 16),
                                          SizedBox(width: 4),
                                          Text(
                                            'تم الإرسال',
                                            style: TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ],
                                      ),
                                    )
                                  : ElevatedButton.icon(
                                      onPressed: () => _inviteUser(user),
                                      icon: const Icon(Icons.person_add, size: 18),
                                      label: const Text('دعوة'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.blue,
                                        foregroundColor: Colors.white,
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(25),
                                        ),
                                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                      ),
                                    ),
                            ),
                          );
                        },
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, int count, Color color) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }
}
