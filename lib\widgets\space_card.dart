import 'package:flutter/material.dart';
import '../models/space.dart';
import '../services/spaces_service.dart';
import '../utils/number_format.dart';

class SpaceCard extends StatefulWidget {
  final Space space;
  final VoidCallback? onTap;
  final VoidCallback? onFollowToggle;
  final bool showOwnerBadge;
  final bool isHorizontal;

  const SpaceCard({
    super.key,
    required this.space,
    this.onTap,
    this.onFollowToggle,
    this.showOwnerBadge = false,
    this.isHorizontal = false,
  });

  @override
  State<SpaceCard> createState() => _SpaceCardState();
}

class _SpaceCardState extends State<SpaceCard> {
  final _spacesService = SpacesService();
  bool _isFollowing = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _isFollowing = widget.space.isFollowing;
  }

  Future<void> _toggleFollow() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final newFollowState = await _spacesService.toggleSpaceFollow(widget.space.id);
      setState(() {
        _isFollowing = newFollowState;
        _isLoading = false;
      });

      if (widget.onFollowToggle != null) {
        widget.onFollowToggle!();
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(newFollowState ? 'تمت متابعة المساحة' : 'تم إلغاء متابعة المساحة'),
            backgroundColor: newFollowState ? Colors.green : Colors.orange,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحديث المتابعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isHorizontal) {
      return _buildHorizontalCard();
    }
    return _buildVerticalCard();
  }

  Widget _buildVerticalCard() {
    return Container(
      width: double.infinity, // عرض كامل الشاشة
      color: Colors.white,
      child: InkWell(
        onTap: widget.onTap,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة الغلاف بعرض كامل الشاشة
            Container(
              height: 150, // ارتفاع أكبر
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    SpaceCategoryHelper.getCategoryColor(widget.space.category).withOpacity(0.7),
                    SpaceCategoryHelper.getCategoryColor(widget.space.category),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Stack(
                children: [
                  if (widget.space.coverImage != null)
                    Container(
                      width: double.infinity,
                      height: double.infinity,
                      decoration: BoxDecoration(
                        image: DecorationImage(
                          image: NetworkImage(widget.space.coverImage!),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  
                  // صورة الملف الشخصي للمساحة
                  Positioned(
                    bottom: 16,
                    right: 16,
                    child: Container(
                      width: 60,
                      height: 60,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 3),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withOpacity(0.2),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: CircleAvatar(
                        radius: 27,
                        backgroundColor: Colors.red[100],
                        backgroundImage: widget.space.profileImage != null && widget.space.profileImage!.isNotEmpty
                            ? NetworkImage(widget.space.profileImage!)
                            : null,
                        child: widget.space.profileImage == null || widget.space.profileImage!.isEmpty
                            ? Icon(
                                Icons.business,
                                color: Colors.red[600],
                                size: 30,
                              )
                            : null,
                      ),
                    ),
                  ),

                  // شارة المالك
                  if (widget.showOwnerBadge)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.red[600], // تغيير اللون إلى الأحمر
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'مساحتي',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),

                  // أيقونة الفئة
                  Positioned(
                    bottom: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withOpacity(0.9),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        SpaceCategoryHelper.getCategoryIcon(widget.space.category),
                        size: 20,
                        color: SpaceCategoryHelper.getCategoryColor(widget.space.category),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // اسم المساحة
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          widget.space.name,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      
                      // زر المتابعة
                      if (!widget.space.isOwner)
                        SizedBox(
                          height: 32,
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _toggleFollow,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: _isFollowing ? Colors.grey[300] : Colors.blue[600],
                              foregroundColor: _isFollowing ? Colors.black87 : Colors.white,
                              padding: const EdgeInsets.symmetric(horizontal: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                            ),
                            child: _isLoading
                                ? const SizedBox(
                                    width: 16,
                                    height: 16,
                                    child: CircularProgressIndicator(strokeWidth: 2),
                                  )
                                : Text(
                                    _isFollowing ? 'متابع' : 'متابعة',
                                    style: const TextStyle(fontSize: 12),
                                  ),
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: 4),

                  // اسم المالك والفئة
                  Row(
                    children: [
                      Text(
                        'بواسطة ${widget.space.ownerName}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: SpaceCategoryHelper.getCategoryColor(widget.space.category).withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          SpaceCategoryHelper.getCategoryName(widget.space.category),
                          style: TextStyle(
                            fontSize: 10,
                            color: SpaceCategoryHelper.getCategoryColor(widget.space.category),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // الوصف
                  Text(
                    widget.space.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 12),

                  // الإحصائيات
                  Row(
                    children: [
                      _buildStat(Icons.people, NumberFormatUtil.prettyCount(widget.space.followersCount), 'متابع'),
                      const SizedBox(width: 16),
                      _buildStat(Icons.article, NumberFormatUtil.prettyCount(widget.space.postsCount), 'منشور'),
                      const SizedBox(width: 16),
                      _buildStat(Icons.visibility, NumberFormatUtil.prettyCount(widget.space.viewsCount), 'مشاهدة'),
                    ],
                  ),
                ],
              ),
            ),

            // فاصل بين البطاقات
            Container(
              height: 8,
              color: Colors.grey[100],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHorizontalCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: widget.onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          height: 180,
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الهيدر
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: SpaceCategoryHelper.getCategoryColor(widget.space.category).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      SpaceCategoryHelper.getCategoryIcon(widget.space.category),
                      size: 20,
                      color: SpaceCategoryHelper.getCategoryColor(widget.space.category),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.space.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        Text(
                          widget.space.ownerName,
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (!widget.space.isOwner)
                    SizedBox(
                      height: 28,
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _toggleFollow,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _isFollowing ? Colors.grey[300] : Colors.blue[600],
                          foregroundColor: _isFollowing ? Colors.black87 : Colors.white,
                          padding: const EdgeInsets.symmetric(horizontal: 8),
                        ),
                        child: Text(
                          _isFollowing ? 'متابع' : 'متابعة',
                          style: const TextStyle(fontSize: 11),
                        ),
                      ),
                    ),
                ],
              ),

              const SizedBox(height: 12),

              // الوصف
              Expanded(
                child: Text(
                  widget.space.description,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[700],
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ),

              const SizedBox(height: 8),

              // الإحصائيات
              Row(
                children: [
                  _buildStat(Icons.people, NumberFormatUtil.prettyCount(widget.space.followersCount), ''),
                  const SizedBox(width: 12),
                  _buildStat(Icons.article, NumberFormatUtil.prettyCount(widget.space.postsCount), ''),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStat(IconData icon, String count, String label) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          count,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
        if (label.isNotEmpty) ...[
          const SizedBox(width: 2),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }
}
