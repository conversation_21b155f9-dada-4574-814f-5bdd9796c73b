-- إصلاح مشكلة المنشورات الفارغة
-- Fix posts media issue

-- 1. التحقق من وجود عمود media_urls
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' AND column_name = 'media_urls'
    ) THEN
        ALTER TABLE posts ADD COLUMN media_urls TEXT[];
        RAISE NOTICE 'تم إضافة عمود media_urls لجدول posts بنجاح';
    ELSE
        RAISE NOTICE 'عمود media_urls موجود بالفعل في جدول posts';
    END IF;
END $$;

-- 2. التحقق من وجود bucket media
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'media',
  'media', 
  true,  -- عام للجميع
  52428800,  -- 50MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/avi', 'video/mov']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 52428800,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/avi', 'video/mov'];

-- 3. إنشاء سياسات التخزين إذا لم تكن موجودة
DROP POLICY IF EXISTS "media_select_policy" ON storage.objects;
DROP POLICY IF EXISTS "media_insert_policy" ON storage.objects;
DROP POLICY IF EXISTS "media_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "media_delete_policy" ON storage.objects;

CREATE POLICY "media_select_policy"
ON storage.objects FOR SELECT
USING (bucket_id = 'media');

CREATE POLICY "media_insert_policy"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'media' 
  AND auth.uid() IS NOT NULL
);

CREATE POLICY "media_update_policy"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'media' 
  AND auth.uid() IS NOT NULL
);

CREATE POLICY "media_delete_policy"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'media' 
  AND auth.uid() IS NOT NULL
);

-- 4. إنشاء فهرس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_posts_media_urls ON posts USING GIN (media_urls);

-- 5. التحقق من المنشورات الأخيرة
SELECT 
    id,
    content,
    type,
    media_url,
    media_urls,
    created_at,
    user_id
FROM posts 
ORDER BY created_at DESC 
LIMIT 10;

-- 6. إنشاء منشور اختبار
INSERT INTO posts (
    user_id,
    content,
    type,
    media_urls,
    created_at
) VALUES (
    (SELECT id FROM auth.users LIMIT 1),
    'منشور اختبار للتحقق من إصلاح المشكلة',
    'image',
    ARRAY['https://example.com/test1.jpg', 'https://example.com/test2.jpg'],
    NOW()
) RETURNING id, content, media_urls;

-- 7. رسالة نجاح
SELECT 'تم إصلاح مشكلة المنشورات الفارغة بنجاح!' as result; 