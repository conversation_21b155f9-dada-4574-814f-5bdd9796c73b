import 'package:flutter/material.dart';
import '../models/charity_item.dart';
import '../services/charity_service.dart';
import '../widgets/charity_card.dart';
import 'add_charity_page.dart';
import 'charity_settings_page.dart';

class CharityPage extends StatefulWidget {
  const CharityPage({super.key});

  @override
  State<CharityPage> createState() => _CharityPageState();
}

class _CharityPageState extends State<CharityPage> with TickerProviderStateMixin {
  late TabController _tabController;
  final CharityService _charityService = CharityService();
  
  List<CharityItem> _donationItems = [];
  List<CharityItem> _requestItems = [];
  List<CharityItem> _urgentItems = [];

  bool _loading = true;
  String _selectedCity = 'الكل';
  String _selectedCategory = 'الكل';

  Map<String, int> _stats = {
    'completed': 0,
    'active_users': 0,
    'this_week': 0,
  };

  final List<String> _cities = [
    'الكل', 'الرياض', 'جدة', 'مكة', 'المدينة', 'الدمام', 'الخبر', 'تبوك', 'أبها', 'جازان'
  ];

  final List<String> _categories = [
    'الكل', 'طعام', 'ملابس', 'أثاث', 'أدوية', 'كتب', 'إلكترونيات', 'أخرى'
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadCharityItems();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadCharityItems() async {
    setState(() => _loading = true);
    try {
      final donations = await _charityService.getDonationItems();
      final requests = await _charityService.getRequestItems();
      final urgent = await _charityService.getUrgentItems();
      final stats = await _charityService.getCharityStats();

      setState(() {
        _donationItems = donations;
        _requestItems = requests;
        _urgentItems = urgent;
        _stats = stats;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'الصدقات',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.teal[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => const CharitySettingsPage()),
            ).then((_) => _loadCharityItems()),
            tooltip: 'الإعدادات',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.volunteer_activism),
              text: 'التبرعات',
            ),
            Tab(
              icon: Icon(Icons.help_outline),
              text: 'طلبات المساعدة',
            ),
            Tab(
              icon: Icon(Icons.priority_high),
              text: 'حالات طارئة',
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // شريط الإحصائيات
          _buildStatsBar(),
          
          // شريط التصفية
          _buildFilterBar(),
          
          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildCharityList(_donationItems, 'لا توجد تبرعات متاحة'),
                _buildCharityList(_requestItems, 'لا توجد طلبات مساعدة'),
                _buildCharityList(_urgentItems, 'لا توجد حالات طارئة'),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddCharityDialog(),
        backgroundColor: Colors.teal[600],
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text(
          'إضافة',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _buildStatsBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.teal[600],
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('تم التوصيل', '${_stats['completed']}', Icons.check_circle, Colors.white),
          _buildStatItem('متبرعين نشطين', '${_stats['active_users']}', Icons.people, Colors.white),
          _buildStatItem('هذا الأسبوع', '${_stats['this_week']}', Icons.calendar_today, Colors.white),
        ],
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          title,
          style: TextStyle(
            color: color,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Row(
        children: [
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedCity,
              decoration: InputDecoration(
                labelText: 'المدينة',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: _cities.map((city) => DropdownMenuItem(
                value: city,
                child: Text(city),
              )).toList(),
              onChanged: (value) {
                setState(() => _selectedCity = value!);
                _applyFilters();
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: InputDecoration(
                labelText: 'الفئة',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              ),
              items: _categories.map((category) => DropdownMenuItem(
                value: category,
                child: Text(category),
              )).toList(),
              onChanged: (value) {
                setState(() => _selectedCategory = value!);
                _applyFilters();
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCharityList(List<CharityItem> items, String emptyMessage) {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (items.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              emptyMessage,
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadCharityItems,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: items.length,
        itemBuilder: (context, index) {
          return CharityCard(
            item: items[index],
            onTap: () => _showCharityDetails(items[index]),
            onInterest: () => _showInterestDialog(items[index]),
          );
        },
      ),
    );
  }

  void _applyFilters() {
    // تطبيق التصفية حسب المدينة والفئة
    _loadCharityItems();
  }

  void _showAddCharityDialog() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            const Text(
              'ماذا تريد أن تفعل؟',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    'أريد التبرع',
                    'عرض شيء للتبرع',
                    Icons.volunteer_activism,
                    Colors.green,
                    () => _openAddCharity(CharityType.donation),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildActionButton(
                    'أحتاج مساعدة',
                    'طلب المساعدة',
                    Icons.help_outline,
                    Colors.blue,
                    () => _openAddCharity(CharityType.request),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 32),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: color,
                fontSize: 12,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _openAddCharity(CharityType type) {
    Navigator.pop(context);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AddCharityPage(type: type),
      ),
    ).then((_) => _loadCharityItems());
  }

  void _showCharityDetails(CharityItem item) {
    // عرض تفاصيل العنصر
  }

  void _showInterestDialog(CharityItem item) {
    // عرض حوار الاهتمام
  }
}
