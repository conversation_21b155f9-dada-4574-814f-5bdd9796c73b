import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../models/job_seeker.dart';
import '../services/job_seekers_service.dart';

class CreateJobSeekerPage extends StatefulWidget {
  const CreateJobSeekerPage({super.key});

  @override
  State<CreateJobSeekerPage> createState() => _CreateJobSeekerPageState();
}

class _CreateJobSeekerPageState extends State<CreateJobSeekerPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ageController = TextEditingController();
  final _nationalityController = TextEditingController();
  final _currentCountryController = TextEditingController();
  final _currentCityController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _preferredLocationController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _socialLinksController = TextEditingController();
  
  String _selectedGender = 'ذكر';
  MaritalStatus _selectedMaritalStatus = MaritalStatus.single;
  JobCategory _selectedCategory = JobCategory.other;
  JobType _selectedJobType = JobType.fullTime;
  int _experienceYears = 0;
  
  List<String> _skills = [];
  List<String> _languages = [];
  final TextEditingController _skillController = TextEditingController();
  final TextEditingController _languageController = TextEditingController();
  
  XFile? _profileImage;
  List<XFile> _portfolioImages = [];
  final ImagePicker _picker = ImagePicker();
  
  bool _loading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _ageController.dispose();
    _nationalityController.dispose();
    _currentCountryController.dispose();
    _currentCityController.dispose();
    _descriptionController.dispose();
    _preferredLocationController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _socialLinksController.dispose();
    _skillController.dispose();
    _languageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'إنشاء ملفك المهني',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.indigo[600],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // بطاقة ترحيبية
            _buildWelcomeCard(),
            
            const SizedBox(height: 16),
            
            // المعلومات الشخصية
            _buildPersonalInfoSection(),
            
            const SizedBox(height: 16),
            
            // المعلومات المهنية
            _buildProfessionalInfoSection(),
            
            const SizedBox(height: 16),
            
            // المهارات واللغات
            _buildSkillsSection(),
            
            const SizedBox(height: 16),
            
            // معلومات التواصل
            _buildContactSection(),
            
            const SizedBox(height: 16),
            
            // الصور
            _buildImagesSection(),
            
            const SizedBox(height: 24),
            
            // زر الحفظ
            _buildSaveButton(),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              Icons.person_add,
              size: 48,
              color: Colors.indigo[600],
            ),
            const SizedBox(height: 12),
            const Text(
              'أنشئ ملفك المهني',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'اعرض مهاراتك وخبراتك لأصحاب العمل بطريقة احترافية',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('المعلومات الشخصية', Icons.person),
            
            const SizedBox(height: 16),
            
            // الاسم الكامل
            _buildTextField(
              controller: _nameController,
              label: 'الاسم الكامل',
              icon: Icons.badge,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال الاسم الكامل';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // العمر والجنس
            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: _ageController,
                    label: 'العمر',
                    icon: Icons.cake,
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال العمر';
                      }
                      final age = int.tryParse(value);
                      if (age == null || age < 16 || age > 70) {
                        return 'العمر يجب أن يكون بين 16-70';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedGender,
                    decoration: InputDecoration(
                      labelText: 'الجنس',
                      prefixIcon: Icon(Icons.person_outline, color: Colors.indigo[600]),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                    items: ['ذكر', 'أنثى'].map((gender) {
                      return DropdownMenuItem(
                        value: gender,
                        child: Text(gender),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedGender = value!);
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // الحالة الاجتماعية
            DropdownButtonFormField<MaritalStatus>(
              value: _selectedMaritalStatus,
              decoration: InputDecoration(
                labelText: 'الحالة الاجتماعية',
                prefixIcon: Icon(Icons.family_restroom, color: Colors.indigo[600]),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              items: MaritalStatus.values.map((status) {
                return DropdownMenuItem(
                  value: status,
                  child: Text(status.arabicName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() => _selectedMaritalStatus = value!);
              },
            ),
            
            const SizedBox(height: 16),
            
            // الجنسية
            _buildTextField(
              controller: _nationalityController,
              label: 'الجنسية',
              icon: Icons.flag,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال الجنسية';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // البلد والمدينة الحالية
            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: _currentCountryController,
                    label: 'البلد الحالي',
                    icon: Icons.public,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال البلد';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildTextField(
                    controller: _currentCityController,
                    label: 'المدينة الحالية',
                    icon: Icons.location_city,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال المدينة';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfessionalInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('المعلومات المهنية', Icons.work),
            
            const SizedBox(height: 16),
            
            // فئة العمل
            DropdownButtonFormField<JobCategory>(
              value: _selectedCategory,
              decoration: InputDecoration(
                labelText: 'فئة العمل',
                prefixIcon: Icon(_selectedCategory.icon, color: Colors.indigo[600]),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              items: JobCategory.values.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Row(
                    children: [
                      Icon(category.icon, size: 20, color: category.color),
                      const SizedBox(width: 8),
                      Text(category.arabicName),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() => _selectedCategory = value!);
              },
            ),
            
            const SizedBox(height: 16),
            
            // سنوات الخبرة ونوع العمل المطلوب
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<int>(
                    value: _experienceYears,
                    decoration: InputDecoration(
                      labelText: 'سنوات الخبرة',
                      prefixIcon: Icon(Icons.work_history, color: Colors.indigo[600]),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                    items: List.generate(21, (index) => index).map((years) {
                      return DropdownMenuItem(
                        value: years,
                        child: Text(years == 0 ? 'بدون خبرة' : '$years سنة'),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _experienceYears = value!);
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<JobType>(
                    value: _selectedJobType,
                    decoration: InputDecoration(
                      labelText: 'نوع العمل المطلوب',
                      prefixIcon: Icon(_selectedJobType.icon, color: Colors.indigo[600]),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                    items: JobType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type.arabicName),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedJobType = value!);
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // الوصف
            _buildTextField(
              controller: _descriptionController,
              label: 'نبذة عن خبراتك ومهاراتك',
              icon: Icons.description,
              maxLines: 4,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال نبذة عن خبراتك';
                }
                if (value.trim().length < 20) {
                  return 'النبذة قصيرة جداً (20 حرف على الأقل)';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // المكان المفضل للعمل
            _buildTextField(
              controller: _preferredLocationController,
              label: 'المكان المفضل للعمل',
              icon: Icons.location_on,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال المكان المفضل للعمل';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkillsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('المهارات واللغات', Icons.star),
            
            const SizedBox(height: 16),
            
            // إضافة مهارة
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _skillController,
                    decoration: InputDecoration(
                      labelText: 'أضف مهارة',
                      prefixIcon: Icon(Icons.add_circle, color: Colors.indigo[600]),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _addSkill,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo[600],
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('إضافة'),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // عرض المهارات
            if (_skills.isNotEmpty)
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _skills.map((skill) {
                  return Chip(
                    label: Text(skill),
                    deleteIcon: const Icon(Icons.close, size: 18),
                    onDeleted: () => _removeSkill(skill),
                    backgroundColor: Colors.blue[50],
                    deleteIconColor: Colors.blue[700],
                  );
                }).toList(),
              ),
            
            const SizedBox(height: 16),
            
            // إضافة لغة
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _languageController,
                    decoration: InputDecoration(
                      labelText: 'أضف لغة',
                      prefixIcon: Icon(Icons.language, color: Colors.indigo[600]),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _addLanguage,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo[600],
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('إضافة'),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // عرض اللغات
            if (_languages.isNotEmpty)
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _languages.map((language) {
                  return Chip(
                    label: Text(language),
                    deleteIcon: const Icon(Icons.close, size: 18),
                    onDeleted: () => _removeLanguage(language),
                    backgroundColor: Colors.purple[50],
                    deleteIconColor: Colors.purple[700],
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('معلومات التواصل', Icons.contact_phone),
            
            const SizedBox(height: 16),
            
            // رقم الهاتف
            _buildTextField(
              controller: _phoneController,
              label: 'رقم الهاتف',
              icon: Icons.phone,
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال رقم الهاتف';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // البريد الإلكتروني (اختياري)
            _buildTextField(
              controller: _emailController,
              label: 'البريد الإلكتروني (اختياري)',
              icon: Icons.email,
              keyboardType: TextInputType.emailAddress,
            ),
            
            const SizedBox(height: 16),
            
            // روابط التواصل الاجتماعي (اختياري)
            _buildTextField(
              controller: _socialLinksController,
              label: 'روابط التواصل الاجتماعي (اختياري)',
              icon: Icons.link,
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImagesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('الصور', Icons.photo_camera),
            
            const SizedBox(height: 16),
            
            // الصورة الشخصية
            Row(
              children: [
                const Text('الصورة الشخصية:'),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: _pickProfileImage,
                  icon: const Icon(Icons.camera_alt),
                  label: Text(_profileImage == null ? 'اختر صورة' : 'تغيير الصورة'),
                ),
              ],
            ),
            
            if (_profileImage != null) ...[
              const SizedBox(height: 12),
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  _profileImage!.path,
                  height: 100,
                  width: 100,
                  fit: BoxFit.cover,
                ),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // صور الأعمال السابقة
            Row(
              children: [
                const Text('صور الأعمال السابقة:'),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: _pickPortfolioImages,
                  icon: const Icon(Icons.add_photo_alternate),
                  label: const Text('إضافة صور'),
                ),
              ],
            ),
            
            if (_portfolioImages.isNotEmpty) ...[
              const SizedBox(height: 12),
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _portfolioImages.length,
                  itemBuilder: (context, index) {
                    return Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              _portfolioImages[index].path,
                              width: 100,
                              height: 100,
                              fit: BoxFit.cover,
                            ),
                          ),
                          Positioned(
                            top: 4,
                            right: 4,
                            child: GestureDetector(
                              onTap: () => _removePortfolioImage(index),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _loading ? null : _saveJobSeeker,
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.indigo[600],
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _loading
            ? const CircularProgressIndicator(color: Colors.white)
            : const Text(
                'إنشاء الملف المهني',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.indigo[600]),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: Colors.indigo[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.indigo[600]!),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
    );
  }

  void _addSkill() {
    final skill = _skillController.text.trim();
    if (skill.isNotEmpty && !_skills.contains(skill)) {
      setState(() {
        _skills.add(skill);
        _skillController.clear();
      });
    }
  }

  void _removeSkill(String skill) {
    setState(() {
      _skills.remove(skill);
    });
  }

  void _addLanguage() {
    final language = _languageController.text.trim();
    if (language.isNotEmpty && !_languages.contains(language)) {
      setState(() {
        _languages.add(language);
        _languageController.clear();
      });
    }
  }

  void _removeLanguage(String language) {
    setState(() {
      _languages.remove(language);
    });
  }

  Future<void> _pickProfileImage() async {
    final image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _profileImage = image;
      });
    }
  }

  Future<void> _pickPortfolioImages() async {
    final images = await _picker.pickMultiImage();
    if (images.isNotEmpty) {
      setState(() {
        _portfolioImages.addAll(images);
        if (_portfolioImages.length > 5) {
          _portfolioImages = _portfolioImages.take(5).toList();
        }
      });
    }
  }

  void _removePortfolioImage(int index) {
    setState(() {
      _portfolioImages.removeAt(index);
    });
  }

  Future<void> _saveJobSeeker() async {
    if (!_formKey.currentState!.validate()) return;

    if (_skills.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة مهارة واحدة على الأقل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_languages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة لغة واحدة على الأقل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _loading = true);

    try {
      // رفع الصور (سيتم تطبيقه لاحقاً)
      String? profileImageUrl;
      List<String> portfolioImageUrls = [];

      // إنشاء الملف المهني
      final jobSeeker = JobSeeker(
        id: '',
        userId: '',
        fullName: _nameController.text.trim(),
        profileImage: profileImageUrl,
        age: int.parse(_ageController.text),
        gender: _selectedGender,
        maritalStatus: _selectedMaritalStatus,
        currentCountry: _currentCountryController.text.trim(),
        currentCity: _currentCityController.text.trim(),
        nationality: _nationalityController.text.trim(),
        category: _selectedCategory,
        skills: _skills,
        languages: _languages,
        experienceYears: _experienceYears,
        description: _descriptionController.text.trim(),
        preferredJobType: _selectedJobType,
        preferredLocation: _preferredLocationController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty 
            ? null 
            : _emailController.text.trim(),
        socialLinks: _socialLinksController.text.trim().isEmpty 
            ? null 
            : _socialLinksController.text.trim(),
        cvUrl: null,
        portfolioImages: portfolioImageUrls,
        isActive: true,
        viewsCount: 0,
        likesCount: 0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await JobSeekersService().addJobSeeker(jobSeeker);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء ملفك المهني بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء الملف المهني: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }
}
