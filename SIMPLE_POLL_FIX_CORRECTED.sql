-- إصلا<PERSON> مبسط لمشاكل التصويت (مصحح)
-- Simple poll voting fix (corrected)

-- 1. التحقق من وجود الجداول
SELECT 
    table_name,
    column_name,
    data_type
FROM information_schema.columns 
WHERE table_name IN ('polls', 'poll_options', 'poll_votes')
ORDER BY table_name, ordinal_position;

-- 2. إضافة فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_poll_votes_poll_id ON poll_votes(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_user_id ON poll_votes(user_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_option_id ON poll_votes(option_id);
CREATE INDEX IF NOT EXISTS idx_poll_options_poll_id ON poll_options(poll_id);

-- 3. إنشاء دالة لتحديث عدد الأصوات لخيار معين
CREATE OR REPLACE FUNCTION update_option_votes(p_option_id UUID)
RETURNS VOID AS $$
DECLARE
    votes_count INTEGER;
BEGIN
    -- حساب عدد الأصوات للخيار
    SELECT COUNT(*) INTO votes_count
    FROM poll_votes
    WHERE option_id = p_option_id;
    
    -- تحديث عدد الأصوات في جدول الخيارات
    UPDATE poll_options
    SET votes = votes_count
    WHERE id = p_option_id;
    
    RAISE NOTICE 'تم تحديث عدد الأصوات للخيار %: %', p_option_id, votes_count;
END;
$$ LANGUAGE plpgsql;

-- 4. إنشاء trigger لتحديث عدد الأصوات تلقائياً
CREATE OR REPLACE FUNCTION trigger_update_votes()
RETURNS TRIGGER AS $$
BEGIN
    -- إذا تم إضافة تصويت جديد
    IF TG_OP = 'INSERT' THEN
        PERFORM update_option_votes(NEW.option_id);
        RETURN NEW;
    END IF;
    
    -- إذا تم حذف تصويت
    IF TG_OP = 'DELETE' THEN
        PERFORM update_option_votes(OLD.option_id);
        RETURN OLD;
    END IF;
    
    -- إذا تم تحديث تصويت
    IF TG_OP = 'UPDATE' THEN
        -- تحديث الخيار القديم
        IF OLD.option_id != NEW.option_id THEN
            PERFORM update_option_votes(OLD.option_id);
        END IF;
        -- تحديث الخيار الجديد
        PERFORM update_option_votes(NEW.option_id);
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 5. إنشاء trigger
DROP TRIGGER IF EXISTS poll_votes_trigger ON poll_votes;
CREATE TRIGGER poll_votes_trigger
    AFTER INSERT OR UPDATE OR DELETE ON poll_votes
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_votes();

-- 6. تحديث عدد الأصوات للتصويتات الموجودة
DO $$
DECLARE
    option_record RECORD;
BEGIN
    FOR option_record IN 
        SELECT id FROM poll_options
    LOOP
        PERFORM update_option_votes(option_record.id);
    END LOOP;
    
    RAISE NOTICE 'تم تحديث عدد الأصوات لجميع الخيارات';
END $$;

-- 7. عرض إحصائيات التصويت
SELECT 
    'Poll Statistics' as info,
    COUNT(*) as total_polls,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_polls
FROM polls;

-- 8. عرض عينة من التصويتات
SELECT 
    p.id,
    p.question,
    COUNT(pv.id) as total_votes,
    COUNT(po.id) as options_count
FROM polls p
LEFT JOIN poll_options po ON p.id = po.poll_id
LEFT JOIN poll_votes pv ON p.id = pv.poll_id
WHERE p.is_active = true
GROUP BY p.id, p.question
ORDER BY p.created_at DESC
LIMIT 10;

-- 9. رسالة نجاح
SELECT 'تم إصلاح مشاكل التصويت بنجاح!' as result; 