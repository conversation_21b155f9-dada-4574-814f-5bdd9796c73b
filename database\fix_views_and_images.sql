-- إصلا<PERSON> مشكلة المشاهدات والصور

-- 1. إضافة سياسة تحديث المشاهدات للجميع
DROP POLICY IF EXISTS "تحديث المشاهدات للجميع" ON real_estate_properties;

CREATE POLICY "تحديث المشاهدات للجميع" ON real_estate_properties
    FOR UPDATE USING (is_active = true)
    WITH CHECK (is_active = true);

-- 2. حذف الدالة القديمة وإنشاء دالة جديدة لتحديث المشاهدات
DROP FUNCTION IF EXISTS increment_property_views(UUID);

CREATE OR REPLACE FUNCTION increment_property_views(property_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE real_estate_properties
  SET views_count = views_count + 1,
      updated_at = NOW()
  WHERE id = property_id AND is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. من<PERSON> صلاحية تنفيذ الدالة
GRANT EXECUTE ON FUNCTION increment_property_views(UUID) TO authenticated;

-- 4. التأكد من وجود bucket للصور
INSERT INTO storage.buckets (id, name, public)
VALUES ('property-images', 'property-images', true)
ON CONFLICT (id) DO NOTHING;

-- 5. إضافة سياسة للسماح برفع الصور
DROP POLICY IF EXISTS "Users can upload property images" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can view property images" ON storage.objects;

CREATE POLICY "Users can upload property images" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'property-images' AND auth.role() = 'authenticated');

CREATE POLICY "Anyone can view property images" ON storage.objects
    FOR SELECT USING (bucket_id = 'property-images');

-- 6. السماح بحذف الصور للمالك
CREATE POLICY "Users can delete their property images" ON storage.objects
    FOR DELETE USING (bucket_id = 'property-images' AND auth.role() = 'authenticated');

-- 7. تحديث سياسات جدول property_images
DROP POLICY IF EXISTS "الجميع يمكنهم قراءة صور العقارات النشطة" ON property_images;
DROP POLICY IF EXISTS "صاحب العقار يمكنه إدارة الصور" ON property_images;

CREATE POLICY "الجميع يمكنهم قراءة صور العقارات النشطة" ON property_images
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM real_estate_properties 
            WHERE id = property_images.property_id AND is_active = true
        )
    );

CREATE POLICY "المستخدمون يمكنهم إدارة صور عقاراتهم" ON property_images
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM real_estate_properties 
            WHERE id = property_images.property_id AND user_id = auth.uid()
        )
    );

-- 8. السماح للجميع بإدراج صور العقارات (للمستخدمين المسجلين)
CREATE POLICY "المستخدمون يمكنهم إضافة صور العقارات" ON property_images
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- رسالة نجاح
SELECT 'تم إصلاح مشاكل المشاهدات والصور بنجاح!' as message;
