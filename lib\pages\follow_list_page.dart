import 'package:flutter/material.dart';
import '../supabase_service.dart';
import 'profile_page.dart';

class FollowListPage extends StatefulWidget {
  final String userId;
  final bool showFollowers; // true => followers, false => following
  const FollowListPage({super.key, required this.userId, required this.showFollowers});

  @override
  State<FollowListPage> createState() => _FollowListPageState();
}

class _FollowListPageState extends State<FollowListPage> {
  List<Map<String, dynamic>> _users = [];
  bool _loading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    try {
      List<Map<String, dynamic>> list;
      if (widget.showFollowers) {
        list = await SupabaseService().fetchFollowersOf(widget.userId);
      } else {
        list = await SupabaseService().fetchFollowingOf(widget.userId);
      }
      if (mounted) {
        setState(() {
          _users = list;
          _loading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _loading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final title = widget.showFollowers ? 'المتابعون' : 'يتابع';
    return Scaffold(
      appBar: AppBar(title: Text(title)),
      body: _loading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(child: Text(_error!))
              : _users.isEmpty
                  ? const Center(child: Text('لا يوجد شيء لعرضه'))
                  : ListView.separated(
                      itemCount: _users.length,
                      separatorBuilder: (_, __) => const Divider(height: 0),
                      itemBuilder: (_, i) {
                        final u = _users[i];
                        return ListTile(
                          leading: CircleAvatar(
                            backgroundImage: (u['avatar_url'] ?? '').toString().isNotEmpty ? NetworkImage(u['avatar_url']) : null,
                            child: (u['avatar_url'] ?? '').toString().isEmpty ? Text((u['name'] ?? 'م')[0]) : null,
                          ),
                          title: Text(u['name'] ?? 'مستخدم'),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(builder: (_) => ProfilePage(userId: u['id'].toString(), username: '')),
                            );
                          },
                        );
                      },
                    ),
    );
  }
} 