import 'package:flutter/material.dart';
import '../models/marriage_profile.dart';
import '../services/marriage_service.dart';
import '../widgets/marriage_profile_card.dart';
import 'create_marriage_profile_page.dart';
import 'marriage_profile_details_page.dart';
import 'marriage_requests_page.dart';
import 'marriage_chats_list_page.dart';

class MarriagePage extends StatefulWidget {
  const MarriagePage({super.key});

  @override
  State<MarriagePage> createState() => _MarriagePageState();
}

class _MarriagePageState extends State<MarriagePage> with TickerProviderStateMixin {
  late TabController _tabController;
  List<MarriageProfile> _profiles = [];
  MarriageProfile? _myProfile;
  bool _loading = true;
  Gender? _selectedGender;
  int? _minAge;
  int? _maxAge;
  String? _selectedCity;
  MaritalStatus? _selectedMaritalStatus;
  MarriageGoal? _selectedGoal;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _loading = true);
    try {
      final profiles = await MarriageService().getAllProfiles();
      final myProfile = await MarriageService().getMyProfile();
      
      setState(() {
        _profiles = profiles;
        _myProfile = myProfile;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تحتاج إلى إنشاء جداول قاعدة البيانات أولاً'),
            backgroundColor: Colors.orange,
            action: SnackBarAction(
              label: 'معرفة المزيد',
              textColor: Colors.white,
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('إعداد قاعدة البيانات'),
                    content: const Text(
                      'يبدو أن جداول الزواج الشرعي غير موجودة في قاعدة البيانات.\n\n'
                      'يرجى تشغيل ملف create_marriage_tables.sql في قاعدة البيانات أولاً.'
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('حسناً'),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      }
    }
  }

  List<MarriageProfile> get _filteredProfiles {
    List<MarriageProfile> filtered = _profiles;

    // فلترة حسب الجنس
    if (_selectedGender != null) {
      filtered = filtered.where((profile) => profile.gender == _selectedGender).toList();
    }

    // فلترة حسب العمر
    if (_minAge != null) {
      filtered = filtered.where((profile) => profile.age >= _minAge!).toList();
    }
    if (_maxAge != null) {
      filtered = filtered.where((profile) => profile.age <= _maxAge!).toList();
    }

    // فلترة حسب المدينة
    if (_selectedCity != null && _selectedCity!.isNotEmpty) {
      filtered = filtered.where((profile) => 
        profile.city.toLowerCase().contains(_selectedCity!.toLowerCase())
      ).toList();
    }

    // فلترة حسب الحالة الاجتماعية
    if (_selectedMaritalStatus != null) {
      filtered = filtered.where((profile) => profile.maritalStatus == _selectedMaritalStatus).toList();
    }

    // فلترة حسب الهدف
    if (_selectedGoal != null) {
      filtered = filtered.where((profile) => profile.goal == _selectedGoal).toList();
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('بيت الحلال'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.pink,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: Colors.pink,
          tabs: const [
            Tab(text: 'تصفح الملفات'),
            Tab(text: 'طلبات التواصل'),
            Tab(text: 'المحادثات'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
          if (_myProfile != null)
            IconButton(
              icon: const Icon(Icons.person),
              onPressed: () => _viewMyProfile(),
            ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildProfilesTab(),
          _buildRequestsTab(),
          _buildChatsTab(),
        ],
      ),
      floatingActionButton: _myProfile == null ? FloatingActionButton.extended(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (_) => const CreateMarriageProfilePage()),
          );
          if (result == true) {
            _loadData();
          }
        },
        backgroundColor: Colors.pink,
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text('إنشاء ملف', style: TextStyle(color: Colors.white)),
      ) : null,
    );
  }

  Widget _buildProfilesTab() {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    final filteredProfiles = _filteredProfiles;

    if (filteredProfiles.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.pink[50],
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.favorite_outline, size: 64, color: Colors.pink[600]),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد ملفات متاحة حالياً',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'كن أول من ينشئ ملف في بيت الحلال!',
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            if (_myProfile == null) ...[
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: () async {
                  final result = await Navigator.push(
                    context,
                    MaterialPageRoute(builder: (_) => const CreateMarriageProfilePage()),
                  );
                  if (result == true) {
                    _loadData();
                  }
                },
                icon: const Icon(Icons.add),
                label: const Text('إنشاء ملف جديد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.pink,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredProfiles.length,
        itemBuilder: (context, index) {
          final profile = filteredProfiles[index];
          return MarriageProfileCard(
            profile: profile,
            onTap: () => _openProfileDetails(profile),
            showContactButton: _myProfile != null && profile.userId != _myProfile!.userId,
          );
        },
      ),
    );
  }

  Widget _buildRequestsTab() {
    if (_myProfile == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.person_add_disabled, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'تحتاج إلى إنشاء ملف أولاً',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'أنشئ ملفك الشخصي لتتمكن من إرسال واستقبال طلبات التواصل',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const CreateMarriageProfilePage()),
                );
                if (result == true) {
                  _loadData();
                }
              },
              icon: const Icon(Icons.add),
              label: const Text('إنشاء ملف'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.pink,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return const MarriageRequestsPage();
  }

  Widget _buildChatsTab() {
    if (_myProfile == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.chat_bubble_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'تحتاج إلى إنشاء ملف أولاً',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'أنشئ ملفك الشخصي لتتمكن من بدء المحادثات',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const CreateMarriageProfilePage()),
                );
                if (result == true) {
                  _loadData();
                }
              },
              icon: const Icon(Icons.add),
              label: const Text('إنشاء ملف'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.pink,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return const MarriageChatsListPage();
  }

  void _openProfileDetails(MarriageProfile profile) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => MarriageProfileDetailsPage(profile: profile),
      ),
    );
  }

  void _viewMyProfile() {
    if (_myProfile != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => MarriageProfileDetailsPage(
            profile: _myProfile!,
            isMyProfile: true,
          ),
        ),
      ).then((_) => _loadData());
    }
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة الملفات'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // فلترة الجنس
              DropdownButtonFormField<Gender?>(
                value: _selectedGender,
                decoration: const InputDecoration(labelText: 'الجنس'),
                items: [
                  const DropdownMenuItem(value: null, child: Text('الكل')),
                  ...Gender.values.map((gender) => DropdownMenuItem(
                    value: gender,
                    child: Text(gender == Gender.male ? 'ذكر' : 'أنثى'),
                  )),
                ],
                onChanged: (value) => setState(() => _selectedGender = value),
              ),
              const SizedBox(height: 16),
              // فلترة العمر
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(labelText: 'العمر من'),
                      keyboardType: TextInputType.number,
                      onChanged: (value) => _minAge = int.tryParse(value),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      decoration: const InputDecoration(labelText: 'إلى'),
                      keyboardType: TextInputType.number,
                      onChanged: (value) => _maxAge = int.tryParse(value),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              // فلترة المدينة
              TextFormField(
                decoration: const InputDecoration(labelText: 'المدينة'),
                onChanged: (value) => _selectedCity = value,
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedGender = null;
                _minAge = null;
                _maxAge = null;
                _selectedCity = null;
                _selectedMaritalStatus = null;
                _selectedGoal = null;
              });
              Navigator.pop(context);
            },
            child: const Text('مسح الفلاتر'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }
}
