import 'package:flutter/material.dart';
import '../models/space.dart';
import '../services/spaces_service.dart';
import '../widgets/space_card.dart';
import 'create_space_page.dart';
import 'space_details_page.dart';

class MySpacesPage extends StatefulWidget {
  const MySpacesPage({super.key});

  @override
  State<MySpacesPage> createState() => _MySpacesPageState();
}

class _MySpacesPageState extends State<MySpacesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _spacesService = SpacesService();
  final _searchController = TextEditingController();

  List<Space> _mySpaces = [];
  List<Space> _suggestedSpaces = [];
  List<Space> _searchResults = [];
  List<Space> _followedSpaces = [];

  bool _isLoading = true;
  bool _isSearching = false;
  SpaceCategory? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final results = await Future.wait([
        _spacesService.getUserSpacesWithPostsCount(), // استخدام الدالة الجديدة
        _spacesService.getSuggestedSpaces(),
        _spacesService.getFollowedSpaces(),
      ]);

      setState(() {
        _mySpaces = results[0];
        _suggestedSpaces = results[1];
        _followedSpaces = results[2];
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل البيانات: $e')),
        );
      }
    }
  }

  Future<void> _searchSpaces(String query) async {
    if (query.isEmpty) {
      setState(() {
        _isSearching = false;
        _searchResults.clear();
      });
      return;
    }

    setState(() {
      _isSearching = true;
    });

    try {
      final results = await _spacesService.searchSpaces(
        query: query,
        category: _selectedCategory,
      );

      setState(() {
        _searchResults = results;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في البحث: $e')),
        );
      }
    }
  }

  void _openSpaceDetails(Space space) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SpaceDetailsPage(space: space),
      ),
    ).then((_) => _loadData());
  }

  void _createNewSpace() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateSpacePage(),
      ),
    ).then((_) => _loadData());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'مساحاتي',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              labelColor: Colors.black,
              unselectedLabelColor: Colors.grey[600],
              indicatorColor: Colors.red[600],
              indicatorWeight: 3,
              labelStyle: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
              unselectedLabelStyle: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 15,
              ),
              tabs: const [
                Tab(text: 'مساحاتي'),
                Tab(text: 'المقترحة'),
                Tab(text: 'المتابعة'),
                Tab(text: 'استكشاف'),
              ],
            ),
          ),
        ),
        actions: [
          Container(
            margin: const EdgeInsets.only(left: 16, top: 8, bottom: 8),
            child: ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const CreateSpacePage()),
                ).then((_) => _loadData());
              },
              icon: const Icon(Icons.add, size: 18),
              label: const Text('إنشاء'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
                elevation: 2,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey[50],
            child: Column(
              children: [
                TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث عن مساحة...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon: _searchController.text.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              _searchSpaces('');
                            },
                          )
                        : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(25),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: _searchSpaces,
                ),
                
                // فلتر الفئات
                if (_isSearching) ...[
                  const SizedBox(height: 12),
                  SizedBox(
                    height: 40,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: SpaceCategory.values.length + 1,
                      itemBuilder: (context, index) {
                        if (index == 0) {
                          return Padding(
                            padding: const EdgeInsets.only(left: 8),
                            child: FilterChip(
                              label: const Text('الكل'),
                              selected: _selectedCategory == null,
                              onSelected: (selected) {
                                setState(() {
                                  _selectedCategory = null;
                                });
                                _searchSpaces(_searchController.text);
                              },
                            ),
                          );
                        }
                        
                        final category = SpaceCategory.values[index - 1];
                        return Padding(
                          padding: const EdgeInsets.only(left: 8),
                          child: FilterChip(
                            label: Text(SpaceCategoryHelper.getCategoryName(category)),
                            selected: _selectedCategory == category,
                            onSelected: (selected) {
                              setState(() {
                                _selectedCategory = selected ? category : null;
                              });
                              _searchSpaces(_searchController.text);
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ],
            ),
          ),

          // المحتوى
          Expanded(
            child: _isSearching
                ? _buildSearchResults()
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildMySpaces(),
                      _buildSuggestedSpaces(),
                      _buildFollowedSpaces(),
                      _buildExploreSpaces(),
                    ],
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewSpace,
        backgroundColor: Colors.blue[600],
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildSearchResults() {
    if (_searchResults.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('لا توجد نتائج للبحث'),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.zero, // إزالة الهوامش لعرض كامل الشاشة
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        return SpaceCard(
          space: _searchResults[index],
          onTap: () => _openSpaceDetails(_searchResults[index]),
          onFollowToggle: () => _loadData(),
        );
      },
    );
  }

  Widget _buildMySpaces() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_mySpaces.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.space_dashboard, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لم تقم بإنشاء أي مساحة بعد',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'أنشئ مساحتك الأولى لتبدأ في مشاركة المحتوى',
              style: TextStyle(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _createNewSpace,
              icon: const Icon(Icons.add),
              label: const Text('إنشاء مساحة جديدة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: EdgeInsets.zero, // إزالة الهوامش لعرض كامل الشاشة
        itemCount: _mySpaces.length,
        itemBuilder: (context, index) {
          return SpaceCard(
            space: _mySpaces[index],
            onTap: () => _openSpaceDetails(_mySpaces[index]),
            showOwnerBadge: true,
          );
        },
      ),
    );
  }

  Widget _buildSuggestedSpaces() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: EdgeInsets.zero, // إزالة الهوامش لعرض كامل الشاشة
        itemCount: _suggestedSpaces.length,
        itemBuilder: (context, index) {
          return SpaceCard(
            space: _suggestedSpaces[index],
            onTap: () => _openSpaceDetails(_suggestedSpaces[index]),
            onFollowToggle: () => _loadData(),
          );
        },
      ),
    );
  }

  Widget _buildFollowedSpaces() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_followedSpaces.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.favorite_border, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('لا تتابع أي مساحة بعد'),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: EdgeInsets.zero, // إزالة الهوامش لعرض كامل الشاشة
        itemCount: _followedSpaces.length,
        itemBuilder: (context, index) {
          return SpaceCard(
            space: _followedSpaces[index],
            onTap: () => _openSpaceDetails(_followedSpaces[index]),
            onFollowToggle: () => _loadData(),
          );
        },
      ),
    );
  }

  Widget _buildExploreSpaces() {
    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // المساحات الجديدة
          _buildSectionHeader('المساحات الجديدة', Icons.new_releases),
          SizedBox(
            height: 200,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _suggestedSpaces.take(5).length,
              itemBuilder: (context, index) {
                final space = _suggestedSpaces[index];
                return Container(
                  width: 300,
                  margin: const EdgeInsets.only(left: 12),
                  child: SpaceCard(
                    space: space,
                    onTap: () => _openSpaceDetails(space),
                    onFollowToggle: () => _loadData(),
                    isHorizontal: true,
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 24),

          // المساحات الشائعة
          _buildSectionHeader('المساحات الشائعة', Icons.trending_up),
          ...(_suggestedSpaces.take(3).map((space) => SpaceCard(
                space: space,
                onTap: () => _openSpaceDetails(space),
                onFollowToggle: () => _loadData(),
              ))),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, color: Colors.blue[600]),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
