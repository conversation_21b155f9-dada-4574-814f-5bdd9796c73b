import 'package:flutter/material.dart';
import '../models/space_post.dart';
import '../models/reaction_type.dart';
import '../services/space_posts_service.dart';
import '../utils/number_format.dart';
import '../widgets/interactive_verified_badge.dart';

class SpacePostCard extends StatefulWidget {
  final SpacePost post;
  final VoidCallback? onPostUpdated;
  final VoidCallback? onPostDeleted;

  const SpacePostCard({
    super.key,
    required this.post,
    this.onPostUpdated,
    this.onPostDeleted,
  });

  @override
  State<SpacePostCard> createState() => _SpacePostCardState();
}

class _SpacePostCardState extends State<SpacePostCard> {
  final _spacePostsService = SpacePostsService();
  late SpacePost _post;
  bool _isReacting = false;

  @override
  void initState() {
    super.initState();
    _post = widget.post;
  }

  Future<void> _toggleReaction(ReactionType reactionType) async {
    if (_isReacting) return;

    setState(() {
      _isReacting = true;
    });

    try {
      final hasReaction = await _spacePostsService.toggleSpacePostReaction(
        postId: _post.id,
        reactionType: reactionType,
      );

      // تحديث التفاعل محلياً
      final newReactionCounts = Map<ReactionType, int>.from(_post.reactionCounts);
      
      if (_post.currentUserReaction != null) {
        // إزالة التفاعل السابق
        final oldCount = newReactionCounts[_post.currentUserReaction!] ?? 0;
        newReactionCounts[_post.currentUserReaction!] = (oldCount - 1).clamp(0, double.infinity).toInt();
      }

      if (hasReaction) {
        // إضافة التفاعل الجديد
        final newCount = newReactionCounts[reactionType] ?? 0;
        newReactionCounts[reactionType] = newCount + 1;
      }

      setState(() {
        _post = _post.copyWith(
          currentUserReaction: hasReaction ? reactionType : null,
          reactionCounts: newReactionCounts,
          likesCount: newReactionCounts[ReactionType.like] ?? 0,
        );
        _isReacting = false;
      });

      if (widget.onPostUpdated != null) {
        widget.onPostUpdated!();
      }
    } catch (e) {
      setState(() {
        _isReacting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في التفاعل: $e')),
        );
      }
    }
  }

  Future<void> _deletePost() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المنشور'),
        content: const Text('هل أنت متأكد من حذف هذا المنشور؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        final success = await _spacePostsService.deleteSpacePost(_post.id);
        if (success && mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف المنشور')),
          );
          if (widget.onPostDeleted != null) {
            widget.onPostDeleted!();
          }
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('فشل في حذف المنشور: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // هيدر المنشور
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // صورة المساحة
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.blue[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.space_dashboard,
                    color: Colors.blue[600],
                    size: 24,
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // معلومات المساحة والمؤلف
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            _post.spaceName,
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          if (_post.isAuthor) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.blue[100],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'مسؤول',
                                style: TextStyle(
                                  fontSize: 10,
                                  color: Colors.blue[700],
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      Row(
                        children: [
                          Text(
                            'بواسطة ${_post.authorName} • ${_post.timeAgo}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          // شارة التحقق
                          if (_post.isAuthorVerified) ...[
                            const SizedBox(width: 4),
                            InteractiveVerifiedBadge(
                              size: 12,
                              userName: _post.authorName,
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                
                // قائمة الخيارات
                if (_post.canEdit || _post.canDelete)
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          // TODO: إضافة تعديل المنشور
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('تعديل المنشور قيد التطوير')),
                          );
                          break;
                        case 'delete':
                          _deletePost();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      if (_post.canEdit)
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 20),
                              SizedBox(width: 8),
                              Text('تعديل'),
                            ],
                          ),
                        ),
                      if (_post.canDelete)
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 20, color: Colors.red),
                              SizedBox(width: 8),
                              Text('حذف', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                    ],
                  ),
              ],
            ),
          ),

          // محتوى المنشور
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              _post.content,
              style: const TextStyle(fontSize: 16),
            ),
          ),

          // الوسائط (إذا وجدت)
          if (_post.hasMedia) ...[
            const SizedBox(height: 12),
            SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _post.mediaUrls.length,
                itemBuilder: (context, index) {
                  return Container(
                    width: 200,
                    margin: const EdgeInsets.only(left: 8),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image: NetworkImage(_post.mediaUrls[index]),
                        fit: BoxFit.cover,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],

          // الرابط (إذا وجد)
          if (_post.hasLink) ...[
            const SizedBox(height: 12),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (_post.linkTitle != null)
                    Text(
                      _post.linkTitle!,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  if (_post.linkDescription != null) ...[
                    const SizedBox(height: 4),
                    Text(
                      _post.linkDescription!,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                  const SizedBox(height: 4),
                  Text(
                    _post.linkUrl!,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue[600],
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 16),

          // أزرار التفاعل
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              border: Border(top: BorderSide(color: Colors.grey[200]!)),
            ),
            child: Row(
              children: [
                // زر الإعجاب
                Expanded(
                  child: InkWell(
                    onTap: _isReacting ? null : () => _toggleReaction(ReactionType.like),
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _post.hasReaction(ReactionType.like) ? Icons.thumb_up : Icons.thumb_up_outlined,
                            size: 20,
                            color: _post.hasReaction(ReactionType.like) ? Colors.blue[600] : Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            NumberFormatUtil.prettyCount(_post.getReactionCount(ReactionType.like)),
                            style: TextStyle(
                              color: _post.hasReaction(ReactionType.like) ? Colors.blue[600] : Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // زر التعليق
                Expanded(
                  child: InkWell(
                    onTap: () {
                      // TODO: إضافة التعليقات
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('التعليقات قيد التطوير')),
                      );
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.comment_outlined, size: 20, color: Colors.grey[600]),
                          const SizedBox(width: 4),
                          Text(
                            NumberFormatUtil.prettyCount(_post.commentsCount),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),

                // زر المشاركة
                Expanded(
                  child: InkWell(
                    onTap: () {
                      // TODO: إضافة المشاركة
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('المشاركة قيد التطوير')),
                      );
                    },
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.share_outlined, size: 20, color: Colors.grey[600]),
                          const SizedBox(width: 4),
                          Text(
                            NumberFormatUtil.prettyCount(_post.sharesCount),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
