-- =============================================================
--  إعداد نظام السوق الشامل
--  Comprehensive Marketplace System Setup
-- =============================================================

-- 1) إنشاء جدول فئات المنتجات
-- -------------------------------------------------------

CREATE TABLE IF NOT EXISTS product_categories (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name_ar TEXT NOT NULL,
  name_en TEXT NOT NULL,
  icon TEXT NOT NULL,
  color TEXT DEFAULT '#2196F3',
  parent_id UUID REFERENCES product_categories(id),
  is_active BOOLEAN DEFAULT true,
  sort_order INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 2) إنشاء جدول المنتجات الرئيسي
-- -------------------------------------------------------

CREATE TABLE IF NOT EXISTS marketplace_products (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  category_id UUID NOT NULL REFERENCES product_categories(id),

  -- معلومات أساسية
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  price DECIMAL(10,2) NOT NULL DEFAULT 0,
  currency TEXT DEFAULT 'MAD',
  is_negotiable BOOLEAN DEFAULT false,
  is_free BOOLEAN DEFAULT false,
  is_exchange BOOLEAN DEFAULT false,

  -- الموقع
  city TEXT NOT NULL,
  country TEXT DEFAULT 'Morocco',
  latitude DECIMAL(10,8),
  longitude DECIMAL(11,8),

  -- الحالة والنوع
  condition TEXT CHECK (condition IN ('new', 'used', 'refurbished')) DEFAULT 'used',
  status TEXT CHECK (status IN ('active', 'sold', 'reserved', 'inactive')) DEFAULT 'active',

  -- الصور والوسائط
  images TEXT[] DEFAULT '{}',
  video_url TEXT,

  -- معلومات الاتصال
  phone_enabled BOOLEAN DEFAULT true,
  whatsapp_enabled BOOLEAN DEFAULT true,
  chat_enabled BOOLEAN DEFAULT true,
  phone_number TEXT,
  whatsapp_number TEXT,

  -- إحصائيات
  views_count INTEGER DEFAULT 0,
  favorites_count INTEGER DEFAULT 0,
  contact_count INTEGER DEFAULT 0,

  -- الحقول الديناميكية (JSON لكل فئة)
  custom_fields JSONB DEFAULT '{}',

  -- التواريخ
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ DEFAULT (NOW() + INTERVAL '30 days'),

  -- فهارس
  CONSTRAINT marketplace_products_title_length CHECK (char_length(title) >= 3 AND char_length(title) <= 100),
  CONSTRAINT marketplace_products_description_length CHECK (char_length(description) >= 10 AND char_length(description) <= 2000),
  CONSTRAINT marketplace_products_price_positive CHECK (price >= 0)
);

-- 3) إنشاء جدول المفضلة
-- -------------------------------------------------------

CREATE TABLE IF NOT EXISTS product_favorites (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  product_id UUID NOT NULL REFERENCES marketplace_products(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, product_id)
);

-- 4) إنشاء جدول المحادثات
-- -------------------------------------------------------

CREATE TABLE IF NOT EXISTS product_chats (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID NOT NULL REFERENCES marketplace_products(id) ON DELETE CASCADE,
  buyer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  seller_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  last_message TEXT,
  last_message_at TIMESTAMPTZ DEFAULT NOW(),
  is_read_by_buyer BOOLEAN DEFAULT false,
  is_read_by_seller BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(product_id, buyer_id, seller_id)
);

-- 5) إنشاء جدول رسائل المحادثات
-- -------------------------------------------------------

CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  chat_id UUID NOT NULL REFERENCES product_chats(id) ON DELETE CASCADE,
  sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  message TEXT NOT NULL,
  message_type TEXT CHECK (message_type IN ('text', 'image', 'location', 'offer')) DEFAULT 'text',
  offer_price DECIMAL(10,2),
  is_read BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 6) إنشاء جدول التقييمات
-- -------------------------------------------------------

CREATE TABLE IF NOT EXISTS seller_reviews (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  seller_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  reviewer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  product_id UUID REFERENCES marketplace_products(id) ON DELETE SET NULL,
  rating INTEGER CHECK (rating >= 1 AND rating <= 5) NOT NULL,
  comment TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(seller_id, reviewer_id, product_id)
);

-- 7) إنشاء جدول الإبلاغات
-- -------------------------------------------------------

CREATE TABLE IF NOT EXISTS product_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  product_id UUID NOT NULL REFERENCES marketplace_products(id) ON DELETE CASCADE,
  reporter_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  reason TEXT NOT NULL,
  description TEXT,
  status TEXT CHECK (status IN ('pending', 'reviewed', 'resolved', 'dismissed')) DEFAULT 'pending',
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 8) إدراج الفئات الأساسية
-- -------------------------------------------------------

INSERT INTO product_categories (name_ar, name_en, icon, color, sort_order) VALUES
('الملابس', 'Clothing', 'checkroom', '#E91E63', 1),
('الأحذية', 'Shoes', 'sports_tennis', '#9C27B0', 2),
('الحقائب', 'Bags', 'work', '#673AB7', 3),
('الهواتف', 'Phones', 'smartphone', '#3F51B5', 4),
('أجهزة الكمبيوتر', 'Computers', 'computer', '#2196F3', 5),
('الإلكترونيات', 'Electronics', 'devices', '#03A9F4', 6),
('الأثاث والأفرشة', 'Furniture', 'chair', '#00BCD4', 7),
('الأواني المنزلية', 'Kitchenware', 'kitchen', '#009688', 8),
('السيارات', 'Cars', 'directions_car', '#4CAF50', 9),
('الكتب', 'Books', 'menu_book', '#8BC34A', 10),
('المأكولات', 'Food', 'restaurant', '#CDDC39', 11),
('الخدمات المنزلية', 'Home Services', 'home_repair_service', '#FFC107', 12);

-- 9) إدراج الفئات الفرعية للملابس
-- -------------------------------------------------------

INSERT INTO product_categories (name_ar, name_en, icon, parent_id, sort_order)
SELECT 'ملابس رجالية', 'Men Clothing', 'man', id, 1 FROM product_categories WHERE name_en = 'Clothing';

INSERT INTO product_categories (name_ar, name_en, icon, parent_id, sort_order)
SELECT 'ملابس نسائية', 'Women Clothing', 'woman', id, 2 FROM product_categories WHERE name_en = 'Clothing';

INSERT INTO product_categories (name_ar, name_en, icon, parent_id, sort_order)
SELECT 'ملابس أطفال', 'Kids Clothing', 'child_care', id, 3 FROM product_categories WHERE name_en = 'Clothing';

-- 10) إنشاء الفهارس
-- -------------------------------------------------------

CREATE INDEX IF NOT EXISTS idx_marketplace_products_user_id ON marketplace_products(user_id);
CREATE INDEX IF NOT EXISTS idx_marketplace_products_category_id ON marketplace_products(category_id);
CREATE INDEX IF NOT EXISTS idx_marketplace_products_city ON marketplace_products(city);
CREATE INDEX IF NOT EXISTS idx_marketplace_products_price ON marketplace_products(price);
CREATE INDEX IF NOT EXISTS idx_marketplace_products_condition ON marketplace_products(condition);
CREATE INDEX IF NOT EXISTS idx_marketplace_products_status ON marketplace_products(status);
CREATE INDEX IF NOT EXISTS idx_marketplace_products_created_at ON marketplace_products(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_marketplace_products_location ON marketplace_products(latitude, longitude);

-- فهرس البحث النصي
CREATE INDEX IF NOT EXISTS idx_marketplace_products_search ON marketplace_products
USING GIN(to_tsvector('arabic', title || ' ' || description));

-- فهارس الجداول الأخرى
CREATE INDEX IF NOT EXISTS idx_product_favorites_user_id ON product_favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_product_favorites_product_id ON product_favorites(product_id);
CREATE INDEX IF NOT EXISTS idx_product_chats_buyer_id ON product_chats(buyer_id);
CREATE INDEX IF NOT EXISTS idx_product_chats_seller_id ON product_chats(seller_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_chat_id ON chat_messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_seller_reviews_seller_id ON seller_reviews(seller_id);

-- 11) إنشاء دالة تحديث updated_at
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION update_marketplace_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 12) إنشاء المحفزات
-- -------------------------------------------------------

DROP TRIGGER IF EXISTS trigger_update_marketplace_products_updated_at ON marketplace_products;
CREATE TRIGGER trigger_update_marketplace_products_updated_at
  BEFORE UPDATE ON marketplace_products
  FOR EACH ROW
  EXECUTE FUNCTION update_marketplace_updated_at();

-- 13) إعداد RLS (Row Level Security)
-- -------------------------------------------------------

ALTER TABLE marketplace_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE seller_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_reports ENABLE ROW LEVEL SECURITY;

-- 14) سياسات الأمان للمنتجات
-- -------------------------------------------------------

DROP POLICY IF EXISTS "Anyone can view active products" ON marketplace_products;
CREATE POLICY "Anyone can view active products" ON marketplace_products
  FOR SELECT USING (status = 'active');

DROP POLICY IF EXISTS "Users can create their products" ON marketplace_products;
CREATE POLICY "Users can create their products" ON marketplace_products
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their products" ON marketplace_products;
CREATE POLICY "Users can update their products" ON marketplace_products
  FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their products" ON marketplace_products;
CREATE POLICY "Users can delete their products" ON marketplace_products
  FOR DELETE USING (auth.uid() = user_id);

-- 15) سياسات المفضلة
-- -------------------------------------------------------

DROP POLICY IF EXISTS "Users can view their favorites" ON product_favorites;
CREATE POLICY "Users can view their favorites" ON product_favorites
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can add favorites" ON product_favorites;
CREATE POLICY "Users can add favorites" ON product_favorites
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can remove favorites" ON product_favorites;
CREATE POLICY "Users can remove favorites" ON product_favorites
  FOR DELETE USING (auth.uid() = user_id);

-- 16) سياسات المحادثات
-- -------------------------------------------------------

DROP POLICY IF EXISTS "Users can view their chats" ON product_chats;
CREATE POLICY "Users can view their chats" ON product_chats
  FOR SELECT USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

DROP POLICY IF EXISTS "Users can create chats" ON product_chats;
CREATE POLICY "Users can create chats" ON product_chats
  FOR INSERT WITH CHECK (auth.uid() = buyer_id);

DROP POLICY IF EXISTS "Users can update their chats" ON product_chats;
CREATE POLICY "Users can update their chats" ON product_chats
  FOR UPDATE USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

-- 17) سياسات الرسائل
-- -------------------------------------------------------

DROP POLICY IF EXISTS "Users can view chat messages" ON chat_messages;
CREATE POLICY "Users can view chat messages" ON chat_messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM product_chats
      WHERE id = chat_id AND (buyer_id = auth.uid() OR seller_id = auth.uid())
    )
  );

DROP POLICY IF EXISTS "Users can send messages" ON chat_messages;
CREATE POLICY "Users can send messages" ON chat_messages
  FOR INSERT WITH CHECK (auth.uid() = sender_id);

-- 18) دوال البحث والإحصائيات
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION search_marketplace_products(
  search_query TEXT DEFAULT '',
  category_filter UUID DEFAULT NULL,
  city_filter TEXT DEFAULT NULL,
  min_price DECIMAL DEFAULT NULL,
  max_price DECIMAL DEFAULT NULL,
  condition_filter TEXT DEFAULT NULL,
  sort_by TEXT DEFAULT 'created_at',
  sort_order TEXT DEFAULT 'DESC',
  limit_count INTEGER DEFAULT 20,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  title TEXT,
  description TEXT,
  price DECIMAL,
  currency TEXT,
  is_negotiable BOOLEAN,
  is_free BOOLEAN,
  city TEXT,
  condition TEXT,
  images TEXT[],
  user_id UUID,
  category_id UUID,
  created_at TIMESTAMPTZ,
  views_count INTEGER,
  favorites_count INTEGER
) AS $$
BEGIN
  RETURN QUERY
  EXECUTE format('
    SELECT
      p.id, p.title, p.description, p.price, p.currency, p.is_negotiable, p.is_free,
      p.city, p.condition, p.images, p.user_id, p.category_id, p.created_at,
      p.views_count, p.favorites_count
    FROM marketplace_products p
    WHERE p.status = ''active''
      AND ($1 = '''' OR to_tsvector(''arabic'', p.title || '' '' || p.description) @@ plainto_tsquery(''arabic'', $1))
      AND ($2 IS NULL OR p.category_id = $2)
      AND ($3 IS NULL OR LOWER(p.city) = LOWER($3))
      AND ($4 IS NULL OR p.price >= $4)
      AND ($5 IS NULL OR p.price <= $5)
      AND ($6 IS NULL OR p.condition = $6)
    ORDER BY %I %s
    LIMIT $9 OFFSET $10
  ', sort_by, sort_order)
  USING search_query, category_filter, city_filter, min_price, max_price, condition_filter, sort_by, sort_order, limit_count, offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE OR REPLACE FUNCTION get_seller_stats(seller_user_id UUID)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_products', (
      SELECT COUNT(*) FROM marketplace_products
      WHERE user_id = seller_user_id AND status = 'active'
    ),
    'sold_products', (
      SELECT COUNT(*) FROM marketplace_products
      WHERE user_id = seller_user_id AND status = 'sold'
    ),
    'average_rating', (
      SELECT COALESCE(AVG(rating), 0) FROM seller_reviews
      WHERE seller_id = seller_user_id
    ),
    'total_reviews', (
      SELECT COUNT(*) FROM seller_reviews
      WHERE seller_id = seller_user_id
    ),
    'member_since', (
      SELECT created_at FROM auth.users WHERE id = seller_user_id
    )
  ) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 19) دالة تحديث عداد المشاهدات
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION increment_product_views(product_uuid UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE marketplace_products
  SET views_count = views_count + 1
  WHERE id = product_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 20) دالة تحديث عداد المفضلة
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION update_favorites_count(product_uuid UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE marketplace_products
  SET favorites_count = (
    SELECT COUNT(*) FROM product_favorites WHERE product_id = product_uuid
  )
  WHERE id = product_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 21) محفز تحديث عداد المفضلة
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION trigger_update_favorites_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    PERFORM update_favorites_count(NEW.product_id);
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    PERFORM update_favorites_count(OLD.product_id);
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_favorites_count ON product_favorites;
CREATE TRIGGER trigger_favorites_count
  AFTER INSERT OR DELETE ON product_favorites
  FOR EACH ROW EXECUTE FUNCTION trigger_update_favorites_count();

-- 22) منح الصلاحيات
-- -------------------------------------------------------

GRANT EXECUTE ON FUNCTION search_marketplace_products TO authenticated;
GRANT EXECUTE ON FUNCTION get_seller_stats TO authenticated;
GRANT EXECUTE ON FUNCTION increment_product_views TO authenticated;
GRANT EXECUTE ON FUNCTION update_favorites_count TO authenticated;

-- 23) التحقق من النجاح
-- -------------------------------------------------------

SELECT
  '✅ MARKETPLACE SETUP' as test_type,
  CASE
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'marketplace_products')
    AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'product_categories')
    THEN '✅ Marketplace tables created successfully'
    ELSE '❌ Failed to create marketplace tables'
  END as result;

SELECT
  '🎉 FINAL RESULT' as test_type,
  '✅ Marketplace system setup completed successfully!' as result;

-- 13) إعداد RLS (Row Level Security)
-- -------------------------------------------------------

ALTER TABLE marketplace_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE seller_reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_reports ENABLE ROW LEVEL SECURITY;

-- 14) سياسات الأمان للمنتجات
-- -------------------------------------------------------

-- الجميع يمكنهم رؤية المنتجات النشطة
DROP POLICY IF EXISTS "Anyone can view active products" ON marketplace_products;
CREATE POLICY "Anyone can view active products" ON marketplace_products
  FOR SELECT USING (status = 'active');

-- المستخدمون يمكنهم إنشاء منتجاتهم
DROP POLICY IF EXISTS "Users can create their products" ON marketplace_products;
CREATE POLICY "Users can create their products" ON marketplace_products
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- المستخدمون يمكنهم تحديث منتجاتهم
DROP POLICY IF EXISTS "Users can update their products" ON marketplace_products;
CREATE POLICY "Users can update their products" ON marketplace_products
  FOR UPDATE USING (auth.uid() = user_id);

-- المستخدمون يمكنهم حذف منتجاتهم
DROP POLICY IF EXISTS "Users can delete their products" ON marketplace_products;
CREATE POLICY "Users can delete their products" ON marketplace_products
  FOR DELETE USING (auth.uid() = user_id);

-- 15) سياسات المفضلة
-- -------------------------------------------------------

DROP POLICY IF EXISTS "Users can view their favorites" ON product_favorites;
CREATE POLICY "Users can view their favorites" ON product_favorites
  FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can add favorites" ON product_favorites;
CREATE POLICY "Users can add favorites" ON product_favorites
  FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can remove favorites" ON product_favorites;
CREATE POLICY "Users can remove favorites" ON product_favorites
  FOR DELETE USING (auth.uid() = user_id);

-- 16) سياسات المحادثات
-- -------------------------------------------------------

DROP POLICY IF EXISTS "Users can view their chats" ON product_chats;
CREATE POLICY "Users can view their chats" ON product_chats
  FOR SELECT USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

DROP POLICY IF EXISTS "Users can create chats" ON product_chats;
CREATE POLICY "Users can create chats" ON product_chats
  FOR INSERT WITH CHECK (auth.uid() = buyer_id);

DROP POLICY IF EXISTS "Users can update their chats" ON product_chats;
CREATE POLICY "Users can update their chats" ON product_chats
  FOR UPDATE USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

-- 17) سياسات الرسائل
-- -------------------------------------------------------

DROP POLICY IF EXISTS "Users can view chat messages" ON chat_messages;
CREATE POLICY "Users can view chat messages" ON chat_messages
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM product_chats
      WHERE id = chat_id AND (buyer_id = auth.uid() OR seller_id = auth.uid())
    )
  );

DROP POLICY IF EXISTS "Users can send messages" ON chat_messages;
CREATE POLICY "Users can send messages" ON chat_messages
  FOR INSERT WITH CHECK (auth.uid() = sender_id);

-- 18) دوال البحث والإحصائيات
-- -------------------------------------------------------

-- دالة البحث في المنتجات
CREATE OR REPLACE FUNCTION search_marketplace_products(
  search_query TEXT DEFAULT '',
  category_filter UUID DEFAULT NULL,
  city_filter TEXT DEFAULT NULL,
  min_price DECIMAL DEFAULT NULL,
  max_price DECIMAL DEFAULT NULL,
  condition_filter TEXT DEFAULT NULL,
  sort_by TEXT DEFAULT 'created_at',
  sort_order TEXT DEFAULT 'DESC',
  limit_count INTEGER DEFAULT 20,
  offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
  id UUID,
  title TEXT,
  description TEXT,
  price DECIMAL,
  currency TEXT,
  is_negotiable BOOLEAN,
  is_free BOOLEAN,
  city TEXT,
  condition TEXT,
  images TEXT[],
  user_id UUID,
  category_id UUID,
  created_at TIMESTAMPTZ,
  views_count INTEGER,
  favorites_count INTEGER
) AS $$
BEGIN
  RETURN QUERY
  EXECUTE format('
    SELECT
      p.id, p.title, p.description, p.price, p.currency, p.is_negotiable, p.is_free,
      p.city, p.condition, p.images, p.user_id, p.category_id, p.created_at,
      p.views_count, p.favorites_count
    FROM marketplace_products p
    WHERE p.status = ''active''
      AND ($1 = '''' OR to_tsvector(''arabic'', p.title || '' '' || p.description) @@ plainto_tsquery(''arabic'', $1))
      AND ($2 IS NULL OR p.category_id = $2)
      AND ($3 IS NULL OR LOWER(p.city) = LOWER($3))
      AND ($4 IS NULL OR p.price >= $4)
      AND ($5 IS NULL OR p.price <= $5)
      AND ($6 IS NULL OR p.condition = $6)
    ORDER BY %I %s
    LIMIT $9 OFFSET $10
  ', sort_by, sort_order)
  USING search_query, category_filter, city_filter, min_price, max_price, condition_filter, sort_by, sort_order, limit_count, offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- دالة إحصائيات البائع
CREATE OR REPLACE FUNCTION get_seller_stats(seller_user_id UUID)
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_products', (
      SELECT COUNT(*) FROM marketplace_products
      WHERE user_id = seller_user_id AND status = 'active'
    ),
    'sold_products', (
      SELECT COUNT(*) FROM marketplace_products
      WHERE user_id = seller_user_id AND status = 'sold'
    ),
    'average_rating', (
      SELECT COALESCE(AVG(rating), 0) FROM seller_reviews
      WHERE seller_id = seller_user_id
    ),
    'total_reviews', (
      SELECT COUNT(*) FROM seller_reviews
      WHERE seller_id = seller_user_id
    ),
    'member_since', (
      SELECT created_at FROM auth.users WHERE id = seller_user_id
    )
  ) INTO result;

  RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 19) دالة تحديث عداد المشاهدات
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION increment_product_views(product_uuid UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE marketplace_products
  SET views_count = views_count + 1
  WHERE id = product_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 20) دالة تحديث عداد المفضلة
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION update_favorites_count(product_uuid UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE marketplace_products
  SET favorites_count = (
    SELECT COUNT(*) FROM product_favorites WHERE product_id = product_uuid
  )
  WHERE id = product_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 21) محفز تحديث عداد المفضلة
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION trigger_update_favorites_count()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' THEN
    PERFORM update_favorites_count(NEW.product_id);
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    PERFORM update_favorites_count(OLD.product_id);
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_favorites_count ON product_favorites;
CREATE TRIGGER trigger_favorites_count
  AFTER INSERT OR DELETE ON product_favorites
  FOR EACH ROW EXECUTE FUNCTION trigger_update_favorites_count();

-- 22) منح الصلاحيات
-- -------------------------------------------------------

GRANT EXECUTE ON FUNCTION search_marketplace_products TO authenticated;
GRANT EXECUTE ON FUNCTION get_seller_stats TO authenticated;
GRANT EXECUTE ON FUNCTION increment_product_views TO authenticated;
GRANT EXECUTE ON FUNCTION update_favorites_count TO authenticated;

-- 23) التحقق من النجاح
-- -------------------------------------------------------

SELECT
  '✅ MARKETPLACE SETUP' as test_type,
  CASE
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'marketplace_products')
    AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'product_categories')
    THEN '✅ Marketplace tables created successfully'
    ELSE '❌ Failed to create marketplace tables'
  END as result;

SELECT
  '🎉 FINAL RESULT' as test_type,
  '✅ Marketplace system setup completed successfully!' as result;
