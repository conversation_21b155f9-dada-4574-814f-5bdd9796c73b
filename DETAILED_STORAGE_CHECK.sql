-- التحقق التفصيلي من مشكلة التخزين
-- Detailed storage issue check

-- 1. التحقق من جميع buckets الموجودة
SELECT 
    id, 
    name, 
    public, 
    file_size_limit,
    allowed_mime_types
FROM storage.buckets 
ORDER BY id;

-- 2. التحقق من وجود bucket media
DO $$
DECLARE
    bucket_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO bucket_count FROM storage.buckets WHERE id = 'media';
    
    IF bucket_count = 0 THEN
        RAISE EXCEPTION 'Bucket media غير موجود!';
    ELSE
        RAISE NOTICE 'Bucket media موجود';
    END IF;
END $$;

-- 3. التحقق من سياسات التخزين
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
ORDER BY policyname;

-- 4. إنشاء bucket media إذا لم يكن موجوداً
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'media') THEN
        INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
        VALUES (
            'media',
            'media', 
            true,
            52428800,
            ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/avi', 'video/mov', 'video/quicktime']
        );
        RAISE NOTICE 'تم إنشاء bucket media';
    ELSE
        RAISE NOTICE 'Bucket media موجود بالفعل';
    END IF;
END $$;

-- 5. حذف السياسات القديمة
DROP POLICY IF EXISTS "media_select_policy" ON storage.objects;
DROP POLICY IF EXISTS "media_insert_policy" ON storage.objects;
DROP POLICY IF EXISTS "media_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "media_delete_policy" ON storage.objects;

-- 6. إنشاء السياسات الجديدة
CREATE POLICY "media_select_policy"
ON storage.objects FOR SELECT
USING (bucket_id = 'media');

CREATE POLICY "media_insert_policy"
ON storage.objects FOR INSERT
WITH CHECK (
    bucket_id = 'media' 
    AND auth.uid() IS NOT NULL
);

CREATE POLICY "media_update_policy"
ON storage.objects FOR UPDATE
USING (
    bucket_id = 'media' 
    AND auth.uid() IS NOT NULL
);

CREATE POLICY "media_delete_policy"
ON storage.objects FOR DELETE
USING (
    bucket_id = 'media' 
    AND auth.uid() IS NOT NULL
);

-- 7. التحقق من السياسات الجديدة
SELECT 
    policyname, 
    permissive, 
    cmd 
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname LIKE '%media%'
ORDER BY policyname;

-- 8. اختبار رفع ملف (اختبار نظري)
DO $$
DECLARE
    test_user_id UUID;
BEGIN
    -- الحصول على أول مستخدم
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    
    IF test_user_id IS NULL THEN
        RAISE EXCEPTION 'لا يوجد مستخدمين في النظام!';
    END IF;
    
    RAISE NOTICE 'معرف المستخدم للاختبار: %', test_user_id;
    RAISE NOTICE 'Bucket media جاهز للاستخدام';
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'خطأ في التحقق: %', SQLERRM;
END $$;

-- 9. عرض ملخص الحالة
SELECT 
    'Storage Status' as check_type,
    (SELECT COUNT(*) FROM storage.buckets WHERE id = 'media') as media_bucket_exists,
    (SELECT COUNT(*) FROM pg_policies WHERE tablename = 'objects' AND schemaname = 'storage' AND policyname LIKE '%media%') as media_policies_count,
    (SELECT COUNT(*) FROM auth.users) as users_count; 