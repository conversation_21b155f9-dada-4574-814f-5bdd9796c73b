import 'package:supabase_flutter/supabase_flutter.dart';

class PropertyChatService {
  final SupabaseClient _client = Supabase.instance.client;

  // إنشاء أو الحصول على محادثة عقار
  Future<String> createOrGetPropertyChat({
    required String propertyId,
    required String sellerId,
  }) async {
    try {
      final buyerId = _client.auth.currentUser?.id;
      if (buyerId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // البحث عن محادثة موجودة
      final existingChat = await _client
          .from('property_chats')
          .select('id')
          .eq('property_id', propertyId)
          .eq('buyer_id', buyerId)
          .eq('seller_id', sellerId)
          .maybeSingle();

      if (existingChat != null) {
        return existingChat['id'];
      }

      // إنشاء محادثة جديدة
      final newChat = await _client
          .from('property_chats')
          .insert({
            'property_id': propertyId,
            'buyer_id': buyerId,
            'seller_id': sellerId,
          })
          .select('id')
          .single();

      // إرسال رسالة ترحيبية
      await _client
          .from('property_messages')
          .insert({
            'chat_id': newChat['id'],
            'sender_id': buyerId,
            'content': 'مرحباً، أنا مهتم بهذا العقار. هل يمكنني الحصول على مزيد من المعلومات؟',
            'message_type': 'text',
          });

      return newChat['id'];
    } catch (e) {
      throw Exception('فشل في إنشاء المحادثة: $e');
    }
  }

  // الحصول على محادثات المستخدم للعقارات
  Future<List<Map<String, dynamic>>> getUserPropertyChats() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return [];

      final response = await _client
          .from('property_chats')
          .select('''
            *,
            real_estate_properties!inner(title, property_type, price, currency),
            last_message:property_messages(content, created_at, sender_id)
          ''')
          .or('buyer_id.eq.$userId,seller_id.eq.$userId')
          .eq('is_active', true)
          .order('last_message_at', ascending: false);

      List<Map<String, dynamic>> chats = [];
      
      for (var chatData in response) {
        // الحصول على معلومات المستخدم الآخر
        final otherUserId = chatData['buyer_id'] == userId 
            ? chatData['seller_id'] 
            : chatData['buyer_id'];
            
        final otherUserProfile = await _client
            .from('profiles')
            .select('username, full_name, avatar_url')
            .eq('id', otherUserId)
            .maybeSingle();

        chatData['other_user_name'] = otherUserProfile?['full_name'] ?? 
                                     otherUserProfile?['username'] ?? 
                                     'مستخدم';
        chatData['other_user_avatar'] = otherUserProfile?['avatar_url'];
        chatData['is_buyer'] = chatData['buyer_id'] == userId;
        
        chats.add(chatData);
      }

      return chats;
    } catch (e) {
      return [];
    }
  }

  // الحصول على رسائل المحادثة
  Future<List<Map<String, dynamic>>> getChatMessages(String chatId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return [];

      // التحقق من أن المستخدم جزء من المحادثة
      final chat = await _client
          .from('property_chats')
          .select('buyer_id, seller_id')
          .eq('id', chatId)
          .or('buyer_id.eq.$userId,seller_id.eq.$userId')
          .maybeSingle();

      if (chat == null) return [];

      final response = await _client
          .from('property_messages')
          .select('*')
          .eq('chat_id', chatId)
          .order('created_at', ascending: true);

      return response as List<Map<String, dynamic>>;
    } catch (e) {
      return [];
    }
  }

  // إرسال رسالة
  Future<Map<String, dynamic>?> sendMessage({
    required String chatId,
    required String content,
    String messageType = 'text',
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // التحقق من أن المستخدم جزء من المحادثة
      final chat = await _client
          .from('property_chats')
          .select('buyer_id, seller_id')
          .eq('id', chatId)
          .or('buyer_id.eq.$userId,seller_id.eq.$userId')
          .maybeSingle();

      if (chat == null) throw Exception('المحادثة غير موجودة');

      final messageData = {
        'chat_id': chatId,
        'sender_id': userId,
        'content': content,
        'message_type': messageType,
      };

      final response = await _client
          .from('property_messages')
          .insert(messageData)
          .select()
          .single();

      return response;
    } catch (e) {
      throw Exception('فشل في إرسال الرسالة: $e');
    }
  }

  // تحديد الرسائل كمقروءة
  Future<void> markMessagesAsRead(String chatId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return;

      await _client
          .from('property_messages')
          .update({'is_read': true})
          .eq('chat_id', chatId)
          .neq('sender_id', userId)
          .eq('is_read', false);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  // الحصول على عدد الرسائل غير المقروءة
  Future<int> getUnreadMessagesCount() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return 0;

      // الحصول على جميع محادثات المستخدم
      final chats = await _client
          .from('property_chats')
          .select('id')
          .or('buyer_id.eq.$userId,seller_id.eq.$userId')
          .eq('is_active', true);

      if (chats.isEmpty) return 0;

      final chatIds = (chats as List).map((chat) => chat['id']).toList();

      final response = await _client
          .from('property_messages')
          .select('id')
          .inFilter('chat_id', chatIds)
          .neq('sender_id', userId)
          .eq('is_read', false);

      return (response as List).length;
    } catch (e) {
      return 0;
    }
  }

  // الاستماع للرسائل الجديدة في الوقت الفعلي
  Stream<List<Map<String, dynamic>>> subscribeToMessages(String chatId) {
    final userId = _client.auth.currentUser?.id;
    if (userId == null) return Stream.empty();

    return _client
        .from('property_messages')
        .stream(primaryKey: ['id'])
        .order('created_at')
        .map((data) => data
            .where((json) => json['chat_id'] == chatId)
            .map((json) => json as Map<String, dynamic>)
            .toList());
  }

  // إنهاء المحادثة
  Future<void> endChat(String chatId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('property_chats')
          .update({'is_active': false})
          .eq('id', chatId)
          .or('buyer_id.eq.$userId,seller_id.eq.$userId');
    } catch (e) {
      throw Exception('فشل في إنهاء المحادثة: $e');
    }
  }
}
