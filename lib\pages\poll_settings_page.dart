import 'package:flutter/material.dart';
import '../models/poll.dart';
import '../services/poll_service.dart';

class PollSettingsPage extends StatefulWidget {
  final Poll poll;

  const PollSettingsPage({super.key, required this.poll});

  @override
  State<PollSettingsPage> createState() => _PollSettingsPageState();
}

class _PollSettingsPageState extends State<PollSettingsPage> {
  final PollService _pollService = PollService();
  bool _loading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات التصويت'),
        backgroundColor: Colors.purple[600],
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // معلومات التصويت
          _buildPollInfo(),
          
          const SizedBox(height: 24),
          
          // إعدادات التصويت
          _buildPollSettings(),
          
          const SizedBox(height: 24),
          
          // إحصائيات التصويت
          _buildPollStats(),
          
          const SizedBox(height: 24),
          
          // إجراءات خطيرة
          _buildDangerousActions(),
        ],
      ),
    );
  }

  Widget _buildPollInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.poll, color: Colors.purple[600]),
                const SizedBox(width: 8),
                Text(
                  'معلومات التصويت',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              widget.poll.question,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('الفئة', widget.poll.category.arabicName),
            _buildInfoRow('النوع', widget.poll.type.arabicName),
            _buildInfoRow('المدة', widget.poll.duration.arabicName),
            _buildInfoRow('تاريخ الإنشاء', _formatDate(widget.poll.createdAt)),
            if (widget.poll.expiresAt != null)
              _buildInfoRow('تاريخ الانتهاء', _formatDate(widget.poll.expiresAt!)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPollSettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Text(
                  'إعدادات التصويت',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // تفعيل/إلغاء التصويت
            SwitchListTile(
              title: const Text('التصويت نشط'),
              subtitle: Text(widget.poll.isActive ? 'التصويت مفتوح للمشاركة' : 'التصويت مغلق'),
              value: widget.poll.isActive,
              onChanged: _togglePollStatus,
              activeColor: Colors.green,
            ),
            
            const Divider(),
            
            // السماح بالتعليقات
            SwitchListTile(
              title: const Text('السماح بالتعليقات'),
              subtitle: const Text('يمكن للمستخدمين إضافة تعليقات'),
              value: widget.poll.allowComments,
              onChanged: _toggleComments,
              activeColor: Colors.blue,
            ),
            
            const Divider(),
            
            // السماح بإعادة التصويت
            SwitchListTile(
              title: const Text('السماح بإعادة التصويت'),
              subtitle: const Text('يمكن للمستخدمين تغيير تصويتهم'),
              value: widget.poll.allowRevote,
              onChanged: _toggleRevote,
              activeColor: Colors.orange,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPollStats() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.green[600]),
                const SizedBox(width: 8),
                Text(
                  'إحصائيات التصويت',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatCard('إجمالي الأصوات', '${widget.poll.totalVotes}', Icons.how_to_vote, Colors.blue),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard('عدد الخيارات', '${widget.poll.options.length}', Icons.list, Colors.green),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: _buildStatCard('التعليقات', '0', Icons.comment, Colors.orange),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard('المشاركات', '0', Icons.share, Colors.purple),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDangerousActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: Colors.red[600]),
                const SizedBox(width: 8),
                Text(
                  'إجراءات خطيرة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // حذف التصويت
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _loading ? null : _deletePoll,
                icon: const Icon(Icons.delete_forever),
                label: const Text('حذف التصويت نهائياً'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            
            const SizedBox(height: 8),
            
            Text(
              'تحذير: حذف التصويت سيؤدي إلى فقدان جميع الأصوات والتعليقات المرتبطة به نهائياً.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.red[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _togglePollStatus(bool value) async {
    setState(() => _loading = true);
    try {
      await _pollService.updatePollStatus(widget.poll.id, value);
      setState(() {
        _loading = false;
      });
      _showSuccessMessage('تم تحديث حالة التصويت');
    } catch (e) {
      setState(() => _loading = false);
      _showErrorMessage('خطأ في تحديث حالة التصويت: $e');
    }
  }

  void _toggleComments(bool value) async {
    setState(() => _loading = true);
    try {
      await _pollService.updatePollSettings(widget.poll.id, allowComments: value);
      setState(() {
        _loading = false;
      });
      _showSuccessMessage('تم تحديث إعدادات التعليقات');
    } catch (e) {
      setState(() => _loading = false);
      _showErrorMessage('خطأ في تحديث إعدادات التعليقات: $e');
    }
  }

  void _toggleRevote(bool value) async {
    setState(() => _loading = true);
    try {
      await _pollService.updatePollSettings(widget.poll.id, allowRevote: value);
      setState(() {
        _loading = false;
      });
      _showSuccessMessage('تم تحديث إعدادات إعادة التصويت');
    } catch (e) {
      setState(() => _loading = false);
      _showErrorMessage('خطأ في تحديث إعدادات إعادة التصويت: $e');
    }
  }

  void _deletePoll() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text(
          'هل أنت متأكد من حذف هذا التصويت؟\n\n'
          'سيتم حذف جميع الأصوات والتعليقات المرتبطة به نهائياً.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              setState(() => _loading = true);
              try {
                await _pollService.deletePoll(widget.poll.id);
                if (mounted) {
                  final navigator = Navigator.of(context);
                  final messenger = ScaffoldMessenger.of(context);
                  navigator.pop(true); // العودة مع إشارة التحديث
                  messenger.showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف التصويت بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                setState(() => _loading = false);
                _showErrorMessage('خطأ في حذف التصويت: $e');
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red[600]),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
