import 'package:flutter/material.dart';
import '../models/space.dart';
import '../services/spaces_service.dart';

class EditSpacePage extends StatefulWidget {
  final Space space;

  const EditSpacePage({super.key, required this.space});

  @override
  State<EditSpacePage> createState() => _EditSpacePageState();
}

class _EditSpacePageState extends State<EditSpacePage> {
  final _formKey = GlobalKey<FormState>();
  final _spacesService = SpacesService();

  // متحكمات النص
  late final TextEditingController _nameController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _goalController;
  late final TextEditingController _professionController;
  late final TextEditingController _phoneController;
  late final TextEditingController _emailController;
  late final TextEditingController _websiteController;
  late final TextEditingController _facebookController;
  late final TextEditingController _instagramController;
  late final TextEditingController _twitterController;
  late final TextEditingController _youtubeController;
  late final TextEditingController _linkedinController;

  late SpaceCategory _selectedCategory;
  late SpacePrivacy _selectedPrivacy;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    
    // تهيئة المتحكمات بالقيم الحالية
    _nameController = TextEditingController(text: widget.space.name);
    _descriptionController = TextEditingController(text: widget.space.description);
    _goalController = TextEditingController(text: widget.space.goal ?? '');
    _professionController = TextEditingController(text: widget.space.profession ?? '');
    _phoneController = TextEditingController(text: widget.space.phoneNumber ?? '');
    _emailController = TextEditingController(text: widget.space.email ?? '');
    _websiteController = TextEditingController(text: widget.space.website ?? '');
    
    // تهيئة روابط وسائل التواصل
    _facebookController = TextEditingController(text: widget.space.socialLinks['facebook'] ?? '');
    _instagramController = TextEditingController(text: widget.space.socialLinks['instagram'] ?? '');
    _twitterController = TextEditingController(text: widget.space.socialLinks['twitter'] ?? '');
    _youtubeController = TextEditingController(text: widget.space.socialLinks['youtube'] ?? '');
    _linkedinController = TextEditingController(text: widget.space.socialLinks['linkedin'] ?? '');

    _selectedCategory = widget.space.category;
    _selectedPrivacy = widget.space.privacy;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _goalController.dispose();
    _professionController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _websiteController.dispose();
    _facebookController.dispose();
    _instagramController.dispose();
    _twitterController.dispose();
    _youtubeController.dispose();
    _linkedinController.dispose();
    super.dispose();
  }

  Future<void> _updateSpace() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final socialLinks = <String, String>{};
      if (_facebookController.text.isNotEmpty) {
        socialLinks['facebook'] = _facebookController.text;
      }
      if (_instagramController.text.isNotEmpty) {
        socialLinks['instagram'] = _instagramController.text;
      }
      if (_twitterController.text.isNotEmpty) {
        socialLinks['twitter'] = _twitterController.text;
      }
      if (_youtubeController.text.isNotEmpty) {
        socialLinks['youtube'] = _youtubeController.text;
      }
      if (_linkedinController.text.isNotEmpty) {
        socialLinks['linkedin'] = _linkedinController.text;
      }

      await _spacesService.updateSpace(
        spaceId: widget.space.id,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        goal: _goalController.text.trim().isEmpty ? null : _goalController.text.trim(),
        category: _selectedCategory,
        profession: _professionController.text.trim().isEmpty ? null : _professionController.text.trim(),
        privacy: _selectedPrivacy,
        phoneNumber: _phoneController.text.trim().isEmpty ? null : _phoneController.text.trim(),
        email: _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        website: _websiteController.text.trim().isEmpty ? null : _websiteController.text.trim(),
        socialLinks: socialLinks,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث المساحة بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحديث المساحة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل المساحة'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _updateSpace,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Text(
                    'حفظ',
                    style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                  ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // المعلومات الأساسية
            _buildSectionHeader('المعلومات الأساسية', Icons.info),
            
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'اسم المساحة *',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'اسم المساحة مطلوب';
                }
                if (value.trim().length < 3) {
                  return 'اسم المساحة يجب أن يكون 3 أحرف على الأقل';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف المساحة *',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'وصف المساحة مطلوب';
                }
                if (value.trim().length < 10) {
                  return 'الوصف يجب أن يكون 10 أحرف على الأقل';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            TextFormField(
              controller: _goalController,
              decoration: const InputDecoration(
                labelText: 'الهدف من المساحة',
                border: OutlineInputBorder(),
              ),
              maxLines: 2,
            ),

            const SizedBox(height: 16),

            // الفئة
            DropdownButtonFormField<SpaceCategory>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'فئة المساحة *',
                border: OutlineInputBorder(),
              ),
              items: SpaceCategory.values.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Row(
                    children: [
                      Icon(
                        SpaceCategoryHelper.getCategoryIcon(category),
                        size: 20,
                        color: SpaceCategoryHelper.getCategoryColor(category),
                      ),
                      const SizedBox(width: 8),
                      Text(SpaceCategoryHelper.getCategoryName(category)),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCategory = value!;
                });
              },
            ),

            const SizedBox(height: 16),

            TextFormField(
              controller: _professionController,
              decoration: const InputDecoration(
                labelText: 'المهنة/التخصص',
                border: OutlineInputBorder(),
              ),
            ),

            const SizedBox(height: 24),

            // إعدادات الخصوصية
            _buildSectionHeader('إعدادات الخصوصية', Icons.privacy_tip),
            
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: SpacePrivacy.values.map((privacy) {
                    return RadioListTile<SpacePrivacy>(
                      title: Text(_getPrivacyName(privacy)),
                      subtitle: Text(_getPrivacyDescription(privacy)),
                      value: privacy,
                      groupValue: _selectedPrivacy,
                      onChanged: (value) {
                        setState(() {
                          _selectedPrivacy = value!;
                        });
                      },
                    );
                  }).toList(),
                ),
              ),
            ),

            const SizedBox(height: 24),

            // معلومات التواصل
            _buildSectionHeader('معلومات التواصل', Icons.contact_phone),
            
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'رقم الهاتف',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
            ),

            const SizedBox(height: 16),

            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),

            const SizedBox(height: 16),

            TextFormField(
              controller: _websiteController,
              decoration: const InputDecoration(
                labelText: 'الموقع الإلكتروني',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.language),
              ),
              keyboardType: TextInputType.url,
            ),

            const SizedBox(height: 24),

            // روابط وسائل التواصل
            _buildSectionHeader('وسائل التواصل الاجتماعي', Icons.share),
            
            _buildSocialField('فيسبوك', _facebookController, Icons.facebook, Colors.blue),
            const SizedBox(height: 12),
            _buildSocialField('إنستغرام', _instagramController, Icons.camera_alt, Colors.purple),
            const SizedBox(height: 12),
            _buildSocialField('تويتر', _twitterController, Icons.alternate_email, Colors.lightBlue),
            const SizedBox(height: 12),
            _buildSocialField('يوتيوب', _youtubeController, Icons.play_circle, Colors.red),
            const SizedBox(height: 12),
            _buildSocialField('لينكد إن', _linkedinController, Icons.work, Colors.indigo),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(icon, color: Colors.blue[600]),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSocialField(String label, TextEditingController controller, IconData icon, Color color) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: 'رابط $label',
        border: const OutlineInputBorder(),
        prefixIcon: Icon(icon, color: color),
      ),
      keyboardType: TextInputType.url,
    );
  }

  String _getPrivacyName(SpacePrivacy privacy) {
    switch (privacy) {
      case SpacePrivacy.public:
        return 'عامة';
      case SpacePrivacy.private:
        return 'خاصة';
      case SpacePrivacy.followers:
        return 'للمتابعين فقط';
    }
  }

  String _getPrivacyDescription(SpacePrivacy privacy) {
    switch (privacy) {
      case SpacePrivacy.public:
        return 'يمكن لأي شخص رؤية المساحة والمنشورات';
      case SpacePrivacy.private:
        return 'المساحة مخفية ولا يمكن العثور عليها';
      case SpacePrivacy.followers:
        return 'يمكن رؤية المساحة لكن المنشورات للمتابعين فقط';
    }
  }
}
