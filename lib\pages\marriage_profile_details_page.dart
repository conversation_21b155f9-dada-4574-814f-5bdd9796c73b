import 'package:flutter/material.dart';
import '../models/marriage_profile.dart';
import '../services/marriage_service.dart';
import 'create_marriage_profile_page.dart';
import 'marriage_settings_page.dart';
import 'chat_page.dart';

class MarriageProfileDetailsPage extends StatefulWidget {
  final MarriageProfile profile;
  final bool isMyProfile;

  const MarriageProfileDetailsPage({
    super.key,
    required this.profile,
    this.isMyProfile = false,
  });

  @override
  State<MarriageProfileDetailsPage> createState() => _MarriageProfileDetailsPageState();
}

class _MarriageProfileDetailsPageState extends State<MarriageProfileDetailsPage> {
  bool _hasApproval = false;
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    if (!widget.isMyProfile) {
      _checkApprovalStatus();
    }
  }

  Future<void> _checkApprovalStatus() async {
    try {
      final hasApproval = await MarriageService().hasAcceptedRequest(widget.profile.userId);
      setState(() => _hasApproval = hasApproval);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.profile.getDisplayName(_hasApproval || widget.isMyProfile)),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        actions: [
          if (widget.isMyProfile) ...[
            IconButton(
              icon: const Icon(Icons.edit),
              onPressed: () => _editProfile(),
            ),
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () => _openSettings(),
            ),
            PopupMenuButton(
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.red[600]),
                      const SizedBox(width: 8),
                      Text('حذف الملف', style: TextStyle(color: Colors.red[600])),
                    ],
                  ),
                ),
              ],
              onSelected: (value) {
                if (value == 'delete') {
                  _deleteProfile();
                }
              },
            ),
          ] else ...[
            PopupMenuButton(
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'report',
                  child: Row(
                    children: [
                      Icon(Icons.report, color: Colors.red[600]),
                      const SizedBox(width: 8),
                      Text('إبلاغ', style: TextStyle(color: Colors.red[600])),
                    ],
                  ),
                ),
              ],
              onSelected: (value) {
                if (value == 'report') {
                  _reportProfile();
                }
              },
            ),
          ],
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // رأس الملف الشخصي
            _buildProfileHeader(),
            
            // المعلومات الأساسية
            _buildBasicInfo(),
            
            // الوصف الشخصي
            _buildDescription(),
            
            // مواصفات الطرف الآخر
            _buildDesiredPartnerSpecs(),
            
            // معلومات التواصل (إذا كان مقبولاً)
            if (_hasApproval || widget.isMyProfile)
              _buildContactInfo(),
            
            const SizedBox(height: 100), // مساحة للزر العائم
          ],
        ),
      ),
      floatingActionButton: !widget.isMyProfile ? FloatingActionButton.extended(
        onPressed: _loading ? null : _sendContactRequest,
        backgroundColor: widget.profile.gender == Gender.male ? Colors.blue : Colors.pink,
        icon: _loading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2),
              )
            : const Icon(Icons.message, color: Colors.white),
        label: const Text('طلب تواصل', style: TextStyle(color: Colors.white)),
      ) : null,
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: widget.profile.gender == Gender.male
              ? [Colors.blue[400]!, Colors.blue[600]!]
              : [Colors.pink[400]!, Colors.pink[600]!],
        ),
      ),
      child: Column(
        children: [
          const SizedBox(height: 32),
          
          // صورة الملف الشخصي
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 4),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  spreadRadius: 2,
                  blurRadius: 10,
                ),
              ],
            ),
            child: widget.profile.getDisplayImage(_hasApproval || widget.isMyProfile) != null
                ? ClipOval(
                    child: Image.network(
                      widget.profile.getDisplayImage(_hasApproval || widget.isMyProfile)!,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) => _buildDefaultAvatar(),
                    ),
                  )
                : _buildDefaultAvatar(),
          ),
          
          const SizedBox(height: 16),
          
          // الاسم والعمر
          Text(
            widget.profile.getDisplayName(_hasApproval || widget.isMyProfile),
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            '${widget.profile.age} سنة',
            style: const TextStyle(
              fontSize: 18,
              color: Colors.white70,
            ),
          ),
          
          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white.withValues(alpha: 0.2),
      ),
      child: Icon(
        widget.profile.gender == Gender.male ? Icons.man : Icons.woman,
        size: 60,
        color: Colors.white,
      ),
    );
  }

  Widget _buildBasicInfo() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 10,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المعلومات الأساسية',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 16),
          
          _buildInfoRow(Icons.wc, 'الجنس', widget.profile.genderText),
          _buildInfoRow(Icons.location_on, 'المكان', '${widget.profile.city}, ${widget.profile.country}'),
          _buildInfoRow(Icons.work, 'المهنة', widget.profile.profession),
          _buildInfoRow(Icons.favorite, 'الحالة الاجتماعية', widget.profile.maritalStatusText),
          _buildInfoRow(Icons.flag, 'الهدف', widget.profile.goalText),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Text(
            '$label: ',
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Colors.grey[700],
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: Colors.grey[800]),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDescription() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 10,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الوصف الشخصي',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),
          Text(
            widget.profile.description,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[700],
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesiredPartnerSpecs() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 10,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مواصفات الطرف الآخر المرغوب',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 12),
          Text(
            widget.profile.desiredPartnerSpecs,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[700],
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactInfo() {
    if (widget.profile.contactMethods.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.verified, color: Colors.green[600]),
              const SizedBox(width: 8),
              Text(
                'معلومات التواصل',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.green[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          
          ...widget.profile.contactMethods.entries.map((entry) {
            IconData icon;
            switch (entry.key) {
              case 'email':
                icon = Icons.email;
                break;
              case 'whatsapp':
                icon = Icons.phone;
                break;
              default:
                icon = Icons.contact_phone;
            }
            
            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(icon, size: 20, color: Colors.green[600]),
                  const SizedBox(width: 12),
                  Text(
                    '${entry.key}: ',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: Colors.green[700],
                    ),
                  ),
                  Expanded(
                    child: Text(
                      entry.value,
                      style: TextStyle(color: Colors.green[800]),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  Future<void> _sendContactRequest() async {
    setState(() => _loading = true);

    try {
      await MarriageService().sendContactRequest(receiverId: widget.profile.userId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال طلب التواصل بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(e.toString().replaceAll('Exception: ', '')),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _loading = false);
      }
    }
  }

  void _editProfile() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => CreateMarriageProfilePage(profile: widget.profile),
      ),
    ).then((_) => Navigator.pop(context, true));
  }

  void _openSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => MarriageSettingsPage(profile: widget.profile),
      ),
    ).then((result) {
      if (result == true) {
        Navigator.pop(context, true); // العودة إذا تم حذف الملف
      }
    });
  }

  void _deleteProfile() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الملف'),
        content: const Text('هل أنت متأكد من حذف ملفك الشخصي؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await MarriageService().deleteProfile(widget.profile.id);
                if (mounted) {
                  Navigator.pop(context, true);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف الملف بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('فشل في حذف الملف: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _reportProfile() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إبلاغ عن الملف'),
        content: const Text('سبب الإبلاغ:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await MarriageService().reportProfile(
                  widget.profile.id,
                  'محتوى غير مناسب',
                );
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم الإبلاغ بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('فشل في الإبلاغ: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إبلاغ', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }
}
