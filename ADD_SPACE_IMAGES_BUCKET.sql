-- إنشاء bucket لصور منشورات المساحات
-- Create bucket for space post images

-- 1. إنشاء bucket للصور
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'space-images',
  'space-images', 
  true,  -- عام للجميع
  52428800,  -- 50MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 52428800,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];

-- 2. حذف السياسات القديمة إن وجدت
DROP POLICY IF EXISTS "space_images_select_policy" ON storage.objects;
DROP POLICY IF EXISTS "space_images_insert_policy" ON storage.objects;
DROP POLICY IF EXISTS "space_images_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "space_images_delete_policy" ON storage.objects;

-- 3. إنشاء سياسات جديدة
-- السماح للجميع بقراءة صور المساحات
CREATE POLICY "space_images_select_policy"
ON storage.objects FOR SELECT
USING (bucket_id = 'space-images');

-- السماح للمستخدمين المسجلين برفع الصور
CREATE POLICY "space_images_insert_policy"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'space-images' 
  AND auth.uid() IS NOT NULL
);

-- السماح للمستخدمين المسجلين بتحديث الصور
CREATE POLICY "space_images_update_policy"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'space-images' 
  AND auth.uid() IS NOT NULL
);

-- السماح للمستخدمين المسجلين بحذف الصور
CREATE POLICY "space_images_delete_policy"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'space-images' 
  AND auth.uid() IS NOT NULL
);

-- 4. التحقق من نجاح الإنشاء
SELECT 
  'SUCCESS: Space images bucket created' as status,
  id, 
  name, 
  public,
  file_size_limit,
  allowed_mime_types
FROM storage.buckets 
WHERE id = 'space-images';

-- 5. رسالة نجاح
SELECT 'تم إنشاء bucket space-images بنجاح!' as result; 