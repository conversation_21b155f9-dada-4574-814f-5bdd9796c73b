import 'package:flutter/material.dart';
import '../supabase_service.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'verified_badge.dart';
import 'interactive_verified_badge.dart';

class ProfileAvatar extends StatefulWidget {
  final String userId;
  final double radius;
  const ProfileAvatar({super.key, required this.userId, this.radius = 20});

  @override
  State<ProfileAvatar> createState() => _ProfileAvatarState();
}

class _ProfileAvatarState extends State<ProfileAvatar> {
  // لتجنب طلبات مكررة لشبكة إذا استُخدِم الأفتار عدة مرات قبل اكتمال الجلب
  static final Set<String> _requested = <String>{};
  bool _imageError = false;

  @override
  void initState() {
    super.initState();
    final cache = SupabaseService().profilesCache.value;
    final hasAvatar = ((cache[widget.userId]?['avatar_url'] ?? '') as String).isNotEmpty;
    if ((!cache.containsKey(widget.userId) || !hasAvatar) && !_requested.contains(widget.userId)) {
      _requested.add(widget.userId);
      // جلب الملف الشخصى سريعًا وإضافته للكاش عند وصوله
      SupabaseService().fetchProfile(widget.userId).then((data) {
        if (data != null) {
          final map = Map<String, Map<String, dynamic>>.from(SupabaseService().profilesCache.value);
          map[widget.userId] = data;
          SupabaseService().profilesCache.value = map;
        }
      }).catchError((_) {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Map<String, Map<String, dynamic>>>(
      valueListenable: SupabaseService().profilesCache,
      builder: (_, cache, __) {
        final profile = cache[widget.userId];
        final url = profile?['avatar_url'] ?? '';
        String name = (profile?['name'] ?? '').toString().trim();
        if (name.isEmpty) {
          name = (profile?['username'] ?? '').toString();
        }
        // إذا تغيَّر الرابط نحاول تحميل الصورة من جديد
        if (_imageError && url.isNotEmpty) {
          _imageError = false;
        }
        // لا نعرض أرقام الـ userId؛ ننتظر الاسم وإلا نظهر أيقونة افتراضية
        final initials = name.isNotEmpty
            ? name.trim().split(' ').take(2).map((e) => e.characters.first).join().toUpperCase()
            : '';

        final avatar = CircleAvatar(
          radius: widget.radius,
          backgroundColor: Colors.grey.shade300,
          backgroundImage: (url.isNotEmpty && !_imageError) ? NetworkImage(url) : null,
          onBackgroundImageError: (_, __) {
            if (!_imageError) {
              setState(() => _imageError = true);
            }
          },
          child: (url.isEmpty || _imageError)
              ? (initials.isNotEmpty
                  ? Text(
                      initials,
                      style: TextStyle(fontSize: widget.radius * 0.6, color: Colors.black),
                    )
                  : Icon(Icons.person, size: widget.radius))
              : null,
        );

        // إضافة شارة التحقق إذا كان المستخدم موثقاً
        final isVerified = profile?['is_verified'] ?? false;
        
        if (isVerified) {
          return Stack(
            children: [
              avatar,
              Positioned(
                bottom: 0,
                right: 0,
                child: InteractiveVerifiedBadge(
                  size: widget.radius * 0.6,
                  userName: name,
                ),
              ),
            ],
          );
        }

        return avatar;
      },
    );
  }
} 