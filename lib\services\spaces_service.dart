import 'dart:typed_data';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/space.dart';
import '../supabase_service.dart';

class SpacesService {
  static final SpacesService _instance = SpacesService._internal();
  factory SpacesService() => _instance;
  SpacesService._internal();

  final _supabase = Supabase.instance.client;

  // إنشاء مساحة جديدة
  Future<Space> createSpace({
    required String name,
    required String description,
    String? goal,
    required SpaceCategory category,
    String? profession,
    SpacePrivacy privacy = SpacePrivacy.public,
    String? phoneNumber,
    String? email,
    String? website,
    Map<String, String> socialLinks = const {},
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      // جلب اسم المستخدم
      final userProfile = await _supabase
          .from('profiles')
          .select('name, username')
          .eq('id', userId)
          .single();

      final spaceData = {
        'owner_id': userId,
        'owner_name': userProfile['name'] ?? userProfile['username'] ?? 'مستخدم',
        'name': name,
        'description': description,
        'goal': goal,
        'category': category.name,
        'profession': profession,
        'privacy': privacy.name,
        'phone_number': phoneNumber,
        'email': email,
        'website': website,
        'social_links': socialLinks,
        'status': SpaceStatus.active.name,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await _supabase
          .from('spaces')
          .insert(spaceData)
          .select()
          .single();

      return Space.fromJson(response).copyWith(isOwner: true);
    } catch (e) {
      throw Exception('فشل في إنشاء المساحة: $e');
    }
  }

  // جلب مساحات المستخدم
  Future<List<Space>> getUserSpaces([String? userId]) async {
    try {
      final targetUserId = userId ?? _supabase.auth.currentUser?.id;
      if (targetUserId == null) return [];

      final response = await _supabase
          .from('spaces')
          .select('''
            *,
            space_followers!left(follower_id),
            posts:space_posts(count)
          ''')
          .eq('owner_id', targetUserId)
          .eq('status', SpaceStatus.active.name)
          .order('created_at', ascending: false);

      final currentUserId = _supabase.auth.currentUser?.id;

      return (response as List).map((json) {
        final followers = json['space_followers'] as List? ?? [];
        final isFollowing = followers.any((f) => f['follower_id'] == currentUserId);
        final isOwner = json['owner_id'] == currentUserId;
        
        // حساب عدد المنشورات مع طباعة للتشخيص
        final postsData = json['posts'] as List? ?? [];
        final postsCount = postsData.isNotEmpty ? (postsData.first['count'] ?? 0) as int : 0;
        
        print('📊 المساحة: ${json['name']}, عدد المنشورات: $postsCount, البيانات: $postsData');

        return Space.fromJson(json).copyWith(
          isFollowing: isFollowing,
          isOwner: isOwner,
          followersCount: followers.length,
          postsCount: postsCount,
        );
      }).toList();
    } catch (e) {
      print('❌ خطأ في جلب مساحات المستخدم: $e');
      // إذا لم تكن الجداول موجودة، إرجاع قائمة فارغة
      return [];
    }
  }

  // جلب المساحات المقترحة
  Future<List<Space>> getSuggestedSpaces({int limit = 20}) async {
    try {
      final currentUserId = _supabase.auth.currentUser?.id;

      final response = await _supabase
          .from('spaces')
          .select('''
            *,
            space_followers!left(follower_id),
            posts:space_posts(count)
          ''')
          .eq('status', SpaceStatus.active.name)
          .eq('privacy', SpacePrivacy.public.name)
          .neq('owner_id', currentUserId ?? '')
          .order('followers_count', ascending: false)
          .limit(limit);

      return (response as List).map((json) {
        final followers = json['space_followers'] as List? ?? [];
        final isFollowing = followers.any((f) => f['follower_id'] == currentUserId);
        
        // حساب عدد المنشورات
        final postsCount = ((json['posts'] as List?)?.first?['count'] ?? 0) as int;

        return Space.fromJson(json).copyWith(
          isFollowing: isFollowing,
          isOwner: false,
          followersCount: followers.length,
          postsCount: postsCount,
        );
      }).toList();
    } catch (e) {
      print('❌ خطأ في جلب المساحات المقترحة: $e');
      // إذا لم تكن الجداول موجودة، إرجاع قائمة فارغة
      return [];
    }
  }

  // البحث في المساحات
  Future<List<Space>> searchSpaces({
    String? query,
    SpaceCategory? category,
    int limit = 50,
  }) async {
    try {
      final currentUserId = _supabase.auth.currentUser?.id;

      var queryBuilder = _supabase
          .from('spaces')
          .select('''
            *,
            space_followers!left(follower_id),
            posts:space_posts(count)
          ''')
          .eq('status', SpaceStatus.active.name)
          .eq('privacy', SpacePrivacy.public.name);

      if (query != null && query.isNotEmpty) {
        queryBuilder = queryBuilder.or('name.ilike.%$query%,description.ilike.%$query%');
      }

      if (category != null) {
        queryBuilder = queryBuilder.eq('category', category.name);
      }

      final response = await queryBuilder
          .order('followers_count', ascending: false)
          .limit(limit);

      return (response as List).map((json) {
        final followers = json['space_followers'] as List? ?? [];
        final isFollowing = followers.any((f) => f['follower_id'] == currentUserId);
        final isOwner = json['owner_id'] == currentUserId;
        
        // حساب عدد المنشورات
        final postsCount = ((json['posts'] as List?)?.first?['count'] ?? 0) as int;

        return Space.fromJson(json).copyWith(
          isFollowing: isFollowing,
          isOwner: isOwner,
          followersCount: followers.length,
          postsCount: postsCount,
        );
      }).toList();
    } catch (e) {
      print('❌ خطأ في البحث في المساحات: $e');
      // إذا لم تكن الجداول موجودة، إرجاع قائمة فارغة
      return [];
    }
  }

  // متابعة/إلغاء متابعة مساحة
  Future<bool> toggleSpaceFollow(String spaceId) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      // التحقق من حالة المتابعة الحالية
      final existingFollow = await _supabase
          .from('space_followers')
          .select()
          .eq('space_id', spaceId)
          .eq('follower_id', userId)
          .maybeSingle();

      if (existingFollow != null) {
        // إلغاء المتابعة
        await _supabase
            .from('space_followers')
            .delete()
            .eq('space_id', spaceId)
            .eq('follower_id', userId);

        // تحديث عداد المتابعين
        await _supabase.rpc('decrement_space_followers', params: {
          'space_id': spaceId,
        });

        return false;
      } else {
        // إضافة المتابعة
        await _supabase.from('space_followers').insert({
          'space_id': spaceId,
          'follower_id': userId,
          'followed_at': DateTime.now().toIso8601String(),
        });

        // تحديث عداد المتابعين
        await _supabase.rpc('increment_space_followers', params: {
          'space_id': spaceId,
        });

        return true;
      }
    } catch (e) {
      throw Exception('فشل في تحديث المتابعة: $e');
    }
  }

  // زيادة مشاهدات المساحة
  Future<void> incrementSpaceViews(String spaceId) async {
    try {
      final currentUserId = _supabase.auth.currentUser?.id;
      if (currentUserId == null) return;

      // التحقق من أن المستخدم لم يزُر المساحة مؤخراً (لتجنب التكرار)
      final lastView = await _supabase
          .from('space_views')
          .select('created_at')
          .eq('space_id', spaceId)
          .eq('viewer_id', currentUserId)
          .order('created_at', ascending: false)
          .limit(1)
          .maybeSingle();

      // إذا لم يزُر المساحة في آخر 5 دقائق، أضف مشاهدة جديدة
      if (lastView == null || 
          DateTime.now().difference(DateTime.parse(lastView['created_at'])).inMinutes > 5) {
        
        // إضافة مشاهدة جديدة
        await _supabase
            .from('space_views')
            .insert({
              'space_id': spaceId,
              'viewer_id': currentUserId,
              'created_at': DateTime.now().toIso8601String(),
            });

        // زيادة عداد المشاهدات في المساحة
        try {
          await _supabase.rpc('increment_space_views', params: {
            'space_id': spaceId,
          });
        } catch (e) {
          // إذا لم تكن الدالة موجودة، استخدم الطريقة البديلة
          final currentViews = await _supabase
              .from('spaces')
              .select('views_count')
              .eq('id', spaceId)
              .single();
          
          await _supabase
              .from('spaces')
              .update({
                'views_count': (currentViews['views_count'] ?? 0) + 1,
                'updated_at': DateTime.now().toIso8601String(),
              })
              .eq('id', spaceId);
        }

        print('👁️ تم زيادة مشاهدات المساحة: $spaceId');
      }
    } catch (e) {
      print('❌ خطأ في زيادة مشاهدات المساحة: $e');
    }
  }

  // جلب تفاصيل مساحة
  Future<Space?> getSpaceDetails(String spaceId) async {
    try {
      final currentUserId = _supabase.auth.currentUser?.id;

      final response = await _supabase
          .from('spaces')
          .select('''
            *,
            space_followers!left(follower_id),
            posts:space_posts(count)
          ''')
          .eq('id', spaceId)
          .single();

      final followers = response['space_followers'] as List? ?? [];
      final isFollowing = followers.any((f) => f['follower_id'] == currentUserId);
      final isOwner = response['owner_id'] == currentUserId;
      
      // حساب عدد المنشورات
      final postsCount = ((response['posts'] as List?)?.first?['count'] ?? 0) as int;

      // زيادة مشاهدات المساحة لجميع الزوار (وليس المالك فقط)
      await incrementSpaceViews(spaceId);

      return Space.fromJson(response).copyWith(
        isFollowing: isFollowing,
        isOwner: isOwner,
        followersCount: followers.length,
        postsCount: postsCount,
      );
    } catch (e) {
      print('❌ خطأ في جلب تفاصيل المساحة: $e');
      return null;
    }
  }

  // جلب مساحة واحدة مع عدد المنشورات
  Future<Space?> getSpaceById(String spaceId) async {
    try {
      final currentUserId = _supabase.auth.currentUser?.id;

      final response = await _supabase
          .from('spaces')
          .select('''
            *,
            space_followers!left(follower_id),
            posts:space_posts(count)
          ''')
          .eq('id', spaceId)
          .eq('status', SpaceStatus.active.name)
          .maybeSingle();

      if (response == null) return null;

      final followers = response['space_followers'] as List? ?? [];
      final isFollowing = followers.any((f) => f['follower_id'] == currentUserId);
      final isOwner = response['owner_id'] == currentUserId;
      
      // حساب عدد المنشورات
      final postsCount = ((response['posts'] as List?)?.first?['count'] ?? 0) as int;

      // زيادة مشاهدات المساحة لجميع الزوار
      await incrementSpaceViews(spaceId);

      return Space.fromJson(response).copyWith(
        isFollowing: isFollowing,
        isOwner: isOwner,
        followersCount: followers.length,
        postsCount: postsCount,
      );
    } catch (e) {
      print('❌ خطأ في جلب المساحة: $e');
      return null;
    }
  }

  // تحديث مساحة
  Future<Space> updateSpace({
    required String spaceId,
    String? name,
    String? description,
    String? goal,
    SpaceCategory? category,
    String? profession,
    SpacePrivacy? privacy,
    String? phoneNumber,
    String? email,
    String? website,
    Map<String, String>? socialLinks,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (name != null) updateData['name'] = name;
      if (description != null) updateData['description'] = description;
      if (goal != null) updateData['goal'] = goal;
      if (category != null) updateData['category'] = category.name;
      if (profession != null) updateData['profession'] = profession;
      if (privacy != null) updateData['privacy'] = privacy.name;
      if (phoneNumber != null) updateData['phone_number'] = phoneNumber;
      if (email != null) updateData['email'] = email;
      if (website != null) updateData['website'] = website;
      if (socialLinks != null) updateData['social_links'] = socialLinks;

      final response = await _supabase
          .from('spaces')
          .update(updateData)
          .eq('id', spaceId)
          .eq('owner_id', userId)
          .select()
          .single();

      return Space.fromJson(response).copyWith(isOwner: true);
    } catch (e) {
      throw Exception('فشل في تحديث المساحة: $e');
    }
  }

  // حذف مساحة
  Future<bool> deleteSpace(String spaceId) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      await _supabase
          .from('spaces')
          .delete()
          .eq('id', spaceId)
          .eq('owner_id', userId);

      return true;
    } catch (e) {
      return false;
    }
  }

  // جلب المساحات المتابعة
  Future<List<Space>> getFollowedSpaces() async {
    try {
      final currentUserId = _supabase.auth.currentUser?.id;
      if (currentUserId == null) return [];

      final response = await _supabase
          .from('space_followers')
          .select('''
            spaces!inner(
              *,
              space_followers!left(follower_id),
              posts:space_posts(count)
            )
          ''')
          .eq('follower_id', currentUserId)
          .order('created_at', ascending: false);

      return (response as List).map((json) {
        final spaceData = json['spaces'] as Map<String, dynamic>;
        final followers = spaceData['space_followers'] as List? ?? [];
        final isFollowing = followers.any((f) => f['follower_id'] == currentUserId);
        final isOwner = spaceData['owner_id'] == currentUserId;
        
        // حساب عدد المنشورات
        final postsCount = ((spaceData['posts'] as List?)?.first?['count'] ?? 0) as int;

        return Space.fromJson(spaceData).copyWith(
          isFollowing: isFollowing,
          isOwner: isOwner,
          followersCount: followers.length,
          postsCount: postsCount,
        );
      }).toList();
    } catch (e) {
      print('❌ خطأ في جلب المساحات المتبوعة: $e');
      return [];
    }
  }

  // تحديث صورة الملف الشخصي للمساحة - حل بسيط وفعال
  Future<String> updateSpaceAvatar(String spaceId, Uint8List imageBytes) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) {
      throw Exception('يجب تسجيل الدخول أولاً');
    }

    try {
      // استخدام نظام رفع الوسائط العادي (مثل المنشورات)
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'space_avatar_${spaceId}_$timestamp.jpg';
      final filePath = 'spaces/avatars/$fileName';

      // رفع الصورة باستخدام نظام الوسائط العادي
      final supabaseService = SupabaseService();
      final imageUrl = await supabaseService.uploadMedia(imageBytes, filePath);

      // تحديث قاعدة البيانات
      await _supabase
          .from('spaces')
          .update({
            'profile_image': imageUrl, // استخدام profile_image بدلاً من avatar_url
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', spaceId);

      return imageUrl;
    } catch (e) {
      throw Exception('فشل في تحديث صورة الملف الشخصي: $e');
    }
  }

  // تحديث صورة الغلاف للمساحة - حل بسيط وفعال
  Future<String> updateSpaceCover(String spaceId, Uint8List imageBytes) async {
    final userId = _supabase.auth.currentUser?.id;
    if (userId == null) {
      throw Exception('يجب تسجيل الدخول أولاً');
    }

    try {
      // استخدام نظام رفع الوسائط العادي (مثل المنشورات)
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'space_cover_${spaceId}_$timestamp.jpg';
      final filePath = 'spaces/covers/$fileName';

      // رفع الصورة باستخدام نظام الوسائط العادي
      final supabaseService = SupabaseService();
      final imageUrl = await supabaseService.uploadMedia(imageBytes, filePath);

      // تحديث قاعدة البيانات
      await _supabase
          .from('spaces')
          .update({
            'cover_image': imageUrl, // استخدام cover_image بدلاً من cover_url
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', spaceId);

      return imageUrl;
    } catch (e) {
      throw Exception('فشل في تحديث صورة الغلاف: $e');
    }
  }

  // تحديث المعرف الفريد للمساحة
  Future<void> updateSpaceUsername(String spaceId, String username) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      // التحقق من ملكية المساحة
      final space = await _supabase
          .from('spaces')
          .select('owner_id')
          .eq('id', spaceId)
          .single();

      if (space['owner_id'] != userId) {
        throw Exception('ليس لديك صلاحية لتعديل هذه المساحة');
      }

      // التحقق من صحة المعرف الفريد
      if (!RegExp(r'^[a-zA-Z0-9_]{3,30}$').hasMatch(username)) {
        throw Exception('المعرف الفريد يجب أن يحتوي على أحرف وأرقام فقط (3-30 حرف)');
      }

      // التحقق من عدم وجود المعرف مسبقاً
      final existing = await _supabase
          .from('spaces')
          .select('id')
          .eq('username', username)
          .neq('id', spaceId)
          .maybeSingle();

      if (existing != null) {
        throw Exception('هذا المعرف الفريد مستخدم بالفعل');
      }

      // تحديث قاعدة البيانات
      await _supabase
          .from('spaces')
          .update({
            'username': username,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', spaceId);

    } catch (e) {
      throw Exception('فشل في تحديث المعرف الفريد: $e');
    }
  }

  // التحقق من توفر المعرف الفريد
  Future<bool> isUsernameAvailable(String username) async {
    try {
      if (!RegExp(r'^[a-zA-Z0-9_]{3,30}$').hasMatch(username)) {
        return false;
      }

      final existing = await _supabase
          .from('spaces')
          .select('id')
          .eq('username', username)
          .maybeSingle();

      return existing == null;
    } catch (e) {
      return false;
    }
  }

  // حساب عدد المنشورات في مساحة معينة
  Future<int> getSpacePostsCount(String spaceId) async {
    try {
      print('🔍 البحث عن منشورات المساحة: $spaceId');
      
      // أولاً، تحقق من وجود المنشورات
      final postsResponse = await _supabase
          .from('space_posts')
          .select('id, content, space_id')
          .eq('space_id', spaceId);
      
      print('📋 المنشورات الموجودة: ${postsResponse.length}');
      for (final post in postsResponse) {
        print('  - منشور: ${post['id']}, المحتوى: ${post['content']?.toString().substring(0, 20)}...');
      }
      
      // العدد هو طول القائمة
      final count = postsResponse.length;
      print('📊 عدد المنشورات في المساحة $spaceId: $count');
      return count;
    } catch (e) {
      print('❌ خطأ في حساب عدد المنشورات: $e');
      return 0;
    }
  }

  // جلب مساحات المستخدم مع عدد المنشورات المحدث
  Future<List<Space>> getUserSpacesWithPostsCount([String? userId]) async {
    try {
      final targetUserId = userId ?? _supabase.auth.currentUser?.id;
      if (targetUserId == null) return [];

      final response = await _supabase
          .from('spaces')
          .select('''
            *,
            space_followers!left(follower_id)
          ''')
          .eq('owner_id', targetUserId)
          .eq('status', SpaceStatus.active.name)
          .order('created_at', ascending: false);

      final currentUserId = _supabase.auth.currentUser?.id;
      final List<Space> spaces = [];

      for (final json in response as List) {
        final followers = json['space_followers'] as List? ?? [];
        final isFollowing = followers.any((f) => f['follower_id'] == currentUserId);
        final isOwner = json['owner_id'] == currentUserId;
        
        // حساب عدد المنشورات بطريقة منفصلة
        final postsCount = await getSpacePostsCount(json['id']);

        spaces.add(Space.fromJson(json).copyWith(
          isFollowing: isFollowing,
          isOwner: isOwner,
          followersCount: followers.length,
          postsCount: postsCount,
        ));
      }

      return spaces;
    } catch (e) {
      print('❌ خطأ في جلب مساحات المستخدم: $e');
      return [];
    }
  }


}
