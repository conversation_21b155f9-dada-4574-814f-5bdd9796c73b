-- =============================================================
--  تشخيص مشكلة عدم ظهور منشورات المجتمع
--  Diagnose Community Posts Display Issues
-- =============================================================

-- هذا السكريپت يفحص مشاكل عرض منشورات المجتمعات

-- 1) فحص جدول منشورات المجتمع
-- -------------------------------------------------------

SELECT 
  '🔍 COMMUNITY_POSTS TABLE' as check_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'community_posts')
    THEN '✅ EXISTS: Community posts table found'
    ELSE '❌ MISSING: Community posts table not found'
  END as status,
  'Basic table existence check' as details;

-- 2) فحص البيانات الموجودة
-- -------------------------------------------------------

SELECT 
  '🔍 DATA CHECK' as check_type,
  '📊 FOUND: ' || COUNT(*)::text || ' community posts' as status,
  'Total posts in community_posts table' as details
FROM community_posts;

-- 3) فحص أعمدة الجدول
-- -------------------------------------------------------

SELECT 
  '🔍 TABLE COLUMNS' as check_type,
  '✅ COLUMNS: ' || STRING_AGG(column_name, ', ') as status,
  'Available columns in community_posts table' as details
FROM information_schema.columns 
WHERE table_name = 'community_posts' 
AND table_schema = 'public';

-- 4) فحص RLS على جدول منشورات المجتمع
-- -------------------------------------------------------

SELECT 
  '🔍 RLS STATUS' as check_type,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'public' 
      AND c.relname = 'community_posts'
      AND c.relrowsecurity = false
    )
    THEN '✅ GOOD: RLS disabled on community_posts'
    WHEN EXISTS (
      SELECT 1 FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'public' 
      AND c.relname = 'community_posts'
      AND c.relrowsecurity = true
    )
    THEN '⚠️ WARNING: RLS enabled - may block reads'
    ELSE '❓ UNKNOWN: Cannot determine RLS status'
  END as status,
  'Row Level Security check' as details;

-- 5) فحص السياسات
-- -------------------------------------------------------

SELECT 
  '🔍 POLICIES CHECK' as check_type,
  CASE 
    WHEN NOT EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE tablename = 'community_posts' 
      AND schemaname = 'public'
    )
    THEN '✅ GOOD: No policies blocking reads'
    ELSE '⚠️ WARNING: Policies exist - may block reads'
  END as status,
  COALESCE(
    (SELECT STRING_AGG(policyname || ' (' || cmd || ')', ', ')
     FROM pg_policies 
     WHERE tablename = 'community_posts' AND schemaname = 'public'),
    'No policies'
  ) as details;

-- 6) اختبار استعلام بسيط
-- -------------------------------------------------------

DO $$
DECLARE
  post_count INTEGER;
  test_status TEXT := '❌ FAILED';
  test_details TEXT := 'Query test failed';
BEGIN
  -- محاولة استعلام بسيط
  SELECT COUNT(*) INTO post_count FROM community_posts;
  
  IF post_count >= 0 THEN
    test_status := '✅ SUCCESS';
    test_details := 'Simple query works - ' || post_count || ' posts found';
  END IF;
  
  -- حفظ النتيجة
  CREATE TEMP TABLE IF NOT EXISTS query_test (
    check_type TEXT,
    status TEXT,
    details TEXT
  );
  
  INSERT INTO query_test VALUES (
    '🔍 SIMPLE QUERY TEST',
    test_status,
    test_details
  );
EXCEPTION 
  WHEN OTHERS THEN
    INSERT INTO query_test VALUES (
      '🔍 SIMPLE QUERY TEST',
      '❌ FAILED',
      'Query failed: ' || SQLERRM
    );
END $$;

-- عرض نتيجة الاختبار
SELECT check_type, status, details FROM query_test;

-- 7) اختبار استعلام مع join
-- -------------------------------------------------------

DO $$
DECLARE
  join_count INTEGER;
  test_status TEXT := '❌ FAILED';
  test_details TEXT := 'Join test failed';
BEGIN
  -- محاولة استعلام مع join للـ profiles
  SELECT COUNT(*) INTO join_count 
  FROM community_posts cp
  LEFT JOIN profiles p ON p.id = cp.user_id;
  
  IF join_count >= 0 THEN
    test_status := '✅ SUCCESS';
    test_details := 'Join query works - ' || join_count || ' posts with profiles';
  END IF;
  
  INSERT INTO query_test VALUES (
    '🔍 JOIN QUERY TEST',
    test_status,
    test_details
  );
EXCEPTION 
  WHEN OTHERS THEN
    INSERT INTO query_test VALUES (
      '🔍 JOIN QUERY TEST',
      '❌ FAILED',
      'Join query failed: ' || SQLERRM
    );
END $$;

-- عرض نتيجة اختبار Join
SELECT check_type, status, details FROM query_test WHERE check_type = '🔍 JOIN QUERY TEST';

-- 8) اختبار الاستعلام الكامل (مثل التطبيق)
-- -------------------------------------------------------

DO $$
DECLARE
  full_count INTEGER;
  test_status TEXT := '❌ FAILED';
  test_details TEXT := 'Full query test failed';
  sample_community_id UUID;
BEGIN
  -- البحث عن مجتمع للاختبار
  SELECT id INTO sample_community_id FROM communities LIMIT 1;
  
  IF sample_community_id IS NOT NULL THEN
    -- محاولة الاستعلام الكامل مثل التطبيق
    SELECT COUNT(*) INTO full_count 
    FROM community_posts cp
    LEFT JOIN profiles p ON p.id = cp.user_id
    LEFT JOIN community_post_votes cpv ON cpv.post_id = cp.id
    WHERE cp.community_id = sample_community_id;
    
    IF full_count >= 0 THEN
      test_status := '✅ SUCCESS';
      test_details := 'Full query works - ' || full_count || ' posts for community ' || sample_community_id;
    END IF;
  ELSE
    test_status := '⚠️ NO DATA';
    test_details := 'No communities found to test';
  END IF;
  
  INSERT INTO query_test VALUES (
    '🔍 FULL QUERY TEST',
    test_status,
    test_details
  );
EXCEPTION 
  WHEN OTHERS THEN
    INSERT INTO query_test VALUES (
      '🔍 FULL QUERY TEST',
      '❌ FAILED',
      'Full query failed: ' || SQLERRM
    );
END $$;

-- عرض نتيجة الاختبار الكامل
SELECT check_type, status, details FROM query_test WHERE check_type = '🔍 FULL QUERY TEST';

-- 9) فحص آخر المنشورات
-- -------------------------------------------------------

SELECT 
  '🔍 RECENT POSTS' as check_type,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM community_posts 
      WHERE created_at > NOW() - INTERVAL '1 hour'
    )
    THEN '✅ ACTIVE: Recent posts found'
    ELSE '⚠️ STALE: No recent posts'
  END as status,
  COALESCE(
    (SELECT 'Last post: ' || MAX(created_at)::text 
     FROM community_posts),
    'No posts found'
  ) as details;

-- 10) التوصية النهائية
-- -------------------------------------------------------

SELECT 
  '💡 RECOMMENDATION' as check_type,
  CASE 
    WHEN NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'community_posts')
    THEN '🔧 CRITICAL: Create community_posts table'
    WHEN EXISTS (
      SELECT 1 FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'public' 
      AND c.relname = 'community_posts'
      AND c.relrowsecurity = true
    ) AND EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE tablename = 'community_posts' 
      AND schemaname = 'public'
      AND cmd = 'SELECT'
    )
    THEN '🔧 FIX: Disable RLS or create permissive SELECT policy'
    WHEN NOT EXISTS (SELECT 1 FROM community_posts)
    THEN '⚠️ INFO: No posts exist yet - create some posts to test'
    ELSE '🎉 GOOD: Database setup looks correct - check app code'
  END as status,
  'Follow this recommendation to fix display issues' as details;

-- تنظيف
DROP TABLE IF EXISTS query_test;

-- =============================================================
--  تفسير النتائج
-- =============================================================

/*

كيفية قراءة النتائج:

✅ GOOD/SUCCESS/EXISTS = ممتاز، يعمل
⚠️ WARNING/STALE/INFO = تحذير أو معلومة
❌ FAILED/MISSING/CRITICAL = مشكلة حرجة

إذا رأيت "🎉 GOOD: Database setup looks correct - check app code"
فهذا يعني أن قاعدة البيانات سليمة والمشكلة في كود التطبيق.

إذا رأيت أي توصية أخرى، اتبعها لإصلاح مشكلة قاعدة البيانات.

*/
