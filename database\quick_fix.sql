-- ===================================
-- إصلاح سريع لقاعدة بيانات الصدقات
-- ===================================

-- 1. إنشاء جدول profiles إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT,
    avatar_url TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. تفعيل RLS على جدول profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- 3. سياسات profiles
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON profiles;
CREATE POLICY "Public profiles are viewable by everyone" ON profiles
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 4. إنشاء جدول charity_items إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS charity_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('donation', 'request', 'urgent')),
    category TEXT NOT NULL CHECK (category IN ('food', 'clothes', 'furniture', 'medicine', 'books', 'electronics', 'other')),
    condition TEXT NOT NULL CHECK (condition IN ('new_item', 'used', 'good', 'urgent')),
    delivery_method TEXT NOT NULL CHECK (delivery_method IN ('hand', 'person', 'charity')),
    city TEXT NOT NULL,
    country TEXT DEFAULT 'السعودية',
    phone_number TEXT,
    images TEXT[] DEFAULT '{}',
    is_urgent BOOLEAN DEFAULT FALSE,
    is_anonymous BOOLEAN DEFAULT FALSE,
    is_completed BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    interest_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. إنشاء جدول charity_interests إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS charity_interests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    item_id UUID REFERENCES charity_items(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(item_id, user_id)
);

-- 6. إنشاء جدول charity_reports إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS charity_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    item_id UUID REFERENCES charity_items(id) ON DELETE CASCADE,
    reporter_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    reason TEXT NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 7. تفعيل RLS على جميع الجداول
ALTER TABLE charity_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE charity_interests ENABLE ROW LEVEL SECURITY;
ALTER TABLE charity_reports ENABLE ROW LEVEL SECURITY;

-- 8. سياسات charity_items
DROP POLICY IF EXISTS "Anyone can view active charity items" ON charity_items;
CREATE POLICY "Anyone can view active charity items" ON charity_items
    FOR SELECT USING (is_active = true);

DROP POLICY IF EXISTS "Authenticated users can insert charity items" ON charity_items;
CREATE POLICY "Authenticated users can insert charity items" ON charity_items
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update own charity items" ON charity_items;
CREATE POLICY "Users can update own charity items" ON charity_items
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete own charity items" ON charity_items;
CREATE POLICY "Users can delete own charity items" ON charity_items
    FOR DELETE USING (auth.uid() = user_id);

-- 9. سياسات charity_interests
DROP POLICY IF EXISTS "Users can view interests for their items" ON charity_interests;
CREATE POLICY "Users can view interests for their items" ON charity_interests
    FOR SELECT USING (
        auth.uid() = user_id OR 
        auth.uid() IN (SELECT user_id FROM charity_items WHERE id = item_id)
    );

DROP POLICY IF EXISTS "Authenticated users can show interest" ON charity_interests;
CREATE POLICY "Authenticated users can show interest" ON charity_interests
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can remove own interest" ON charity_interests;
CREATE POLICY "Users can remove own interest" ON charity_interests
    FOR DELETE USING (auth.uid() = user_id);

-- 10. سياسات charity_reports
DROP POLICY IF EXISTS "Authenticated users can report items" ON charity_reports;
CREATE POLICY "Authenticated users can report items" ON charity_reports
    FOR INSERT WITH CHECK (auth.uid() = reporter_id);

DROP POLICY IF EXISTS "Users can view own reports" ON charity_reports;
CREATE POLICY "Users can view own reports" ON charity_reports
    FOR SELECT USING (auth.uid() = reporter_id);

-- 11. إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_charity_items_type ON charity_items(type);
CREATE INDEX IF NOT EXISTS idx_charity_items_category ON charity_items(category);
CREATE INDEX IF NOT EXISTS idx_charity_items_city ON charity_items(city);
CREATE INDEX IF NOT EXISTS idx_charity_items_urgent ON charity_items(is_urgent);
CREATE INDEX IF NOT EXISTS idx_charity_items_active ON charity_items(is_active, is_completed);
CREATE INDEX IF NOT EXISTS idx_charity_items_created_at ON charity_items(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_charity_interests_item_id ON charity_interests(item_id);
CREATE INDEX IF NOT EXISTS idx_charity_interests_user_id ON charity_interests(user_id);

-- 12. إنشاء الدوال المساعدة
CREATE OR REPLACE FUNCTION update_charity_interest_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE charity_items 
        SET interest_count = interest_count + 1 
        WHERE id = NEW.item_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE charity_items 
        SET interest_count = GREATEST(interest_count - 1, 0) 
        WHERE id = OLD.item_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 13. إنشاء المحفزات
DROP TRIGGER IF EXISTS charity_interest_count_trigger ON charity_interests;
CREATE TRIGGER charity_interest_count_trigger
    AFTER INSERT OR DELETE ON charity_interests
    FOR EACH ROW EXECUTE FUNCTION update_charity_interest_count();

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_charity_items_updated_at ON charity_items;
CREATE TRIGGER update_charity_items_updated_at
    BEFORE UPDATE ON charity_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 14. إدراج بيانات تجريبية (اختياري)
INSERT INTO charity_items (
    user_id, title, description, type, category, condition, 
    delivery_method, city, phone_number, is_urgent, is_anonymous
) 
SELECT 
    auth.uid(),
    'ملابس شتوية للأطفال',
    'مجموعة ملابس شتوية جديدة للأطفال من عمر 5-10 سنوات، نظيفة وفي حالة ممتازة',
    'donation',
    'clothes',
    'new_item',
    'hand',
    'الرياض',
    '0501234567',
    false,
    false
WHERE auth.uid() IS NOT NULL
  AND NOT EXISTS (SELECT 1 FROM charity_items WHERE user_id = auth.uid() LIMIT 1);

INSERT INTO charity_items (
    user_id, title, description, type, category, condition, 
    delivery_method, city, is_urgent, is_anonymous
) 
SELECT 
    auth.uid(),
    'أحتاج أدوية للضغط',
    'أحتاج أدوية للضغط بشكل عاجل، الحالة طارئة',
    'urgent',
    'medicine',
    'urgent',
    'hand',
    'جدة',
    true,
    true
WHERE auth.uid() IS NOT NULL
  AND (SELECT COUNT(*) FROM charity_items WHERE user_id = auth.uid()) < 2;

-- تم الانتهاء من الإعداد
SELECT 'تم إنشاء جداول الصدقات بنجاح!' as message;
