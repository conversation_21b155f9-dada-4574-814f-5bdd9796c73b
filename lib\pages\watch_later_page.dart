import 'package:flutter/material.dart';
import '../supabase_service.dart';
import '../widgets/post_card.dart';

class WatchLaterPage extends StatelessWidget {
  const WatchLaterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('للمشاهدة لاحقًا')),
      body: StreamBuilder(
        stream: SupabaseService().watchLaterStream(),
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return const Center(child: CircularProgressIndicator());
          }
          final posts = snapshot.data as List;
          if (posts.isEmpty) {
            return const Center(child: Text('لا توجد فيديوهات محفوظة.'));
          }
          return ListView.builder(
            itemCount: posts.length,
            itemBuilder: (_, i) => PostCard(post: posts[i]),
          );
        },
      ),
    );
  }
} 