-- =============================================================
--  إنشاء جداول دعوات المجتمع
--  Create Community Invites Tables
-- =============================================================

-- 1) جدول دعوات المجتمع
-- -------------------------------------------------------

CREATE TABLE IF NOT EXISTS community_invites (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    community_id UUID NOT NULL REFERENCES communities(id) ON DELETE CASCADE,
    invited_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    invited_by_user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(community_id, invited_user_id)
);

-- إضافة فهارس للبحث السريع
CREATE INDEX IF NOT EXISTS idx_community_invites_invited_user ON community_invites(invited_user_id);
CREATE INDEX IF NOT EXISTS idx_community_invites_community ON community_invites(community_id);
CREATE INDEX IF NOT EXISTS idx_community_invites_status ON community_invites(status);

-- 2) تعطيل RLS على جدول الدعوات
-- -------------------------------------------------------

ALTER TABLE community_invites DISABLE ROW LEVEL SECURITY;
GRANT ALL ON community_invites TO authenticated, public;

-- 3) دالة قبول دعوة المجتمع
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION accept_community_invite(
  p_invite_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
  invite_record RECORD;
BEGIN
  -- جلب بيانات الدعوة
  SELECT * INTO invite_record 
  FROM community_invites 
  WHERE id = p_invite_id 
    AND invited_user_id = p_user_id 
    AND status = 'pending';
  
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- إضافة المستخدم كعضو في المجتمع
  INSERT INTO community_members (community_id, user_id, joined_at)
  VALUES (invite_record.community_id, p_user_id, NOW())
  ON CONFLICT (community_id, user_id) DO NOTHING;
  
  -- تحديث حالة الدعوة
  UPDATE community_invites 
  SET status = 'accepted', responded_at = NOW()
  WHERE id = p_invite_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- 4) دالة رفض دعوة المجتمع
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION decline_community_invite(
  p_invite_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE community_invites 
  SET status = 'declined', responded_at = NOW()
  WHERE id = p_invite_id 
    AND invited_user_id = p_user_id 
    AND status = 'pending';
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql;

-- 5) دالة جلب دعوات المستخدم
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION get_user_community_invites(p_user_id UUID)
RETURNS TABLE (
  invite_id UUID,
  community_id UUID,
  community_name TEXT,
  community_avatar_url TEXT,
  invited_by_name TEXT,
  invited_by_avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    ci.id as invite_id,
    c.id as community_id,
    c.name as community_name,
    c.avatar_url as community_avatar_url,
    p.name as invited_by_name,
    p.avatar_url as invited_by_avatar_url,
    ci.created_at
  FROM community_invites ci
  JOIN communities c ON c.id = ci.community_id
  JOIN profiles p ON p.id = ci.invited_by_user_id
  WHERE ci.invited_user_id = p_user_id
    AND ci.status = 'pending'
  ORDER BY ci.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- 6) فحص النتائج
-- -------------------------------------------------------

SELECT 
  '🔍 TABLES CHECK' as check_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'community_invites')
    AND EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'accept_community_invite')
    AND EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'decline_community_invite')
    AND EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'get_user_community_invites')
    THEN '✅ SUCCESS: All community invite functions created'
    ELSE '❌ FAILED: Some functions missing'
  END as status;

-- 7) النتيجة النهائية
-- -------------------------------------------------------

SELECT 
  '🎉 FINAL RESULT' as check_type,
  '✅ COMMUNITY INVITES SYSTEM READY!' as status,
  'Users can now invite followers and receive notifications' as details;

-- =============================================================
--  تعليمات الاستخدام
-- =============================================================

/*

بعد تشغيل هذا السكريپت:

1. جدول community_invites جاهز لحفظ الدعوات
2. دوال قبول/رفض الدعوات متاحة
3. دالة جلب دعوات المستخدم متاحة
4. نظام الإشعارات سيعمل مع الدعوات

الاستخدام في التطبيق:
- inviteUserToCommunity() - إرسال دعوة
- acceptCommunityInvite() - قبول دعوة  
- declineCommunityInvite() - رفض دعوة
- getUserCommunityInvites() - جلب دعوات المستخدم

*/

-- =============================================================
--  انتهى إنشاء جداول دعوات المجتمع
-- =============================================================
