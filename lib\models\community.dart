class Community {
  final String id;
  final String ownerId;
  final String name;
  final String? description;
  final String? category;
  final String? coverUrl;
  final String? avatarUrl; // NEW: صورة شخصية للمجتمع
  final bool isPrivate;
  final bool isArchived; // NEW: حالة الأرشفة
  final bool isDisabled; // NEW: حالة التعطيل
  final DateTime createdAt;
  final int membersCount;
  // NEW: إعدادات عامة للمجتمع
  final bool allowMemberPosts; // السماح للأعضاء بالنشر
  final bool requireApproval; // تتطلب موافقة على المنشورات
  final bool allowComments; // السماح بالتعليقات
  final bool allowInvites; // السماح للأعضاء بدعوة آخرين
  final String postPermission; // من يمكنه النشر: 'all', 'members', 'admins'
  final String joinType; // نوع الانضمام: 'open', 'approval', 'invite_only'

  Community({
    required this.id,
    required this.ownerId,
    required this.name,
    this.description,
    this.category,
    this.coverUrl,
    this.avatarUrl,
    required this.isPrivate,
    this.isArchived = false,
    this.isDisabled = false,
    required this.createdAt,
    this.membersCount = 0,
    this.allowMemberPosts = true,
    this.requireApproval = false,
    this.allowComments = true,
    this.allowInvites = true,
    this.postPermission = 'members',
    this.joinType = 'open',
  });

  factory Community.fromMap(Map<String, dynamic> map) {
    // Supabase قد ترجع members: [] بداخله count
    int count = 0;
    if (map['members'] is List && (map['members'] as List).isNotEmpty) {
      count = (map['members'][0]['count'] ?? 0) as int;
    } else if (map['members_count'] != null) {
      count = map['members_count'] as int;
    }

    return Community(
      id: map['id'].toString(),
      ownerId: map['owner_id'].toString(),
      name: map['name'] ?? '',
      description: map['description'],
      category: map['category'],
      coverUrl: map['cover_url'],
      avatarUrl: map['avatar_url'],
      isPrivate: map['is_private'] ?? false,
      isArchived: map['is_archived'] ?? false,
      isDisabled: map['is_disabled'] ?? false,
      createdAt: DateTime.parse(map['created_at']),
      membersCount: count,
      allowMemberPosts: map['allow_member_posts'] ?? true,
      requireApproval: map['require_approval'] ?? false,
      allowComments: map['allow_comments'] ?? true,
      allowInvites: map['allow_invites'] ?? true,
      postPermission: map['post_permission'] ?? 'members',
      joinType: map['join_type'] ?? 'open',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'owner_id': ownerId,
      'name': name,
      'description': description,
      'category': category,
      'cover_url': coverUrl,
      'avatar_url': avatarUrl,
      'is_private': isPrivate,
      'is_archived': isArchived,
      'is_disabled': isDisabled,
      'created_at': createdAt.toIso8601String(),
      'allow_member_posts': allowMemberPosts,
      'require_approval': requireApproval,
      'allow_comments': allowComments,
      'allow_invites': allowInvites,
      'post_permission': postPermission,
      'join_type': joinType,
    };
  }

  // Helper methods لحالة المجتمع
  bool get isActive => !isArchived && !isDisabled;
  bool get canPost => isActive && allowMemberPosts;
  bool get canComment => isActive && allowComments;
  bool get canInvite => isActive && allowInvites;
}