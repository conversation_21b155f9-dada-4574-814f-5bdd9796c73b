import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../supabase_service.dart';
import 'saved_items_page.dart';
import 'story_archive_page.dart';
import 'blocked_users_page.dart';
import 'chat_settings_page.dart';
import 'verify_account_screen.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  bool notifyFollow = true;
  bool notifyChat = true;
  bool notifyApp = true;
  String postVisibility = 'public';
  String commentPermission = 'everyone';
  bool _isSaving = false; // حالة تحميل الزر
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    final s = await SupabaseService().fetchSettings();
    setState(() {
      notifyFollow = s['notify_follow'] ?? true;
      notifyChat = s['notify_chat'] ?? true;
      notifyApp = s['notify_app'] ?? true;
      postVisibility = s['post_visibility'] ?? 'public';
      commentPermission = s['comment_permission'] ?? 'everyone';
    });
  }

  Future<void> _saveToggles() async {
    if (_isSaving) return; // منع الضغط المتكرر
    
    print('=== بدء حفظ الإعدادات ===');
    print('notifyFollow: $notifyFollow');
    print('notifyChat: $notifyChat');
    print('notifyApp: $notifyApp');
    print('postVisibility: $postVisibility');
    print('commentPermission: $commentPermission');
    
    setState(() {
      _isSaving = true;
    });

    try {
      print('جاري إرسال البيانات إلى Supabase...');
      
      // إظهار مؤشر التحميل
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 16),
                Text('جاري حفظ الإعدادات...'),
              ],
            ),
            duration: Duration(seconds: 2),
          ),
        );
      }

      final settingsData = {
        'notify_follow': notifyFollow,
        'notify_chat': notifyChat,
        'notify_app': notifyApp,
        'post_visibility': postVisibility,
        'comment_permission': commentPermission,
      };
      
      print('بيانات الإعدادات: $settingsData');
      
      await SupabaseService().updateSettings(settingsData);
      
      print('تم حفظ الإعدادات بنجاح!');

      // إظهار رسالة نجاح
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الإعدادات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('خطأ في حفظ الإعدادات: $e');
      // إظهار رسالة خطأ
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الإعدادات: $e'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      print('=== انتهاء حفظ الإعدادات ===');
      // إعادة تعيين حالة التحميل
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  Future<void> _changeEmail() async {
    final newEmail = _emailController.text.trim();
    final password = _passwordController.text.trim();
    if (newEmail.isEmpty || password.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى ملء جميع الحقول'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }
    try {
      await SupabaseService().changeEmail(newEmail, password);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال رابط التحقق إلى بريدك الإلكتروني الجديد'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _changePassword(String currentPassword, String newPassword, String confirmPassword) async {
    try {
      await SupabaseService().changePassword(currentPassword, newPassword, confirmPassword);
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تغيير كلمة المرور بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('الإعدادات'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: Container(
        color: Colors.white,
        child: ListView(
          children: [
            Container(
              color: Colors.white,
              child: Column(
                children: [
                  Container(
                    color: Colors.white,
                    child: const ListTile(
                      title: Text('الخصوصية', style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: ListTile(
                      title: const Text('من يمكنه رؤية منشوراتي الجديدة؟'),
                      subtitle: Text(postVisibility == 'public' ? 'الجميع' : 'المتابِعون فقط'),
                      trailing: DropdownButton<String>(
                        value: postVisibility,
                        items: const [
                          DropdownMenuItem(value: 'public', child: Text('الجميع')),
                          DropdownMenuItem(value: 'followers', child: Text('المتابِعون فقط')),
                        ],
                        onChanged: (v) => setState(() => postVisibility = v!),
                      ),
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: ListTile(
                      title: const Text('من يمكنه التعليق على منشوراتي؟'),
                      subtitle: Text(commentPermission == 'everyone' ? 'الجميع' : 'المتابِعون فقط'),
                      trailing: DropdownButton<String>(
                        value: commentPermission,
                        items: const [
                          DropdownMenuItem(value: 'everyone', child: Text('الجميع')),
                          DropdownMenuItem(value: 'followers', child: Text('المتابِعون فقط')),
                        ],
                        onChanged: (v) => setState(() => commentPermission = v!),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const Divider(),
            Container(
              color: Colors.white,
              child: Column(
                children: [
                  Container(
                    color: Colors.white,
                    child: const ListTile(
                      title: Text('الإشعارات', style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: SwitchListTile(
                      title: const Text('إشعارات المتابعين'),
                      value: notifyFollow,
                      onChanged: (v) => setState(() => notifyFollow = v),
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: SwitchListTile(
                      title: const Text('إشعارات الدردشة'),
                      value: notifyChat,
                      onChanged: (v) => setState(() => notifyChat = v),
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: SwitchListTile(
                      title: const Text('إشعارات التطبيق العامة'),
                      value: notifyApp,
                      onChanged: (v) => setState(() => notifyApp = v),
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: ElevatedButton(
                        onPressed: _isSaving ? null : () {
                          print('=== زر الحفظ تم الضغط عليه ===');
                          _saveToggles();
                        },
                        child: _isSaving 
                          ? const Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                ),
                                SizedBox(width: 8),
                                Text('جاري الحفظ...'),
                              ],
                            )
                          : const Text('حفظ الإعدادات'),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const Divider(),
            Container(
              color: Colors.white,
              child: Column(
                children: [
                  Container(
                    color: Colors.white,
                    child: const ListTile(
                      title: Text('الحساب', style: TextStyle(fontWeight: FontWeight.bold)),
                    ),
                  ),

                  // زر طلب توثيق الحساب
                  Container(
                    color: Colors.white,
                    child: ListTile(
                      leading: const Icon(Icons.verified, color: Colors.blue),
                      title: const Text('توثيق الحساب'),
                      subtitle: const Text('احصل على علامة التوثيق الزرقاء'),
                      trailing: const Icon(Icons.chevron_left),
                      onTap: () {
                        Navigator.push(context, MaterialPageRoute(builder: (_) => const VerifyAccountScreen()));
                      },
                    ),
                  ),

                  Container(
                    color: Colors.white,
                    child: ListTile(
                      leading: const Icon(Icons.bookmark, color: Colors.blueAccent),
                      title: const Text('المحفوظات'),
                      trailing: const Icon(Icons.chevron_left),
                      onTap: () {
                        Navigator.push(context, MaterialPageRoute(builder: (_) => const SavedItemsPage()));
                      },
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: ListTile(
                      leading: const Icon(Icons.history_edu, color: Colors.orangeAccent),
                      title: const Text('أرشيف القصص'),
                      trailing: const Icon(Icons.chevron_left),
                      onTap: () {
                        Navigator.push(context, MaterialPageRoute(builder: (_) => const StoryArchivePage()));
                      },
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: ListTile(
                      leading: const Icon(Icons.block, color: Colors.redAccent),
                      title: const Text('المستخدمون المحظورون'),
                      trailing: const Icon(Icons.chevron_left),
                      onTap: () {
                        Navigator.push(context, MaterialPageRoute(builder: (_) => const BlockedUsersPage()));
                      },
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: ListTile(
                      leading: const Icon(Icons.chat_bubble_outline, color: Colors.indigo),
                      title: const Text('إعدادات الدردشة'),
                      trailing: const Icon(Icons.chevron_left),
                      onTap: () => Navigator.push(context, MaterialPageRoute(builder: (_) => const ChatSettingsPage())),
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: ListTile(
                      leading: const Icon(Icons.email_outlined, color: Colors.teal),
                      title: const Text('تغيير البريد الإلكترونى'),
                      trailing: const Icon(Icons.chevron_left),
                      onTap: () => _showChangeEmailDialog(),
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: ListTile(
                      leading: const Icon(Icons.lock_outline, color: Colors.deepPurple),
                      title: const Text('تغيير كلمة المرور'),
                      trailing: const Icon(Icons.chevron_left),
                      onTap: () => _showChangePasswordDialog(),
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: ListTile(
                      leading: const Icon(Icons.logout, color: Colors.grey),
                      title: const Text('تسجيل الخروج'),
                      onTap: () async {
                        await SupabaseService().signOut();
                        if (context.mounted) Navigator.of(context).pushNamedAndRemoveUntil('/', (_) => false);
                      },
                    ),
                  ),
                  Container(
                    color: Colors.white,
                    child: ListTile(
                      leading: const Icon(Icons.delete_forever, color: Colors.red),
                      title: const Text('حذف الحساب'),
                      textColor: Colors.red,
                      onTap: () async {
                        final ok = await showDialog<bool>(
                          context: context,
                          builder: (_) => AlertDialog(
                            title: const Text('تأكيد الحذف'),
                            content: const Text('سيتم حذف حسابك نهائياً، هل أنت متأكد؟'),
                            actions: [
                              TextButton(onPressed: () => Navigator.pop(context,false), child: const Text('إلغاء')),
                              TextButton(onPressed: () => Navigator.pop(context,true), child: const Text('حذف', style: TextStyle(color: Colors.red))),
                            ],
                          ),
                        );
                        if (ok == true) {
                          await SupabaseService().deleteAccount();
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showChangeEmailDialog() {
    _emailController.clear();
    _passwordController.clear();
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('تغيير البريد الإلكترونى'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'البريد الإلكتروني الجديد',
                hintText: 'أدخل بريدك الإلكتروني الجديد',
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _passwordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور الحالية',
                hintText: 'أدخل كلمة المرور الحالية للتأكيد',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _changeEmail();
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _showChangePasswordDialog() {
    final currentPasswordController = TextEditingController();
    final newPasswordController = TextEditingController();
    final confirmPasswordController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('تغيير كلمة المرور'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: currentPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور الحالية',
                hintText: 'أدخل كلمة المرور الحالية',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: newPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'كلمة المرور الجديدة',
                hintText: 'أدخل كلمة المرور الجديدة',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: confirmPasswordController,
              obscureText: true,
              decoration: const InputDecoration(
                labelText: 'تأكيد كلمة المرور الجديدة',
                hintText: 'أعد إدخال كلمة المرور الجديدة',
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _changePassword(
                currentPasswordController.text,
                newPasswordController.text,
                confirmPasswordController.text,
              );
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }
} 