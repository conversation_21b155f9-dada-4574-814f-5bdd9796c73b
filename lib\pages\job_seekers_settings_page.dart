import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/job_seeker.dart';
import '../services/job_seekers_service.dart';
import '../services/job_seeker_settings_service.dart';
import 'edit_job_seeker_page.dart';

class JobSeekersSettingsPage extends StatefulWidget {
  const JobSeekersSettingsPage({super.key});

  @override
  State<JobSeekersSettingsPage> createState() => _JobSeekersSettingsPageState();
}

class _JobSeekersSettingsPageState extends State<JobSeekersSettingsPage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final JobSeekersService _jobSeekersService = JobSeekersService();
  final JobSeekerSettingsService _settingsService = JobSeekerSettingsService();

  JobSeeker? _userProfile;
  List<JobSeeker> _savedProfiles = [];
  Map<String, dynamic> _userStats = {};

  bool _loading = true;
  bool _profileNotificationsEnabled = true;
  bool _showPhoneNumber = true;
  bool _showEmail = true;
  bool _allowDirectContact = true;
  bool _showOnlineStatus = true;
  bool _profileVisible = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadUserData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    setState(() => _loading = true);
    try {
      final userProfile = await _jobSeekersService.getUserJobSeeker();
      final savedProfiles = await _jobSeekersService.getSavedJobSeekers();
      final userStats = await _settingsService.getUserStats();
      final userSettings = await _settingsService.getUserSettings();

      setState(() {
        _userProfile = userProfile;
        _savedProfiles = savedProfiles;
        _userStats = userStats;

        // تحديث الإعدادات من قاعدة البيانات
        _profileNotificationsEnabled = userSettings['profile_notifications_enabled'] ?? true;
        _showPhoneNumber = userSettings['show_phone_number'] ?? true;
        _showEmail = userSettings['show_email'] ?? true;
        _allowDirectContact = userSettings['allow_direct_contact'] ?? true;
        _showOnlineStatus = userSettings['show_online_status'] ?? true;
        _profileVisible = userSettings['profile_visible_in_search'] ?? true;

        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'إعدادات البحث عن عمل',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.indigo[600],
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.person),
              text: 'ملفي المهني',
            ),
            Tab(
              icon: Icon(Icons.bookmark),
              text: 'المحفوظات',
            ),
            Tab(
              icon: Icon(Icons.settings),
              text: 'الإعدادات',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMyProfileTab(),
          _buildSavedProfilesTab(),
          _buildSettingsTab(),
        ],
      ),
    );
  }

  Widget _buildMyProfileTab() {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_userProfile == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person_add_outlined,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لم تقم بإنشاء ملف مهني بعد',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => Navigator.pop(context),
              icon: const Icon(Icons.add),
              label: const Text('إنشاء ملف مهني'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo[600],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadUserData,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // بطاقة الملف المهني
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المستخدم
                  Row(
                    children: [
                      CircleAvatar(
                        radius: 30,
                        backgroundColor: Colors.indigo[100],
                        backgroundImage: _userProfile!.profileImage != null
                            ? NetworkImage(_userProfile!.profileImage!)
                            : null,
                        child: _userProfile!.profileImage == null
                            ? Icon(
                                Icons.person,
                                color: Colors.indigo[600],
                                size: 30,
                              )
                            : null,
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _userProfile!.fullName,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(
                                  _userProfile!.category.icon,
                                  size: 16,
                                  color: _userProfile!.category.color,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  _userProfile!.category.arabicName,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: _userProfile!.category.color,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${_userProfile!.currentCity}, ${_userProfile!.currentCountry}',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _userProfile!.isActive ? Colors.green[100] : Colors.red[100],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              _userProfile!.isActive ? Icons.visibility : Icons.visibility_off,
                              size: 14,
                              color: _userProfile!.isActive ? Colors.green[600] : Colors.red[600],
                            ),
                            const SizedBox(width: 4),
                            Text(
                              _userProfile!.isActive ? 'نشط' : 'مخفي',
                              style: TextStyle(
                                fontSize: 12,
                                color: _userProfile!.isActive ? Colors.green[600] : Colors.red[600],
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // إحصائيات الملف
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildStatItem(Icons.visibility, 'مشاهدة', '${_userProfile!.viewsCount}'),
                      _buildStatItem(Icons.favorite, 'إعجاب', '${_userProfile!.likesCount}'),
                      _buildStatItem(Icons.work_history, 'خبرة', _userProfile!.formattedExperience),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // أزرار الإجراءات
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _editProfile(),
                          icon: const Icon(Icons.edit, size: 16),
                          label: const Text('تعديل'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.blue[600],
                            side: BorderSide(color: Colors.blue[600]!),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _toggleProfileVisibility(),
                          icon: Icon(_userProfile!.isActive ? Icons.visibility_off : Icons.visibility, size: 16),
                          label: Text(_userProfile!.isActive ? 'إخفاء' : 'إظهار'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.orange[600],
                            side: BorderSide(color: Colors.orange[600]!),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: () => _deleteProfile(),
                          icon: const Icon(Icons.delete, size: 16),
                          label: const Text('حذف'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Colors.red[600],
                            side: BorderSide(color: Colors.red[600]!),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // معلومات إضافية
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.indigo[600]),
                      const SizedBox(width: 8),
                      const Text(
                        'معلومات الملف',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  
                  _buildInfoRow(Icons.cake, 'العمر', _userProfile!.formattedAge),
                  _buildInfoRow(Icons.person_outline, 'الجنس', _userProfile!.gender),
                  _buildInfoRow(Icons.family_restroom, 'الحالة الاجتماعية', _userProfile!.maritalStatus.arabicName),
                  _buildInfoRow(Icons.flag, 'الجنسية', _userProfile!.nationality),
                  _buildInfoRow(Icons.work_outline, 'نوع العمل المطلوب', _userProfile!.preferredJobType.arabicName),
                  _buildInfoRow(Icons.location_on, 'المكان المفضل', _userProfile!.preferredLocation),
                  _buildInfoRow(Icons.calendar_today, 'تاريخ الإنشاء', _formatDate(_userProfile!.createdAt)),
                  _buildInfoRow(Icons.update, 'آخر تحديث', _formatDate(_userProfile!.updatedAt)),

                  const SizedBox(height: 16),

                  // إحصائيات إضافية من خدمة الإعدادات
                  if (_userStats.isNotEmpty) ...[
                    const Divider(),
                    const SizedBox(height: 8),
                    Text(
                      'إحصائيات التفاعل',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.indigo[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    _buildInfoRow(Icons.bookmark, 'الملفات المحفوظة', '${_userStats['saved_profiles_count'] ?? 0}'),
                    _buildInfoRow(Icons.favorite, 'الإعجابات المعطاة', '${_userStats['given_likes_count'] ?? 0}'),
                    _buildInfoRow(Icons.visibility, 'حالة الملف', _userStats['profile_active'] == true ? 'نشط' : 'مخفي'),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSavedProfilesTab() {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_savedProfiles.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bookmark_border,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لم تقم بحفظ أي ملفات مهنية بعد',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadUserData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _savedProfiles.length,
        itemBuilder: (context, index) {
          final profile = _savedProfiles[index];
          return Card(
            margin: const EdgeInsets.only(bottom: 16),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: Colors.indigo[100],
                backgroundImage: profile.profileImage != null
                    ? NetworkImage(profile.profileImage!)
                    : null,
                child: profile.profileImage == null
                    ? Icon(Icons.person, color: Colors.indigo[600])
                    : null,
              ),
              title: Text(
                profile.fullName,
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(profile.category.arabicName),
                  Text('${profile.currentCity} • ${profile.formattedExperience}'),
                ],
              ),
              trailing: PopupMenuButton(
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'view',
                    child: Row(
                      children: [
                        Icon(Icons.visibility),
                        SizedBox(width: 8),
                        Text('عرض'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'unsave',
                    child: Row(
                      children: [
                        Icon(Icons.bookmark_remove, color: Colors.red),
                        SizedBox(width: 8),
                        Text('إلغاء الحفظ', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                ],
                onSelected: (value) {
                  if (value == 'view') {
                    // عرض تفاصيل الملف
                  } else if (value == 'unsave') {
                    _unsaveProfile(profile);
                  }
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildSettingsTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // إعدادات الإشعارات
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.notifications, color: Colors.indigo[600]),
                    const SizedBox(width: 8),
                    const Text(
                      'الإشعارات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                SwitchListTile(
                  title: const Text('إشعارات الملف المهني'),
                  subtitle: const Text('استقبال إشعارات عند وجود اهتمام بملفك'),
                  value: _profileNotificationsEnabled,
                  onChanged: (value) {
                    setState(() => _profileNotificationsEnabled = value);
                    _saveSettings();
                  },
                  activeColor: Colors.indigo[600],
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // إعدادات الخصوصية
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.privacy_tip, color: Colors.indigo[600]),
                    const SizedBox(width: 8),
                    const Text(
                      'الخصوصية',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                SwitchListTile(
                  title: const Text('إظهار رقم الهاتف'),
                  subtitle: const Text('السماح للآخرين برؤية رقم هاتفك كاملاً'),
                  value: _showPhoneNumber,
                  onChanged: (value) {
                    setState(() => _showPhoneNumber = value);
                    _saveSettings();
                  },
                  activeColor: Colors.indigo[600],
                ),
                SwitchListTile(
                  title: const Text('إظهار البريد الإلكتروني'),
                  subtitle: const Text('السماح للآخرين برؤية بريدك الإلكتروني'),
                  value: _showEmail,
                  onChanged: (value) {
                    setState(() => _showEmail = value);
                    _saveSettings();
                  },
                  activeColor: Colors.indigo[600],
                ),
                SwitchListTile(
                  title: const Text('السماح بالاتصال المباشر'),
                  subtitle: const Text('السماح لأصحاب العمل بالاتصال بك مباشرة'),
                  value: _allowDirectContact,
                  onChanged: (value) {
                    setState(() => _allowDirectContact = value);
                    _saveSettings();
                  },
                  activeColor: Colors.indigo[600],
                ),
                SwitchListTile(
                  title: const Text('إظهار حالة الاتصال'),
                  subtitle: const Text('إظهار ما إذا كنت متصلاً أم لا'),
                  value: _showOnlineStatus,
                  onChanged: (value) {
                    setState(() => _showOnlineStatus = value);
                    _saveSettings();
                  },
                  activeColor: Colors.indigo[600],
                ),
                SwitchListTile(
                  title: const Text('إظهار الملف في البحث'),
                  subtitle: const Text('السماح للآخرين بالعثور على ملفك في البحث'),
                  value: _profileVisible,
                  onChanged: (value) {
                    setState(() => _profileVisible = value);
                    _saveSettings();
                  },
                  activeColor: Colors.indigo[600],
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // إعدادات الحساب
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.account_circle, color: Colors.indigo[600]),
                    const SizedBox(width: 8),
                    const Text(
                      'إدارة الحساب',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ListTile(
                  leading: const Icon(Icons.download),
                  title: const Text('تصدير بياناتي'),
                  subtitle: const Text('تحميل نسخة من جميع بياناتك'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _exportData,
                ),
                ListTile(
                  leading: const Icon(Icons.cleaning_services),
                  title: const Text('مسح البيانات المحفوظة'),
                  subtitle: const Text('حذف جميع الملفات المحفوظة والإعجابات'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _clearSavedData,
                ),
                ListTile(
                  leading: const Icon(Icons.visibility_off, color: Colors.orange),
                  title: const Text('إخفاء الملف المهني', style: TextStyle(color: Colors.orange)),
                  subtitle: const Text('إخفاء ملفك من البحث مؤقتاً'),
                  trailing: const Icon(Icons.arrow_forward_ios, color: Colors.orange),
                  onTap: _hideProfile,
                ),
                ListTile(
                  leading: const Icon(Icons.delete_forever, color: Colors.red),
                  title: const Text('حذف الملف المهني نهائياً', style: TextStyle(color: Colors.red)),
                  subtitle: const Text('حذف ملفك المهني وجميع البيانات المرتبطة به'),
                  trailing: const Icon(Icons.arrow_forward_ios, color: Colors.red),
                  onTap: _deleteProfile,
                ),
              ],
            ),
          ),
        ),
        
        const SizedBox(height: 16),
        
        // معلومات التطبيق
        Card(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.info, color: Colors.indigo[600]),
                    const SizedBox(width: 8),
                    const Text(
                      'معلومات',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                ListTile(
                  leading: const Icon(Icons.help_outline),
                  title: const Text('مساعدة'),
                  subtitle: const Text('كيفية استخدام قسم البحث عن عمل'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _showHelp,
                ),
                ListTile(
                  leading: const Icon(Icons.policy),
                  title: const Text('سياسة الخصوصية'),
                  subtitle: const Text('اطلع على سياسة حماية البيانات'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _showPrivacyPolicy,
                ),
                ListTile(
                  leading: const Icon(Icons.report),
                  title: const Text('الإبلاغ عن مشكلة'),
                  subtitle: const Text('أبلغ عن مشكلة أو اقتراح تحسين'),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: _reportIssue,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStatItem(IconData icon, String label, String value) {
    return Column(
      children: [
        Icon(icon, color: Colors.indigo[600]),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  void _editProfile() {
    if (_userProfile != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => EditJobSeekerPage(jobSeeker: _userProfile!),
        ),
      ).then((_) => _loadUserData());
    }
  }

  void _toggleProfileVisibility() async {
    if (_userProfile == null) return;
    
    try {
      await _jobSeekersService.updateJobSeeker(_userProfile!.id, {
        'is_active': !_userProfile!.isActive,
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_userProfile!.isActive ? 'تم إخفاء الملف المهني' : 'تم إظهار الملف المهني'),
          backgroundColor: Colors.green,
        ),
      );
      
      _loadUserData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _deleteProfile() {
    if (_userProfile == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحذير!'),
        content: const Text(
          'هل أنت متأكد من حذف ملفك المهني نهائياً؟\n\n'
          'سيتم حذف:\n'
          '• جميع معلومات ملفك المهني\n'
          '• جميع الإعجابات والمشاهدات\n'
          '• جميع التفاعلات المرتبطة بملفك\n'
          '• جميع الإعدادات\n\n'
          'هذا الإجراء لا يمكن التراجع عنه!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _settingsService.deleteAllUserData();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف جميع البيانات بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  Navigator.pop(context);
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في الحذف: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('حذف نهائياً', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _unsaveProfile(JobSeeker profile) async {
    try {
      await _jobSeekersService.toggleSave(profile.id);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إلغاء حفظ الملف المهني'),
          backgroundColor: Colors.green,
        ),
      );
      _loadUserData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _saveSettings() async {
    try {
      await _settingsService.updateUserSettings({
        'profile_notifications_enabled': _profileNotificationsEnabled,
        'show_phone_number': _showPhoneNumber,
        'show_email': _showEmail,
        'allow_direct_contact': _allowDirectContact,
        'show_online_status': _showOnlineStatus,
        'profile_visible_in_search': _profileVisible,
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الإعدادات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الإعدادات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _exportData() async {
    try {
      final exportedData = await _settingsService.exportUserData();

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تصدير البيانات'),
            content: SingleChildScrollView(
              child: Text(
                'تم تصدير البيانات بنجاح!\n\n'
                'تاريخ التصدير: ${exportedData['export_date']}\n'
                'عدد الملفات المحفوظة: ${(exportedData['saved_profiles'] as List).length}\n'
                'عدد الإعجابات: ${(exportedData['likes'] as List).length}',
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _clearSavedData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مسح البيانات المحفوظة'),
        content: const Text('هل تريد حذف جميع الملفات المحفوظة والإعجابات؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _settingsService.clearSavedData();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم مسح البيانات المحفوظة بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _loadUserData(); // إعادة تحميل البيانات
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في مسح البيانات: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('مسح', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _hideProfile() {
    _toggleProfileVisibility();
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مساعدة'),
        content: const Text(
          'قسم البحث عن عمل يتيح لك:\n\n'
          '• إنشاء ملف مهني شامل\n'
          '• عرض مهاراتك وخبراتك\n'
          '• التواصل مع أصحاب العمل\n'
          '• البحث عن فرص عمل\n'
          '• حفظ الملفات المهنية المهمة\n\n'
          'لأي استفسارات إضافية، يرجى التواصل مع الدعم الفني.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showPrivacyPolicy() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم فتح سياسة الخصوصية قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  void _reportIssue() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة الإبلاغ عن المشاكل ستكون متاحة قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
