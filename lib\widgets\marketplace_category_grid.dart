import 'package:flutter/material.dart';
import '../models/product_category.dart';

class MarketplaceCategoryGrid extends StatelessWidget {
  final List<ProductCategory> categories;
  final Function(ProductCategory) onCategoryTap;
  final bool showSubcategories;

  const MarketplaceCategoryGrid({
    super.key,
    required this.categories,
    required this.onCategoryTap,
    this.showSubcategories = true,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الفئات الرئيسية
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              return _buildCategoryCard(context, category);
            },
          ),
          
          // الفئات الفرعية (إذا كانت مطلوبة)
          if (showSubcategories)
            ...categories.where((cat) => cat.hasSubcategories).map(
              (mainCategory) => _buildSubcategoriesSection(context, mainCategory),
            ),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(BuildContext context, ProductCategory category) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => onCategoryTap(category),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(int.parse(category.color.replaceFirst('#', '0xFF'))),
                Color(int.parse(category.color.replaceFirst('#', '0xFF')))
                    .withOpacity(0.7),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // الأيقونة
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _getIconData(category.icon),
                  size: 32,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 12),
              
              // النص
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  category.nameAr,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              
              // عدد الفئات الفرعية (إن وجدت)
              if (category.hasSubcategories) ...[
                const SizedBox(height: 4),
                Text(
                  '${category.subcategories!.length} فئة فرعية',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.white.withOpacity(0.8),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSubcategoriesSection(BuildContext context, ProductCategory mainCategory) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 24),
        
        // عنوان القسم
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 4),
          child: Row(
            children: [
              Icon(
                _getIconData(mainCategory.icon),
                size: 20,
                color: Color(int.parse(mainCategory.color.replaceFirst('#', '0xFF'))),
              ),
              const SizedBox(width: 8),
              Text(
                mainCategory.nameAr,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
        
        const SizedBox(height: 12),
        
        // الفئات الفرعية
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: mainCategory.subcategories!.map((subCategory) {
            return _buildSubcategoryChip(context, subCategory, mainCategory.color);
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildSubcategoryChip(BuildContext context, ProductCategory subCategory, String mainColor) {
    return ActionChip(
      label: Text(
        subCategory.nameAr,
        style: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      onPressed: () => onCategoryTap(subCategory),
      backgroundColor: Color(int.parse(mainColor.replaceFirst('#', '0xFF')))
          .withOpacity(0.1),
      side: BorderSide(
        color: Color(int.parse(mainColor.replaceFirst('#', '0xFF')))
            .withOpacity(0.3),
        width: 1,
      ),
      labelStyle: TextStyle(
        color: Color(int.parse(mainColor.replaceFirst('#', '0xFF'))),
      ),
      avatar: Icon(
        _getIconData(subCategory.icon),
        size: 16,
        color: Color(int.parse(mainColor.replaceFirst('#', '0xFF'))),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
    );
  }

  IconData _getIconData(String iconName) {
    // تحويل اسم الأيقونة إلى IconData
    switch (iconName.toLowerCase()) {
      case 'checkroom':
        return Icons.checkroom;
      case 'sports_tennis':
        return Icons.sports_tennis;
      case 'work':
        return Icons.work;
      case 'smartphone':
        return Icons.smartphone;
      case 'computer':
        return Icons.computer;
      case 'devices':
        return Icons.devices;
      case 'chair':
        return Icons.chair;
      case 'kitchen':
        return Icons.kitchen;
      case 'directions_car':
        return Icons.directions_car;
      case 'menu_book':
        return Icons.menu_book;
      case 'restaurant':
        return Icons.restaurant;
      case 'home_repair_service':
        return Icons.home_repair_service;
      case 'man':
        return Icons.man;
      case 'woman':
        return Icons.woman;
      case 'child_care':
        return Icons.child_care;
      default:
        return Icons.category;
    }
  }
}

// ويدجت مبسط للفئات الأفقية
class HorizontalCategoryList extends StatelessWidget {
  final List<ProductCategory> categories;
  final Function(ProductCategory) onCategoryTap;
  final String? selectedCategoryId;

  const HorizontalCategoryList({
    super.key,
    required this.categories,
    required this.onCategoryTap,
    this.selectedCategoryId,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = category.id == selectedCategoryId;
          
          return Padding(
            padding: EdgeInsets.only(
              right: index == 0 ? 16 : 8,
              left: index == categories.length - 1 ? 16 : 8,
            ),
            child: GestureDetector(
              onTap: () => onCategoryTap(category),
              child: Column(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Color(int.parse(category.color.replaceFirst('#', '0xFF')))
                          : Colors.grey[100],
                      shape: BoxShape.circle,
                      border: isSelected
                          ? null
                          : Border.all(color: Colors.grey[300]!, width: 1),
                    ),
                    child: Icon(
                      _getIconData(category.icon),
                      size: 24,
                      color: isSelected
                          ? Colors.white
                          : Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: 70,
                    child: Text(
                      category.nameAr,
                      style: TextStyle(
                        fontSize: 11,
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        color: isSelected
                            ? Color(int.parse(category.color.replaceFirst('#', '0xFF')))
                            : Colors.grey[700],
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  IconData _getIconData(String iconName) {
    // نفس الدالة السابقة
    switch (iconName.toLowerCase()) {
      case 'checkroom':
        return Icons.checkroom;
      case 'sports_tennis':
        return Icons.sports_tennis;
      case 'work':
        return Icons.work;
      case 'smartphone':
        return Icons.smartphone;
      case 'computer':
        return Icons.computer;
      case 'devices':
        return Icons.devices;
      case 'chair':
        return Icons.chair;
      case 'kitchen':
        return Icons.kitchen;
      case 'directions_car':
        return Icons.directions_car;
      case 'menu_book':
        return Icons.menu_book;
      case 'restaurant':
        return Icons.restaurant;
      case 'home_repair_service':
        return Icons.home_repair_service;
      case 'man':
        return Icons.man;
      case 'woman':
        return Icons.woman;
      case 'child_care':
        return Icons.child_care;
      default:
        return Icons.category;
    }
  }
}
