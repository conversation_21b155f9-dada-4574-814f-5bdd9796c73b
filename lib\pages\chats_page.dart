import 'package:flutter/material.dart';
import 'chat_page.dart';
import '../models/chat.dart';
import '../supabase_service.dart';
import '../widgets/interactive_verified_badge.dart';

class ChatsPage extends StatefulWidget {
  const ChatsPage({super.key});

  @override
  State<ChatsPage> createState() => _ChatsPageState();
}

class _ChatsPageState extends State<ChatsPage> {
  late final Stream<List<Chat>> _stream = SupabaseService().chatsStream();

  @override
  void initState() {
    super.initState();
    // Start presence heartbeat once when the chats page is first shown
    SupabaseService().initPresence();
    // Load pin & mute state once
    SupabaseService().loadPinnedMuted();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: null,
      floatingActionButton: FloatingActionButton(
        heroTag: 'startChat',
        onPressed: _showStartChat,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add_comment_outlined),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.startFloat,
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 40, 16, 8),
            child: Row(
              children: const [
                Text('الدردشات', style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold)),
              ],
            ),
          ),
          Expanded(
            child: ValueListenableBuilder<Set<String>>(
              valueListenable: SupabaseService().onlineUsers,
              builder: (_, onlineIds, __) {
                return StreamBuilder<List<Chat>>(
                  stream: _stream,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      return const Center(child: CircularProgressIndicator());
                    }
                    if (snapshot.hasError) {
                      return Center(child: Text('خطأ: ${snapshot.error}'));
                    }
                    final allChats = snapshot.data ?? [];

                    final pinnedSet = SupabaseService().pinnedChats.value;
                    final onlineChats = allChats.where((c) => onlineIds.contains(c.otherId) && !pinnedSet.contains(c.id)).toList();
                    final pinnedChats = allChats.where((c) => pinnedSet.contains(c.id)).toList();
                    final otherChats  = allChats.where((c) => !onlineIds.contains(c.otherId) && !pinnedSet.contains(c.id)).toList();

                    if (allChats.isEmpty) {
                      return const Center(child: Text('لا توجد محادثات'));
                    }

                    return ListView(
                      children: [
                        if (onlineChats.isNotEmpty) ...[
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            child: Text('الأصدقاء المتصلون الآن', style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600, color: Theme.of(context).primaryColor)),
                          ),
                          SizedBox(
                            height: 100,
                            child: ListView.separated(
                              scrollDirection: Axis.horizontal,
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              itemCount: onlineChats.length,
                              separatorBuilder: (_, __) => const SizedBox(width: 12),
                              itemBuilder: (context, index) {
                                final ch = onlineChats[index];
                                return Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    _buildAvatar(ch.otherAvatar, true, radius: 28),
                                    const SizedBox(height: 4),
                                    SizedBox(
                                      width: 70,
                                      child: Text(
                                        ch.otherName,
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        textAlign: TextAlign.center,
                                        style: const TextStyle(fontSize: 12),
                                      ),
                                    ),
                                  ],
                                );
                              },
                            ),
                          ),
                          const Divider(height: 24),
                        ],

                        if (pinnedChats.isNotEmpty) ...[
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                            child: Row(
                              children: const [Icon(Icons.push_pin, size: 18), SizedBox(width:4), Text('المثبتة', style: TextStyle(fontWeight: FontWeight.w600))],
                            ),
                          ),
                          ...pinnedChats.map((chat)=>_buildChatTile(chat, onlineIds.contains(chat.otherId), isPinned:true)).toList(),
                          const Divider(height: 24),
                        ],

                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                          child: Text('جميع الدردشات', style: Theme.of(context).textTheme.titleMedium),
                        ),
                        ...otherChats.map((c)=>_buildChatTile(c, onlineIds.contains(c.otherId))).toList(),
                      ],
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String _getTimeAgo(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  Widget _buildAvatar(String avatarUrl, bool online, {double radius = 24}) {
    return Stack(
      children: [
        CircleAvatar(
          radius: radius,
          backgroundColor: Colors.grey.shade300,
          backgroundImage: avatarUrl.isNotEmpty ? NetworkImage(avatarUrl) : null,
          child: avatarUrl.isEmpty ? const Icon(Icons.person) : null,
        ),
        Positioned(
          bottom: 0,
          right: 0,
          child: Container(
            width: 10,
            height: 10,
            decoration: BoxDecoration(
              color: online ? Colors.green : Colors.red,
              shape: BoxShape.circle,
              border: Border.all(color: Colors.white, width: 1),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildChatTile(Chat chat, bool isOnline, {bool isPinned = false}) {
      final bool hasUnread = chat.unreadCount > 0;
      return InkWell(
        onLongPress: () async {
          // نفس منطق الإجراءات السابق
          final action = await showModalBottomSheet<String>(
            context: context,
            builder: (_) => SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ListTile(
                    leading: const Icon(Icons.archive),
                    title: const Text('أرشفة'),
                    dense: true,
                    onTap: () => Navigator.pop(context, 'archive'),
                  ),
                  ListTile(
                    leading: const Icon(Icons.delete),
                    title: const Text('حذف من القائمة'),
                    dense: true,
                    onTap: () => Navigator.pop(context, 'delete'),
                  ),
                  ListTile(
                    leading: const Icon(Icons.block),
                    title: const Text('حظر المستخدم'),
                    dense: true,
                    onTap: () => Navigator.pop(context, 'block'),
                  ),
                ],
              ),
            ),
          );

          if (action == 'archive') {
            await SupabaseService().archiveChat(chat.id, true);
          } else if (action == 'delete') {
            await SupabaseService().deleteChatLocally(chat.id);
          } else if (action == 'block') {
            await SupabaseService().blockUser(chat.otherId);
          }
        },
        child: Container(
          color: hasUnread ? Colors.grey.shade100 : Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              ListTile(
                contentPadding: EdgeInsets.zero,
                leading: _buildAvatar(chat.otherAvatar, isOnline, radius: 28),
                title: Row(
                  children: [
                    Text(
                      chat.otherName,
                      style: TextStyle(
                        fontWeight: hasUnread ? FontWeight.bold : FontWeight.w500,
                        fontSize: 16,
                      ),
                    ),
                    // شارة التحقق
                    FutureBuilder<Map<String, dynamic>?>(
                      future: SupabaseService().fetchProfile(chat.otherId),
                      builder: (context, snapshot) {
                        if (snapshot.hasData && snapshot.data?['is_verified'] == true) {
                          return Row(
                            children: [
                              const SizedBox(width: 4),
                              InteractiveVerifiedBadge(
                                size: 14,
                                userName: chat.otherName,
                              ),
                            ],
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ],
                ),
                subtitle: Row(
                  children: [
                    if (chat.lastFromMe) ...[
                      Icon(
                        chat.lastRead ? Icons.done_all : Icons.done_all,
                        size: 16,
                        color: chat.lastRead ? Colors.blue : Colors.grey,
                      ),
                      const SizedBox(width: 4),
                    ],
                    Expanded(
                      child: Text(
                        chat.lastMessage,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(color: hasUnread ? Colors.black : Colors.grey.shade700),
                      ),
                    ),
                  ],
                ),
                trailing: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (chat.lastAt != null)
                      Text(
                        _getTimeAgo(chat.lastAt!),
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                    if (hasUnread)
                      Container(
                        margin: const EdgeInsets.only(top: 4),
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(color: Colors.blue, borderRadius: BorderRadius.circular(12)),
                        child: Text(
                          chat.unreadCount.toString(),
                          style: const TextStyle(color: Colors.white, fontSize: 12),
                        ),
                      ),
                  ],
                ),
                onTap: () async {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => ChatPage(
                        chatId: chat.id,
                        otherId: chat.otherId,
                        username: chat.otherName,
                        avatarUrl: chat.otherAvatar,
                      ),
                    ),
                  );
                },
              ),
              const Divider(height: 0),
            ],
          ),
        ),
      );
  }

  Future<void> _showStartChat() async {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      isScrollControlled: true,
      builder: (ctx) {
        return DraggableScrollableSheet(
          expand: false,
          initialChildSize: 0.8,
          minChildSize: 0.4,
          maxChildSize: 0.9,
          builder: (context, scrollController) {
            return FutureBuilder<List<Map<String, dynamic>>>(
              future: SupabaseService().fetchUsers(),
              builder: (context, snap) {
                if (snap.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snap.hasError) {
                  return Center(child: Text('خطأ: ${snap.error}'));
                }
                final users = snap.data ?? [];
                if (users.isEmpty) {
                  return const Center(child: Text('لا يوجد مستخدمون'));
                }
                return ListView.builder(
                  controller: scrollController,
                  itemCount: users.length,
                  itemBuilder: (context, index) {
                    final u = users[index];
                    return ListTile(
                      leading: CircleAvatar(
                        radius: 24,
                        backgroundColor: Colors.grey.shade300,
                        backgroundImage: (u['avatar_url'] as String?)?.isNotEmpty == true ? NetworkImage(u['avatar_url']) : null,
                        child: (u['avatar_url'] as String?)?.isNotEmpty == true ? null : const Icon(Icons.person),
                      ),
                      title: Text(u['name'] as String),
                      onTap: () async {
                        final chatId = await SupabaseService().getOrCreateChat(u['id'] as String);
                        if (!mounted) return;
                        Navigator.pop(context); // Close sheet
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (_) => ChatPage(
                              chatId: chatId,
                              otherId: u['id'] as String,
                              username: u['name'] as String,
                              avatarUrl: u['avatar_url'] as String? ?? '',
                            ),
                          ),
                        );
                      },
                    );
                  },
                );
              },
            );
          },
        );
      },
    );
  }
} 