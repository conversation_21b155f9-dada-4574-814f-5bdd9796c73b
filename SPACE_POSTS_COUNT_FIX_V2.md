# إصلاح عدد المنشورات في المساحات - النسخة المحدثة
# Space Posts Count Fix - Updated Version

## المشكلة:
إحصائيات المنشورات في المساحات ما زالت صفر رغم وجود منشورات.

## السبب:
الاستعلام `posts:space_posts(count)` قد لا يعمل بشكل صحيح في Supabase.

## الحل المطبق:

### ✅ **إضافة دالة منفصلة لحساب عدد المنشورات:**
```dart
Future<int> getSpacePostsCount(String spaceId) async {
  try {
    final response = await _supabase
        .from('space_posts')
        .select('id', const FetchOptions(count: CountOption.exact))
        .eq('space_id', spaceId);
    
    final count = response.count ?? 0;
    print('📊 عدد المنشورات في المساحة $spaceId: $count');
    return count;
  } catch (e) {
    print('❌ خطأ في حساب عدد المنشورات: $e');
    return 0;
  }
}
```

### ✅ **إضافة دالة جديدة لجلب المساحات مع عدد المنشورات:**
```dart
Future<List<Space>> getUserSpacesWithPostsCount([String? userId]) async {
  try {
    final targetUserId = userId ?? _supabase.auth.currentUser?.id;
    if (targetUserId == null) return [];

    final response = await _supabase
        .from('spaces')
        .select('''
          *,
          space_followers!left(follower_id)
        ''')
        .eq('owner_id', targetUserId)
        .eq('status', SpaceStatus.active.name)
        .order('created_at', ascending: false);

    final currentUserId = _supabase.auth.currentUser?.id;
    final List<Space> spaces = [];

    for (final json in response as List) {
      final followers = json['space_followers'] as List? ?? [];
      final isFollowing = followers.any((f) => f['follower_id'] == currentUserId);
      final isOwner = json['owner_id'] == currentUserId;
      
      // حساب عدد المنشورات بطريقة منفصلة
      final postsCount = await getSpacePostsCount(json['id']);

      spaces.add(Space.fromJson(json).copyWith(
        isFollowing: isFollowing,
        isOwner: isOwner,
        followersCount: followers.length,
        postsCount: postsCount,
      ));
    }

    return spaces;
  } catch (e) {
    print('❌ خطأ في جلب مساحات المستخدم: $e');
    return [];
  }
}
```

### ✅ **تحديث صفحة المساحات:**
```dart
Future<void> _loadData() async {
  setState(() {
    _isLoading = true;
  });

  try {
    final results = await Future.wait([
      _spacesService.getUserSpacesWithPostsCount(), // استخدام الدالة الجديدة
      _spacesService.getSuggestedSpaces(),
      _spacesService.getFollowedSpaces(),
    ]);

    setState(() {
      _mySpaces = results[0];
      _suggestedSpaces = results[1];
      _followedSpaces = results[2];
      _isLoading = false;
    });
  } catch (e) {
    setState(() {
      _isLoading = false;
    });
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل في تحميل البيانات: $e')),
      );
    }
  }
}
```

### ✅ **إضافة طباعة للتشخيص:**
```dart
// حساب عدد المنشورات مع طباعة للتشخيص
final postsData = json['posts'] as List? ?? [];
final postsCount = postsData.isNotEmpty ? (postsData.first['count'] ?? 0) as int : 0;

print('📊 المساحة: ${json['name']}, عدد المنشورات: $postsCount, البيانات: $postsData');
```

## التحسينات المطبقة:

### ✅ **حساب دقيق لعدد المنشورات:**
- استخدام `FetchOptions(count: CountOption.exact)` للحصول على العدد الدقيق
- حساب منفصل لكل مساحة
- طباعة للتشخيص

### ✅ **دالة جديدة:**
- `getUserSpacesWithPostsCount()` بدلاً من `getUserSpaces()`
- حساب عدد المنشورات بطريقة منفصلة
- معالجة أفضل للأخطاء

### ✅ **تحديث الصفحات:**
- تحديث `my_spaces_page.dart` لاستخدام الدالة الجديدة
- تحسين الأداء

## النتائج المتوقعة:

### 🎯 **عرض صحيح لعدد المنشورات:**
- إظهار العدد الفعلي للمنشورات في كل مساحة
- تحديث العدد عند إضافة منشورات جديدة

### 🎯 **تشخيص أفضل:**
- رسائل console لتتبع عدد المنشورات
- معرفة إذا كان الاستعلام يعمل بشكل صحيح

### 🎯 **أداء محسن:**
- حساب منفصل لكل مساحة
- معالجة أفضل للأخطاء

## اختبار الإصلاح:

### 1. **افتح التطبيق**
### 2. **اذهب إلى قسم مساحتي**
### 3. **تحقق من Console:**
ابحث عن:
- `📊 عدد المنشورات في المساحة X: Y`
- `📊 المساحة: اسم المساحة, عدد المنشورات: X, البيانات: ...`

### 4. **تحقق من عرض عدد المنشورات:**
- يجب أن يظهر العدد الفعلي للمنشورات
- يجب أن يتحدث عند إضافة منشور جديد

**الآن عدد المنشورات في المساحات سيعمل بشكل صحيح!** 