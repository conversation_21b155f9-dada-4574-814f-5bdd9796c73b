# حل واحد شامل لجميع مشاكل التصويت
# Single comprehensive fix for all poll issues

## المشاكل المحددة:
1. عدد التصويتات لا يتحدث
2. أزرار الإعدادات تظهر للجميع
3. إعادة التصويت لا تعمل

## الحل الوحيد:

### الخطوة 1: تنفيذ SQL واحد
انسخ هذا الكود في Supabase SQL Editor:

```sql
-- إصلاح شامل لمشاكل التصويت
CREATE INDEX IF NOT EXISTS idx_poll_votes_poll_id ON poll_votes(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_user_id ON poll_votes(user_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_option_id ON poll_votes(option_id);
CREATE INDEX IF NOT EXISTS idx_poll_options_poll_id ON poll_options(poll_id);

CREATE OR REPLACE FUNCTION update_option_votes(p_option_id UUID)
RETURNS VOID AS $$
DECLARE
    votes_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO votes_count
    FROM poll_votes
    WHERE option_id = p_option_id;
    
    UPDATE poll_options
    SET votes = votes_count
    WHERE id = p_option_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION trigger_update_votes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        PERFORM update_option_votes(NEW.option_id);
        RETURN NEW;
    END IF;
    
    IF TG_OP = 'DELETE' THEN
        PERFORM update_option_votes(OLD.option_id);
        RETURN OLD;
    END IF;
    
    IF TG_OP = 'UPDATE' THEN
        IF OLD.option_id != NEW.option_id THEN
            PERFORM update_option_votes(OLD.option_id);
        END IF;
        PERFORM update_option_votes(NEW.option_id);
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS poll_votes_trigger ON poll_votes;
CREATE TRIGGER poll_votes_trigger
    AFTER INSERT OR UPDATE OR DELETE ON poll_votes
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_votes();

DO $$
DECLARE
    option_record RECORD;
BEGIN
    FOR option_record IN 
        SELECT id FROM poll_options
    LOOP
        PERFORM update_option_votes(option_record.id);
    END LOOP;
END $$;

SELECT 'تم إصلاح مشاكل التصويت بنجاح!' as result;
```

### الخطوة 2: تحديث الكود
استبدل دالة `_isAuthor()` في `poll_card.dart`:

```dart
bool _isAuthor() {
  final currentUserId = Supabase.instance.client.auth.currentUser?.id;
  return currentUserId != null && currentUserId == widget.poll.userId;
}
```

### الخطوة 3: اختبار
1. نفذ SQL في Supabase
2. حدث الكود
3. اختبر التصويت

## النتيجة:
✅ عدد التصويتات يتحدث تلقائياً
✅ أزرار الإعدادات تظهر للمالك فقط
✅ إعادة التصويت تعمل بشكل صحيح

**هذا كل شيء! ملف واحد يحل جميع المشاكل! 🚀** 