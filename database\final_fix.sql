-- الإصلاح النهائي للمشاهدات والصور

-- 1. إصلاح سياسات العقارات للسماح بتحديث المشاهدات
DROP POLICY IF EXISTS "المستخدم يمكنه تحديث عقاراته" ON real_estate_properties;
DROP POLICY IF EXISTS "تحديث المشاهدات للجميع" ON real_estate_properties;

-- السماح للمالك بتحديث عقاره
CREATE POLICY "المستخدم يمكنه تحديث عقاراته" ON real_estate_properties
    FOR UPDATE USING (auth.uid() = user_id)
    WITH CHECK (auth.uid() = user_id);

-- السماح للجميع بتحديث المشاهدات فقط
CREATE POLICY "تحديث المشاهدات للجميع" ON real_estate_properties
    FOR UPDATE USING (is_active = true)
    WITH CHECK (is_active = true);

-- 2. إنشاء bucket الصور إذا لم يكن موجود
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'property-images', 
    'property-images', 
    true, 
    52428800, -- 50MB
    ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO UPDATE SET
    public = true,
    file_size_limit = 52428800,
    allowed_mime_types = ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif'];

-- 3. حذف سياسات التخزين القديمة
DROP POLICY IF EXISTS "Users can upload property images" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can view property images" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their property images" ON storage.objects;
DROP POLICY IF EXISTS "Users can update their property images" ON storage.objects;

-- 4. إنشاء سياسات تخزين جديدة
-- السماح للجميع بعرض الصور
CREATE POLICY "Anyone can view property images" ON storage.objects
    FOR SELECT USING (bucket_id = 'property-images');

-- السماح للمستخدمين المسجلين برفع الصور
CREATE POLICY "Authenticated users can upload property images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'property-images' 
        AND auth.role() = 'authenticated'
        AND (storage.foldername(name))[1] = auth.uid()::text
    );

-- السماح للمستخدمين بحذف صورهم
CREATE POLICY "Users can delete their property images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'property-images' 
        AND auth.role() = 'authenticated'
        AND (storage.foldername(name))[1] = auth.uid()::text
    );

-- 5. تحديث سياسات جدول property_images
DROP POLICY IF EXISTS "الجميع يمكنهم قراءة صور العقارات النشطة" ON property_images;
DROP POLICY IF EXISTS "المستخدمون يمكنهم إدارة صور عقاراتهم" ON property_images;
DROP POLICY IF EXISTS "المستخدمون يمكنهم إضافة صور العقارات" ON property_images;

-- السماح للجميع بقراءة صور العقارات النشطة
CREATE POLICY "الجميع يمكنهم قراءة صور العقارات النشطة" ON property_images
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM real_estate_properties 
            WHERE id = property_images.property_id AND is_active = true
        )
    );

-- السماح للمستخدمين المسجلين بإضافة صور
CREATE POLICY "المستخدمون يمكنهم إضافة صور العقارات" ON property_images
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

-- السماح لمالك العقار بإدارة صوره
CREATE POLICY "المالك يمكنه إدارة صور عقاره" ON property_images
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM real_estate_properties 
            WHERE id = property_images.property_id AND user_id = auth.uid()
        )
    );

-- 6. إنشاء دالة تحديث المشاهدات
DROP FUNCTION IF EXISTS increment_property_views(UUID);

CREATE OR REPLACE FUNCTION increment_property_views(property_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE real_estate_properties 
    SET views_count = COALESCE(views_count, 0) + 1,
        updated_at = NOW()
    WHERE id = property_id AND is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- منح الصلاحيات
GRANT EXECUTE ON FUNCTION increment_property_views(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION increment_property_views(UUID) TO anon;

-- 7. تحديث العقارات الموجودة لضمان وجود قيم صحيحة
UPDATE real_estate_properties 
SET views_count = COALESCE(views_count, 0)
WHERE views_count IS NULL;

-- 8. إضافة فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_real_estate_properties_active ON real_estate_properties(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_property_images_property_id ON property_images(property_id);
CREATE INDEX IF NOT EXISTS idx_property_images_main ON property_images(property_id, is_main) WHERE is_main = true;

-- رسالة نجاح
SELECT 'تم الإصلاح النهائي بنجاح! المشاهدات والصور ستعمل الآن.' as result;
