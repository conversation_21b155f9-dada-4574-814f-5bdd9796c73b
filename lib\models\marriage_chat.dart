// نماذج بيانات المحادثات للزواج الشرعي

class MarriageChat {
  final String id;
  final String contactRequestId;
  final String user1Id;
  final String user2Id;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime lastMessageAt;
  final bool isActive;
  final String? lastMessage;
  final String? otherUserName;
  final String? otherUserAvatar;

  MarriageChat({
    required this.id,
    required this.contactRequestId,
    required this.user1Id,
    required this.user2Id,
    required this.createdAt,
    required this.updatedAt,
    required this.lastMessageAt,
    this.isActive = true,
    this.lastMessage,
    this.otherUserName,
    this.otherUserAvatar,
  });

  factory MarriageChat.fromJson(Map<String, dynamic> json) {
    return MarriageChat(
      id: json['id'] ?? '',
      contactRequestId: json['contact_request_id'] ?? '',
      user1Id: json['user1_id'] ?? '',
      user2Id: json['user2_id'] ?? '',
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      lastMessageAt: DateTime.parse(json['last_message_at']),
      isActive: json['is_active'] ?? true,
      lastMessage: json['last_message'],
      otherUserName: json['other_user_name'],
      otherUserAvatar: json['other_user_avatar'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'contact_request_id': contactRequestId,
      'user1_id': user1Id,
      'user2_id': user2Id,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'last_message_at': lastMessageAt.toIso8601String(),
      'is_active': isActive,
    };
  }

  // الحصول على معرف المستخدم الآخر
  String getOtherUserId(String currentUserId) {
    return currentUserId == user1Id ? user2Id : user1Id;
  }

  // الحصول على اسم المستخدم الآخر
  String getOtherUserName(String currentUserId) {
    if (otherUserName != null) return otherUserName!;
    return currentUserId == user1Id ? 'المستخدم الثاني' : 'المستخدم الأول';
  }

  // تنسيق وقت آخر رسالة
  String getFormattedLastMessageTime() {
    final now = DateTime.now();
    final difference = now.difference(lastMessageAt);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}

class MarriageMessage {
  final String id;
  final String chatId;
  final String senderId;
  final String content;
  final MessageType messageType;
  final String? fileUrl;
  final DateTime createdAt;
  final bool isRead;
  final bool isDeleted;

  MarriageMessage({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.content,
    this.messageType = MessageType.text,
    this.fileUrl,
    required this.createdAt,
    this.isRead = false,
    this.isDeleted = false,
  });

  factory MarriageMessage.fromJson(Map<String, dynamic> json) {
    return MarriageMessage(
      id: json['id'] ?? '',
      chatId: json['chat_id'] ?? '',
      senderId: json['sender_id'] ?? '',
      content: json['content'] ?? '',
      messageType: MessageType.values.firstWhere(
        (e) => e.name == json['message_type'],
        orElse: () => MessageType.text,
      ),
      fileUrl: json['file_url'],
      createdAt: DateTime.parse(json['created_at']),
      isRead: json['is_read'] ?? false,
      isDeleted: json['is_deleted'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chat_id': chatId,
      'sender_id': senderId,
      'content': content,
      'message_type': messageType.name,
      'file_url': fileUrl,
      'created_at': createdAt.toIso8601String(),
      'is_read': isRead,
      'is_deleted': isDeleted,
    };
  }

  // تحديد ما إذا كانت الرسالة من المستخدم الحالي
  bool isFromCurrentUser(String currentUserId) {
    return senderId == currentUserId;
  }

  // تنسيق وقت الرسالة
  String getFormattedTime() {
    final hour = createdAt.hour;
    final minute = createdAt.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'م' : 'ص';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    
    return '$displayHour:$minute $period';
  }

  // تنسيق تاريخ الرسالة
  String getFormattedDate() {
    final now = DateTime.now();
    final messageDate = DateTime(createdAt.year, createdAt.month, createdAt.day);
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    
    if (messageDate == today) {
      return 'اليوم';
    } else if (messageDate == yesterday) {
      return 'أمس';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }

  // نسخ الرسالة مع تحديث حالة القراءة
  MarriageMessage copyWith({
    bool? isRead,
    bool? isDeleted,
  }) {
    return MarriageMessage(
      id: id,
      chatId: chatId,
      senderId: senderId,
      content: content,
      messageType: messageType,
      fileUrl: fileUrl,
      createdAt: createdAt,
      isRead: isRead ?? this.isRead,
      isDeleted: isDeleted ?? this.isDeleted,
    );
  }
}

enum MessageType {
  text,
  image,
  file,
}

// نموذج لإحصائيات المحادثة
class ChatStats {
  final int totalMessages;
  final int unreadMessages;
  final DateTime? lastActivity;

  ChatStats({
    required this.totalMessages,
    required this.unreadMessages,
    this.lastActivity,
  });

  factory ChatStats.fromJson(Map<String, dynamic> json) {
    return ChatStats(
      totalMessages: json['total_messages'] ?? 0,
      unreadMessages: json['unread_messages'] ?? 0,
      lastActivity: json['last_activity'] != null 
          ? DateTime.parse(json['last_activity'])
          : null,
    );
  }
}
