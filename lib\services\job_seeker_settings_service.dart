import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class JobSeekerSettingsService {
  final SupabaseClient _client = Supabase.instance.client;

  // الحصول على إعدادات المستخدم
  Future<Map<String, dynamic>> getUserSettings() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final response = await _client
          .from('job_seeker_user_settings')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();

      if (response == null) {
        // إنشاء إعدادات افتراضية
        return await _createDefaultSettings();
      }

      return response;
    } catch (e) {
      throw Exception('فشل في جلب الإعدادات: $e');
    }
  }

  // إنشاء إعدادات افتراضية
  Future<Map<String, dynamic>> _createDefaultSettings() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final defaultSettings = {
        'user_id': userId,
        'profile_notifications_enabled': true,
        'show_phone_number': true,
        'show_email': true,
        'allow_direct_contact': true,
        'show_online_status': true,
        'profile_visible_in_search': true,
      };

      await _client.from('job_seeker_user_settings').insert(defaultSettings);
      
      return defaultSettings;
    } catch (e) {
      throw Exception('فشل في إنشاء الإعدادات الافتراضية: $e');
    }
  }

  // تحديث إعدادات المستخدم
  Future<void> updateUserSettings(Map<String, dynamic> settings) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      settings['updated_at'] = DateTime.now().toIso8601String();

      await _client
          .from('job_seeker_user_settings')
          .upsert({
            'user_id': userId,
            ...settings,
          });
    } catch (e) {
      throw Exception('فشل في تحديث الإعدادات: $e');
    }
  }

  // حذف جميع بيانات المستخدم
  Future<void> deleteAllUserData() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // استخدام الدالة المخصصة في قاعدة البيانات
      await _client.rpc('delete_user_job_data');
    } catch (e) {
      throw Exception('فشل في حذف البيانات: $e');
    }
  }

  // مسح البيانات المحفوظة فقط
  Future<void> clearSavedData() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // استخدام الدالة المخصصة في قاعدة البيانات
      await _client.rpc('clear_saved_data');
    } catch (e) {
      throw Exception('فشل في مسح البيانات المحفوظة: $e');
    }
  }

  // تصدير بيانات المستخدم
  Future<Map<String, dynamic>> exportUserData() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // الحصول على الملف المهني
      final profileResponse = await _client
          .from('job_seekers')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();

      // الحصول على الملفات المحفوظة
      final savedResponse = await _client
          .from('job_seeker_saves')
          .select('seeker_id, created_at')
          .eq('user_id', userId);

      // الحصول على الإعجابات
      final likesResponse = await _client
          .from('job_seeker_likes')
          .select('seeker_id, created_at')
          .eq('user_id', userId);

      // الحصول على الإعدادات
      final settingsResponse = await getUserSettings();

      return {
        'export_date': DateTime.now().toIso8601String(),
        'user_id': userId,
        'profile': profileResponse,
        'saved_profiles': savedResponse,
        'likes': likesResponse,
        'settings': settingsResponse,
      };
    } catch (e) {
      throw Exception('فشل في تصدير البيانات: $e');
    }
  }

  // الحصول على إحصائيات المستخدم
  Future<Map<String, dynamic>> getUserStats() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // الحصول على الملف المهني
      final profileResponse = await _client
          .from('job_seekers')
          .select('views_count, likes_count, created_at, updated_at, is_active')
          .eq('user_id', userId)
          .maybeSingle();

      if (profileResponse == null) {
        return {
          'has_profile': false,
          'profile_views': 0,
          'profile_likes': 0,
          'saved_profiles_count': 0,
          'given_likes_count': 0,
        };
      }

      // عدد الملفات المحفوظة
      final savedCount = await _client
          .from('job_seeker_saves')
          .select('id')
          .eq('user_id', userId);

      // عدد الإعجابات التي قام بها
      final likesGivenCount = await _client
          .from('job_seeker_likes')
          .select('id')
          .eq('user_id', userId);

      return {
        'has_profile': true,
        'profile_views': profileResponse['views_count'] ?? 0,
        'profile_likes': profileResponse['likes_count'] ?? 0,
        'saved_profiles_count': (savedCount as List).length,
        'given_likes_count': (likesGivenCount as List).length,
        'profile_created': profileResponse['created_at'],
        'profile_updated': profileResponse['updated_at'],
        'profile_active': profileResponse['is_active'] ?? false,
      };
    } catch (e) {
      throw Exception('فشل في جلب إحصائيات المستخدم: $e');
    }
  }

  // تبديل حالة الملف المهني (نشط/مخفي)
  Future<void> toggleProfileVisibility() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // الحصول على الحالة الحالية
      final currentProfile = await _client
          .from('job_seekers')
          .select('is_active')
          .eq('user_id', userId)
          .single();

      final currentStatus = currentProfile['is_active'] as bool;

      // تغيير الحالة
      await _client
          .from('job_seekers')
          .update({'is_active': !currentStatus})
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في تغيير حالة الملف: $e');
    }
  }

  // التحقق من وجود ملف مهني
  Future<bool> hasJobSeekerProfile() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return false;

      final response = await _client
          .from('job_seekers')
          .select('id')
          .eq('user_id', userId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      return false;
    }
  }

  // حذف الملف المهني فقط (بدون الإعجابات والحفظ)
  Future<void> deleteProfileOnly() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('job_seekers')
          .delete()
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في حذف الملف المهني: $e');
    }
  }

  // إخفاء الملف المهني مؤقتاً
  Future<void> hideProfile() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('job_seekers')
          .update({'is_active': false})
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في إخفاء الملف: $e');
    }
  }

  // إظهار الملف المهني
  Future<void> showProfile() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('job_seekers')
          .update({'is_active': true})
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في إظهار الملف: $e');
    }
  }

  // الحصول على حالة الملف المهني
  Future<bool> isProfileActive() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return false;

      final response = await _client
          .from('job_seekers')
          .select('is_active')
          .eq('user_id', userId)
          .maybeSingle();

      if (response == null) return false;
      return response['is_active'] as bool;
    } catch (e) {
      return false;
    }
  }
}
