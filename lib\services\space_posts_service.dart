import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/space_post.dart';
import '../models/reaction_type.dart';

class SpacePostsService {
  static final SpacePostsService _instance = SpacePostsService._internal();
  factory SpacePostsService() => _instance;
  SpacePostsService._internal();

  final _supabase = Supabase.instance.client;

  // إنشاء منشور جديد في المساحة
  Future<SpacePost> createSpacePost({
    required String spaceId,
    required String content,
    List<String> mediaUrls = const [],
    String? linkUrl,
    String? linkTitle,
    String? linkDescription,
    String? linkImage,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      // التحقق من أن المستخدم مالك المساحة
      final space = await _supabase
          .from('spaces')
          .select('owner_id, name')
          .eq('id', spaceId)
          .single();

      if (space['owner_id'] != userId) {
        throw Exception('ليس لديك صلاحية للنشر في هذه المساحة');
      }

      // جلب اسم المستخدم
      final userProfile = await _supabase
          .from('profiles')
          .select('name, username')
          .eq('id', userId)
          .single();

      final postData = {
        'space_id': spaceId,
        'author_id': userId,
        'author_name': userProfile['name'] ?? userProfile['username'] ?? 'مستخدم',
        'content': content,
        'media_urls': mediaUrls,
        'link_url': linkUrl,
        'link_title': linkTitle,
        'link_description': linkDescription,
        'link_image': linkImage,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      final response = await _supabase
          .from('space_posts')
          .insert(postData)
          .select()
          .single();

      print('✅ تم إنشاء منشور جديد في المساحة: $spaceId');
      print('📝 محتوى المنشور: ${content.substring(0, content.length > 50 ? 50 : content.length)}...');
      print('🆔 معرف المنشور: ${response['id']}');

      // تحديث عداد المنشورات في المساحة
      try {
        await _supabase.rpc('increment_space_posts', params: {
          'space_id': spaceId,
        });
        print('📊 تم تحديث عداد المنشورات في المساحة: $spaceId');
      } catch (e) {
        print('⚠️ تحذير: فشل في تحديث عداد المنشورات: $e');
      }

      return SpacePost.fromJson(response).copyWith(
        spaceName: space['name'],
        isAuthor: true,
        canEdit: true,
        canDelete: true,
      );
    } catch (e) {
      throw Exception('فشل في إنشاء المنشور: $e');
    }
  }

  // جلب منشورات المساحة
  Future<List<SpacePost>> getSpacePosts({
    required String spaceId,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final currentUserId = _supabase.auth.currentUser?.id;

      final response = await _supabase
          .from('space_posts')
          .select('''
            *,
            spaces!inner(name, owner_id),
            space_post_reactions!left(reaction_type)
          ''')
          .eq('space_id', spaceId)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List).map((json) {
        // حساب التفاعلات
        final reactions = json['space_post_reactions'] as List? ?? [];
        final reactionCounts = <ReactionType, int>{};
        ReactionType? currentUserReaction;

        for (final reaction in reactions) {
          final reactionType = ReactionType.values.firstWhere(
            (type) => type.name == reaction['reaction_type'],
            orElse: () => ReactionType.like,
          );
          
          reactionCounts[reactionType] = (reactionCounts[reactionType] ?? 0) + 1;
          
          if (reaction['user_id'] == currentUserId) {
            currentUserReaction = reactionType;
          }
        }

        final space = json['spaces'];
        final isAuthor = json['author_id'] == currentUserId;
        final isSpaceOwner = space['owner_id'] == currentUserId;

        return SpacePost.fromJson(json).copyWith(
          spaceName: space['name'],
          currentUserReaction: currentUserReaction,
          reactionCounts: reactionCounts,
          likesCount: reactionCounts[ReactionType.like] ?? 0,
          isAuthor: isAuthor,
          canEdit: isAuthor || isSpaceOwner,
          canDelete: isAuthor || isSpaceOwner,
        );
      }).toList();
    } catch (e) {
      return [];
    }
  }

  // تحديث منشور
  Future<SpacePost> updateSpacePost({
    required String postId,
    String? content,
    List<String>? mediaUrls,
    String? linkUrl,
    String? linkTitle,
    String? linkDescription,
    String? linkImage,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (content != null) updateData['content'] = content;
      if (mediaUrls != null) updateData['media_urls'] = mediaUrls;
      if (linkUrl != null) updateData['link_url'] = linkUrl;
      if (linkTitle != null) updateData['link_title'] = linkTitle;
      if (linkDescription != null) updateData['link_description'] = linkDescription;
      if (linkImage != null) updateData['link_image'] = linkImage;

      final response = await _supabase
          .from('space_posts')
          .update(updateData)
          .eq('id', postId)
          .select('''
            *,
            spaces!inner(name, owner_id)
          ''')
          .single();

      final space = response['spaces'];
      final isAuthor = response['author_id'] == userId;
      final isSpaceOwner = space['owner_id'] == userId;

      return SpacePost.fromJson(response).copyWith(
        spaceName: space['name'],
        isAuthor: isAuthor,
        canEdit: isAuthor || isSpaceOwner,
        canDelete: isAuthor || isSpaceOwner,
      );
    } catch (e) {
      throw Exception('فشل في تحديث المنشور: $e');
    }
  }

  // حذف منشور
  Future<bool> deleteSpacePost(String postId) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      // جلب معلومات المنشور للتحقق من الصلاحيات
      final post = await _supabase
          .from('space_posts')
          .select('space_id, author_id, spaces!inner(owner_id)')
          .eq('id', postId)
          .single();

      final isAuthor = post['author_id'] == userId;
      final isSpaceOwner = post['spaces']['owner_id'] == userId;

      if (!isAuthor && !isSpaceOwner) {
        throw Exception('ليس لديك صلاحية لحذف هذا المنشور');
      }

      await _supabase
          .from('space_posts')
          .delete()
          .eq('id', postId);

      // تحديث عداد المنشورات في المساحة
      try {
        await _supabase.rpc('decrement_space_posts', params: {
          'space_id': post['space_id'],
        });
      } catch (e) {
        // تجاهل خطأ الدالة إذا لم تكن موجودة
      }

      return true;
    } catch (e) {
      return false;
    }
  }

  // التفاعل مع منشور
  Future<bool> toggleSpacePostReaction({
    required String postId,
    required ReactionType reactionType,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        throw Exception('يجب تسجيل الدخول أولاً');
      }

      // التحقق من وجود تفاعل سابق
      final existingReaction = await _supabase
          .from('space_post_reactions')
          .select()
          .eq('post_id', postId)
          .eq('user_id', userId)
          .maybeSingle();

      if (existingReaction != null) {
        if (existingReaction['reaction_type'] == reactionType.name) {
          // إزالة التفاعل
          await _supabase
              .from('space_post_reactions')
              .delete()
              .eq('post_id', postId)
              .eq('user_id', userId);
          return false;
        } else {
          // تحديث نوع التفاعل
          await _supabase
              .from('space_post_reactions')
              .update({
                'reaction_type': reactionType.name,
                'created_at': DateTime.now().toIso8601String(),
              })
              .eq('post_id', postId)
              .eq('user_id', userId);
          return true;
        }
      } else {
        // إضافة تفاعل جديد
        await _supabase.from('space_post_reactions').insert({
          'post_id': postId,
          'user_id': userId,
          'reaction_type': reactionType.name,
          'created_at': DateTime.now().toIso8601String(),
        });
        return true;
      }
    } catch (e) {
      throw Exception('فشل في التفاعل: $e');
    }
  }

  // جلب منشورات المساحات المتابعة (للصفحة الرئيسية)
  Future<List<SpacePost>> getFollowedSpacesPosts({
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) return [];

      // جلب IDs المساحات المتابعة أولاً
      final followedSpaces = await _supabase
          .from('space_followers')
          .select('space_id')
          .eq('follower_id', userId);

      final spaceIds = (followedSpaces as List)
          .map((item) => item['space_id'] as String)
          .toList();

      if (spaceIds.isEmpty) return [];

      final response = await _supabase
          .from('space_posts')
          .select('''
            *,
            spaces!inner(name, owner_id),
            space_post_reactions!left(reaction_type, user_id)
          ''')
          .inFilter('space_id', spaceIds)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List).map((json) {
        // حساب التفاعلات
        final reactions = json['space_post_reactions'] as List? ?? [];
        final reactionCounts = <ReactionType, int>{};
        ReactionType? currentUserReaction;

        for (final reaction in reactions) {
          final reactionType = ReactionType.values.firstWhere(
            (type) => type.name == reaction['reaction_type'],
            orElse: () => ReactionType.like,
          );
          
          reactionCounts[reactionType] = (reactionCounts[reactionType] ?? 0) + 1;
          
          if (reaction['user_id'] == userId) {
            currentUserReaction = reactionType;
          }
        }

        final space = json['spaces'];
        final isAuthor = json['author_id'] == userId;
        final isSpaceOwner = space['owner_id'] == userId;

        return SpacePost.fromJson(json).copyWith(
          spaceName: space['name'],
          currentUserReaction: currentUserReaction,
          reactionCounts: reactionCounts,
          likesCount: reactionCounts[ReactionType.like] ?? 0,
          isAuthor: isAuthor,
          canEdit: isAuthor || isSpaceOwner,
          canDelete: isAuthor || isSpaceOwner,
        );
      }).toList();
    } catch (e) {
      return [];
    }
  }
}
