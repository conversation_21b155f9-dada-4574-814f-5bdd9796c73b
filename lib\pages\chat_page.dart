import 'dart:typed_data';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/services.dart';
import 'package:chewie/chewie.dart';
import 'package:video_player/video_player.dart';
import 'package:readmore/readmore.dart';
import 'dart:io';
import 'package:record/record.dart' show AudioRecorder, RecordConfig, AudioEncoder;
import 'package:path_provider/path_provider.dart';
import 'package:audioplayers/audioplayers.dart';


import '../supabase_service.dart';
import '../models/message.dart';
import '../widgets/interactive_verified_badge.dart';
import 'call_page.dart';

class ChatPage extends StatefulWidget {
  final String chatId;
  final String otherId;
  final String username;
  final String avatarUrl;

  const ChatPage({
    super.key,
    required this.chatId,
    required this.otherId,
    required this.username,
    required this.avatarUrl,
  });

  @override
  State<ChatPage> createState() => _ChatPageState();
}

class _ChatPageState extends State<ChatPage> {
  final TextEditingController _controller = TextEditingController();
  final ImagePicker _picker = ImagePicker();
  final ScrollController _scrollController = ScrollController();
  Message? _editingMsg;
  Message? _replyToMsg;

  RealtimeChannel? _typingChannel;
  bool _otherTyping = false;
  Timer? _typingDebounce;

  // -------- Voice recording state -------- //
  final AudioRecorder _recorder = AudioRecorder();
  bool _isRecording = false;
  int _recordSeconds = 0;
  Timer? _recordTimer;

  bool get _isEditing => _editingMsg != null;

  String get _myId => Supabase.instance.client.auth.currentUser!.id;

  Future<void> _sendText() async {
    final text = _controller.text.trim();
    if (text.isEmpty) return;

    try {
      if (_isEditing) {
        await SupabaseService().editMessage(messageId: _editingMsg!.id, newContent: text);
        setState(() => _editingMsg = null);
      } else if (_replyToMsg != null) {
        await SupabaseService().sendReply(chatId: widget.chatId, replyTo: _replyToMsg!.id, content: text);
        setState(() => _replyToMsg = null);
      } else {
        await SupabaseService().sendMessage(
          chatId: widget.chatId,
          content: text,
          type: MediaType.text,
        );
      }

      _controller.clear();
      // تحديث فوري للرسائل
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) setState(() {});
      });
      _scrollToBottom();
    } catch (e) {
      debugPrint('SEND TEXT ERROR → $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('تعذّر إرسال الرسالة: $e')));
      }
    }
  }

  Future<void> _sendImage() async {
    final XFile? img = await _picker.pickImage(source: ImageSource.gallery);
    if (img == null) return;
    try {
      final Uint8List bytes = await img.readAsBytes();
      await SupabaseService().sendMessage(
        chatId: widget.chatId,
        content: '',
        type: MediaType.image,
        bytes: bytes,
      );
      // تحديث فوري للرسائل
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) setState(() {});
      });
      _scrollToBottom();
    } catch (e) {
      debugPrint('SEND IMAGE ERROR → $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('تعذّر إرسال الصورة: $e')));
      }
    }
  }

  // ---------- Voice clips helpers ---------- //
  Future<void> _startRecording() async {
    if (_isRecording) return;
    final hasPerm = await _recorder.hasPermission();
    if (!hasPerm) {
      return;
    }
    final dir = await getTemporaryDirectory();
    final path = '${dir.path}/${DateTime.now().millisecondsSinceEpoch}.aac';
    await _recorder.start(const RecordConfig(encoder: AudioEncoder.aacLc), path: path);
    setState(() {
      _isRecording = true;
      _recordSeconds = 0;
    });
    _recordTimer?.cancel();
    _recordTimer = Timer.periodic(const Duration(seconds: 1), (_) {
      setState(() => _recordSeconds++);
    });
  }

  Future<void> _stopRecording() async {
    if (!_isRecording) return;
    final path = await _recorder.stop();
    _recordTimer?.cancel();
    setState(() => _isRecording = false);

    if (path == null) return;
    try {
      final bytes = await File(path).readAsBytes();
      await SupabaseService().sendMessage(
        chatId: widget.chatId,
        content: '',
        type: MediaType.audio,
        bytes: bytes,
      );
      // تحديث فوري للرسائل
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) setState(() {});
      });
      _scrollToBottom();
    } catch (e) {
      debugPrint('SEND AUDIO ERROR → $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل إرسال المقطع الصوتي: $e')),
        );
      }
    } finally {
      File(path).delete().catchError((_) {});
    }
  }

  // إلغاء التسجيل
  Future<void> _cancelRecording() async {
    if (!_isRecording) return;
    await _recorder.stop();
    _recordTimer?.cancel();
    setState(() {
      _isRecording = false;
      _recordSeconds = 0;
    });
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent + 100,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }

  @override
  void initState() {
    super.initState();
    _setupTypingChannel();
  }

  void _setupTypingChannel() {
    final supa = Supabase.instance.client;
    final channel = supa.channel('chat_typing:${widget.chatId}', opts: const RealtimeChannelConfig(self: true));

    channel.onBroadcast(event: 'typing', callback: (payload) {
      if (payload is Map && payload['uid'] != _myId) {
        setState(() => _otherTyping = true);
        // reset after 3s
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted) setState(() => _otherTyping = false);
        });
      }
    });

    channel.subscribe();
    _typingChannel = channel;
  }

  void _sendTyping() {
    if (_typingChannel == null) return;
    _typingChannel!.sendBroadcastMessage(event: 'typing', payload: {'uid': _myId});
  }

  Widget _buildBubble(Message msg) {
    final bool isMe = msg.senderId == _myId;
    final Color primary = Theme.of(context).colorScheme.primary;
    final Color bubbleColor = isMe ? primary : Colors.grey.shade200;
    final TextStyle textStyle = TextStyle(color: isMe ? Colors.white : Colors.black87);

    Widget content;
    if (msg.type == MediaType.text) {
      content = ReadMoreText(
        msg.content,
        trimLines: 1,
        trimMode: TrimMode.Line,
        trimCollapsedText: ' عرض المزيد',
        trimExpandedText: ' عرض أقل',
        moreStyle: const TextStyle(color: Colors.blue),
        lessStyle: const TextStyle(color: Colors.blue),
        style: textStyle,
      );
    } else if (msg.content == 'تم حذف هذه الرسالة') {
      content = Text(
        msg.content,
        style: textStyle.copyWith(fontStyle: FontStyle.italic, color: Colors.grey.shade400),
      );
    } else if (msg.type == MediaType.image && msg.mediaUrl != null) {
      content = ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.network(msg.mediaUrl!, fit: BoxFit.cover),
      );
    } else if (msg.type == MediaType.video && msg.mediaUrl != null) {
      final vidController = VideoPlayerController.network(msg.mediaUrl!);
      content = SizedBox(
        width: 200,
        child: AspectRatio(
          aspectRatio: 16 / 9,
          child: Chewie(
            controller: ChewieController(
              videoPlayerController: vidController,
              autoPlay: false,
              looping: false,
              showControls: true,
            ),
          ),
        ),
      );
    } else if (msg.type == MediaType.audio && msg.mediaUrl != null) {
      content = _AudioPlayerBubble(url: msg.mediaUrl!, isMe: isMe);
    } else {
      content = Text('رسالة غير مدعومة', style: textStyle);
    }

    Widget bubble = GestureDetector(
      onLongPress: () => _onMessageLongPress(msg, isMe),
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Row(
          mainAxisAlignment: isMe ? MainAxisAlignment.end : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (!isMe) ...[
              CircleAvatar(
                radius: 16,
                backgroundColor: Colors.grey.shade300,
                backgroundImage: widget.avatarUrl.isNotEmpty ? NetworkImage(widget.avatarUrl) : null,
                child: widget.avatarUrl.isEmpty ? const Icon(Icons.person, size: 16) : null,
              ),
              const SizedBox(width: 8),
            ],
            Flexible(
              child: Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: bubbleColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: content,
              ),
            ),
          ],
        ),
      ),
    );

    if (isMe) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Flexible(child: bubble),
          const SizedBox(width: 4),
          Icon(
            Icons.done_all,
            size: 16,
            color: msg.readAt != null ? Colors.blue : Colors.grey,
          ),
        ],
      );
    }
    return bubble;
  }

  void _onMessageLongPress(Message msg, bool isMe) async {
    final action = await showModalBottomSheet<String>(
      context: context,
      builder: (_) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isMe) ...[
              ListTile(
                leading: const Icon(Icons.edit),
                title: const Text('تعديل'),
                onTap: () => Navigator.pop(context, 'edit'),
              ),
              if (DateTime.now().difference(msg.createdAt) < const Duration(minutes: 10))
                ListTile(
                  leading: const Icon(Icons.delete_forever),
                  title: const Text('حذف للجميع'),
                  onTap: () => Navigator.pop(context, 'delete'),
                ),
            ],
            ListTile(
              leading: const Icon(Icons.reply),
              title: const Text('رد'),
              onTap: () => Navigator.pop(context, 'reply'),
            ),
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('نسخ'),
              onTap: () => Navigator.pop(context, 'copy'),
            ),
          ],
        ),
      ),
    );

    if (action == null) return;
    switch (action) {
      case 'edit':
        setState(() {
          _editingMsg = msg;
          _controller.text = msg.content;
          _controller.selection = TextSelection.collapsed(offset: _controller.text.length);
        });
        break;
      case 'reply':
        setState(() => _replyToMsg = msg);
        break;
      case 'delete':
        await SupabaseService().deleteMessageForAll(msg.id);
        break;
      case 'copy':
        await Clipboard.setData(ClipboardData(text: msg.content));
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final stream = SupabaseService().messagesStream(widget.chatId);
    final Color primary = Theme.of(context).colorScheme.primary;

    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.black87,
        title: Row(
          children: [
            // صورة الملف الشخصي الحقيقية
            CircleAvatar(
              radius: 18,
              backgroundColor: Colors.grey[300],
              backgroundImage: widget.avatarUrl.isNotEmpty
                  ? NetworkImage(widget.avatarUrl)
                  : null,
              child: widget.avatarUrl.isEmpty
                  ? Text(
                      widget.username.isNotEmpty ? widget.username[0].toUpperCase() : '?',
                      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    )
                  : null,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        widget.username,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      // شارة التحقق
                      FutureBuilder<Map<String, dynamic>?>(
                        future: SupabaseService().fetchProfile(widget.otherId),
                        builder: (context, snapshot) {
                          if (snapshot.hasData && snapshot.data?['is_verified'] == true) {
                            return Row(
                              children: [
                                const SizedBox(width: 4),
                                InteractiveVerifiedBadge(
                                  size: 16,
                                  userName: widget.username,
                                ),
                              ],
                            );
                          }
                          return const SizedBox.shrink();
                        },
                      ),
                      const SizedBox(width: 8),
                      ValueListenableBuilder<Set<String>>(
                        valueListenable: SupabaseService().onlineUsers,
                        builder: (_, online, __) {
                          final onlineNow = online.contains(widget.otherId);
                          return Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: onlineNow ? Colors.green : Colors.grey,
                              shape: BoxShape.circle,
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                if (_otherTyping)
                  const Text('يكتب الآن…', style: TextStyle(fontSize: 12, color: Colors.grey)),
                ],
              ),
            ),
          ],
        ),
        actions: [
          PopupMenuButton<String>(
            onSelected: _onChatMenuSelected,
            itemBuilder: (_) => [
              const PopupMenuItem(value: 'block',    child: Text('حظر المستخدم')),
              const PopupMenuItem(value: 'unblock',  child: Text('إلغاء الحظر')),
              const PopupMenuItem(value: 'report',   child: Text('الإبلاغ عن محادثة')),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.video_call),
            onPressed: () async {
              final callId = await SupabaseService().createCall(chatId: widget.chatId, type: 'video');
              if (!mounted) return;
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => CallPage(callId: callId, otherName: widget.username, type: 'video'),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.call),
            onPressed: () async {
              final callId = await SupabaseService().createCall(chatId: widget.chatId, type: 'voice');
              if (!mounted) return;
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => CallPage(callId: callId, otherName: widget.username, type: 'voice'),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: StreamBuilder<List<Message>>(
              stream: stream,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return Center(child: Text('خطأ: ${snapshot.error}'));
                }
                final List<Message> msgs = snapshot.data ?? [];
                // علّم الرسائل كمقروءة عند وصولها
                if (msgs.isNotEmpty) {
                  SupabaseService().markMessagesRead(widget.chatId);
                }
                return ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  itemCount: msgs.length,
                  itemBuilder: (context, index) => _buildBubble(msgs[index]),
                );
              },
            ),
          ),
          if (_replyToMsg != null)
            Container(
              color: Colors.grey.shade100,
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              child: Row(
                children: [
                  Icon(Icons.reply, color: primary, size: 18),
                  const SizedBox(width: 6),
                  Expanded(
                    child: Text(
                      _replyToMsg!.content,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(color: Colors.black87),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close, size: 18),
                    onPressed: () => setState(() => _replyToMsg = null),
                  ),
                ],
              ),
            ),
          SafeArea(
            top: false,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(color: Colors.grey.withOpacity(0.2), blurRadius: 3),
                ],
              ),
              child: _isRecording ? _buildRecordingInterface() : _buildChatInterface(),
            ),
          ),
        ],
      ),
    );
  }

  // واجهة الدردشة العادية
  Widget _buildChatInterface() {
    return Row(
      children: [
        IconButton(
          icon: const Icon(Icons.image),
          color: Colors.red[600],
          onPressed: _sendImage,
        ),
        Expanded(
          child: TextField(
            controller: _controller,
            decoration: const InputDecoration(
              hintText: 'اكتب رسالة...',
              border: InputBorder.none,
            ),
            textDirection: TextDirection.rtl,
            onChanged: (_) {
              _sendTyping();
            },
          ),
        ),
        GestureDetector(
          onLongPressStart: (_) => _startRecording(),
          onLongPressEnd: (_) => _stopRecording(),
          onLongPressCancel: () => _cancelRecording(),
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.red[600],
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.mic,
              color: Colors.white,
              size: 20,
            ),
          ),
        ),
        const SizedBox(width: 4),
        IconButton(
          icon: const Icon(Icons.send),
          color: Colors.red[600],
          onPressed: _sendText,
        ),
      ],
    );
  }

  // واجهة التسجيل مثل واتساب
  Widget _buildRecordingInterface() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // أيقونة الميكروفون النشطة
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red[600],
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.red.withValues(alpha: 0.3),
                  spreadRadius: 2,
                  blurRadius: 8,
                ),
              ],
            ),
            child: const Icon(
              Icons.mic,
              color: Colors.white,
              size: 24,
            ),
          ),

          const SizedBox(width: 16),

          // شريط التقدم والوقت
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // نقاط متحركة تدل على التسجيل
                    ...List.generate(3, (index) =>
                      Container(
                        margin: const EdgeInsets.only(right: 4),
                        child: AnimatedContainer(
                          duration: Duration(milliseconds: 300 + (index * 100)),
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: Colors.red[600],
                            shape: BoxShape.circle,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'جاري التسجيل...',
                      style: TextStyle(
                        color: Colors.red[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                // شريط التقدم
                LinearProgressIndicator(
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.red[600]!),
                ),
              ],
            ),
          ),

          const SizedBox(width: 16),

          // عداد الوقت
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.red[50],
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              '${_recordSeconds ~/ 60}:${(_recordSeconds % 60).toString().padLeft(2, '0')}',
              style: TextStyle(
                color: Colors.red[600],
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),

          const SizedBox(width: 12),

          // زر الإلغاء
          GestureDetector(
            onTap: _cancelRecording,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.close,
                color: Colors.grey,
                size: 20,
              ),
            ),
          ),

          const SizedBox(width: 8),

          // زر الإرسال
          GestureDetector(
            onTap: _stopRecording,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.green[600],
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.send,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _onChatMenuSelected(String value) async {
    switch (value) {
      case 'block':
        await SupabaseService().blockUser(widget.otherId);
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم حظر المستخدم')));
        break;
      case 'unblock':
        await SupabaseService().unblockUser(widget.otherId);
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم إلغاء الحظر')));
        break;
      case 'report':
        final reason = await _askReason();
        if (reason != null) {
          await SupabaseService().reportChat(widget.chatId, reason);
          if (!mounted) return;
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم إرسال البلاغ')));
        }
        break;
    }
  }

  Future<String?> _askReason() async {
    final controller = TextEditingController();
    return showDialog<String>(
      context: context,
      builder: (_) => AlertDialog(
        title: const Text('سبب الإبلاغ'),
        content: TextField(
          controller: controller,
          maxLines: 3,
          decoration: const InputDecoration(hintText: 'اكتب السبب هنا'),
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context), child: const Text('إلغاء')),
          ElevatedButton(onPressed: () => Navigator.pop(context, controller.text.trim()), child: const Text('إرسال')),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _typingChannel?.unsubscribe();
    _recordTimer?.cancel();
    // AudioRecorder لا يحتاج صراحةً إلى التخلص من الموارد
    _controller.dispose();
    _scrollController.dispose();
    super.dispose();
  }
}

// ---------- Audio bubble widget ---------- //

class _AudioPlayerBubble extends StatefulWidget {
  final String url;
  final bool isMe;
  const _AudioPlayerBubble({required this.url, required this.isMe});

  @override
  State<_AudioPlayerBubble> createState() => _AudioPlayerBubbleState();
}

class _AudioPlayerBubbleState extends State<_AudioPlayerBubble> {
  final AudioPlayer _player = AudioPlayer();
  bool _playing = false;
  Duration _position = Duration.zero;
  Duration _duration = Duration.zero;

  @override
  void initState() {
    super.initState();
    _player.onDurationChanged.listen((d) => setState(() => _duration = d));
    _player.onPositionChanged.listen((p) => setState(() => _position = p));
    _player.onPlayerComplete.listen((_) => setState(() {
          _playing = false;
          _position = _duration;
        }));
  }

  @override
  void dispose() {
    _player.dispose();
    super.dispose();
  }

  String _fmt(Duration d) {
    final m = d.inMinutes;
    final s = d.inSeconds % 60;
    return '$m:${s.toString().padLeft(2, '0')}';
  }

  Future<void> _toggle() async {
    if (_playing) {
      await _player.pause();
      setState(() => _playing = false);
    } else {
      await _player.play(UrlSource(widget.url));
      setState(() => _playing = true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      constraints: const BoxConstraints(maxWidth: 280),
      decoration: BoxDecoration(
        color: widget.isMe ? Colors.red[600] : Colors.grey[200],
        borderRadius: BorderRadius.circular(18),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // زر التشغيل/الإيقاف
          GestureDetector(
            onTap: _toggle,
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                color: widget.isMe ? Colors.white : Colors.red[600],
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    spreadRadius: 1,
                    blurRadius: 2,
                  ),
                ],
              ),
              child: Icon(
                _playing ? Icons.pause : Icons.play_arrow,
                color: widget.isMe ? Colors.red[600] : Colors.white,
                size: 18,
              ),
            ),
          ),

          const SizedBox(width: 12),

          // شريط التقدم والوقت
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // شريط التقدم مع تصميم واتساب
                Container(
                  height: 3,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(1.5),
                    color: widget.isMe ? Colors.white.withValues(alpha: 0.3) : Colors.grey[300],
                  ),
                  child: LinearProgressIndicator(
                    value: _duration.inMilliseconds > 0 ? _position.inMilliseconds / _duration.inMilliseconds : 0,
                    backgroundColor: Colors.transparent,
                    valueColor: AlwaysStoppedAnimation(
                      widget.isMe ? Colors.white : Colors.red[600],
                    ),
                    minHeight: 3,
                  ),
                ),

                const SizedBox(height: 6),

                // الوقت
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // أيقونة الميكروفون
                    Icon(
                      Icons.mic,
                      size: 14,
                      color: widget.isMe ? Colors.white.withValues(alpha: 0.8) : Colors.grey[600],
                    ),

                    // الوقت
                    Text(
                      _duration.inMilliseconds > 0
                          ? '${_duration.inMinutes}:${(_duration.inSeconds % 60).toString().padLeft(2, '0')}'
                          : _fmt(_position),
                      style: TextStyle(
                        fontSize: 12,
                        color: widget.isMe ? Colors.white.withValues(alpha: 0.9) : Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}