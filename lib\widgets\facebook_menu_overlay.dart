import 'package:flutter/material.dart';
import '../pages/jobs_page.dart';
import '../pages/real_estate_page.dart';
import '../pages/products_page.dart';
import '../pages/marriage_page.dart';
import '../pages/charity_page.dart';
import '../pages/job_seekers_page.dart';
import '../pages/notes_page.dart';
import '../pages/marketplace_page.dart';

class FacebookMenuOverlay {
  static OverlayEntry? _overlayEntry;
  static bool _isShowing = false;

  static void show(BuildContext context, {VoidCallback? onRefresh}) {
    if (_isShowing) return;

    _isShowing = true;

    // الحصول على موضع زر الزائد
    final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
    final size = renderBox?.size ?? Size.zero;
    final offset = renderBox?.localToGlobal(Offset.zero) ?? Offset.zero;

    _overlayEntry = OverlayEntry(
      builder: (context) => FacebookDropdownMenu(
        buttonOffset: offset,
        buttonSize: size,
        onClose: hide,
        onRefresh: onRefresh,
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  static void hide() {
    if (!_isShowing) return;

    _overlayEntry?.remove();
    _overlayEntry = null;
    _isShowing = false;
  }

  static bool get isShowing => _isShowing;
}

class FacebookDropdownMenu extends StatefulWidget {
  final Offset buttonOffset;
  final Size buttonSize;
  final VoidCallback onClose;
  final VoidCallback? onRefresh;

  const FacebookDropdownMenu({
    super.key,
    required this.buttonOffset,
    required this.buttonSize,
    required this.onClose,
    this.onRefresh,
  });

  @override
  State<FacebookDropdownMenu> createState() => _FacebookDropdownMenuState();
}

class _FacebookDropdownMenuState extends State<FacebookDropdownMenu>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -1.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutQuart,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _close() async {
    await _animationController.reverse();
    widget.onClose();
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final menuWidth = screenWidth * 0.85;

    // حساب موضع القائمة بناءً على موضع الزر
    final left = screenWidth - menuWidth - 16; // من الجهة اليمنى
    final top = widget.buttonOffset.dy + widget.buttonSize.height + 4; // تحت الزر مباشرة

    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        onTap: _close,
        child: Container(
          color: Colors.black.withValues(alpha: 0.1),
          child: Stack(
            children: [
              // القائمة المنزلقة
              Positioned(
                left: left,
                top: top,
                child: GestureDetector(
                  onTap: () {}, // منع إغلاق القائمة عند النقر عليها
                  child: AnimatedBuilder(
                    animation: _animationController,
                    builder: (context, child) {
                      return FadeTransition(
                        opacity: _fadeAnimation,
                        child: SlideTransition(
                          position: _slideAnimation,
                          child: ScaleTransition(
                            scale: _scaleAnimation,
                            alignment: Alignment.topCenter,
                            child: Container(
                              width: menuWidth,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(8),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.08),
                                    blurRadius: 12,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 2),
                                  ),
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.04),
                                    blurRadius: 24,
                                    spreadRadius: 0,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  // Header
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                                    decoration: BoxDecoration(
                                      border: Border(
                                        bottom: BorderSide(color: Colors.grey[200]!, width: 1),
                                      ),
                                    ),
                                    child: Row(
                                      children: [
                                        Text(
                                          'إنشاء',
                                          style: TextStyle(
                                            fontSize: 18,
                                            fontWeight: FontWeight.w600,
                                            color: Colors.grey[800],
                                          ),
                                        ),
                                        const Spacer(),
                                        GestureDetector(
                                          onTap: _close,
                                          child: Container(
                                            padding: const EdgeInsets.all(6),
                                            decoration: BoxDecoration(
                                              color: Colors.grey[100],
                                              shape: BoxShape.circle,
                                            ),
                                            child: Icon(
                                              Icons.close,
                                              size: 18,
                                              color: Colors.grey[600],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  // Menu Items
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 8),
                                    child: Column(
                                      children: [
                                        _buildMenuItem(
                                          icon: Icons.work_rounded,
                                          title: 'الوظائف',
                                          subtitle: 'ابحث عن وظيفة أو اعرض وظيفة',
                                          color: Colors.blue,
                                          onTap: () => _openJobs(context),
                                        ),
                                        _buildMenuItem(
                                          icon: Icons.home_rounded,
                                          title: 'العقارات',
                                          subtitle: 'ابحث عن عقار أو اعرض عقار',
                                          color: Colors.green,
                                          onTap: () => _openRealEstate(context),
                                        ),
                                        _buildMenuItem(
                                          icon: Icons.shopping_bag_rounded,
                                          title: 'السوق',
                                          subtitle: 'تسوق واعرض جميع أنواع المنتجات والخدمات',
                                          color: Colors.orange,
                                          onTap: () => _openMarketplace(context),
                                        ),
                                        _buildMenuItem(
                                          icon: Icons.favorite_rounded,
                                          title: 'بيت الحلال',
                                          subtitle: 'ابحث عن شريك الحياة',
                                          color: Colors.pink,
                                          onTap: () => _openMarriage(context),
                                        ),
                                        _buildMenuItem(
                                          icon: Icons.volunteer_activism_rounded,
                                          title: 'الصدقات',
                                          subtitle: 'تبرع أو اطلب المساعدة',
                                          color: Colors.teal,
                                          onTap: () => _openCharity(context),
                                        ),
                                        _buildMenuItem(
                                          icon: Icons.person_search_rounded,
                                          title: 'البحث عن عمل',
                                          subtitle: 'أنشئ ملفك المهني واعرض مهاراتك',
                                          color: Colors.indigo,
                                          onTap: () => _openJobSeekers(context),
                                        ),
                                        _buildMenuItem(
                                          icon: Icons.note_rounded,
                                          title: 'المذكرة',
                                          subtitle: 'اكتب مذكراتك ونظم مهامك اليومية',
                                          color: Colors.deepPurple,
                                          onTap: () => _openNotes(context),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        margin: const EdgeInsets.symmetric(vertical: 2),
        child: Row(
          children: [
            // Icon Container
            Container(
              width: 44,
              height: 44,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.12),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Icon(
                icon,
                color: color,
                size: 22,
              ),
            ),

            const SizedBox(width: 12),

            // Text Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 1),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _openJobs(BuildContext context) {
    _close();
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const JobsPage()),
    );
  }

  void _openRealEstate(BuildContext context) {
    _close();
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const RealEstatePage()),
    );
  }

  void _openMarketplace(BuildContext context) {
    _close();
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const MarketplacePage()),
    );
  }

  void _openMarriage(BuildContext context) {
    _close();
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const MarriagePage()),
    );
  }

  void _openCharity(BuildContext context) {
    _close();
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CharityPage()),
    );
  }

  void _openJobSeekers(BuildContext context) {
    _close();
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const JobSeekersPage()),
    );
  }

  void _openNotes(BuildContext context) {
    _close();
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const NotesPage()),
    );
  }
}
