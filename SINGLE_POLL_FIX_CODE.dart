// إصلاح شامل لمشاكل التصويت - ملف كود واحد
// Complete poll voting fix - single code file

// انسخ هذا الكود واستبدل الملفات الموجودة

import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:share_plus/share_plus.dart';

// ===== PollCard Widget =====
class PollCard extends StatefulWidget {
  final Poll poll;
  final Function(String pollId, String optionId) onVote;
  final VoidCallback? onRefresh;

  const PollCard({
    super.key,
    required this.poll,
    required this.onVote,
    this.onRefresh,
  });

  @override
  State<PollCard> createState() => _PollCardState();
}

class _PollCardState extends State<PollCard> {
  bool _loading = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAuthorInfo(),
            const SizedBox(height: 12),
            _buildQuestion(),
            const SizedBox(height: 16),
            _buildOptions(context),
            const SizedBox(height: 16),
            _buildPollInfo(),
            const SizedBox(height: 12),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildAuthorInfo() {
    return Row(
      children: [
        CircleAvatar(
          radius: 20,
          backgroundColor: Colors.purple[100],
          backgroundImage: widget.poll.authorAvatar != null
              ? NetworkImage(widget.poll.authorAvatar!)
              : null,
          child: widget.poll.authorAvatar == null
              ? Icon(Icons.person, color: Colors.purple[600])
              : null,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    widget.poll.authorName ?? 'مستخدم',
                    style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                  ),
                  if (widget.poll.isVerified) ...[
                    const SizedBox(width: 4),
                    Icon(Icons.verified, color: Colors.blue, size: 16),
                  ],
                ],
              ),
              const SizedBox(height: 2),
              Text(
                _formatTime(widget.poll.createdAt),
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
        // فئة التصويت
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: widget.poll.category.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: widget.poll.category.color.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(widget.poll.category.icon, size: 12, color: widget.poll.category.color),
              const SizedBox(width: 4),
              Text(
                widget.poll.category.arabicName,
                style: TextStyle(
                  fontSize: 10,
                  color: widget.poll.category.color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        // زر الإعدادات (للمؤلف فقط)
        if (_isAuthor()) ...[
          const SizedBox(width: 8),
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert, color: Colors.grey[600]),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings, size: 20),
                    SizedBox(width: 8),
                    Text('إعدادات التصويت'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 20, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف التصويت', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildQuestion() {
    return Text(
      widget.poll.question,
      style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600, height: 1.4),
    );
  }

  Widget _buildOptions(BuildContext context) {
    final hasVoted = widget.poll.userVote != null;
    final canVote = widget.poll.canVote && !hasVoted;
    
    return Column(
      children: widget.poll.options.map((option) {
        final isSelected = widget.poll.userVote == option.id;
        final percentage = option.percentage;
        
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          child: InkWell(
            onTap: canVote ? () => widget.onVote(widget.poll.id, option.id) : null,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isSelected ? Colors.purple.withValues(alpha: 0.1) : Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? Colors.purple : Colors.grey[300]!,
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          option.text,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                            color: isSelected ? Colors.purple[700] : Colors.black87,
                          ),
                        ),
                        const SizedBox(height: 4),
                        LinearProgressIndicator(
                          value: percentage / 100,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            isSelected ? Colors.purple : Colors.grey[600]!,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          '${option.votes} صوت (${percentage.toStringAsFixed(1)}%)',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isSelected)
                    Icon(Icons.check_circle, color: Colors.purple, size: 20),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPollInfo() {
    return Row(
      children: [
        Icon(Icons.how_to_vote, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          '${widget.poll.totalVotes} صوت',
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
        const Spacer(),
        if (widget.poll.userVote != null)
          TextButton(
            onPressed: _loading ? null : _revote,
            child: Text(
              'إعادة التصويت',
              style: TextStyle(color: Colors.purple[600]),
            ),
          ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        IconButton(
          onPressed: () => _sharePoll(),
          icon: Icon(Icons.share, color: Colors.grey[600]),
        ),
        IconButton(
          onPressed: () => _openComments(context),
          icon: Icon(Icons.comment, color: Colors.grey[600]),
        ),
      ],
    );
  }

  // ===== Helper Methods =====

  bool _isAuthor() {
    final currentUserId = Supabase.instance.client.auth.currentUser?.id;
    return currentUserId != null && currentUserId == widget.poll.userId;
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'settings':
        _openSettings();
        break;
      case 'delete':
        _confirmDelete();
        break;
    }
  }

  void _openSettings() {
    // تنفيذ فتح الإعدادات
  }

  void _confirmDelete() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف التصويت'),
        content: const Text('هل أنت متأكد من حذف هذا التصويت؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _deletePoll();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red[600]),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  void _deletePoll() {
    // تنفيذ حذف التصويت
  }

  void _revote() async {
    setState(() => _loading = true);
    try {
      // تنفيذ إعادة التصويت
      if (widget.onRefresh != null) {
        widget.onRefresh!();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في إعادة التصويت: $e')),
      );
    } finally {
      setState(() => _loading = false);
    }
  }

  void _sharePoll() {
    Share.share('تصويت: ${widget.poll.question}');
  }

  void _openComments(BuildContext context) {
    // تنفيذ فتح التعليقات
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}

// ===== PollService =====
class PollService {
  final SupabaseClient _client = Supabase.instance.client;

  Future<void> vote(String pollId, String optionId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // التحقق من وجود تصويت سابق
      final existingVote = await _client
          .from('poll_votes')
          .select('id, option_id')
          .eq('poll_id', pollId)
          .eq('user_id', userId)
          .maybeSingle();

      if (existingVote != null) {
        // التحقق من إمكانية إعادة التصويت
        final poll = await _client
            .from('polls')
            .select('allow_revote')
            .eq('id', pollId)
            .single();

        if (!poll['allow_revote']) {
          throw Exception('لا يمكن تغيير التصويت');
        }

        // تحديث التصويت
        await _client
            .from('poll_votes')
            .update({'option_id': optionId})
            .eq('id', existingVote['id']);
      } else {
        // إضافة تصويت جديد
        await _client
            .from('poll_votes')
            .insert({
              'poll_id': pollId,
              'option_id': optionId,
              'user_id': userId,
            });
      }
    } catch (e) {
      throw Exception('فشل في التصويت: $e');
    }
  }

  Future<void> revote(String pollId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // حذف التصويت الحالي
      await _client
          .from('poll_votes')
          .delete()
          .eq('poll_id', pollId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في إعادة التصويت: $e');
    }
  }
} 