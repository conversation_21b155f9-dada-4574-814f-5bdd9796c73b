-- إضافة أعمدة تعطيل الحساب إلى جدول profiles
-- قم بتنفيذ هذه الأوامر في Supabase SQL Editor

-- إضافة عمود status إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'status') THEN
        ALTER TABLE profiles ADD COLUMN status TEXT DEFAULT 'active';
    END IF;
END $$;

-- إضافة عمود deactivated_at إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'deactivated_at') THEN
        ALTER TABLE profiles ADD COLUMN deactivated_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
    END IF;
END $$;

-- إضافة عمود deactivated_until إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'deactivated_until') THEN
        ALTER TABLE profiles ADD COLUMN deactivated_until TIMESTAMP WITH TIME ZONE DEFAULT NULL;
    END IF;
END $$;

-- إضافة عمود deactivation_duration_days إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'deactivation_duration_days') THEN
        ALTER TABLE profiles ADD COLUMN deactivation_duration_days INTEGER DEFAULT NULL;
    END IF;
END $$;

-- إنشاء index لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_profiles_status ON profiles(status);
CREATE INDEX IF NOT EXISTS idx_profiles_deactivated_until ON profiles(deactivated_until);

-- التحقق من الأعمدة المضافة
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND column_name IN ('status', 'deactivated_at', 'deactivated_until', 'deactivation_duration_days'); 