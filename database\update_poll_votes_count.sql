-- تحديث عدد الأصوات في جميع خيارات التصويت

-- دالة لتحديث عدد الأصوات لجميع الخيارات
CREATE OR REPLACE FUNCTION update_all_poll_votes_count()
RETURNS void AS $$
BEGIN
    -- تحديث عدد الأصوات لكل خيار بناءً على الأصوات الفعلية
    UPDATE poll_options 
    SET votes = (
        SELECT COUNT(*) 
        FROM poll_votes 
        WHERE poll_votes.option_id = poll_options.id
    );
    
    RAISE NOTICE 'تم تحديث عدد الأصوات لجميع خيارات التصويت';
END;
$$ LANGUAGE plpgsql;

-- تنفيذ التحديث
SELECT update_all_poll_votes_count();

-- عرض إحصائيات التصويتات
SELECT 
    p.question,
    p.category,
    COUNT(pv.id) as total_votes,
    p.created_at
FROM polls p
LEFT JOIN poll_votes pv ON p.id = pv.poll_id
WHERE p.is_active = true
GROUP BY p.id, p.question, p.category, p.created_at
ORDER BY total_votes DESC, p.created_at DESC;

-- عرض أكثر التصويتات تفاعلاً
SELECT 
    p.question,
    p.category,
    COUNT(pv.id) as votes_count,
    COUNT(pc.id) as comments_count,
    (COUNT(pv.id) + COUNT(pc.id)) as total_engagement
FROM polls p
LEFT JOIN poll_votes pv ON p.id = pv.poll_id
LEFT JOIN poll_comments pc ON p.id = pc.poll_id
WHERE p.is_active = true
GROUP BY p.id, p.question, p.category
HAVING COUNT(pv.id) > 0
ORDER BY total_engagement DESC, votes_count DESC
LIMIT 10;

-- عرض توزيع التصويتات حسب الفئات
SELECT 
    category,
    COUNT(*) as polls_count,
    SUM((SELECT COUNT(*) FROM poll_votes WHERE poll_id = polls.id)) as total_votes
FROM polls 
WHERE is_active = true
GROUP BY category
ORDER BY total_votes DESC;

-- عرض التصويتات الأحدث مع النتائج
SELECT 
    p.question,
    p.category,
    po.text as option_text,
    po.votes,
    ROUND(
        CASE 
            WHEN (SELECT SUM(votes) FROM poll_options WHERE poll_id = p.id) > 0 
            THEN (po.votes::float / (SELECT SUM(votes) FROM poll_options WHERE poll_id = p.id)) * 100
            ELSE 0 
        END, 1
    ) as percentage,
    p.created_at
FROM polls p
JOIN poll_options po ON p.id = po.poll_id
WHERE p.is_active = true
ORDER BY p.created_at DESC, po.option_order ASC
LIMIT 50;

-- رسالة نجاح
SELECT 'تم تحديث جميع إحصائيات التصويتات بنجاح!' as message;
