import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_stats.dart';
import '../widgets/verification_progress_widget.dart';
import '../supabase_service.dart';

class VerifyAccountScreen extends StatefulWidget {
  const VerifyAccountScreen({super.key});

  @override
  State<VerifyAccountScreen> createState() => _VerifyAccountScreenState();
}

class _VerifyAccountScreenState extends State<VerifyAccountScreen> {
  UserStats? _stats;
  bool _loading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadUserStats();
  }

  Future<void> _loadUserStats() async {
    try {
      setState(() => _loading = true);
      
      final service = SupabaseService();
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // جلب بيانات الملف الشخصي
      final profile = await service.fetchProfile(userId);
      final followersCount = profile?['followers_count'] ?? 0;

      // جلب مجموع المشاهدات من المنشورات
      final posts = await service.fetchPostsByUser(userId);
      final totalViews = posts.fold<int>(0, (sum, post) => sum + post.viewsCount);

      // جلب حالة التوثيق
      final isVerified = profile?['is_verified'] ?? false;
      final verificationRequestedAt = profile?['verification_requested_at'];
      final verificationStatus = profile?['verification_status'];

      setState(() {
        _stats = UserStats(
          followersCount: followersCount,
          totalViews: totalViews,
          isVerified: isVerified,
          verificationRequestedAt: verificationRequestedAt != null 
              ? DateTime.parse(verificationRequestedAt) 
              : null,
          verificationStatus: verificationStatus,
        );
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'فشل في تحميل البيانات: $e';
        _loading = false;
      });
    }
  }

  Future<void> _requestVerification() async {
    try {
      final service = SupabaseService();
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // تحديث حالة طلب التوثيق
      await Supabase.instance.client
          .from('profiles')
          .update({
            'verification_requested_at': DateTime.now().toIso8601String(),
            'verification_status': 'pending',
          })
          .eq('id', userId);

      // تحديث البيانات المحلية
      setState(() {
        _stats = UserStats(
          followersCount: _stats?.followersCount ?? 0,
          totalViews: _stats?.totalViews ?? 0,
          isVerified: _stats?.isVerified ?? false,
          verificationRequestedAt: DateTime.now(),
          verificationStatus: 'pending',
        );
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال طلب التوثيق بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في إرسال الطلب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('توثيق الحساب'),
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
      ),
      body: _loading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorWidget()
              : _buildContent(),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: Colors.red[400]),
          const SizedBox(height: 16),
          Text(
            _error!,
            style: const TextStyle(fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadUserStats,
            child: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_stats == null) return const SizedBox.shrink();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // حالة التوثيق الحالية
          if (_stats!.isVerified)
            _buildVerifiedStatus()
          else if (_stats!.verificationStatus == 'pending')
            _buildPendingStatus()
          else
            _buildUnverifiedStatus(),

          const SizedBox(height: 24),

          // widget التقدم
          VerificationProgressWidget(
            stats: _stats!,
            onRequestVerification: _stats!.isVerified || _stats!.verificationStatus == 'pending'
                ? null
                : _requestVerification,
          ),

          const SizedBox(height: 24),

          // معلومات إضافية
          _buildInfoSection(),
        ],
      ),
    );
  }

  Widget _buildVerifiedStatus() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.green[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.verified, color: Colors.green[600], size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حسابك موثق!',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.green[700],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'يمكنك الآن استخدام علامة التوثيق الزرقاء',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.green[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPendingStatus() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.orange[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.orange[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.pending, color: Colors.orange[600], size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'طلب التوثيق قيد المراجعة',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[700],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'سيتم مراجعة طلبك خلال 3-5 أيام عمل',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.orange[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnverifiedStatus() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.info_outline, color: Colors.blue[600], size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حسابك غير موثق',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[700],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'استوفِ الشروط أدناه لتقديم طلب التوثيق',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.blue[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.help_outline, color: Colors.grey[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'معلومات مهمة',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          _buildInfoItem(
            '• يجب أن يكون حسابك نشطاً لمدة 30 يوم على الأقل',
            Icons.schedule,
          ),
          _buildInfoItem(
            '• يجب أن تكون منشوراتك أصلية وغير منسوخة',
            Icons.content_copy,
          ),
          _buildInfoItem(
            '• يجب أن تلتزم بقوانين التطبيق وسياسات المجتمع',
            Icons.gavel,
          ),
          _buildInfoItem(
            '• قد تستغرق مراجعة الطلب من 3 إلى 5 أيام عمل',
            Icons.timer,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String text, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[700],
              ),
            ),
          ),
        ],
      ),
    );
  }
} 