-- إصلاح بسيط لمشكلة full_name

-- إضا<PERSON>ة العمود المطلوب إذا لم يكن موجوداً
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS full_name TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS avatar_url TEXT;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT false;

-- تحديث المستخدمين الموجودين بأسماء افتراضية
UPDATE profiles 
SET full_name = COALESCE(full_name, SPLIT_PART(email, '@', 1), 'مستخدم')
WHERE full_name IS NULL OR full_name = '';

UPDATE profiles 
SET is_verified = COALESCE(is_verified, false)
WHERE is_verified IS NULL;

-- عرض النتائج
SELECT id, email, full_name, is_verified FROM profiles LIMIT 10;

-- رسالة نجاح
SELECT 'تم إصلاح جدول profiles بنجاح!' as message;
