import 'package:flutter/material.dart';
import '../models/charity_item.dart';
import '../services/charity_service.dart';

class EditCharityItemPage extends StatefulWidget {
  final CharityItem item;

  const EditCharityItemPage({super.key, required this.item});

  @override
  State<EditCharityItemPage> createState() => _EditCharityItemPageState();
}

class _EditCharityItemPageState extends State<EditCharityItemPage> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _phoneController;
  late TextEditingController _cityController;
  
  late CharityCategory _selectedCategory;
  late CharityCondition _selectedCondition;
  late DeliveryMethod _selectedDeliveryMethod;
  
  late bool _isUrgent;
  late bool _isAnonymous;
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.item.title);
    _descriptionController = TextEditingController(text: widget.item.description);
    _phoneController = TextEditingController(text: widget.item.phoneNumber ?? '');
    _cityController = TextEditingController(text: widget.item.city);
    
    _selectedCategory = widget.item.category;
    _selectedCondition = widget.item.condition;
    _selectedDeliveryMethod = widget.item.deliveryMethod;
    _isUrgent = widget.item.isUrgent;
    _isAnonymous = widget.item.isAnonymous;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _phoneController.dispose();
    _cityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'تعديل العنصر',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.teal[600],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // معلومات النوع
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: _getTypeColor().withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        widget.item.type == CharityType.donation 
                            ? Icons.volunteer_activism 
                            : Icons.help_outline,
                        color: _getTypeColor(),
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.item.type.arabicName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'تم الإنشاء: ${widget.item.formattedTime}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // العنوان
            _buildTextField(
              controller: _titleController,
              label: 'العنوان',
              icon: Icons.title,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال العنوان';
                }
                if (value.trim().length < 5) {
                  return 'العنوان قصير جداً';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // الوصف
            _buildTextField(
              controller: _descriptionController,
              label: 'الوصف التفصيلي',
              icon: Icons.description,
              maxLines: 4,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال الوصف';
                }
                if (value.trim().length < 10) {
                  return 'الوصف قصير جداً';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // الفئة والحالة
            Row(
              children: [
                Expanded(child: _buildCategoryDropdown()),
                const SizedBox(width: 12),
                Expanded(child: _buildConditionDropdown()),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // المدينة وطريقة التسليم
            Row(
              children: [
                Expanded(child: _buildCityField()),
                const SizedBox(width: 12),
                Expanded(child: _buildDeliveryMethodDropdown()),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // رقم الهاتف
            _buildTextField(
              controller: _phoneController,
              label: 'رقم الهاتف (اختياري)',
              icon: Icons.phone,
              keyboardType: TextInputType.phone,
            ),
            
            const SizedBox(height: 16),
            
            // الخيارات الإضافية
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.settings, color: Colors.teal[600]),
                        const SizedBox(width: 8),
                        const Text(
                          'خيارات إضافية',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    
                    // حالة طارئة
                    if (widget.item.type != CharityType.donation)
                      CheckboxListTile(
                        title: const Text('حالة طارئة'),
                        subtitle: const Text('سيتم عرض طلبك في قسم الحالات الطارئة'),
                        value: _isUrgent,
                        onChanged: (value) {
                          setState(() => _isUrgent = value!);
                        },
                        activeColor: Colors.red,
                      ),
                    
                    // إخفاء الهوية
                    CheckboxListTile(
                      title: const Text('إخفاء هويتي'),
                      subtitle: const Text('سيتم عرض اسمك كـ "متبرع كريم"'),
                      value: _isAnonymous,
                      onChanged: (value) {
                        setState(() => _isAnonymous = value!);
                      },
                      activeColor: Colors.teal[600],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // أزرار الحفظ والإلغاء
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: BorderSide(color: Colors.grey[400]!),
                    ),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _loading ? null : _saveChanges,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: _getTypeColor(),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: _loading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Text(
                            'حفظ التغييرات',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: Colors.teal[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.teal[600]!),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
    );
  }

  Widget _buildCategoryDropdown() {
    return DropdownButtonFormField<CharityCategory>(
      value: _selectedCategory,
      decoration: InputDecoration(
        labelText: 'الفئة',
        prefixIcon: Icon(_selectedCategory.icon, color: Colors.teal[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      items: CharityCategory.values.map((category) {
        return DropdownMenuItem(
          value: category,
          child: Row(
            children: [
              Icon(category.icon, size: 20, color: category.color),
              const SizedBox(width: 8),
              Text(category.arabicName),
            ],
          ),
        );
      }).toList(),
      onChanged: (value) {
        setState(() => _selectedCategory = value!);
      },
    );
  }

  Widget _buildConditionDropdown() {
    return DropdownButtonFormField<CharityCondition>(
      value: _selectedCondition,
      decoration: InputDecoration(
        labelText: 'الحالة',
        prefixIcon: Icon(Icons.info_outline, color: Colors.teal[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      items: CharityCondition.values.map((condition) {
        return DropdownMenuItem(
          value: condition,
          child: Text(condition.arabicName),
        );
      }).toList(),
      onChanged: (value) {
        setState(() => _selectedCondition = value!);
      },
    );
  }

  Widget _buildCityField() {
    return TextFormField(
      controller: _cityController,
      decoration: InputDecoration(
        labelText: 'المدينة',
        prefixIcon: Icon(Icons.location_city, color: Colors.teal[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال المدينة';
        }
        return null;
      },
    );
  }

  Widget _buildDeliveryMethodDropdown() {
    return DropdownButtonFormField<DeliveryMethod>(
      value: _selectedDeliveryMethod,
      decoration: InputDecoration(
        labelText: 'طريقة التسليم',
        prefixIcon: Icon(Icons.local_shipping, color: Colors.teal[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      items: DeliveryMethod.values.map((method) {
        return DropdownMenuItem(
          value: method,
          child: Text(method.arabicName),
        );
      }).toList(),
      onChanged: (value) {
        setState(() => _selectedDeliveryMethod = value!);
      },
    );
  }

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _loading = true);

    try {
      await CharityService().updateCharityItem(
        widget.item.id,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        category: _selectedCategory,
        condition: _selectedCondition,
        deliveryMethod: _selectedDeliveryMethod,
        city: _cityController.text.trim(),
        phoneNumber: _phoneController.text.trim().isEmpty 
            ? null 
            : _phoneController.text.trim(),
        isUrgent: _isUrgent,
        isAnonymous: _isAnonymous,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ التغييرات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الحفظ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }

  Color _getTypeColor() {
    switch (widget.item.type) {
      case CharityType.donation:
        return Colors.green;
      case CharityType.request:
        return Colors.blue;
      case CharityType.urgent:
        return Colors.red;
    }
  }
}
