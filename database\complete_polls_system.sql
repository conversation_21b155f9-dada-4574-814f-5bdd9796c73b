-- نظام التصويتات "نبض" الشامل مع البيانات الحقيقية

-- إنشاء الجداول أولاً

-- جدول التصويتات الرئيسي
CREATE TABLE IF NOT EXISTS polls (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    question TEXT NOT NULL,
    type VARCHAR(20) NOT NULL DEFAULT 'public', -- public, private
    category VARCHAR(20) NOT NULL DEFAULT 'general', -- general, sports, community, religion, entertainment, technology, health, education, business, politics
    duration VARCHAR(20) NOT NULL DEFAULT 'unlimited', -- oneHour, sixHours, twelveHours, oneDay, threeDays, oneWeek, unlimited
    allow_comments BOOLEAN NOT NULL DEFAULT true,
    allow_revote BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NULL
);

-- جدول خيارات التصويت
CREATE TABLE IF NOT EXISTS poll_options (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    text TEXT NOT NULL,
    votes INTEGER NOT NULL DEFAULT 0,
    option_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول أصوات المستخدمين
CREATE TABLE IF NOT EXISTS poll_votes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    option_id UUID NOT NULL REFERENCES poll_options(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- منع التصويت المتكرر من نفس المستخدم على نفس التصويت
    UNIQUE(poll_id, user_id)
);

-- جدول تعليقات التصويتات
CREATE TABLE IF NOT EXISTS poll_comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول مشاركات التصويتات
CREATE TABLE IF NOT EXISTS poll_shares (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    share_type VARCHAR(20) NOT NULL DEFAULT 'general', -- general, group, profile
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_polls_user_id ON polls(user_id);
CREATE INDEX IF NOT EXISTS idx_polls_category ON polls(category);
CREATE INDEX IF NOT EXISTS idx_polls_active ON polls(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_polls_created_at ON polls(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_polls_expires_at ON polls(expires_at) WHERE expires_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_poll_options_poll_id ON poll_options(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_options_order ON poll_options(poll_id, option_order);

CREATE INDEX IF NOT EXISTS idx_poll_votes_poll_id ON poll_votes(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_option_id ON poll_votes(option_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_user_id ON poll_votes(user_id);

CREATE INDEX IF NOT EXISTS idx_poll_comments_poll_id ON poll_comments(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_comments_user_id ON poll_comments(user_id);

-- دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إضافة triggers لتحديث updated_at
DROP TRIGGER IF EXISTS update_polls_updated_at ON polls;
CREATE TRIGGER update_polls_updated_at
    BEFORE UPDATE ON polls
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_poll_votes_updated_at ON poll_votes;
CREATE TRIGGER update_poll_votes_updated_at
    BEFORE UPDATE ON poll_votes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_poll_comments_updated_at ON poll_comments;
CREATE TRIGGER update_poll_comments_updated_at
    BEFORE UPDATE ON poll_comments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- دالة لتحديث عدد الأصوات تلقائياً
CREATE OR REPLACE FUNCTION update_poll_option_votes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE poll_options 
        SET votes = votes + 1 
        WHERE id = NEW.option_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE poll_options 
        SET votes = votes - 1 
        WHERE id = OLD.option_id;
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        -- إذا تم تغيير الخيار
        IF OLD.option_id != NEW.option_id THEN
            UPDATE poll_options 
            SET votes = votes - 1 
            WHERE id = OLD.option_id;
            
            UPDATE poll_options 
            SET votes = votes + 1 
            WHERE id = NEW.option_id;
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- إضافة trigger لتحديث عدد الأصوات
DROP TRIGGER IF EXISTS update_option_votes_trigger ON poll_votes;
CREATE TRIGGER update_option_votes_trigger
    AFTER INSERT OR UPDATE OR DELETE ON poll_votes
    FOR EACH ROW
    EXECUTE FUNCTION update_poll_option_votes();

-- دالة لإنهاء التصويتات المنتهية الصلاحية
CREATE OR REPLACE FUNCTION expire_polls()
RETURNS void AS $$
BEGIN
    UPDATE polls 
    SET is_active = false 
    WHERE expires_at IS NOT NULL 
    AND expires_at < NOW() 
    AND is_active = true;
END;
$$ LANGUAGE plpgsql;

-- تفعيل Row Level Security
ALTER TABLE polls ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_options ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_shares ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للتصويتات
DROP POLICY IF EXISTS "الجميع يمكنهم قراءة التصويتات العامة النشطة" ON polls;
CREATE POLICY "الجميع يمكنهم قراءة التصويتات العامة النشطة" ON polls
    FOR SELECT USING (is_active = true AND type = 'public');

DROP POLICY IF EXISTS "المستخدم يمكنه إنشاء تصويتاته" ON polls;
CREATE POLICY "المستخدم يمكنه إنشاء تصويتاته" ON polls
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "المستخدم يمكنه تحديث تصويتاته" ON polls;
CREATE POLICY "المستخدم يمكنه تحديث تصويتاته" ON polls
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "المستخدم يمكنه حذف تصويتاته" ON polls;
CREATE POLICY "المستخدم يمكنه حذف تصويتاته" ON polls
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان لخيارات التصويت
DROP POLICY IF EXISTS "الجميع يمكنهم قراءة خيارات التصويتات العامة" ON poll_options;
CREATE POLICY "الجميع يمكنهم قراءة خيارات التصويتات العامة" ON poll_options
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM polls 
            WHERE id = poll_options.poll_id AND is_active = true AND type = 'public'
        )
    );

DROP POLICY IF EXISTS "صاحب التصويت يمكنه إدارة الخيارات" ON poll_options;
CREATE POLICY "صاحب التصويت يمكنه إدارة الخيارات" ON poll_options
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM polls 
            WHERE id = poll_options.poll_id AND user_id = auth.uid()
        )
    );

-- سياسات الأمان للأصوات
DROP POLICY IF EXISTS "المستخدم يمكنه قراءة أصواته" ON poll_votes;
CREATE POLICY "المستخدم يمكنه قراءة أصواته" ON poll_votes
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "المستخدم يمكنه التصويت" ON poll_votes;
CREATE POLICY "المستخدم يمكنه التصويت" ON poll_votes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "المستخدم يمكنه تحديث تصويته" ON poll_votes;
CREATE POLICY "المستخدم يمكنه تحديث تصويته" ON poll_votes
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "المستخدم يمكنه حذف تصويته" ON poll_votes;
CREATE POLICY "المستخدم يمكنه حذف تصويته" ON poll_votes
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان للتعليقات
DROP POLICY IF EXISTS "الجميع يمكنهم قراءة تعليقات التصويتات العامة" ON poll_comments;
CREATE POLICY "الجميع يمكنهم قراءة تعليقات التصويتات العامة" ON poll_comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM polls 
            WHERE id = poll_comments.poll_id AND is_active = true AND type = 'public'
        )
        AND is_active = true
    );

DROP POLICY IF EXISTS "المستخدم يمكنه إضافة تعليقات" ON poll_comments;
CREATE POLICY "المستخدم يمكنه إضافة تعليقات" ON poll_comments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "المستخدم يمكنه تحديث تعليقاته" ON poll_comments;
CREATE POLICY "المستخدم يمكنه تحديث تعليقاته" ON poll_comments
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "المستخدم يمكنه حذف تعليقاته" ON poll_comments;
CREATE POLICY "المستخدم يمكنه حذف تعليقاته" ON poll_comments
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان للمشاركات
DROP POLICY IF EXISTS "المستخدم يمكنه إدارة مشاركاته" ON poll_shares;
CREATE POLICY "المستخدم يمكنه إدارة مشاركاته" ON poll_shares
    FOR ALL USING (auth.uid() = user_id);

-- منح الصلاحيات
GRANT ALL ON polls TO authenticated;
GRANT ALL ON poll_options TO authenticated;
GRANT ALL ON poll_votes TO authenticated;
GRANT ALL ON poll_comments TO authenticated;
GRANT ALL ON poll_shares TO authenticated;

GRANT EXECUTE ON FUNCTION expire_polls() TO authenticated;

-- إدراج البيانات الحقيقية
-- ملاحظة: يجب تسجيل الدخول أولاً أو استخدام user_id صحيح

-- إنشاء دالة لإدراج التصويتات مع التحقق من المستخدم
CREATE OR REPLACE FUNCTION insert_sample_polls()
RETURNS TEXT AS $$
DECLARE
    current_user_id UUID;
    sample_user_id UUID;
BEGIN
    -- الحصول على المستخدم الحالي
    current_user_id := auth.uid();

    -- إذا لم يكن هناك مستخدم مسجل، استخدم أول مستخدم في النظام
    IF current_user_id IS NULL THEN
        SELECT id INTO sample_user_id FROM profiles LIMIT 1;
        IF sample_user_id IS NULL THEN
            RETURN 'خطأ: لا يوجد مستخدمين في النظام. يجب إنشاء مستخدم أولاً.';
        END IF;
        current_user_id := sample_user_id;
    END IF;

-- 1. تصويت رياضي - كأس العالم 2026
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    current_user_id,
    'أي منتخب تتوقع أن يفوز بكأس العالم 2026؟',
    'public',
    'sports',
    'oneWeek',
    true,
    false,
    NOW() + INTERVAL '7 days'
);

INSERT INTO poll_options (poll_id, text, option_order)
SELECT
    (SELECT id FROM polls WHERE question = 'أي منتخب تتوقع أن يفوز بكأس العالم 2026؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES
    ('البرازيل 🇧🇷', 0),
    ('الأرجنتين 🇦🇷', 1),
    ('فرنسا 🇫🇷', 2),
    ('إنجلترا 🏴󠁧󠁢󠁥󠁮󠁧󠁿', 3),
    ('إسبانيا 🇪🇸', 4),
    ('ألمانيا 🇩🇪', 5)
) AS options(option_text, option_order);

-- 2. تصويت تقني - الذكاء الاصطناعي
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    current_user_id,
    'ما هو أفضل نموذج ذكاء اصطناعي للاستخدام اليومي؟',
    'public',
    'technology',
    'unlimited',
    true,
    true
);

INSERT INTO poll_options (poll_id, text, option_order)
SELECT
    (SELECT id FROM polls WHERE question = 'ما هو أفضل نموذج ذكاء اصطناعي للاستخدام اليومي؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES
    ('ChatGPT من OpenAI', 0),
    ('Claude من Anthropic', 1),
    ('Gemini من Google', 2),
    ('Copilot من Microsoft', 3),
    ('Meta AI من فيسبوك', 4)
) AS options(option_text, option_order);

-- 3. تصويت مجتمعي - وسائل التواصل
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    current_user_id,
    'ما هي منصة التواصل الاجتماعي المفضلة لديك؟',
    'public',
    'general',
    'threeDays',
    true,
    true,
    NOW() + INTERVAL '3 days'
);

INSERT INTO poll_options (poll_id, text, option_order)
SELECT
    (SELECT id FROM polls WHERE question = 'ما هي منصة التواصل الاجتماعي المفضلة لديك؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES
    ('تويتر (X) 🐦', 0),
    ('إنستغرام 📸', 1),
    ('تيك توك 🎵', 2),
    ('يوتيوب 📺', 3),
    ('سناب شات 👻', 4),
    ('لينكد إن 💼', 5)
) AS options(option_text, option_order);

-- 4. تصويت صحي - النظام الغذائي
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    current_user_id,
    'ما هو أهم عنصر في النظام الغذائي الصحي؟',
    'public',
    'health',
    'unlimited',
    true,
    false
);

INSERT INTO poll_options (poll_id, text, option_order)
SELECT
    (SELECT id FROM polls WHERE question = 'ما هو أهم عنصر في النظام الغذائي الصحي؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES
    ('الخضروات والفواكه 🥗', 0),
    ('البروتينات 🥩', 1),
    ('الحبوب الكاملة 🌾', 2),
    ('شرب الماء بكثرة 💧', 3),
    ('تجنب السكريات 🚫🍭', 4)
) AS options(option_text, option_order);

-- 5. تصويت تعليمي - التعلم الإلكتروني
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(),
    'ما هو أفضل موقع للتعلم الإلكتروني والدورات المجانية؟',
    'public',
    'education',
    'oneWeek',
    true,
    true,
    NOW() + INTERVAL '7 days'
);

INSERT INTO poll_options (poll_id, text, option_order)
SELECT
    (SELECT id FROM polls WHERE question = 'ما هو أفضل موقع للتعلم الإلكتروني والدورات المجانية؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES
    ('يوتيوب YouTube', 0),
    ('كورسيرا Coursera', 1),
    ('يوديمي Udemy', 2),
    ('خان أكاديمي Khan Academy', 3),
    ('إدكس edX', 4),
    ('رواق (عربي)', 5)
) AS options(option_text, option_order);

-- 6. تصويت ترفيهي - الأفلام
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(),
    'ما هو نوع الأفلام المفضل لديك؟',
    'public',
    'entertainment',
    'unlimited',
    true,
    true
);

INSERT INTO poll_options (poll_id, text, option_order)
SELECT
    (SELECT id FROM polls WHERE question = 'ما هو نوع الأفلام المفضل لديك؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES
    ('الأكشن والمغامرات 🎬', 0),
    ('الكوميديا والضحك 😂', 1),
    ('الدراما والرومانسية 💕', 2),
    ('الخيال العلمي 🚀', 3),
    ('الرعب والإثارة 😱', 4),
    ('الوثائقيات 📚', 5)
) AS options(option_text, option_order);

-- 7. تصويت أعمال - العمل عن بُعد
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(),
    'ما هو أفضل نموذج عمل في عصر التكنولوجيا؟',
    'public',
    'business',
    'oneDay',
    true,
    false,
    NOW() + INTERVAL '1 day'
);

INSERT INTO poll_options (poll_id, text, option_order)
SELECT
    (SELECT id FROM polls WHERE question = 'ما هو أفضل نموذج عمل في عصر التكنولوجيا؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES
    ('العمل من المكتب 🏢', 0),
    ('العمل عن بُعد 🏠', 1),
    ('العمل المختلط (هجين) ⚖️', 2),
    ('العمل المرن حسب المشروع 📋', 3)
) AS options(option_text, option_order);

-- 8. تصويت ديني - الأعمال الخيرية
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(),
    'ما هو أفضل شكل للعمل الخيري والمساعدة؟',
    'public',
    'religion',
    'unlimited',
    true,
    false
);

INSERT INTO poll_options (poll_id, text, option_order)
SELECT
    (SELECT id FROM polls WHERE question = 'ما هو أفضل شكل للعمل الخيري والمساعدة؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES
    ('التبرع المالي 💰', 0),
    ('التطوع بالوقت والجهد ⏰', 1),
    ('تقديم الخدمات المهنية مجاناً 🛠️', 2),
    ('نشر الوعي والتثقيف 📢', 3),
    ('كفالة الأيتام والمحتاجين 🤝', 4)
) AS options(option_text, option_order);

-- 9. تصويت سياسي - القضايا العربية
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(),
    'ما هي أولوية العالم العربي في الوقت الحالي؟',
    'public',
    'politics',
    'unlimited',
    true,
    false
);

INSERT INTO poll_options (poll_id, text, option_order)
SELECT
    (SELECT id FROM polls WHERE question = 'ما هي أولوية العالم العربي في الوقت الحالي؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES
    ('تحقيق الأمن والاستقرار 🕊️', 0),
    ('التنمية الاقتصادية 📈', 1),
    ('التطوير التعليمي والثقافي 📚', 2),
    ('حل القضية الفلسطينية 🇵🇸', 3),
    ('مكافحة الفساد ⚖️', 4),
    ('حماية البيئة 🌍', 5)
) AS options(option_text, option_order);

-- 10. تصويت مجتمعي - البيئة
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(),
    'ما هو أهم إجراء لحماية البيئة يمكن للفرد القيام به؟',
    'public',
    'community',
    'threeDays',
    true,
    true,
    NOW() + INTERVAL '3 days'
);

INSERT INTO poll_options (poll_id, text, option_order)
SELECT
    (SELECT id FROM polls WHERE question = 'ما هو أهم إجراء لحماية البيئة يمكن للفرد القيام به؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES
    ('تقليل استخدام البلاستيك ♻️', 0),
    ('توفير الطاقة والمياه 💡', 1),
    ('استخدام وسائل النقل العام 🚌', 2),
    ('إعادة التدوير 🔄', 3),
    ('زراعة الأشجار 🌳', 4),
    ('شراء منتجات صديقة للبيئة 🌱', 5)
) AS options(option_text, option_order);

-- 11. تصويت تقني - البرمجة
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(),
    'ما هي أفضل لغة برمجة للمبتدئين في 2024؟',
    'public',
    'technology',
    'unlimited',
    true,
    true
);

INSERT INTO poll_options (poll_id, text, option_order)
SELECT
    (SELECT id FROM polls WHERE question = 'ما هي أفضل لغة برمجة للمبتدئين في 2024؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES
    ('Python 🐍', 0),
    ('JavaScript 🌐', 1),
    ('Java ☕', 2),
    ('C++ ⚡', 3),
    ('Swift 📱', 4),
    ('Kotlin 🤖', 5)
) AS options(option_text, option_order);

-- 12. تصويت صحي - الرياضة
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(),
    'ما هو أفضل وقت لممارسة الرياضة؟',
    'public',
    'health',
    'oneDay',
    true,
    true,
    NOW() + INTERVAL '1 day'
);

INSERT INTO poll_options (poll_id, text, option_order)
SELECT
    (SELECT id FROM polls WHERE question = 'ما هو أفضل وقت لممارسة الرياضة؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES
    ('الصباح الباكر (5-8 ص) 🌅', 0),
    ('الصباح المتأخر (8-12 ظ) ☀️', 1),
    ('بعد الظهر (12-6 م) 🌤️', 2),
    ('المساء (6-10 م) 🌆', 3)
) AS options(option_text, option_order);

-- رسالة نجاح
SELECT 'تم إنشاء نظام التصويتات "نبض" مع 12 تصويت حقيقي بنجاح!' as message;
