enum MediaType { text, image, video, audio, voice, link, emoji }

class Message {
  final String id;
  final String chatId;
  final String senderId;
  final String content;
  final MediaType type;
  final String? mediaUrl;
  final DateTime createdAt;
  final DateTime? readAt;

  Message({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.content,
    required this.type,
    this.mediaUrl,
    required this.createdAt,
    this.readAt,
  });
} 