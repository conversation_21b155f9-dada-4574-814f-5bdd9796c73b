import 'package:flutter/material.dart';

// مستويات المستخدمين
enum UserLevel {
  newbie,      // جديد
  beginner,    // مبتدئ
  intermediate, // متوسط
  advanced,    // متقدم
  expert,      // خبير
  influencer,  // مؤثر
  celebrity,   // مشهور
  legend,      // أسطورة
}

// معلومات كل مستوى
class UserLevelInfo {
  final UserLevel level;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final int minFollowers;
  final int minPosts;
  final int minViews;

  const UserLevelInfo({
    required this.level,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.minFollowers,
    required this.minPosts,
    required this.minViews,
  });
}

// تعريف جميع المستويات
class UserLevelSystem {
  static const Map<UserLevel, UserLevelInfo> levels = {
    UserLevel.newbie: UserLevelInfo(
      level: UserLevel.newbie,
      name: 'جديد',
      description: 'مرحباً بك في المجتمع',
      icon: Icons.fiber_new,
      color: Color(0xFF9E9E9E), // رمادي
      minFollowers: 0,
      minPosts: 0,
      minViews: 0,
    ),
    
    UserLevel.beginner: UserLevelInfo(
      level: UserLevel.beginner,
      name: 'مبتدئ',
      description: 'بداية رائعة',
      icon: Icons.trending_up,
      color: Color(0xFF4CAF50), // أخضر
      minFollowers: 10,
      minPosts: 5,
      minViews: 100,
    ),
    
    UserLevel.intermediate: UserLevelInfo(
      level: UserLevel.intermediate,
      name: 'متوسط',
      description: 'تقدم ملحوظ',
      icon: Icons.star_half,
      color: Color(0xFF2196F3), // أزرق
      minFollowers: 100,
      minPosts: 25,
      minViews: 1000,
    ),
    
    UserLevel.advanced: UserLevelInfo(
      level: UserLevel.advanced,
      name: 'متقدم',
      description: 'مستوى متميز',
      icon: Icons.star,
      color: Color(0xFF9C27B0), // بنفسجي
      minFollowers: 500,
      minPosts: 50,
      minViews: 5000,
    ),
    
    UserLevel.expert: UserLevelInfo(
      level: UserLevel.expert,
      name: 'خبير',
      description: 'خبرة عالية',
      icon: Icons.workspace_premium,
      color: Color(0xFFFF9800), // برتقالي
      minFollowers: 2000,
      minPosts: 100,
      minViews: 20000,
    ),
    
    UserLevel.influencer: UserLevelInfo(
      level: UserLevel.influencer,
      name: 'مؤثر',
      description: 'تأثير واسع',
      icon: Icons.campaign,
      color: Color(0xFFE91E63), // وردي
      minFollowers: 10000,
      minPosts: 200,
      minViews: 100000,
    ),
    
    UserLevel.celebrity: UserLevelInfo(
      level: UserLevel.celebrity,
      name: 'نجم صاعد',
      description: 'شهرة واسعة',
      icon: Icons.auto_awesome,
      color: Color(0xFFFFD700), // ذهبي
      minFollowers: 100000,
      minPosts: 500,
      minViews: 1000000,
    ),
    
    UserLevel.legend: UserLevelInfo(
      level: UserLevel.legend,
      name: 'أسطورة',
      description: 'مستوى استثنائي',
      icon: Icons.emoji_events,
      color: Color(0xFFFF6B35), // برتقالي محمر
      minFollowers: 1000000,
      minPosts: 1000,
      minViews: 10000000,
    ),
  };

  // حساب مستوى المستخدم
  static UserLevel calculateUserLevel({
    required int followersCount,
    required int postsCount,
    required int totalViews,
  }) {
    // ترتيب المستويات من الأعلى للأقل
    final sortedLevels = levels.entries.toList()
      ..sort((a, b) => b.value.minFollowers.compareTo(a.value.minFollowers));

    for (final entry in sortedLevels) {
      final levelInfo = entry.value;
      
      // التحقق من استيفاء جميع المتطلبات
      if (followersCount >= levelInfo.minFollowers &&
          postsCount >= levelInfo.minPosts &&
          totalViews >= levelInfo.minViews) {
        return levelInfo.level;
      }
    }

    return UserLevel.newbie; // المستوى الافتراضي
  }

  // الحصول على معلومات المستوى
  static UserLevelInfo getLevelInfo(UserLevel level) {
    return levels[level]!;
  }

  // الحصول على المستوى التالي
  static UserLevel? getNextLevel(UserLevel currentLevel) {
    final currentIndex = UserLevel.values.indexOf(currentLevel);
    if (currentIndex < UserLevel.values.length - 1) {
      return UserLevel.values[currentIndex + 1];
    }
    return null; // أعلى مستوى
  }

  // حساب التقدم للمستوى التالي
  static double calculateProgress({
    required UserLevel currentLevel,
    required int followersCount,
    required int postsCount,
    required int totalViews,
  }) {
    final nextLevel = getNextLevel(currentLevel);
    if (nextLevel == null) return 1.0; // أعلى مستوى

    final nextLevelInfo = getLevelInfo(nextLevel);
    final currentLevelInfo = getLevelInfo(currentLevel);

    // حساب التقدم بناءً على المتابعين (المؤشر الرئيسي)
    final followersProgress = (followersCount - currentLevelInfo.minFollowers) /
        (nextLevelInfo.minFollowers - currentLevelInfo.minFollowers);

    return followersProgress.clamp(0.0, 1.0);
  }
}
