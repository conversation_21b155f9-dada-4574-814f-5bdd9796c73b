class CommunityComment {
  final String id;
  final String postId;
  final String userId;
  final String userName;
  final String userAvatar;
  final String content;
  final DateTime createdAt;
  final int upVotes;
  final int downVotes;
  final int? userVote;
  final bool isVerified; // حالة توثيق المستخدم

  CommunityComment({
    required this.id,
    required this.postId,
    required this.userId,
    required this.userName,
    required this.userAvatar,
    required this.content,
    required this.createdAt,
    required this.upVotes,
    required this.downVotes,
    this.userVote,
    this.isVerified = false,
  });

  factory CommunityComment.fromMap(Map<String, dynamic> map) {
    final profile = map['profiles'] ?? {};
    return CommunityComment(
      id: map['id'].toString(),
      postId: map['post_id'].toString(),
      userId: map['user_id'].toString(),
      userName: profile['name'] ?? 'مستخدم',
      userAvatar: profile['avatar_url'] ?? '',
      content: map['content'] ?? '',
      createdAt: DateTime.parse(map['created_at']),
      upVotes: map['up_votes'] ?? 0,
      downVotes: map['down_votes'] ?? 0,
      userVote: map['current_vote'],
      isVerified: profile['is_verified'] ?? false,
    );
  }
} 