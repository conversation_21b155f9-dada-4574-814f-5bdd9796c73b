import 'package:flutter/material.dart';
import '../supabase_service.dart';
import '../models/group.dart';
import 'group_home_page.dart';

class GroupsPage extends StatefulWidget {
  const GroupsPage({super.key});

  @override
  State<GroupsPage> createState() => _GroupsPageState();
}

class _GroupsPageState extends State<GroupsPage> {
  final TextEditingController _searchController = TextEditingController();
  List<Group> _groups = [];
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    setState(() => _loading = true);
    _groups = await SupabaseService().fetchGroups();
    if (mounted) setState(() => _loading = false);
  }

  Future<void> _createGroup() async {
    final nameController = TextEditingController();
    final descController = TextEditingController();
    final ok = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('إنشاء مجموعة'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(controller: nameController, decoration: const InputDecoration(labelText: 'اسم المجموعة')),
            TextField(controller: descController, decoration: const InputDecoration(labelText: 'وصف')),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('إلغاء')),
          TextButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('إنشاء')),
        ],
      ),
    );
    if (ok == true && nameController.text.trim().isNotEmpty) {
      await SupabaseService().createGroup(name: nameController.text.trim(), description: descController.text.trim());
      _load();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      floatingActionButton: FloatingActionButton(
        onPressed: _createGroup,
        child: const Icon(Icons.group_add),
      ),
      body: _loading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _load,
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(16),
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'ابحث عن مجموعات...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(25)),
                        filled: true,
                        fillColor: Colors.grey[200],
                      ),
                      onChanged: (_) => setState(() {}),
                    ),
                  ),
                  Expanded(
                    child: _filtered().isEmpty
                        ? const Center(child: Text('لا توجد نتائج'))
                        : ListView.separated(
                            padding: const EdgeInsets.all(16),
                            itemCount: _filtered().length,
                            separatorBuilder: (_, __) => const SizedBox(height: 8),
                            itemBuilder: (context, index) {
                              final g = _filtered()[index];
                              return Card(
                                child: ListTile(
                                  onTap: () {
                                    Navigator.push(context, MaterialPageRoute(builder: (_) => GroupHomePage(groupId: g.id)));
                                  },
                                  title: Text(g.name),
                                  subtitle: Text('${g.membersCount} أعضاء'),
                                  trailing: ElevatedButton(
                                    onPressed: () async {
                                      await SupabaseService().toggleGroupMembership(g.id);
                                      _load();
                                    },
                                    style: ElevatedButton.styleFrom(backgroundColor: g.joined ? Colors.grey : Colors.red),
                                    child: Text(g.joined ? 'مغادرة' : 'انضمام'),
                                  ),
                                ),
                              );
                            },
                          ),
                  ),
                ],
              ),
            ),
    );
  }

  List<Group> _filtered() {
    final q = _searchController.text.trim().toLowerCase();
    if (q.isEmpty) return _groups;
    return _groups.where((g) => g.name.toLowerCase().contains(q)).toList();
  }
} 