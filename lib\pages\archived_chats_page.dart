import 'package:flutter/material.dart';
import '../supabase_service.dart';
import '../models/chat.dart';
import 'chat_page.dart';

class ArchivedChatsPage extends StatelessWidget {
  const ArchivedChatsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('الدردشات المؤرشفة')),
      body: FutureBuilder<List<Chat>>(
        future: SupabaseService().fetchArchivedChats(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('خطأ: ${snapshot.error}'));
          }
          final chats = snapshot.data ?? [];
          if (chats.isEmpty) {
            return const Center(child: Text('لا توجد دردشات مؤرشفة'));
          }
          return ListView.builder(
            itemCount: chats.length,
            itemBuilder: (_, index) {
              final chat = chats[index];
              return ListTile(
                leading: CircleAvatar(
                  backgroundImage: chat.otherAvatar.isNotEmpty ? NetworkImage(chat.otherAvatar) : null,
                  child: chat.otherAvatar.isEmpty ? const Icon(Icons.person) : null,
                ),
                title: Text(chat.otherName),
                subtitle: Row(
                  children: [
                    if (chat.lastFromMe) ...[
                      Icon(
                        chat.lastRead ? Icons.done_all : Icons.done_all,
                        size: 16,
                        color: chat.lastRead ? Colors.blue : Colors.grey,
                      ),
                      const SizedBox(width: 4),
                    ],
                    Expanded(
                      child: Text(
                        chat.lastMessage,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => ChatPage(
                        chatId: chat.id,
                        otherId: chat.otherId,
                        username: chat.otherName,
                        avatarUrl: chat.otherAvatar,
                      ),
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
} 