-- إضافة أعمدة حذف الحساب إلى جدول profiles
-- قم بتنفيذ هذه الأوامر في Supabase SQL Editor

-- إضافة عمود delete_requested_at إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'delete_requested_at') THEN
        ALTER TABLE profiles ADD COLUMN delete_requested_at TIMESTAMP WITH TIME ZONE DEFAULT NULL;
    END IF;
END $$;

-- إضافة عمود delete_delay_days إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'profiles' AND column_name = 'delete_delay_days') THEN
        ALTER TABLE profiles ADD COLUMN delete_delay_days INTEGER DEFAULT NULL;
    END IF;
END $$;

-- إنشاء index لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_profiles_deleted_at ON profiles(deleted_at);
CREATE INDEX IF NOT EXISTS idx_profiles_delete_requested_at ON profiles(delete_requested_at);

-- التحقق من الأعمدة المضافة
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'profiles' 
AND column_name IN ('delete_requested_at', 'delete_delay_days'); 