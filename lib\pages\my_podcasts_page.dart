import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/podcast.dart';
import '../services/podcast_service.dart';
import '../widgets/podcast_card.dart';
import '../widgets/podcast_skeleton.dart';
import 'create_podcast_page.dart';

class MyPodcastsPage extends StatefulWidget {
  const MyPodcastsPage({super.key});

  @override
  State<MyPodcastsPage> createState() => _MyPodcastsPageState();
}

class _MyPodcastsPageState extends State<MyPodcastsPage> {
  final PodcastService _podcastService = PodcastService();
  final ScrollController _scrollController = ScrollController();
  
  List<Podcast> _podcasts = [];
  bool _loading = true;
  bool _loadingMore = false;
  bool _hasMore = true;
  String? _error;
  
  final int _limit = 20;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadMyPodcasts();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 300) {
      _loadMorePodcasts();
    }
  }

  Future<void> _loadMyPodcasts() async {
    final userId = Supabase.instance.client.auth.currentUser?.id;
    if (userId == null) {
      setState(() {
        _error = 'يجب تسجيل الدخول أولاً';
        _loading = false;
      });
      return;
    }

    setState(() {
      _loading = true;
      _error = null;
    });

    try {
      final podcasts = await _podcastService.getUserPodcasts(
        userId,
        limit: _limit,
        offset: 0,
      );

      setState(() {
        _podcasts = podcasts;
        _hasMore = podcasts.length == _limit;
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'فشل في تحميل البودكاستات: $e';
        _loading = false;
      });
    }
  }

  Future<void> _loadMorePodcasts() async {
    if (_loadingMore || !_hasMore) return;

    final userId = Supabase.instance.client.auth.currentUser?.id;
    if (userId == null) return;

    setState(() => _loadingMore = true);

    try {
      final morePodcasts = await _podcastService.getUserPodcasts(
        userId,
        limit: _limit,
        offset: _podcasts.length,
      );

      setState(() {
        _podcasts.addAll(morePodcasts);
        _hasMore = morePodcasts.length == _limit;
        _loadingMore = false;
      });
    } catch (e) {
      setState(() => _loadingMore = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل المزيد: $e')),
        );
      }
    }
  }

  Future<void> _onRefresh() async {
    await _loadMyPodcasts();
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: 6,
        itemBuilder: (context, index) => const PodcastSkeleton(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadMyPodcasts,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_podcasts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.podcasts, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لم تنشر أي بودكاست بعد',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'ابدأ بإنشاء أول بودكاست لك',
              style: TextStyle(color: Colors.grey[500]),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const CreatePodcastPage()),
                ).then((_) => _loadMyPodcasts());
              },
              icon: const Icon(Icons.add),
              label: const Text('إنشاء بودكاست'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: _podcasts.length + (_loadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _podcasts.length) {
            return const Padding(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          return PodcastCard(
            podcast: _podcasts[index],
            onRefresh: _loadMyPodcasts,
          );
        },
      ),
    );
  }
}
