-- =============================================================
--  إضافة دالة إنشاء منشور مجتمع
--  Add Create Community Post Function
-- =============================================================

-- إنشاء دالة لنشر منشور مجتمع في جدول posts العادي
CREATE OR REPLACE FUNCTION create_community_post(
  p_community_id UUID,
  p_user_id UUID,
  p_content TEXT,
  p_media_url TEXT DEFAULT NULL,
  p_media_type TEXT DEFAULT 'text'
)
RETURNS UUID AS $$
DECLARE
  new_post_id UUID;
BEGIN
  -- التحقق من عضوية المجتمع
  IF NOT EXISTS (
    SELECT 1 FROM community_members 
    WHERE community_id = p_community_id AND user_id = p_user_id
  ) THEN
    RAISE EXCEPTION 'يجب أن تكون عضواً في المجتمع لنشر منشور';
  END IF;
  
  -- إنشاء المنشور في جدول posts العادي
  INSERT INTO posts (
    user_id, content, type, media_url, community_id, 
    created_at, posts_privacy, likes_count, dislikes_count, 
    shares_count, comments_count, views_count, copies_count
  ) VALUES (
    p_user_id, 
    p_content, 
    CASE 
      WHEN p_media_type = 'image' THEN 'image'
      WHEN p_media_type = 'video' THEN 'video'
      WHEN p_media_type = 'voice' THEN 'voice'
      WHEN p_media_type = 'link' THEN 'link'
      ELSE 'text'
    END,
    p_media_url,
    p_community_id,
    NOW(),
    'everyone',
    0, 0, 0, 0, 0, 0
  ) RETURNING id INTO new_post_id;
  
  RETURN new_post_id;
END;
$$ LANGUAGE plpgsql;

-- فحص إنشاء الدالة
SELECT 
  '🔍 FUNCTION CHECK' as check_type,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM pg_proc 
      WHERE proname = 'create_community_post'
    )
    THEN '✅ SUCCESS: create_community_post function created'
    ELSE '❌ FAILED: Function not created'
  END as status;

-- النتيجة النهائية
SELECT 
  '🎉 FINAL RESULT' as check_type,
  '✅ CREATE COMMUNITY POST FUNCTION READY!' as status,
  'Community posts will be created in the regular posts table' as details;

-- =============================================================
--  تعليمات الاستخدام
-- =============================================================

/*

بعد تشغيل هذا السكريپت:

1. دالة create_community_post متاحة للاستخدام
2. منشورات المجتمع ستُنشأ في جدول posts العادي
3. نفس نظام التفاعل والتعليقات سيعمل تلقائياً

الكود محدث ليستخدم هذه الدالة في createCommunityPost()

*/

-- =============================================================
--  انتهى إضافة دالة إنشاء منشور المجتمع
-- =============================================================
