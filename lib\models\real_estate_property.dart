// نماذج بيانات العقارات

enum PropertyType {
  apartment('apartment', 'شقة', '🏠'),
  house('house', 'منزل', '🏡'),
  land('land', 'أرض', '🌍'),
  shop('shop', 'محل تجاري', '🏪'),
  office('office', 'مكتب', '🏢'),
  villa('villa', 'فيلا', '🏘️'),
  warehouse('warehouse', 'مستودع', '🏭'),
  farm('farm', 'مزرعة', '🚜');

  const PropertyType(this.value, this.arabicName, this.icon);
  final String value;
  final String arabicName;
  final String icon;

  static PropertyType fromString(String value) {
    return PropertyType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => PropertyType.apartment,
    );
  }
}

enum PropertyPurpose {
  sale('sale', 'للبيع', '💰'),
  rent('rent', 'للإيجار', '🔑'),
  exchange('exchange', 'للتبادل', '🔄');

  const PropertyPurpose(this.value, this.arabicName, this.icon);
  final String value;
  final String arabicName;
  final String icon;

  static PropertyPurpose fromString(String value) {
    return PropertyPurpose.values.firstWhere(
      (purpose) => purpose.value == value,
      orElse: () => PropertyPurpose.sale,
    );
  }
}

enum PropertyCategory {
  residential('residential', 'سكني'),
  commercial('commercial', 'تجاري'),
  land('land', 'أراضي'),
  industrial('industrial', 'صناعي');

  const PropertyCategory(this.value, this.arabicName);
  final String value;
  final String arabicName;

  static PropertyCategory fromString(String value) {
    return PropertyCategory.values.firstWhere(
      (category) => category.value == value,
      orElse: () => PropertyCategory.residential,
    );
  }
}

enum Currency {
  usd('USD', 'دولار أمريكي', '\$'),
  eur('EUR', 'يورو', '€'),
  sar('SAR', 'ريال سعودي', 'ر.س'),
  aed('AED', 'درهم إماراتي', 'د.إ'),
  egp('EGP', 'جنيه مصري', 'ج.م'),
  mad('MAD', 'درهم مغربي', 'د.م'),
  tnd('TND', 'دينار تونسي', 'د.ت'),
  dzd('DZD', 'دينار جزائري', 'د.ج');

  const Currency(this.code, this.arabicName, this.symbol);
  final String code;
  final String arabicName;
  final String symbol;

  static Currency fromString(String code) {
    return Currency.values.firstWhere(
      (currency) => currency.code == code,
      orElse: () => Currency.usd,
    );
  }
}

class RealEstateProperty {
  final String id;
  final String userId;
  final String title;
  final String description;
  final PropertyType propertyType;
  final PropertyPurpose purpose;
  final PropertyCategory category;
  
  // معلومات الموقع
  final String country;
  final String city;
  final String? district;
  final String? address;
  final double? latitude;
  final double? longitude;
  
  // معلومات العقار
  final double price;
  final Currency currency;
  final double? area;
  final int bedrooms;
  final int bathrooms;
  final int floors;
  final int parkingSpaces;
  
  // ميزات إضافية
  final List<String> features;
  final List<String> amenities;
  
  // معلومات التواصل
  final String? contactPhone;
  final String? contactWhatsapp;
  final bool allowAppMessages;
  
  // حالة الإعلان
  final bool isActive;
  final bool isFeatured;
  final bool isVerified;
  final int viewsCount;
  
  // تواريخ
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime expiresAt;
  
  // صور العقار
  final List<PropertyImage> images;
  
  // معلومات إضافية
  final bool isFavorite;
  final String? ownerName;
  final String? ownerAvatar;

  RealEstateProperty({
    required this.id,
    required this.userId,
    required this.title,
    required this.description,
    required this.propertyType,
    required this.purpose,
    required this.category,
    required this.country,
    required this.city,
    this.district,
    this.address,
    this.latitude,
    this.longitude,
    required this.price,
    this.currency = Currency.usd,
    this.area,
    this.bedrooms = 0,
    this.bathrooms = 0,
    this.floors = 1,
    this.parkingSpaces = 0,
    this.features = const [],
    this.amenities = const [],
    this.contactPhone,
    this.contactWhatsapp,
    this.allowAppMessages = true,
    this.isActive = true,
    this.isFeatured = false,
    this.isVerified = false,
    this.viewsCount = 0,
    required this.createdAt,
    required this.updatedAt,
    required this.expiresAt,
    this.images = const [],
    this.isFavorite = false,
    this.ownerName,
    this.ownerAvatar,
  });

  factory RealEstateProperty.fromJson(Map<String, dynamic> json) {
    return RealEstateProperty(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      propertyType: PropertyType.fromString(json['property_type'] ?? 'apartment'),
      purpose: PropertyPurpose.fromString(json['purpose'] ?? 'sale'),
      category: PropertyCategory.fromString(json['category'] ?? 'residential'),
      country: json['country'] ?? '',
      city: json['city'] ?? '',
      district: json['district'],
      address: json['address'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      price: (json['price'] ?? 0).toDouble(),
      currency: Currency.fromString(json['currency'] ?? 'USD'),
      area: json['area']?.toDouble(),
      bedrooms: json['bedrooms'] ?? 0,
      bathrooms: json['bathrooms'] ?? 0,
      floors: json['floors'] ?? 1,
      parkingSpaces: json['parking_spaces'] ?? 0,
      features: List<String>.from(json['features'] ?? []),
      amenities: List<String>.from(json['amenities'] ?? []),
      contactPhone: json['contact_phone'],
      contactWhatsapp: json['contact_whatsapp'],
      allowAppMessages: json['allow_app_messages'] ?? true,
      isActive: json['is_active'] ?? true,
      isFeatured: json['is_featured'] ?? false,
      isVerified: json['is_verified'] ?? false,
      viewsCount: json['views_count'] ?? 0,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
      expiresAt: DateTime.parse(json['expires_at']),
      images: (json['property_images'] as List?)
          ?.map((img) => PropertyImage.fromJson(img))
          .toList() ?? [],
      isFavorite: json['is_favorite'] ?? false,
      ownerName: json['owner_name'],
      ownerAvatar: json['owner_avatar'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'description': description,
      'property_type': propertyType.value,
      'purpose': purpose.value,
      'category': category.value,
      'country': country,
      'city': city,
      'district': district,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'price': price,
      'currency': currency.code,
      'area': area,
      'bedrooms': bedrooms,
      'bathrooms': bathrooms,
      'floors': floors,
      'parking_spaces': parkingSpaces,
      'features': features,
      'amenities': amenities,
      'contact_phone': contactPhone,
      'contact_whatsapp': contactWhatsapp,
      'allow_app_messages': allowAppMessages,
      'is_active': isActive,
      'is_featured': isFeatured,
      'is_verified': isVerified,
      'views_count': viewsCount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'expires_at': expiresAt.toIso8601String(),
    };
  }

  // تنسيق السعر
  String getFormattedPrice() {
    if (price >= 1000000) {
      return '${(price / 1000000).toStringAsFixed(1)}M ${currency.symbol}';
    } else if (price >= 1000) {
      return '${(price / 1000).toStringAsFixed(0)}K ${currency.symbol}';
    } else {
      return '${price.toStringAsFixed(0)} ${currency.symbol}';
    }
  }

  // تنسيق المساحة
  String getFormattedArea() {
    if (area == null) return '';
    return '${area!.toStringAsFixed(0)} م²';
  }

  // الحصول على العنوان الكامل
  String getFullAddress() {
    List<String> addressParts = [city];
    if (district != null && district!.isNotEmpty) {
      addressParts.add(district!);
    }
    if (address != null && address!.isNotEmpty) {
      addressParts.add(address!);
    }
    return addressParts.join(', ');
  }

  // الحصول على الصورة الرئيسية
  String? getMainImage() {
    if (images.isEmpty) return null;
    final mainImage = images.where((img) => img.isMain).firstOrNull;
    return mainImage?.imageUrl ?? images.first.imageUrl;
  }

  // تحديد ما إذا كان العقار جديد (أقل من 7 أيام)
  bool get isNew {
    return DateTime.now().difference(createdAt).inDays < 7;
  }

  // تحديد ما إذا كان العقار ينتهي قريباً (أقل من 7 أيام)
  bool get isExpiringSoon {
    return expiresAt.difference(DateTime.now()).inDays < 7;
  }
}

class PropertyImage {
  final String id;
  final String propertyId;
  final String imageUrl;
  final int imageOrder;
  final bool isMain;
  final DateTime createdAt;

  PropertyImage({
    required this.id,
    required this.propertyId,
    required this.imageUrl,
    this.imageOrder = 0,
    this.isMain = false,
    required this.createdAt,
  });

  factory PropertyImage.fromJson(Map<String, dynamic> json) {
    return PropertyImage(
      id: json['id'] ?? '',
      propertyId: json['property_id'] ?? '',
      imageUrl: json['image_url'] ?? '',
      imageOrder: json['image_order'] ?? 0,
      isMain: json['is_main'] ?? false,
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'property_id': propertyId,
      'image_url': imageUrl,
      'image_order': imageOrder,
      'is_main': isMain,
      'created_at': createdAt.toIso8601String(),
    };
  }
}
