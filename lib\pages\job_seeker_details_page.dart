import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/job_seeker.dart';
import '../services/job_seekers_service.dart';

class JobSeekerDetailsPage extends StatefulWidget {
  final JobSeeker jobSeeker;

  const JobSeekerDetailsPage({super.key, required this.jobSeeker});

  @override
  State<JobSeekerDetailsPage> createState() => _JobSeekerDetailsPageState();
}

class _JobSeekerDetailsPageState extends State<JobSeekerDetailsPage> {
  final JobSeekersService _jobSeekersService = JobSeekersService();
  bool _isLiked = false;
  bool _isSaved = false;
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    _loadInteractionStatus();
    _incrementViews();
  }

  Future<void> _loadInteractionStatus() async {
    try {
      final liked = await _jobSeekersService.isLiked(widget.jobSeeker.id);
      final saved = await _jobSeekersService.isSaved(widget.jobSeeker.id);
      
      if (mounted) {
        setState(() {
          _isLiked = liked;
          _isSaved = saved;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل حالة التفاعل: $e');
    }
  }

  Future<void> _incrementViews() async {
    try {
      await _jobSeekersService.incrementViews(widget.jobSeeker.id);
    } catch (e) {
      debugPrint('خطأ في زيادة المشاهدات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          // AppBar مع صورة الخلفية
          _buildSliverAppBar(),
          
          // محتوى الصفحة
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // معلومات المستخدم الأساسية
                  _buildUserInfoCard(),
                  
                  const SizedBox(height: 16),
                  
                  // المهارات
                  _buildSkillsCard(),
                  
                  const SizedBox(height: 16),
                  
                  // الخبرة والتفاصيل المهنية
                  _buildExperienceCard(),
                  
                  const SizedBox(height: 16),
                  
                  // الوصف التفصيلي
                  _buildDescriptionCard(),
                  
                  const SizedBox(height: 16),
                  
                  // معلومات التواصل
                  _buildContactCard(),
                  
                  const SizedBox(height: 16),
                  
                  // صور الأعمال السابقة
                  if (widget.jobSeeker.portfolioImages.isNotEmpty)
                    _buildPortfolioCard(),
                  
                  const SizedBox(height: 80), // مساحة للأزرار العائمة
                ],
              ),
            ),
          ),
        ],
      ),
      
      // أزرار التفاعل العائمة
      bottomNavigationBar: _buildBottomActionBar(),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      backgroundColor: Colors.indigo[600],
      foregroundColor: Colors.white,
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          widget.jobSeeker.fullName,
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            shadows: [
              Shadow(
                offset: Offset(1, 1),
                blurRadius: 3,
                color: Colors.black45,
              ),
            ],
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.indigo[400]!,
                Colors.indigo[600]!,
              ],
            ),
          ),
          child: widget.jobSeeker.profileImage != null
              ? Stack(
                  fit: StackFit.expand,
                  children: [
                    Image.network(
                      widget.jobSeeker.profileImage!,
                      fit: BoxFit.cover,
                    ),
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withValues(alpha: 0.7),
                          ],
                        ),
                      ),
                    ),
                  ],
                )
              : Center(
                  child: Icon(
                    Icons.person,
                    size: 80,
                    color: Colors.white.withValues(alpha: 0.7),
                  ),
                ),
        ),
      ),
      actions: [
        IconButton(
          icon: Icon(_isSaved ? Icons.bookmark : Icons.bookmark_border),
          onPressed: _toggleSave,
        ),
        IconButton(
          icon: Icon(_isLiked ? Icons.favorite : Icons.favorite_border),
          onPressed: _toggleLike,
        ),
      ],
    );
  }

  Widget _buildUserInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الاسم والفئة
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.jobSeeker.fullName,
                        style: const TextStyle(
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(
                            widget.jobSeeker.category.icon,
                            size: 20,
                            color: widget.jobSeeker.category.color,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            widget.jobSeeker.category.arabicName,
                            style: TextStyle(
                              fontSize: 16,
                              color: widget.jobSeeker.category.color,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: widget.jobSeeker.preferredJobType.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: widget.jobSeeker.preferredJobType.color.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        widget.jobSeeker.preferredJobType.icon,
                        size: 16,
                        color: widget.jobSeeker.preferredJobType.color,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        widget.jobSeeker.preferredJobType.arabicName,
                        style: TextStyle(
                          fontSize: 12,
                          color: widget.jobSeeker.preferredJobType.color,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // معلومات شخصية
            _buildInfoRow(Icons.cake, 'العمر', widget.jobSeeker.formattedAge),
            _buildInfoRow(Icons.person_outline, 'الجنس', widget.jobSeeker.gender),
            _buildInfoRow(Icons.family_restroom, 'الحالة الاجتماعية', widget.jobSeeker.maritalStatus.arabicName),
            _buildInfoRow(Icons.flag, 'الجنسية', widget.jobSeeker.nationality),
            _buildInfoRow(Icons.location_on, 'الموقع الحالي', '${widget.jobSeeker.currentCity}, ${widget.jobSeeker.currentCountry}'),
            _buildInfoRow(Icons.work_outline, 'المكان المفضل للعمل', widget.jobSeeker.preferredLocation),
            
            const SizedBox(height: 16),
            
            // إحصائيات
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(Icons.visibility, 'مشاهدة', '${widget.jobSeeker.viewsCount}'),
                _buildStatItem(Icons.favorite, 'إعجاب', '${widget.jobSeeker.likesCount}'),
                _buildStatItem(Icons.work_history, 'خبرة', widget.jobSeeker.formattedExperience),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkillsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.star, color: Colors.amber[600]),
                const SizedBox(width: 8),
                const Text(
                  'المهارات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            if (widget.jobSeeker.skills.isNotEmpty)
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: widget.jobSeeker.skills.map((skill) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Text(
                      skill,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.blue[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
              )
            else
              Text(
                'لم يتم تحديد مهارات',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Icon(Icons.language, color: Colors.purple[600]),
                const SizedBox(width: 8),
                const Text(
                  'اللغات',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            if (widget.jobSeeker.languages.isNotEmpty)
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: widget.jobSeeker.languages.map((language) {
                  return Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.purple[50],
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: Colors.purple[200]!),
                    ),
                    child: Text(
                      language,
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.purple[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  );
                }).toList(),
              )
            else
              Text(
                'لم يتم تحديد لغات',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildExperienceCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.work_history, color: Colors.green[600]),
                const SizedBox(width: 8),
                const Text(
                  'الخبرة المهنية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            _buildInfoRow(Icons.timeline, 'سنوات الخبرة', widget.jobSeeker.formattedExperience),
            _buildInfoRow(Icons.category, 'مجال العمل', widget.jobSeeker.category.arabicName),
            _buildInfoRow(Icons.schedule, 'نوع العمل المطلوب', widget.jobSeeker.preferredJobType.arabicName),
          ],
        ),
      ),
    );
  }

  Widget _buildDescriptionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.description, color: Colors.indigo[600]),
                const SizedBox(width: 8),
                const Text(
                  'نبذة شخصية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Text(
              widget.jobSeeker.description,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[700],
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.contact_phone, color: Colors.teal[600]),
                const SizedBox(width: 8),
                const Text(
                  'معلومات التواصل',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            _buildContactRow(Icons.phone, 'الهاتف', widget.jobSeeker.phoneNumber, () => _makePhoneCall(widget.jobSeeker.phoneNumber)),
            
            if (widget.jobSeeker.email != null && widget.jobSeeker.email!.isNotEmpty)
              _buildContactRow(Icons.email, 'البريد الإلكتروني', widget.jobSeeker.email!, () => _sendEmail(widget.jobSeeker.email!)),
            
            if (widget.jobSeeker.socialLinks != null && widget.jobSeeker.socialLinks!.isNotEmpty)
              _buildContactRow(Icons.link, 'روابط التواصل', widget.jobSeeker.socialLinks!, () => _openSocialLinks(widget.jobSeeker.socialLinks!)),
          ],
        ),
      ),
    );
  }

  Widget _buildPortfolioCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.photo_library, color: Colors.orange[600]),
                const SizedBox(width: 8),
                const Text(
                  'أعمال سابقة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            SizedBox(
              height: 120,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: widget.jobSeeker.portfolioImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    margin: const EdgeInsets.only(right: 8),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        widget.jobSeeker.portfolioImages[index],
                        width: 120,
                        height: 120,
                        fit: BoxFit.cover,
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        children: [
          // زر الإعجاب
          Expanded(
            child: OutlinedButton.icon(
              onPressed: _toggleLike,
              icon: Icon(_isLiked ? Icons.favorite : Icons.favorite_border),
              label: Text(_isLiked ? 'معجب' : 'إعجاب'),
              style: OutlinedButton.styleFrom(
                foregroundColor: _isLiked ? Colors.red : Colors.grey[600],
                side: BorderSide(color: _isLiked ? Colors.red : Colors.grey[400]!),
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // زر الاتصال
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _makePhoneCall(widget.jobSeeker.phoneNumber),
              icon: const Icon(Icons.phone),
              label: const Text('اتصال'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[600],
                foregroundColor: Colors.white,
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          // زر المراسلة
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _sendMessage,
              icon: const Icon(Icons.message),
              label: const Text('مراسلة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo[600],
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactRow(IconData icon, String label, String value, VoidCallback onTap) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: Row(
            children: [
              Icon(icon, size: 16, color: Colors.teal[600]),
              const SizedBox(width: 8),
              Text(
                '$label: ',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
              Expanded(
                child: Text(
                  value,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.teal[600],
                    fontWeight: FontWeight.w600,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
              Icon(Icons.open_in_new, size: 16, color: Colors.teal[600]),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String label, String value) {
    return Column(
      children: [
        Icon(icon, color: Colors.indigo[600]),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Future<void> _toggleLike() async {
    if (_loading) return;
    
    setState(() => _loading = true);
    try {
      await _jobSeekersService.toggleLike(widget.jobSeeker.id);
      setState(() => _isLiked = !_isLiked);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الإعجاب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }

  Future<void> _toggleSave() async {
    if (_loading) return;
    
    setState(() => _loading = true);
    try {
      await _jobSeekersService.toggleSave(widget.jobSeeker.id);
      setState(() => _isSaved = !_isSaved);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isSaved ? 'تم حفظ الملف المهني' : 'تم إلغاء حفظ الملف المهني'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الحفظ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        throw 'لا يمكن إجراء الاتصال';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الاتصال: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _sendEmail(String email) async {
    final Uri emailUri = Uri(scheme: 'mailto', path: email);
    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        throw 'لا يمكن فتح البريد الإلكتروني';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح البريد الإلكتروني: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _openSocialLinks(String links) async {
    final Uri uri = Uri.parse(links);
    try {
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      } else {
        throw 'لا يمكن فتح الرابط';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في فتح الرابط: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _sendMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة المراسلة ستكون متاحة قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
