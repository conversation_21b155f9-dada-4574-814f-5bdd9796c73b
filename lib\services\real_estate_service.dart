import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/real_estate_property.dart';

class RealEstateService {
  final SupabaseClient _client = Supabase.instance.client;

  // الحصول على جميع العقارات مع فلترة
  Future<List<RealEstateProperty>> getProperties({
    PropertyType? propertyType,
    PropertyPurpose? purpose,
    String? city,
    double? minPrice,
    double? maxPrice,
    int? minBedrooms,
    int? maxBedrooms,
    String? sortBy = 'created_at',
    bool ascending = false,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      var query = _client
          .from('real_estate_properties')
          .select('''
            *,
            property_images(*)
          ''')
          .eq('is_active', true);

      // تطبيق الفلاتر
      if (propertyType != null) {
        query = query.eq('property_type', propertyType.value);
      }
      if (purpose != null) {
        query = query.eq('purpose', purpose.value);
      }
      if (city != null && city.isNotEmpty) {
        query = query.ilike('city', '%$city%');
      }
      if (minPrice != null) {
        query = query.gte('price', minPrice);
      }
      if (maxPrice != null) {
        query = query.lte('price', maxPrice);
      }
      if (minBedrooms != null) {
        query = query.gte('bedrooms', minBedrooms);
      }
      if (maxBedrooms != null) {
        query = query.lte('bedrooms', maxBedrooms);
      }

      // ترتيب النتائج وتحديد عدد النتائج
      final response = await query
          .order(sortBy ?? 'created_at', ascending: ascending)
          .range(offset, offset + limit - 1);
      
      List<RealEstateProperty> properties = [];

      for (var json in response as List) {
        // الحصول على معلومات المالك منفصلة
        try {
          final ownerInfo = await _client
              .from('profiles')
              .select('username, full_name, avatar_url')
              .eq('id', json['user_id'])
              .maybeSingle();

          if (ownerInfo != null) {
            json['owner_name'] = ownerInfo['full_name'] ?? ownerInfo['username'];
            json['owner_avatar'] = ownerInfo['avatar_url'];
          }
        } catch (e) {
          // تجاهل الخطأ إذا لم نجد معلومات المالك
        }

        properties.add(RealEstateProperty.fromJson(json));
      }

      return properties;
    } catch (e) {
      throw Exception('فشل في تحميل العقارات: $e');
    }
  }

  // الحصول على عقار محدد
  Future<RealEstateProperty?> getProperty(String propertyId) async {
    try {
      final response = await _client
          .from('real_estate_properties')
          .select('''
            *,
            property_images(*)
          ''')
          .eq('id', propertyId)
          .eq('is_active', true)
          .maybeSingle();

      if (response == null) return null;

      // الحصول على معلومات المالك منفصلة
      try {
        final ownerInfo = await _client
            .from('profiles')
            .select('username, full_name, avatar_url')
            .eq('id', response['user_id'])
            .maybeSingle();

        if (ownerInfo != null) {
          response['owner_name'] = ownerInfo['full_name'] ?? ownerInfo['username'];
          response['owner_avatar'] = ownerInfo['avatar_url'];
        }
      } catch (e) {
        // تجاهل الخطأ إذا لم نجد معلومات المالك
      }

      // زيادة عدد المشاهدات
      await _client.rpc('increment_property_views', params: {'property_uuid': propertyId});

      return RealEstateProperty.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تحميل العقار: $e');
    }
  }

  // إنشاء عقار جديد
  Future<String> createProperty(RealEstateProperty property) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // إنشاء البيانات مع التأكد من وجود user_id
      final propertyData = {
        'user_id': userId,
        'title': property.title,
        'description': property.description,
        'property_type': property.propertyType.value,
        'purpose': property.purpose.value,
        'category': property.category.value,
        'country': property.country,
        'city': property.city,
        'district': property.district,
        'address': property.address,
        'latitude': property.latitude,
        'longitude': property.longitude,
        'price': property.price,
        'currency': property.currency.code,
        'area': property.area,
        'bedrooms': property.bedrooms,
        'bathrooms': property.bathrooms,
        'floors': property.floors,
        'parking_spaces': property.parkingSpaces,
        'features': property.features,
        'amenities': property.amenities,
        'contact_phone': property.contactPhone,
        'contact_whatsapp': property.contactWhatsapp,
        'allow_app_messages': property.allowAppMessages,
      };

      final response = await _client
          .from('real_estate_properties')
          .insert(propertyData)
          .select()
          .single();

      return response['id'];
    } catch (e) {
      print('خطأ في إنشاء العقار: $e'); // للتشخيص
      throw Exception('فشل في إنشاء العقار: $e');
    }
  }

  // تحديث عقار
  Future<void> updateProperty(String propertyId, RealEstateProperty property) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('real_estate_properties')
          .update(property.toJson())
          .eq('id', propertyId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في تحديث العقار: $e');
    }
  }

  // تحديث عدد المشاهدات
  Future<void> incrementViewsCount(String propertyId) async {
    try {
      // تحديث المشاهدات مباشرة بدون RPC
      await _client
          .from('real_estate_properties')
          .update({
            'views_count': 'views_count + 1',
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', propertyId)
          .eq('is_active', true);

      print('تم تحديث المشاهدات للعقار: $propertyId');
    } catch (e) {
      print('خطأ في تحديث المشاهدات: $e');

      // محاولة بديلة - استخدام SQL مباشر
      try {
        await _client.rpc('increment_property_views', params: {
          'property_id': propertyId,
        });
        print('تم تحديث المشاهدات باستخدام RPC');
      } catch (e2) {
        print('فشل في تحديث المشاهدات نهائياً: $e2');
      }
    }
  }

  // حذف عقار
  Future<void> deleteProperty(String propertyId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('real_estate_properties')
          .delete()
          .eq('id', propertyId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في حذف العقار: $e');
    }
  }

  // الحصول على عقارات المستخدم
  Future<List<RealEstateProperty>> getUserProperties() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return [];

      final response = await _client
          .from('real_estate_properties')
          .select('''
            *,
            property_images(*)
          ''')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      return (response as List).map((json) => RealEstateProperty.fromJson(json)).toList();
    } catch (e) {
      return [];
    }
  }

  // إضافة/إزالة من المفضلة
  Future<void> toggleFavorite(String propertyId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // التحقق من وجود العقار في المفضلة
      final existing = await _client
          .from('property_favorites')
          .select()
          .eq('user_id', userId)
          .eq('property_id', propertyId)
          .maybeSingle();

      if (existing != null) {
        // إزالة من المفضلة
        await _client
            .from('property_favorites')
            .delete()
            .eq('user_id', userId)
            .eq('property_id', propertyId);
      } else {
        // إضافة للمفضلة
        await _client
            .from('property_favorites')
            .insert({
              'user_id': userId,
              'property_id': propertyId,
            });
      }
    } catch (e) {
      throw Exception('فشل في تحديث المفضلة: $e');
    }
  }

  // الحصول على المفضلة
  Future<List<RealEstateProperty>> getFavoriteProperties() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return [];

      final response = await _client
          .from('property_favorites')
          .select('''
            real_estate_properties!inner(*,
              property_images(*)
            )
          ''')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      List<RealEstateProperty> properties = [];

      for (var json in response as List) {
        final propertyData = json['real_estate_properties'];
        propertyData['is_favorite'] = true;

        // الحصول على معلومات المالك منفصلة
        try {
          final ownerInfo = await _client
              .from('profiles')
              .select('username, full_name, avatar_url')
              .eq('id', propertyData['user_id'])
              .maybeSingle();

          if (ownerInfo != null) {
            propertyData['owner_name'] = ownerInfo['full_name'] ?? ownerInfo['username'];
            propertyData['owner_avatar'] = ownerInfo['avatar_url'];
          }
        } catch (e) {
          // تجاهل الخطأ إذا لم نجد معلومات المالك
        }

        properties.add(RealEstateProperty.fromJson(propertyData));
      }

      return properties;
    } catch (e) {
      return [];
    }
  }

  // رفع صور العقار
  Future<List<String>> uploadPropertyImages(String propertyId, List<String> imagePaths) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      List<String> imageUrls = [];

      for (int i = 0; i < imagePaths.length; i++) {
        final imagePath = imagePaths[i];
        // إضافة userId في مسار الملف للتوافق مع سياسات الأمان
        final fileName = '$userId/property_${propertyId}_${DateTime.now().millisecondsSinceEpoch}_$i.jpg';

        try {
          // رفع الصورة
          final file = File(imagePath);
          final fileBytes = await file.readAsBytes();

          // رفع الصورة إلى التخزين مع معالجة الأخطاء
          try {
            await _client.storage
                .from('property-images')
                .uploadBinary(fileName, fileBytes);

            print('تم رفع الملف بنجاح: $fileName');
          } catch (uploadError) {
            print('خطأ في رفع الملف: $uploadError');
            // محاولة استخدام upload العادي
            await _client.storage
                .from('property-images')
                .upload(fileName, file);
            print('تم رفع الملف بالطريقة البديلة: $fileName');
          }

          // الحصول على رابط الصورة العام
          final imageUrl = _client.storage
              .from('property-images')
              .getPublicUrl(fileName);

          print('رابط الصورة: $imageUrl');
          imageUrls.add(imageUrl);

          // حفظ معلومات الصورة في قاعدة البيانات
          await _client
              .from('property_images')
              .insert({
                'property_id': propertyId,
                'image_url': imageUrl,
                'image_order': i,
                'is_main': i == 0, // الصورة الأولى هي الرئيسية
              });

          print('تم حفظ معلومات الصورة في قاعدة البيانات');
        } catch (e) {
          print('خطأ شامل في رفع الصورة $i: $e');
          // متابعة رفع باقي الصور حتى لو فشلت إحداها
        }
      }

      return imageUrls;
    } catch (e) {
      throw Exception('فشل في رفع الصور: $e');
    }
  }

  // الإبلاغ عن عقار
  Future<void> reportProperty(String propertyId, String reason, String? details) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('property_reports')
          .insert({
            'reporter_id': userId,
            'property_id': propertyId,
            'reason': reason,
            'details': details,
          });
    } catch (e) {
      throw Exception('فشل في الإبلاغ: $e');
    }
  }

  // البحث في العقارات
  Future<List<RealEstateProperty>> searchProperties(String query) async {
    try {
      final response = await _client
          .from('real_estate_properties')
          .select('''
            *,
            property_images(*)
          ''')
          .eq('is_active', true)
          .or('title.ilike.%$query%,description.ilike.%$query%,city.ilike.%$query%,district.ilike.%$query%')
          .order('created_at', ascending: false)
          .limit(20);

      List<RealEstateProperty> properties = [];

      for (var json in response as List) {
        // الحصول على معلومات المالك منفصلة
        try {
          final ownerInfo = await _client
              .from('profiles')
              .select('username, full_name, avatar_url')
              .eq('id', json['user_id'])
              .maybeSingle();

          if (ownerInfo != null) {
            json['owner_name'] = ownerInfo['full_name'] ?? ownerInfo['username'];
            json['owner_avatar'] = ownerInfo['avatar_url'];
          }
        } catch (e) {
          // تجاهل الخطأ إذا لم نجد معلومات المالك
        }

        properties.add(RealEstateProperty.fromJson(json));
      }

      return properties;
    } catch (e) {
      throw Exception('فشل في البحث: $e');
    }
  }
}
