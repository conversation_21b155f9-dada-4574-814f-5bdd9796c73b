# إصلاح عدد المنشورات في المساحات
# Space Posts Count Fix

## المشكلة:
إحصائيات المنشورات في المساحات لا تعمل بشكل صحيح - تظهر صفر منشور رغم وجود منشورات في المساحة.

## السبب:
الاستعلامات في `spaces_service.dart` لم تكن تحسب عدد المنشورات من جدول `space_posts`.

## الحل المطبق:

### ✅ **تحديث جميع استعلامات المساحات:**

#### **1. جلب مساحات المستخدم:**
```dart
final response = await _supabase
    .from('spaces')
    .select('''
      *,
      space_followers!left(follower_id),
      posts:space_posts(count)
    ''')
    .eq('owner_id', targetUserId)
    .eq('status', SpaceStatus.active.name)
    .order('created_at', ascending: false);
```

#### **2. جلب المساحات المقترحة:**
```dart
final response = await _supabase
    .from('spaces')
    .select('''
      *,
      space_followers!left(follower_id),
      posts:space_posts(count)
    ''')
    .eq('status', SpaceStatus.active.name)
    .eq('privacy', SpacePrivacy.public.name)
    .neq('owner_id', currentUserId ?? '')
    .order('followers_count', ascending: false)
    .limit(limit);
```

#### **3. البحث في المساحات:**
```dart
var queryBuilder = _supabase
    .from('spaces')
    .select('''
      *,
      space_followers!left(follower_id),
      posts:space_posts(count)
    ''')
    .eq('status', SpaceStatus.active.name)
    .eq('privacy', SpacePrivacy.public.name);
```

#### **4. جلب تفاصيل المساحة:**
```dart
final response = await _supabase
    .from('spaces')
    .select('''
      *,
      space_followers!left(follower_id),
      posts:space_posts(count)
    ''')
    .eq('id', spaceId)
    .single();
```

#### **5. جلب المساحات المتبوعة:**
```dart
final response = await _supabase
    .from('space_followers')
    .select('''
      spaces!inner(
        *,
        space_followers!left(follower_id),
        posts:space_posts(count)
      )
    ''')
    .eq('follower_id', currentUserId)
    .order('created_at', ascending: false);
```

### ✅ **إضافة حساب عدد المنشورات:**
```dart
// حساب عدد المنشورات
final postsCount = ((json['posts'] as List?)?.first?['count'] ?? 0) as int;

return Space.fromJson(json).copyWith(
  isFollowing: isFollowing,
  isOwner: isOwner,
  followersCount: followers.length,
  postsCount: postsCount, // إضافة عدد المنشورات
);
```

### ✅ **إضافة رسائل تتبع:**
```dart
} catch (e) {
  print('❌ خطأ في جلب مساحات المستخدم: $e');
  return [];
}
```

## التحسينات المطبقة:

### ✅ **حساب دقيق لعدد المنشورات:**
- استخدام `space_posts(count)` في جميع الاستعلامات
- حساب عدد المنشورات الفعلي من قاعدة البيانات

### ✅ **تحديث جميع الدوال:**
- `getUserSpaces()`
- `getSuggestedSpaces()`
- `searchSpaces()`
- `getSpaceDetails()`
- `getFollowedSpaces()`

### ✅ **معالجة الأخطاء:**
- إضافة رسائل تتبع للأخطاء
- تجاهل الأخطاء إذا لم تكن الجداول موجودة

## النتائج المتوقعة:

### 🎯 **عرض صحيح لعدد المنشورات:**
- إظهار العدد الفعلي للمنشورات في كل مساحة
- تحديث العدد عند إضافة منشورات جديدة

### 🎯 **تحديث تلقائي:**
- تحديث عدد المنشورات عند زيارة المساحة
- تحديث عدد المنشورات في جميع الصفحات

### 🎯 **دقة في الإحصائيات:**
- إحصائيات المشاهدات تعمل
- إحصائيات المتابعين تعمل
- إحصائيات المنشورات تعمل الآن

## اختبار الإصلاح:

### 1. **افتح التطبيق**
### 2. **اذهب إلى قسم مساحتي**
### 3. **تحقق من عدد المنشورات:**
- يجب أن يظهر العدد الفعلي للمنشورات
- يجب أن يتحدث عند إضافة منشور جديد

### 4. **تحقق من Console:**
ابحث عن:
- `❌ خطأ في جلب مساحات المستخدم: ...`
- `❌ خطأ في جلب المساحات المقترحة: ...`

**الآن عدد المنشورات في المساحات سيعمل بشكل صحيح!** 