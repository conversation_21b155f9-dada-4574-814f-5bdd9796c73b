// نموذج بيانات الوظيفة
class Job {
  final String id;
  final String title;
  final String companyName;
  final String jobTitle;
  final JobType jobType;
  final String? salary;
  final String location;
  final bool isRemote;
  final String description;
  final List<String> requiredSkills;
  final JobCategory category;
  final String? applicationLink;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final String publisherId;
  final String publisherName;
  final String? publisherAvatar;
  final String? spaceId; // ربط بالمساحة
  final String? spaceName;
  final bool isActive;
  final int applicationsCount;
  final bool isVerified;

  Job({
    required this.id,
    required this.title,
    required this.companyName,
    required this.jobTitle,
    required this.jobType,
    this.salary,
    required this.location,
    this.isRemote = false,
    required this.description,
    required this.requiredSkills,
    required this.category,
    this.applicationLink,
    required this.createdAt,
    this.expiresAt,
    required this.publisherId,
    required this.publisherName,
    this.publisherAvatar,
    this.spaceId,
    this.spaceName,
    this.isActive = true,
    this.applicationsCount = 0,
    this.isVerified = false,
  });

  factory Job.fromJson(Map<String, dynamic> json) {
    return Job(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      companyName: json['company_name'] ?? '',
      jobTitle: json['job_title'] ?? '',
      jobType: JobType.values.firstWhere(
        (e) => e.name == json['job_type'],
        orElse: () => JobType.fullTime,
      ),
      salary: json['salary'],
      location: json['location'] ?? '',
      isRemote: json['is_remote'] ?? false,
      description: json['description'] ?? '',
      requiredSkills: List<String>.from(json['required_skills'] ?? []),
      category: JobCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => JobCategory.other,
      ),
      applicationLink: json['application_link'],
      createdAt: DateTime.parse(json['created_at']),
      expiresAt: json['expires_at'] != null ? DateTime.parse(json['expires_at']) : null,
      publisherId: json['publisher_id'] ?? '',
      publisherName: json['publisher_name'] ?? '',
      publisherAvatar: json['publisher_avatar'],
      spaceId: json['space_id'],
      spaceName: json['space_name'],
      isActive: json['is_active'] ?? true,
      applicationsCount: json['applications_count'] ?? 0,
      isVerified: json['is_verified'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'company_name': companyName,
      'job_title': jobTitle,
      'job_type': jobType.name,
      'salary': salary,
      'location': location,
      'is_remote': isRemote,
      'description': description,
      'required_skills': requiredSkills,
      'category': category.name,
      'application_link': applicationLink,
      'created_at': createdAt.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'publisher_id': publisherId,
      'publisher_name': publisherName,
      'publisher_avatar': publisherAvatar,
      'space_id': spaceId,
      'space_name': spaceName,
      'is_active': isActive,
      'applications_count': applicationsCount,
      'is_verified': isVerified,
    };
  }

  // نسخة محدثة من الوظيفة
  Job copyWith({
    String? title,
    String? companyName,
    String? jobTitle,
    JobType? jobType,
    String? salary,
    String? location,
    bool? isRemote,
    String? description,
    List<String>? requiredSkills,
    JobCategory? category,
    String? applicationLink,
    DateTime? expiresAt,
    bool? isActive,
    int? applicationsCount,
    bool? isVerified,
  }) {
    return Job(
      id: id,
      title: title ?? this.title,
      companyName: companyName ?? this.companyName,
      jobTitle: jobTitle ?? this.jobTitle,
      jobType: jobType ?? this.jobType,
      salary: salary ?? this.salary,
      location: location ?? this.location,
      isRemote: isRemote ?? this.isRemote,
      description: description ?? this.description,
      requiredSkills: requiredSkills ?? this.requiredSkills,
      category: category ?? this.category,
      applicationLink: applicationLink ?? this.applicationLink,
      createdAt: createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      publisherId: publisherId,
      publisherName: publisherName,
      publisherAvatar: publisherAvatar,
      spaceId: spaceId,
      spaceName: spaceName,
      isActive: isActive ?? this.isActive,
      applicationsCount: applicationsCount ?? this.applicationsCount,
      isVerified: isVerified ?? this.isVerified,
    );
  }

  // التحقق من انتهاء صلاحية الوظيفة
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  // الحصول على نص نوع العمل
  String get jobTypeText {
    switch (jobType) {
      case JobType.fullTime:
        return 'دوام كامل';
      case JobType.partTime:
        return 'دوام جزئي';
      case JobType.freelance:
        return 'عمل حر';
      case JobType.remote:
        return 'عن بعد';
      case JobType.contract:
        return 'عقد مؤقت';
      case JobType.internship:
        return 'تدريب';
    }
  }

  // الحصول على نص الفئة
  String get categoryText {
    switch (category) {
      case JobCategory.technology:
        return 'تكنولوجيا';
      case JobCategory.marketing:
        return 'تسويق';
      case JobCategory.education:
        return 'تعليم';
      case JobCategory.construction:
        return 'بناء';
      case JobCategory.restaurant:
        return 'مطاعم';
      case JobCategory.healthcare:
        return 'صحة';
      case JobCategory.finance:
        return 'مالية';
      case JobCategory.design:
        return 'تصميم';
      case JobCategory.sales:
        return 'مبيعات';
      case JobCategory.customerService:
        return 'خدمة عملاء';
      case JobCategory.other:
        return 'أخرى';
    }
  }
}

// أنواع العمل
enum JobType {
  fullTime,
  partTime,
  freelance,
  remote,
  contract,
  internship,
}

// فئات الوظائف
enum JobCategory {
  technology,
  marketing,
  education,
  construction,
  restaurant,
  healthcare,
  finance,
  design,
  sales,
  customerService,
  other,
}

// نموذج طلب التقديم
class JobApplication {
  final String id;
  final String jobId;
  final String applicantId;
  final String applicantName;
  final String? applicantAvatar;
  final String? resumeUrl;
  final String? coverLetter;
  final DateTime appliedAt;
  final ApplicationStatus status;

  JobApplication({
    required this.id,
    required this.jobId,
    required this.applicantId,
    required this.applicantName,
    this.applicantAvatar,
    this.resumeUrl,
    this.coverLetter,
    required this.appliedAt,
    this.status = ApplicationStatus.pending,
  });

  factory JobApplication.fromJson(Map<String, dynamic> json) {
    return JobApplication(
      id: json['id'] ?? '',
      jobId: json['job_id'] ?? '',
      applicantId: json['applicant_id'] ?? '',
      applicantName: json['applicant_name'] ?? '',
      applicantAvatar: json['applicant_avatar'],
      resumeUrl: json['resume_url'],
      coverLetter: json['cover_letter'],
      appliedAt: DateTime.parse(json['applied_at']),
      status: ApplicationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ApplicationStatus.pending,
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'job_id': jobId,
      'applicant_id': applicantId,
      'applicant_name': applicantName,
      'applicant_avatar': applicantAvatar,
      'resume_url': resumeUrl,
      'cover_letter': coverLetter,
      'applied_at': appliedAt.toIso8601String(),
      'status': status.name,
    };
  }

  String get statusText {
    switch (status) {
      case ApplicationStatus.pending:
        return 'قيد المراجعة';
      case ApplicationStatus.accepted:
        return 'مقبول';
      case ApplicationStatus.rejected:
        return 'مرفوض';
      case ApplicationStatus.interviewed:
        return 'تم إجراء مقابلة';
    }
  }
}

// حالات طلب التقديم
enum ApplicationStatus {
  pending,
  accepted,
  rejected,
  interviewed,
}
