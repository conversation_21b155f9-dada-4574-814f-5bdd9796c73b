-- =============================================================
--  اختبار سريع للتأكد من جاهزية Storage
--  Quick Storage Readiness Test
-- =============================================================

-- هذا الاستعلام يتأكد من أن كل شيء جاهز لرفع الصور

-- 1) فحص bucket
-- -------------------------------------------------------

SELECT 
  '🔍 BUCKET STATUS' as test_name,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM storage.buckets 
      WHERE id = 'community-images' 
      AND public = true 
      AND file_size_limit >= 104857600
    )
    THEN '✅ READY - Bucket exists, public, 100MB+ limit'
    WHEN EXISTS (
      SELECT 1 FROM storage.buckets 
      WHERE id = 'community-images' 
      AND public = false
    )
    THEN '❌ PROBLEM - Bucket exists but not public'
    WHEN EXISTS (
      SELECT 1 FROM storage.buckets 
      WHERE id = 'community-images'
    )
    THEN '⚠️ WARNING - Bucket exists but check settings'
    ELSE '❌ MISSING - Bucket community-images not found'
  END as status,
  COALESCE(
    (SELECT CONCAT(
      'Public: ', public::text, 
      ', Size: ', ROUND(file_size_limit/1024/1024), 'MB',
      ', Types: ', array_length(allowed_mime_types, 1)::text
    ) FROM storage.buckets WHERE id = 'community-images'),
    'N/A'
  ) as details;

-- 2) فحص الدوال المساعدة
-- -------------------------------------------------------

SELECT 
  '🔍 FUNCTIONS STATUS' as test_name,
  CASE 
    WHEN (
      SELECT COUNT(*) FROM pg_proc p
      JOIN pg_namespace n ON n.oid = p.pronamespace
      WHERE n.nspname = 'public' 
      AND p.proname IN (
        'get_current_user_id', 
        'check_community_owner', 
        'upload_community_image',
        'update_community_image_url'
      )
    ) = 4
    THEN '✅ READY - All 4 helper functions exist'
    WHEN (
      SELECT COUNT(*) FROM pg_proc p
      JOIN pg_namespace n ON n.oid = p.pronamespace
      WHERE n.nspname = 'public' 
      AND p.proname LIKE '%community%'
    ) > 0
    THEN '⚠️ PARTIAL - Some functions exist'
    ELSE '❌ MISSING - No helper functions found'
  END as status,
  COALESCE(
    (SELECT STRING_AGG(proname, ', ') 
     FROM pg_proc p
     JOIN pg_namespace n ON n.oid = p.pronamespace
     WHERE n.nspname = 'public' 
     AND p.proname LIKE '%community%'),
    'None'
  ) as details;

-- 3) اختبار دالة المستخدم
-- -------------------------------------------------------

SELECT 
  '🔍 USER FUNCTION TEST' as test_name,
  CASE 
    WHEN public.get_current_user_id() IS NOT NULL
    THEN '✅ WORKING - User function returns value'
    ELSE '❌ FAILED - User function returns null'
  END as status,
  COALESCE(public.get_current_user_id(), 'NULL') as details;

-- 4) فحص view الصور
-- -------------------------------------------------------

SELECT 
  '🔍 VIEW STATUS' as test_name,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM pg_views 
      WHERE schemaname = 'public' 
      AND viewname = 'community_images'
    )
    THEN '✅ READY - Community images view exists'
    ELSE '❌ MISSING - Community images view not found'
  END as status,
  'View for safe image access' as details;

-- 5) اختبار إنشاء مجلد وهمي (محاكاة رفع صورة)
-- -------------------------------------------------------

DO $$
DECLARE
  test_result TEXT := '❌ FAILED';
BEGIN
  -- محاولة إنشاء مجلد اختبار
  IF EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images') THEN
    test_result := '✅ READY';
  END IF;
  
  -- إدراج النتيجة في جدول مؤقت للعرض
  CREATE TEMP TABLE IF NOT EXISTS test_results (
    test_name TEXT,
    status TEXT,
    details TEXT
  );
  
  INSERT INTO test_results VALUES (
    '🔍 UPLOAD SIMULATION',
    test_result,
    'Bucket accessible for upload operations'
  );
END $$;

-- عرض نتيجة اختبار الرفع
SELECT test_name, status, details FROM test_results WHERE test_name = '🔍 UPLOAD SIMULATION';

-- 6) التوصيات النهائية
-- -------------------------------------------------------

SELECT 
  '💡 FINAL RECOMMENDATION' as test_name,
  CASE 
    WHEN NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images')
    THEN '🔧 RUN: safe_storage_fix.sql again'
    WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images' AND public = false)
    THEN '🔧 FIX: Make bucket public in Supabase UI'
    WHEN (
      SELECT COUNT(*) FROM pg_proc p
      JOIN pg_namespace n ON n.oid = p.pronamespace
      WHERE n.nspname = 'public' 
      AND p.proname LIKE '%community%'
    ) < 4
    THEN '🔧 RUN: safe_storage_fix.sql again'
    ELSE '🎉 ALL GOOD: Ready to test image upload in app!'
  END as status,
  'Follow the recommendation above' as details;

-- 7) معلومات إضافية للتشخيص
-- -------------------------------------------------------

SELECT 
  '📊 STORAGE INFO' as test_name,
  'Total buckets' as status,
  (SELECT COUNT(*)::text FROM storage.buckets) as details

UNION ALL

SELECT 
  '📊 STORAGE INFO' as test_name,
  'Images in community-images' as status,
  COALESCE(
    (SELECT COUNT(*)::text FROM storage.objects WHERE bucket_id = 'community-images'),
    '0'
  ) as details

UNION ALL

SELECT 
  '📊 STORAGE INFO' as test_name,
  'Database user' as status,
  COALESCE(current_user, 'unknown') as details;

-- تنظيف الجدول المؤقت
DROP TABLE IF EXISTS test_results;

-- =============================================================
--  تفسير النتائج
-- =============================================================

/*

كيفية قراءة النتائج:

✅ READY/WORKING/ALL GOOD = ممتاز، جاهز للاستخدام
⚠️ WARNING/PARTIAL = يعمل لكن قد يحتاج تحسين
❌ MISSING/FAILED/PROBLEM = يحتاج إصلاح

إذا رأيت "🎉 ALL GOOD: Ready to test image upload in app!"
فهذا يعني أن كل شيء جاهز ويمكنك اختبار رفع الصور في التطبيق.

إذا رأيت أي توصية أخرى، اتبعها قبل اختبار التطبيق.

*/
