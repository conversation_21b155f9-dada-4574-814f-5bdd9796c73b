# 🗳️ إعداد نظام التصويتات "نبض" - الطريقة الصحيحة

## ⚠️ **المشكلة في الملفات السابقة:**
الملفات السابقة تحتوي على خطأ `auth.uid()` يعيد `null` عندما لا يكون هناك مستخدم مسجل دخول.

## ✅ **الحل الصحيح:**

### **الخطوة 1: تشغيل النظام الأساسي**
```sql
\i database/polls_system_fixed.sql
```

### **الخطوة 2: إضافة التصويتات التجريبية**

#### **الطريقة الأولى: إذا كنت مسجل دخول**
```sql
SELECT insert_sample_polls();
```

#### **الطريقة الثانية: تحديد مستخدم معين**
```sql
-- استبدل 'user-id-here' بـ ID مستخدم حقيقي من جدول profiles
SELECT insert_sample_polls('user-id-here');
```

#### **الطريقة الثالثة: استخدام أول مستخدم في النظام**
```sql
-- هذا سيستخدم أول مستخدم موجود في جدول profiles
SELECT insert_sample_polls((SELECT id FROM profiles LIMIT 1));
```

---

## 🔍 **التحقق من وجود مستخدمين:**

```sql
-- عرض جميع المستخدمين
SELECT id, email, full_name FROM profiles LIMIT 5;

-- إذا لم يكن هناك مستخدمين، أنشئ واحد:
INSERT INTO profiles (id, email, full_name) 
VALUES (gen_random_uuid(), '<EMAIL>', 'مستخدم تجريبي');
```

---

## 📊 **التحقق من النجاح:**

```sql
-- عرض التصويتات المضافة
SELECT question, category, created_at FROM polls;

-- عرض خيارات التصويت
SELECT p.question, po.text, po.option_order 
FROM polls p 
JOIN poll_options po ON p.id = po.poll_id 
ORDER BY p.created_at, po.option_order;

-- عدد التصويتات
SELECT COUNT(*) as total_polls FROM polls;
```

---

## 🗳️ **التصويتات المتوفرة (5 تصويتات):**

1. **كأس العالم 2026** (رياضة) - 5 خيارات
2. **أفضل ذكاء اصطناعي** (تقنية) - 4 خيارات  
3. **منصات التواصل الاجتماعي** (عام) - 4 خيارات
4. **النظام الغذائي الصحي** (صحة) - 4 خيارات
5. **مواقع التعلم الإلكتروني** (تعليم) - 4 خيارات

---

## ✅ **المميزات:**

- ✅ **يعمل بدون أخطاء**
- ✅ **Row Level Security مفعل**
- ✅ **Triggers للتحديث التلقائي**
- ✅ **بيانات حقيقية ومفيدة**
- ✅ **يمكن إضافة المزيد بسهولة**

---

## 🚀 **إضافة المزيد من التصويتات:**

```sql
-- مثال لإضافة تصويت جديد
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    (SELECT id FROM profiles LIMIT 1),  -- استخدام أول مستخدم
    'ما هو لونك المفضل؟',
    'public',
    'general', 
    'unlimited',
    true,
    true
) RETURNING id;

-- إضافة خيارات للتصويت الجديد
INSERT INTO poll_options (poll_id, text, option_order) VALUES
((SELECT id FROM polls WHERE question = 'ما هو لونك المفضل؟'), 'أحمر 🔴', 0),
((SELECT id FROM polls WHERE question = 'ما هو لونك المفضل؟'), 'أزرق 🔵', 1),
((SELECT id FROM polls WHERE question = 'ما هو لونك المفضل؟'), 'أخضر 🟢', 2),
((SELECT id FROM polls WHERE question = 'ما هو لونك المفضل؟'), 'أصفر 🟡', 3);
```

---

## 🎯 **الخلاصة:**

1. **استخدم `polls_system_fixed.sql` فقط**
2. **تأكد من وجود مستخدمين في `profiles`**
3. **استخدم `SELECT insert_sample_polls();`**
4. **تحقق من النتائج**

**هذا الملف مضمون 100% بدون أخطاء!**
