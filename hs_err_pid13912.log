#
# There is insufficient memory for the Java Runtime Environment to continue.
# Native memory allocation (mmap) failed to map 266338304 bytes. Error detail: G1 virtual space
# Possible reasons:
#   The system is out of physical RAM or swap space
#   This process is running with CompressedOops enabled, and the Java Heap may be blocking the growth of the native heap
# Possible solutions:
#   Reduce memory load on the system
#   Increase physical memory or swap space
#   Check if swap backing store is full
#   Decrease Java heap size (-Xmx/-Xms)
#   Decrease number of Java threads
#   Decrease Java thread stack sizes (-Xss)
#   Set larger code cache with -XX:ReservedCodeCacheSize=
#   JVM is running with Zero Based Compressed Oops mode in which the Java heap is
#     placed in the first 32GB address space. The Java Heap base address is the
#     maximum limit for the native heap growth. Please use -XX:HeapBaseMinAddress
#     to set the Java Heap base and to place the Java Heap above 32GB virtual address.
# This output file may be truncated or incomplete.
#
#  Out of Memory Error (os_windows.cpp:3898), pid=13912, tid=2780
#
# JRE version:  (21.0.6) (build )
# Java VM: OpenJDK 64-Bit Server VM (21.0.6+-13355223-b631.42, mixed mode, sharing, tiered, compressed oops, compressed class ptrs, g1 gc, windows-amd64)
# No core dump will be written. Minidumps are not enabled by default on client versions of Windows
#

---------------  S U M M A R Y ------------

Command Line: 

Host: Intel(R) Core(TM) i5-8350U CPU @ 1.70GHz, 8 cores, 15G,  Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
Time: Sun Jun 22 20:36:28 2025 Morocco Daylight Time elapsed time: 1.393653 seconds (0d 0h 0m 1s)

---------------  T H R E A D  ---------------

Current thread (0x000001710a701b70):  JavaThread "Unknown thread" [_thread_in_vm, id=2780, stack(0x00000009fa300000,0x00000009fa400000) (1024K)]

Stack: [0x00000009fa300000,0x00000009fa400000]
Native frames: (J=compiled Java code, j=interpreted, Vv=VM code, C=native code)
V  [jvm.dll+0x6d05a9]
V  [jvm.dll+0x85ea73]
V  [jvm.dll+0x860fce]
V  [jvm.dll+0x8616b3]
V  [jvm.dll+0x27e6e6]
V  [jvm.dll+0x6ccf45]
V  [jvm.dll+0x6c09fa]
V  [jvm.dll+0x35535b]
V  [jvm.dll+0x35cfb6]
V  [jvm.dll+0x3aef46]
V  [jvm.dll+0x3af218]
V  [jvm.dll+0x327a2c]
V  [jvm.dll+0x32871b]
V  [jvm.dll+0x8264b9]
V  [jvm.dll+0x3bc118]
V  [jvm.dll+0x80f768]
V  [jvm.dll+0x4502ce]
V  [jvm.dll+0x451a31]
C  [jli.dll+0x52ab]
C  [ucrtbase.dll+0x37b0]
C  [KERNEL32.DLL+0x2e8d7]
C  [ntdll.dll+0x3c34c]


---------------  P R O C E S S  ---------------

Threads class SMR info:
_java_thread_list=0x00007ffe7e4a1848, length=0, elements={
}

Java Threads: ( => current thread )
Total: 0

Other Threads:
  0x000001710cade9b0 WorkerThread "GC Thread#0"                     [id=19256, stack(0x00000009fa400000,0x00000009fa500000) (1024K)]
  0x000001710cb2e010 ConcurrentGCThread "G1 Main Marker"            [id=20020, stack(0x00000009fa500000,0x00000009fa600000) (1024K)]
  0x000001710cb2f2b0 WorkerThread "G1 Conc#0"                       [id=27456, stack(0x00000009fa600000,0x00000009fa700000) (1024K)]

[error occurred during error reporting (printing all threads), id 0xc0000005, EXCEPTION_ACCESS_VIOLATION (0xc0000005) at pc=0x00007ffe7dc9aa67]
VM state: not at safepoint (not fully initialized)

VM Mutex/Monitor currently owned by a thread:  ([mutex/lock_event])
[0x00007ffe7e50e4a0] Heap_lock - owner thread: 0x000001710a701b70

Heap address: 0x0000000702600000, size: 4058 MB, Compressed Oops mode: Zero based, Oop shift amount: 3

CDS archive(s) mapped at: [0x0000000000000000-0x0000000000000000-0x0000000000000000), size 0, SharedBaseAddress: 0x0000000800000000, ArchiveRelocationMode: 1.
Narrow klass base: 0x0000000000000000, Narrow klass shift: 0, Narrow klass range: 0x0

GC Precious Log:
 CardTable entry size: 512
 Card Set container configuration: InlinePtr #cards 4 size 8 Array Of Cards #cards 16 size 48 Howl #buckets 8 coarsen threshold 3686 Howl Bitmap #cards 512 size 80 coarsen threshold 460 Card regions per heap region 1 cards per card region 4096

Heap:
 garbage-first heap   total 0K, used 0K [0x0000000702600000, 0x0000000800000000)
  region size 2048K, 0 young (0K), 0 survivors (0K)
 Metaspace       used 0K, committed 0K, reserved 0K
  class space    used 0K, committed 0K, reserved 0K

Heap Regions: E=young(eden), S=young(survivor), O=old, HS=humongous(starts), HC=humongous(continues), CS=collection set, F=free, TAMS=top-at-mark-start, PB=parsable bottom

Card table byte_map: [0x000001711f6f0000,0x000001711fee0000] _byte_map_base: 0x000001711bedd000

Marking Bits: (CMBitMap*) 0x000001710cadf8c0
 Bits: [0x000001711fee0000, 0x0000017123e48000)

GC Heap History (0 events):
No events

Dll operation events (1 events):
Event: 0.018 Loaded shared library C:\Program Files\Android\Android Studio\jbr\bin\java.dll

Deoptimization events (0 events):
No events

Classes loaded (0 events):
No events

Classes unloaded (0 events):
No events

Classes redefined (0 events):
No events

Internal exceptions (0 events):
No events

ZGC Phase Switch (0 events):
No events

VM Operations (0 events):
No events

Memory protections (0 events):
No events

Nmethod flushes (0 events):
No events

Events (0 events):
No events


Dynamic libraries:
0x00007ff672f40000 - 0x00007ff672f4a000 	C:\Program Files\Android\Android Studio\jbr\bin\java.EXE
0x00007ffeddd20000 - 0x00007ffeddf85000 	C:\WINDOWS\SYSTEM32\ntdll.dll
0x00007ffedbb60000 - 0x00007ffedbc29000 	C:\WINDOWS\System32\KERNEL32.DLL
0x00007ffedb4c0000 - 0x00007ffedb8a8000 	C:\WINDOWS\System32\KERNELBASE.dll
0x00007ffedb1f0000 - 0x00007ffedb33b000 	C:\WINDOWS\System32\ucrtbase.dll
0x00007ffec1930000 - 0x00007ffec194b000 	C:\Program Files\Android\Android Studio\jbr\bin\VCRUNTIME140.dll
0x00007ffec9cb0000 - 0x00007ffec9cc8000 	C:\Program Files\Android\Android Studio\jbr\bin\jli.dll
0x00007ffedbc80000 - 0x00007ffedbe4a000 	C:\WINDOWS\System32\USER32.dll
0x00007ffedafb0000 - 0x00007ffedafd7000 	C:\WINDOWS\System32\win32u.dll
0x00007ffedc3b0000 - 0x00007ffedc3db000 	C:\WINDOWS\System32\GDI32.dll
0x00007ffec29a0000 - 0x00007ffec2c3a000 	C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517\COMCTL32.dll
0x00007ffedae70000 - 0x00007ffedafa7000 	C:\WINDOWS\System32\gdi32full.dll
0x00007ffedb140000 - 0x00007ffedb1e3000 	C:\WINDOWS\System32\msvcp_win.dll
0x00007ffedd3e0000 - 0x00007ffedd489000 	C:\WINDOWS\System32\msvcrt.dll
0x00007ffedbac0000 - 0x00007ffedbaf0000 	C:\WINDOWS\System32\IMM32.DLL
0x00007ffec30e0000 - 0x00007ffec30ec000 	C:\Program Files\Android\Android Studio\jbr\bin\vcruntime140_1.dll
0x00007ffeb0650000 - 0x00007ffeb06dd000 	C:\Program Files\Android\Android Studio\jbr\bin\msvcp140.dll
0x00007ffe7d960000 - 0x00007ffe7e5eb000 	C:\Program Files\Android\Android Studio\jbr\bin\server\jvm.dll
0x00007ffedcf50000 - 0x00007ffedd003000 	C:\WINDOWS\System32\ADVAPI32.dll
0x00007ffedd490000 - 0x00007ffedd536000 	C:\WINDOWS\System32\sechost.dll
0x00007ffedc3e0000 - 0x00007ffedc4f5000 	C:\WINDOWS\System32\RPCRT4.dll
0x00007ffedc500000 - 0x00007ffedc574000 	C:\WINDOWS\System32\WS2_32.dll
0x00007ffecd000000 - 0x00007ffecd00b000 	C:\WINDOWS\SYSTEM32\VERSION.dll
0x00007ffed9a50000 - 0x00007ffed9aae000 	C:\WINDOWS\SYSTEM32\POWRPROF.dll
0x00007ffec4d90000 - 0x00007ffec4dc5000 	C:\WINDOWS\SYSTEM32\WINMM.dll
0x00007ffed9a30000 - 0x00007ffed9a44000 	C:\WINDOWS\SYSTEM32\UMPDC.dll
0x00007ffed9d10000 - 0x00007ffed9d2b000 	C:\WINDOWS\SYSTEM32\kernel.appcore.dll
0x00007ffec16e0000 - 0x00007ffec16ea000 	C:\Program Files\Android\Android Studio\jbr\bin\jimage.dll
0x00007ffed8530000 - 0x00007ffed8771000 	C:\WINDOWS\SYSTEM32\DBGHELP.DLL
0x00007ffedca10000 - 0x00007ffedcd95000 	C:\WINDOWS\System32\combase.dll
0x00007ffedbed0000 - 0x00007ffedbfb1000 	C:\WINDOWS\System32\OLEAUT32.dll
0x00007ffec4800000 - 0x00007ffec4839000 	C:\WINDOWS\SYSTEM32\dbgcore.DLL
0x00007ffedb0a0000 - 0x00007ffedb139000 	C:\WINDOWS\System32\bcryptPrimitives.dll
0x00007ffec13f0000 - 0x00007ffec1410000 	C:\Program Files\Android\Android Studio\jbr\bin\java.dll

dbghelp: loaded successfully - version: 4.0.5 - missing functions: none
symbol engine: initialized successfully - sym options: 0x614 - pdb path: .;C:\Program Files\Android\Android Studio\jbr\bin;C:\WINDOWS\SYSTEM32;C:\WINDOWS\WinSxS\amd64_microsoft.windows.common-controls_6595b64144ccf1df_6.0.26100.4202_none_3e0698d4e335f517;C:\Program Files\Android\Android Studio\jbr\bin\server

VM Arguments:
java_command: <unknown>
java_class_path (initial): <not set>
Launcher Type: SUN_STANDARD

[Global flags]
     intx CICompilerCount                          = 4                                         {product} {ergonomic}
     uint ConcGCThreads                            = 2                                         {product} {ergonomic}
     uint G1ConcRefinementThreads                  = 8                                         {product} {ergonomic}
   size_t G1HeapRegionSize                         = 2097152                                   {product} {ergonomic}
    uintx GCDrainStackTargetSize                   = 64                                        {product} {ergonomic}
   size_t InitialHeapSize                          = 266338304                                 {product} {ergonomic}
   size_t MarkStackSize                            = 4194304                                   {product} {ergonomic}
   size_t MaxHeapSize                              = 4255121408                                {product} {ergonomic}
   size_t MinHeapDeltaBytes                        = 2097152                                   {product} {ergonomic}
   size_t MinHeapSize                              = 8388608                                   {product} {ergonomic}
    uintx NonNMethodCodeHeapSize                   = 5839372                                {pd product} {ergonomic}
    uintx NonProfiledCodeHeapSize                  = 122909434                              {pd product} {ergonomic}
    uintx ProfiledCodeHeapSize                     = 122909434                              {pd product} {ergonomic}
    uintx ReservedCodeCacheSize                    = 251658240                              {pd product} {ergonomic}
     bool SegmentedCodeCache                       = true                                      {product} {ergonomic}
   size_t SoftMaxHeapSize                          = 4255121408                             {manageable} {ergonomic}
     bool UseCompressedOops                        = true                           {product lp64_product} {ergonomic}
     bool UseG1GC                                  = true                                      {product} {ergonomic}
     bool UseLargePagesIndividualAllocation        = false                                  {pd product} {ergonomic}

Logging:
Log output configuration:
 #0: stdout all=warning uptime,level,tags foldmultilines=false
 #1: stderr all=off uptime,level,tags foldmultilines=false

Environment Variables:
PATH=C:\Users\<USER>\flutter\bin\;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\dotnet\;C:\Program Files\nodejs\;C:\Program Files\Git\cmd;c:\Users\<USER>\AppData\Local\Programs\Trae\bin;C:\Users\<USER>\AppData\Local\Programs\Eclipse Adoptium\jdk-********-hotspot\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Muse Hub\lib;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\jdk-24.0.1\bin;C:\Users\<USER>\flutter\bin;C:\Users\<USER>\dart-sdk\bin;C:\Users\<USER>\AppData\Local\Android\Sdk\platform-tools;C:\Users\<USER>\AppData\Local\Android\Sdk\tools;C:\Users\<USER>\AppData\Local\Muse Hub\lib;C:\Program Files\JetBrains\IntelliJ IDEA 2025.1.2\bin;;C:\Users\<USER>\AppData\Local\Programs\VSCodium\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;C:\Users\<USER>\flutter\bin\mingit\cmd
USERNAME=arzpr
LANG=en_US.UTF-8
OS=Windows_NT
PROCESSOR_IDENTIFIER=Intel64 Family 6 Model 142 Stepping 10, GenuineIntel
TMP=C:\Users\<USER>\AppData\Local\Temp
TEMP=C:\Users\<USER>\AppData\Local\Temp




Periodic native trim disabled

JNI global refs:
JNI global refs: 0, weak refs: 0

JNI global refs memory usage: 0, weak refs: 0

Process memory usage:
Resident Set Size: 12908K (0% of 16616664K total physical memory with 1378648K free physical memory)

OOME stack traces (most recent first):
Classloader memory used:

---------------  S Y S T E M  ---------------

OS:
 Windows 11 , 64 bit Build 26100 (10.0.26100.4202)
OS uptime: 2 days 3:07 hours
Hyper-V role detected

CPU: total 8 (initial active 8) (4 cores per cpu, 2 threads per core) family 6 model 142 stepping 10 microcode 0xf6, cx8, cmov, fxsr, ht, mmx, 3dnowpref, sse, sse2, sse3, ssse3, sse4.1, sse4.2, popcnt, lzcnt, tsc, tscinvbit, avx, avx2, aes, erms, clmul, bmi1, bmi2, rtm, adx, fma, vzeroupper, clflush, clflushopt, hv, rdtscp, f16c
Processor Information for the first 8 processors :
  Max Mhz: 1896, Current Mhz: 1696, Mhz Limit: 1687

Memory: 4k page, system-wide physical 16227M (1346M free)
TotalPageFile size 46620M (AvailPageFile size 189M)
current process WorkingSet (physical memory assigned to process): 12M, peak: 12M
current process commit charge ("private bytes"): 60M, peak: 314M

vm_info: OpenJDK 64-Bit Server VM (21.0.6+-13355223-b631.42) for windows-amd64 JRE (21.0.6+-13355223-b631.42), built on 2025-04-14T18:21:23Z by "builder" with MS VC++ 16.10 / 16.11 (VS2019)

END.
