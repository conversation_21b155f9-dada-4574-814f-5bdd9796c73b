import 'package:flutter/material.dart';
import '../services/notes_service.dart';

class NotesSettingsPage extends StatefulWidget {
  const NotesSettingsPage({super.key});

  @override
  State<NotesSettingsPage> createState() => _NotesSettingsPageState();
}

class _NotesSettingsPageState extends State<NotesSettingsPage> {
  final NotesService _notesService = NotesService();
  
  Map<String, int> _stats = {};
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _loadStats();
  }

  Future<void> _loadStats() async {
    setState(() => _loading = true);
    try {
      final stats = await _notesService.getNotesStats();
      setState(() => _stats = stats);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الإحصائيات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'إعدادات المذكرة',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.deepPurple[600],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _loading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.all(16),
              children: [
                // الإحصائيات
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.analytics, color: Colors.deepPurple[600]),
                            const SizedBox(width: 8),
                            const Text(
                              'الإحصائيات',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildStatItem(
                              Icons.note,
                              'المذكرات',
                              '${_stats['active_notes'] ?? 0}',
                              Colors.deepPurple,
                            ),
                            _buildStatItem(
                              Icons.task_alt,
                              'المهام',
                              '${_stats['total_tasks'] ?? 0}',
                              Colors.blue,
                            ),
                            _buildStatItem(
                              Icons.archive,
                              'الأرشيف',
                              '${_stats['archived_notes'] ?? 0}',
                              Colors.grey,
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 16),
                        
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildStatItem(
                              Icons.check_circle,
                              'مكتملة',
                              '${_stats['completed_tasks'] ?? 0}',
                              Colors.green,
                            ),
                            _buildStatItem(
                              Icons.schedule,
                              'معلقة',
                              '${_stats['pending_tasks'] ?? 0}',
                              Colors.orange,
                            ),
                            _buildStatItem(
                              Icons.push_pin,
                              'مثبتة',
                              '${_stats['pinned_notes'] ?? 0}',
                              Colors.amber,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // إدارة البيانات
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.storage, color: Colors.deepPurple[600]),
                            const SizedBox(width: 8),
                            const Text(
                              'إدارة البيانات',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        ListTile(
                          leading: const Icon(Icons.download),
                          title: const Text('تصدير المذكرات'),
                          subtitle: const Text('تحميل نسخة من جميع مذكراتك'),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: _exportNotes,
                        ),
                        
                        ListTile(
                          leading: const Icon(Icons.refresh),
                          title: const Text('تحديث الإحصائيات'),
                          subtitle: const Text('إعادة حساب إحصائيات المذكرات'),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: _loadStats,
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // إعدادات خطيرة
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.warning, color: Colors.red[600]),
                            const SizedBox(width: 8),
                            const Text(
                              'إعدادات خطيرة',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        ListTile(
                          leading: const Icon(Icons.delete_forever, color: Colors.red),
                          title: const Text(
                            'حذف جميع المذكرات',
                            style: TextStyle(color: Colors.red),
                          ),
                          subtitle: const Text('حذف جميع المذكرات والبيانات المرتبطة بها'),
                          trailing: const Icon(Icons.arrow_forward_ios, color: Colors.red),
                          onTap: _deleteAllNotes,
                        ),
                      ],
                    ),
                  ),
                ),
                
                const SizedBox(height: 16),
                
                // معلومات التطبيق
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info, color: Colors.deepPurple[600]),
                            const SizedBox(width: 8),
                            const Text(
                              'معلومات',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        ListTile(
                          leading: const Icon(Icons.help_outline),
                          title: const Text('مساعدة'),
                          subtitle: const Text('كيفية استخدام قسم المذكرة'),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: _showHelp,
                        ),
                        
                        ListTile(
                          leading: const Icon(Icons.info_outline),
                          title: const Text('حول قسم المذكرة'),
                          subtitle: const Text('معلومات عن الميزات والإمكانيات'),
                          trailing: const Icon(Icons.arrow_forward_ios),
                          onTap: _showAbout,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildStatItem(IconData icon, String label, String value, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  void _exportNotes() async {
    try {
      final exportData = await _notesService.exportNotes();
      
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('تصدير المذكرات'),
            content: SingleChildScrollView(
              child: Text(
                'تم تصدير البيانات بنجاح!\n\n'
                'تاريخ التصدير: ${exportData['export_date']}\n'
                'عدد المذكرات: ${exportData['notes_count']}\n'
                'الإحصائيات: ${exportData['statistics']}',
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تصدير البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _deleteAllNotes() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحذير!'),
        content: const Text(
          'هل أنت متأكد من حذف جميع المذكرات؟\n\n'
          'سيتم حذف:\n'
          '• جميع المذكرات والمهام\n'
          '• جميع التذكيرات\n'
          '• جميع الوسوم والفئات\n'
          '• جميع الإعدادات\n\n'
          'هذا الإجراء لا يمكن التراجع عنه!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _notesService.deleteAllNotes();
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف جميع المذكرات بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  _loadStats(); // إعادة تحميل الإحصائيات
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في الحذف: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('حذف نهائياً', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('مساعدة'),
        content: const SingleChildScrollView(
          child: Text(
            'قسم المذكرة يتيح لك:\n\n'
            '📝 إنشاء مذكرات شخصية\n'
            '✅ إدارة المهام والمشاريع\n'
            '⏰ تعيين تذكيرات ومواعيد\n'
            '📚 كتابة يومياتك\n'
            '🏷️ تصنيف المذكرات بالوسوم\n'
            '📌 تثبيت المذكرات المهمة\n'
            '🗂️ أرشفة المذكرات القديمة\n'
            '🔍 البحث السريع\n'
            '📊 عرض الإحصائيات\n'
            '💾 الحفظ التلقائي\n\n'
            'لأي استفسارات إضافية، يرجى التواصل مع الدعم الفني.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  void _showAbout() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حول قسم المذكرة'),
        content: const SingleChildScrollView(
          child: Text(
            'قسم المذكرة هو مساحة شخصية متكاملة لإدارة:\n\n'
            '🎯 المهام والأهداف\n'
            '📖 المذكرات والأفكار\n'
            '⏰ التذكيرات والمواعيد\n'
            '📔 اليوميات الشخصية\n\n'
            'المميزات:\n'
            '• واجهة سهلة وأنيقة\n'
            '• حفظ تلقائي للتغييرات\n'
            '• تصنيف متقدم بالفئات والوسوم\n'
            '• بحث سريع وذكي\n'
            '• إحصائيات مفصلة\n'
            '• حماية وخصوصية تامة\n\n'
            'الإصدار: 1.0.0\n'
            'تم التطوير بواسطة فريق أرزاوو',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }
}
