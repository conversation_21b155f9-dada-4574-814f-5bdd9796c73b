import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/job_seeker.dart';
import '../services/job_seekers_service.dart';

class JobSeekerCard extends StatefulWidget {
  final JobSeeker jobSeeker;
  final VoidCallback? onTap;

  const JobSeekerCard({
    super.key,
    required this.jobSeeker,
    this.onTap,
  });

  @override
  State<JobSeekerCard> createState() => _JobSeekerCardState();
}

class _JobSeekerCardState extends State<JobSeekerCard> {
  final JobSeekersService _jobSeekersService = JobSeekersService();
  bool _isLiked = false;
  bool _isSaved = false;
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    _loadInteractionStatus();
  }

  Future<void> _loadInteractionStatus() async {
    try {
      final liked = await _jobSeekersService.isLiked(widget.jobSeeker.id);
      final saved = await _jobSeekersService.isSaved(widget.jobSeeker.id);
      
      if (mounted) {
        setState(() {
          _isLiked = liked;
          _isSaved = saved;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل حالة التفاعل: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          _incrementViews();
          if (widget.onTap != null) widget.onTap!();
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات المستخدم الأساسية
              _buildUserHeader(),
              
              const SizedBox(height: 12),
              
              // المهارات والخبرة
              _buildSkillsAndExperience(),
              
              const SizedBox(height: 12),
              
              // الوصف
              _buildDescription(),
              
              const SizedBox(height: 12),
              
              // معلومات إضافية
              _buildAdditionalInfo(),
              
              const SizedBox(height: 12),
              
              // أزرار التفاعل
              _buildActionButtons(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserHeader() {
    return Row(
      children: [
        // صورة المستخدم
        CircleAvatar(
          radius: 30,
          backgroundColor: Colors.indigo[100],
          backgroundImage: widget.jobSeeker.profileImage != null
              ? NetworkImage(widget.jobSeeker.profileImage!)
              : null,
          child: widget.jobSeeker.profileImage == null
              ? Icon(
                  Icons.person,
                  color: Colors.indigo[600],
                  size: 30,
                )
              : null,
        ),
        
        const SizedBox(width: 12),
        
        // معلومات المستخدم
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.jobSeeker.fullName,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(
                    widget.jobSeeker.category.icon,
                    size: 16,
                    color: widget.jobSeeker.category.color,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    widget.jobSeeker.category.arabicName,
                    style: TextStyle(
                      fontSize: 14,
                      color: widget.jobSeeker.category.color,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Icon(Icons.location_on, size: 14, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${widget.jobSeeker.currentCity}, ${widget.jobSeeker.currentCountry}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(width: 12),
                  Icon(Icons.cake, size: 14, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    widget.jobSeeker.formattedAge,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        
        // نوع العمل المطلوب
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: widget.jobSeeker.preferredJobType.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: widget.jobSeeker.preferredJobType.color.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                widget.jobSeeker.preferredJobType.icon,
                size: 14,
                color: widget.jobSeeker.preferredJobType.color,
              ),
              const SizedBox(width: 4),
              Text(
                widget.jobSeeker.preferredJobType.arabicName,
                style: TextStyle(
                  fontSize: 11,
                  color: widget.jobSeeker.preferredJobType.color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSkillsAndExperience() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // المهارات
        if (widget.jobSeeker.skills.isNotEmpty) ...[
          Row(
            children: [
              Icon(Icons.star, size: 16, color: Colors.amber[600]),
              const SizedBox(width: 4),
              const Text(
                'المهارات:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Wrap(
            spacing: 6,
            runSpacing: 4,
            children: widget.jobSeeker.skills.take(4).map((skill) {
              return Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Text(
                  skill,
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.blue[700],
                  ),
                ),
              );
            }).toList(),
          ),
          if (widget.jobSeeker.skills.length > 4)
            Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Text(
                '+${widget.jobSeeker.skills.length - 4} مهارة أخرى',
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
        ],
        
        const SizedBox(height: 8),
        
        // الخبرة واللغات
        Row(
          children: [
            Icon(Icons.work_history, size: 16, color: Colors.green[600]),
            const SizedBox(width: 4),
            Text(
              'الخبرة: ${widget.jobSeeker.formattedExperience}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.green[700],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: 16),
            Icon(Icons.language, size: 16, color: Colors.purple[600]),
            const SizedBox(width: 4),
            Expanded(
              child: Text(
                widget.jobSeeker.languagesText,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.purple[700],
                  fontWeight: FontWeight.w500,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDescription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.description, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 4),
            const Text(
              'نبذة:',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          widget.jobSeeker.description,
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey[700],
            height: 1.4,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildAdditionalInfo() {
    return Row(
      children: [
        // الجنسية
        Icon(Icons.flag, size: 14, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          widget.jobSeeker.nationality,
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
        
        const SizedBox(width: 16),
        
        // الحالة الاجتماعية
        Icon(Icons.family_restroom, size: 14, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(
          widget.jobSeeker.maritalStatus.arabicName,
          style: TextStyle(fontSize: 12, color: Colors.grey[600]),
        ),
        
        const Spacer(),
        
        // عدد المشاهدات
        Icon(Icons.visibility, size: 14, color: Colors.grey[500]),
        const SizedBox(width: 4),
        Text(
          '${widget.jobSeeker.viewsCount}',
          style: TextStyle(fontSize: 12, color: Colors.grey[500]),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        // زر الإعجاب
        _buildActionButton(
          icon: _isLiked ? Icons.favorite : Icons.favorite_border,
          label: '${widget.jobSeeker.likesCount}',
          color: _isLiked ? Colors.red : Colors.grey[600]!,
          onTap: _toggleLike,
        ),
        
        const SizedBox(width: 16),
        
        // زر الحفظ
        _buildActionButton(
          icon: _isSaved ? Icons.bookmark : Icons.bookmark_border,
          label: 'حفظ',
          color: _isSaved ? Colors.blue : Colors.grey[600]!,
          onTap: _toggleSave,
        ),
        
        const Spacer(),
        
        // زر الاتصال
        if (widget.jobSeeker.phoneNumber.isNotEmpty)
          ElevatedButton.icon(
            onPressed: () => _makePhoneCall(widget.jobSeeker.phoneNumber),
            icon: const Icon(Icons.phone, size: 16),
            label: const Text('اتصال'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              minimumSize: Size.zero,
            ),
          ),
        
        const SizedBox(width: 8),
        
        // زر المراسلة
        ElevatedButton.icon(
          onPressed: () => _sendMessage(),
          icon: const Icon(Icons.message, size: 16),
          label: const Text('مراسلة'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.indigo[600],
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            minimumSize: Size.zero,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _incrementViews() async {
    try {
      await _jobSeekersService.incrementViews(widget.jobSeeker.id);
    } catch (e) {
      debugPrint('خطأ في زيادة المشاهدات: $e');
    }
  }

  Future<void> _toggleLike() async {
    if (_loading) return;
    
    setState(() => _loading = true);
    try {
      await _jobSeekersService.toggleLike(widget.jobSeeker.id);
      setState(() => _isLiked = !_isLiked);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الإعجاب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }

  Future<void> _toggleSave() async {
    if (_loading) return;
    
    setState(() => _loading = true);
    try {
      await _jobSeekersService.toggleSave(widget.jobSeeker.id);
      setState(() => _isSaved = !_isSaved);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isSaved ? 'تم حفظ الملف المهني' : 'تم إلغاء حفظ الملف المهني'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الحفظ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        throw 'لا يمكن إجراء الاتصال';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الاتصال: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _sendMessage() {
    // فتح نافذة المراسلة أو التطبيق المناسب
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة المراسلة ستكون متاحة قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
