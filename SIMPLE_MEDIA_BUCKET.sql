-- إنشاء bucket media بسيط
-- Create simple media bucket

-- 1. إنشاء bucket media
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'media',
  'media', 
  true,
  52428800,  -- 50MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/avi', 'video/mov', 'video/quicktime']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 52428800,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/avi', 'video/mov', 'video/quicktime'];

-- 2. حذف السياسات القديمة
DROP POLICY IF EXISTS "media_select_policy" ON storage.objects;
DROP POLICY IF EXISTS "media_insert_policy" ON storage.objects;
DROP POLICY IF EXISTS "media_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "media_delete_policy" ON storage.objects;

-- 3. إنشاء السياسات الجديدة
CREATE POLICY "media_select_policy"
ON storage.objects FOR SELECT
USING (bucket_id = 'media');

CREATE POLICY "media_insert_policy"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'media' 
  AND auth.uid() IS NOT NULL
);

CREATE POLICY "media_update_policy"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'media' 
  AND auth.uid() IS NOT NULL
);

CREATE POLICY "media_delete_policy"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'media' 
  AND auth.uid() IS NOT NULL
);

-- 4. التحقق من إنشاء bucket
SELECT 
    id, 
    name, 
    public, 
    file_size_limit
FROM storage.buckets 
WHERE id = 'media';

-- 5. رسالة نجاح
SELECT 'تم إنشاء bucket media بنجاح!' as result; 