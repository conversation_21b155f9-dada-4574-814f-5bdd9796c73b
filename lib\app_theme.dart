import 'package:flutter/material.dart';

class AppTheme {
  // ألوان العلامة التجارية
  // اللون الأساسي الأحمر الغامق المفضّل
  static const Color primaryColor = Color(0xFFC62828); // أحمر غامق
  static const Color secondaryColor = Color(0xFFEF5350); // أحمر فاتح

  // توليد تدرّج مادة للون الأساسى (لأزرار وخلافه)
  static const MaterialColor primarySwatch = MaterialColor(
    0xFFC62828,
    <int, Color>{
      50: Color(0xFFFFEBEE),
      100: Color(0xFFFFCDD2),
      200: Color(0xFFEF9A9A),
      300: Color(0xFFE57373),
      400: Color(0xFFEF5350),
      500: Color(0xFFF44336),
      600: Color(0xFFE53935),
      700: Color(0xFFD32F2F),
      800: Color(0xFFC62828),
      900: Color(0xFFB71C1C),
    },
  );

  // مخطط الألوان الموحّد (مخصص لتجنب الألوان الوردية)
  static const ColorScheme _lightColorScheme = ColorScheme.light(
    primary: primaryColor,
    secondary: secondaryColor,
    surface: Colors.white,
    error: Colors.red,
    onPrimary: Colors.white,
    onSecondary: Colors.white,
    onSurface: Colors.black87,
    onError: Colors.white,
  );

  static const ColorScheme _darkColorScheme = ColorScheme.dark(
    primary: primaryColor,
    secondary: secondaryColor,
    surface: Color(0xFF121212),
    error: Colors.red,
    onPrimary: Colors.white,
    onSecondary: Colors.white,
    onSurface: Colors.white,
    onError: Colors.white,
  );

  // ثيم فاتح
  static final ThemeData lightTheme = ThemeData(
    useMaterial3: true,
    colorScheme: _lightColorScheme,
    scaffoldBackgroundColor: Colors.white,
    cardColor: Colors.white,
    fontFamily: 'Arial',
    appBarTheme: const AppBarTheme(backgroundColor: Colors.white, foregroundColor: Colors.black87, elevation: 1),
    snackBarTheme: const SnackBarThemeData(behavior: SnackBarBehavior.floating),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(backgroundColor: primaryColor, foregroundColor: Colors.white),
    ),
  );

  // ثيم داكن
  static final ThemeData darkTheme = ThemeData(
    useMaterial3: true,
    colorScheme: _darkColorScheme,
    scaffoldBackgroundColor: Colors.grey.shade900,
    fontFamily: 'Arial',
    appBarTheme: const AppBarTheme(backgroundColor: Colors.black, foregroundColor: Colors.white, elevation: 1),
    snackBarTheme: const SnackBarThemeData(behavior: SnackBarBehavior.floating),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(backgroundColor: primaryColor, foregroundColor: Colors.white),
    ),
  );
} 