-- =============================================================
--  دالة حذف المجتمع الآمنة
--  Safe Community Deletion Function
-- =============================================================

-- هذا السكريپت ينشئ دالة لحذف المجتمع بالترتيب الصحيح

-- 1) إنشاء دالة حذف المجتمع الآمنة
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION public.delete_community_safe(
  p_community_id UUID,
  p_user_id UUID DEFAULT NULL
)
RETURNS TABLE(success BOOLEAN, message TEXT, details TEXT)
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  community_record RECORD;
  post_ids UUID[];
  comment_ids UUID[];
  deletion_count INTEGER;
  total_deleted INTEGER := 0;
BEGIN
  -- التحقق من وجود المجتمع
  SELECT * INTO community_record FROM communities WHERE id = p_community_id;
  
  IF NOT FOUND THEN
    RETURN QUERY SELECT false, 'المجتمع غير موجود', 'Community not found';
    RETURN;
  END IF;
  
  -- التحقق من الصلاحيات (إذا تم تمرير user_id)
  IF p_user_id IS NOT NULL AND community_record.owner_id != p_user_id THEN
    RETURN QUERY SELECT false, 'ليس لديك صلاحية لحذف هذا المجتمع', 'Permission denied';
    RETURN;
  END IF;
  
  -- بدء عملية الحذف بالترتيب الصحيح
  
  -- 1. الحصول على معرفات المنشورات
  SELECT ARRAY_AGG(id) INTO post_ids 
  FROM community_posts 
  WHERE community_id = p_community_id;
  
  -- 2. الحصول على معرفات التعليقات (إذا وجدت منشورات)
  IF post_ids IS NOT NULL AND array_length(post_ids, 1) > 0 THEN
    SELECT ARRAY_AGG(id) INTO comment_ids 
    FROM community_comments 
    WHERE post_id = ANY(post_ids);
  END IF;
  
  -- 3. حذف تصويتات التعليقات
  IF comment_ids IS NOT NULL AND array_length(comment_ids, 1) > 0 THEN
    DELETE FROM community_comment_votes WHERE comment_id = ANY(comment_ids);
    GET DIAGNOSTICS deletion_count = ROW_COUNT;
    total_deleted := total_deleted + deletion_count;
    RAISE NOTICE 'حذف % تصويت تعليق', deletion_count;
  END IF;
  
  -- 4. حذف التعليقات
  IF post_ids IS NOT NULL AND array_length(post_ids, 1) > 0 THEN
    DELETE FROM community_comments WHERE post_id = ANY(post_ids);
    GET DIAGNOSTICS deletion_count = ROW_COUNT;
    total_deleted := total_deleted + deletion_count;
    RAISE NOTICE 'حذف % تعليق', deletion_count;
  END IF;
  
  -- 5. حذف تصويتات المنشورات
  IF post_ids IS NOT NULL AND array_length(post_ids, 1) > 0 THEN
    DELETE FROM community_post_votes WHERE post_id = ANY(post_ids);
    GET DIAGNOSTICS deletion_count = ROW_COUNT;
    total_deleted := total_deleted + deletion_count;
    RAISE NOTICE 'حذف % تصويت منشور', deletion_count;
  END IF;
  
  -- 6. حذف المنشورات
  DELETE FROM community_posts WHERE community_id = p_community_id;
  GET DIAGNOSTICS deletion_count = ROW_COUNT;
  total_deleted := total_deleted + deletion_count;
  RAISE NOTICE 'حذف % منشور', deletion_count;
  
  -- 7. حذف الأعضاء
  DELETE FROM community_members WHERE community_id = p_community_id;
  GET DIAGNOSTICS deletion_count = ROW_COUNT;
  total_deleted := total_deleted + deletion_count;
  RAISE NOTICE 'حذف % عضو', deletion_count;
  
  -- 8. حذف المجتمع نفسه
  DELETE FROM communities WHERE id = p_community_id;
  GET DIAGNOSTICS deletion_count = ROW_COUNT;
  
  IF deletion_count > 0 THEN
    total_deleted := total_deleted + deletion_count;
    RETURN QUERY SELECT 
      true, 
      'تم حذف المجتمع بنجاح', 
      'حذف إجمالي: ' || total_deleted::text || ' سجل';
  ELSE
    RETURN QUERY SELECT 
      false, 
      'فشل في حذف المجتمع', 
      'لم يتم حذف المجتمع من الجدول الرئيسي';
  END IF;
  
EXCEPTION 
  WHEN OTHERS THEN
    RETURN QUERY SELECT 
      false, 
      'خطأ في حذف المجتمع: ' || SQLERRM, 
      'تفاصيل الخطأ: ' || SQLSTATE;
END;
$$ LANGUAGE plpgsql;

-- 2) اختبار الدالة
-- -------------------------------------------------------

SELECT 
  '🔍 FUNCTION TEST' as check_type,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM pg_proc 
      WHERE proname = 'delete_community_safe'
    )
    THEN '✅ SUCCESS: Delete function created'
    ELSE '❌ FAILED: Delete function not created'
  END as status,
  'Safe community deletion function' as details;

-- 3) عرض المجتمعات المتاحة للحذف
-- -------------------------------------------------------

SELECT 
  '📊 AVAILABLE COMMUNITIES' as info_type,
  'Communities: ' || COUNT(*)::text as details
FROM communities;

-- إظهار أول 5 مجتمعات مع معرفاتها
SELECT 
  id as community_id,
  name as community_name,
  owner_id,
  created_at
FROM communities 
ORDER BY created_at DESC 
LIMIT 5;

-- 4) تعليمات الاستخدام
-- -------------------------------------------------------

SELECT 
  '💡 USAGE INSTRUCTIONS' as info_type,
  'استخدم: SELECT * FROM delete_community_safe(''community-id-here'');' as details;

-- =============================================================
--  تعليمات الاستخدام
-- =============================================================

/*

لحذف مجتمع باستخدام هذه الدالة:

1. انسخ معرف المجتمع من القائمة أعلاه
2. شغل هذا الأمر في SQL Editor:

SELECT * FROM delete_community_safe('معرف-المجتمع-هنا');

مثال:
SELECT * FROM delete_community_safe('742b8d99-49f2-4944-825e-701e3a3a0083');

3. ستحصل على نتيجة تخبرك إذا نجح الحذف أم لا

المميزات:
- حذف آمن بالترتيب الصحيح
- لا يتطلب تحديث التطبيق
- رسائل خطأ واضحة
- إحصائيات الحذف

*/

-- =============================================================
--  انتهى إنشاء دالة الحذف الآمنة
-- =============================================================
