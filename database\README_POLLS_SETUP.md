# 🗳️ دليل إعداد نظام التصويتات "نبض"

## 📋 الملفات المطلوبة:

### 1. **إعداد قاعدة البيانات الأساسية:**
```sql
-- تشغيل هذا الملف أولاً لإنشاء الجداول والدوال
\i database/polls_with_real_data.sql
```

### 2. **إضافة بيانات حقيقية (اختياري):**
```sql
-- بيانات حقيقية يمكن لأي مستخدم نشرها
\i database/real_polls_data.sql

-- المزيد من البيانات المتنوعة
\i database/more_real_polls.sql
```

### 3. **تحديث عدد الأصوات:**
```sql
-- تحديث إحصائيات الأصوات
\i database/update_poll_votes_count.sql
```

---

## 🚀 خطوات التشغيل:

### **الخطوة 1: إعداد قاعدة البيانات**
```bash
# الاتصال بقاعدة البيانات
psql -h your-host -U your-user -d your-database

# تشغيل ملف الإعداد الأساسي
\i database/polls_with_real_data.sql
```

### **الخطوة 2: إضافة البيانات الحقيقية**
```sql
-- تسجيل الدخول كمستخدم في التطبيق أولاً
-- ثم تشغيل:
\i database/real_polls_data.sql
\i database/more_real_polls.sql
```

### **الخطوة 3: تحديث الإحصائيات**
```sql
\i database/update_poll_votes_count.sql
```

---

## 📊 البيانات المتوفرة:

### **🗳️ التصويتات الحقيقية (24 تصويت):**

#### **رياضة (Sports):**
- كأس العالم 2026
- أفضل لاعب عربي
- الرياضات الأولمبية

#### **تقنية (Technology):**
- أفضل ذكاء اصطناعي
- لغات البرمجة للمبتدئين
- مستقبل العملات الرقمية

#### **صحة (Health):**
- النظام الغذائي الصحي
- أفضل وقت للرياضة
- ساعات النوم المطلوبة

#### **تعليم (Education):**
- مواقع التعلم الإلكتروني
- التخصصات الجامعية المهمة
- طرق التعلم المفضلة

#### **ترفيه (Entertainment):**
- أنواع الأفلام المفضلة
- أفضل المسلسلات العربية
- أنواع الألعاب

#### **أعمال (Business):**
- نماذج العمل الحديثة
- تحديات ريادة الأعمال

#### **دين (Religion):**
- أوقات قراءة القرآن
- أشكال العمل الخيري

#### **سياسة (Politics):**
- أولويات العالم العربي

#### **مجتمع (Community):**
- حماية البيئة
- تمكين المرأة
- وسائل المواصلات

#### **عام (General):**
- وسائل التواصل الاجتماعي
- فصول السنة المفضلة

---

## ⚙️ الميزات المتاحة:

### **🔧 الدوال والإجراءات:**
- ✅ تحديث عدد الأصوات تلقائياً
- ✅ انتهاء صلاحية التصويتات
- ✅ حماية من التصويت المتكرر
- ✅ Row Level Security

### **📊 أنواع التصويتات:**
- ✅ **عامة:** يمكن للجميع رؤيتها
- ✅ **خاصة:** للمستخدمين المحددين فقط

### **⏰ مدد التصويت:**
- ✅ ساعة واحدة
- ✅ 6 ساعات
- ✅ 12 ساعة
- ✅ يوم واحد
- ✅ 3 أيام
- ✅ أسبوع واحد
- ✅ غير محدود

### **🏷️ الفئات المتاحة:**
- ✅ عام (General)
- ✅ رياضة (Sports)
- ✅ مجتمع (Community)
- ✅ دين (Religion)
- ✅ ترفيه (Entertainment)
- ✅ تقنية (Technology)
- ✅ صحة (Health)
- ✅ تعليم (Education)
- ✅ أعمال (Business)
- ✅ سياسة (Politics)

---

## 🔒 الأمان والحماية:

### **Row Level Security:**
```sql
-- المستخدمون يمكنهم:
✅ قراءة التصويتات العامة النشطة
✅ إنشاء تصويتاتهم الخاصة
✅ تحديث وحذف تصويتاتهم فقط
✅ التصويت مرة واحدة لكل تصويت
✅ إضافة تعليقات
```

### **الحماية من:**
```sql
❌ التصويت المتكرر من نفس المستخدم
❌ الوصول للتصويتات الخاصة
❌ تعديل تصويتات الآخرين
❌ حذف أصوات الآخرين
```

---

## 📱 استخدام التطبيق:

### **إنشاء تصويت جديد:**
1. فتح تطبيق أرزاوو
2. النقر على تبويب "نبض"
3. النقر على زر "+" لإنشاء تصويت
4. ملء البيانات المطلوبة
5. النشر

### **التصويت:**
1. تصفح التصويتات في "نبض"
2. اختيار التصويت المطلوب
3. النقر على الخيار المفضل
4. مشاهدة النتائج فوراً

### **البحث والفلترة:**
1. استخدام شريط البحث
2. فلترة حسب الفئة
3. تصفح الأقسام (الأحدث، الأكثر تفاعلاً، الاستكشاف)

---

## 🛠️ استعلامات مفيدة:

### **عرض جميع التصويتات:**
```sql
SELECT * FROM poll_details_view;
```

### **أكثر التصويتات تفاعلاً:**
```sql
SELECT * FROM trending_polls_view LIMIT 10;
```

### **إحصائيات شاملة:**
```sql
SELECT * FROM poll_statistics_view;
```

### **البحث في التصويتات:**
```sql
SELECT * FROM search_polls('كرة القدم');
```

### **نتائج تصويت محدد:**
```sql
SELECT * FROM get_poll_results('poll-uuid-here');
```

---

## 🎯 ملاحظات مهمة:

1. **المستخدم المطلوب:** يجب تسجيل الدخول لإنشاء التصويتات
2. **البيانات الحقيقية:** جميع التصويتات قابلة للنشر والاستخدام
3. **التحديث التلقائي:** عدد الأصوات يتحدث تلقائياً
4. **الأمان:** جميع البيانات محمية بـ RLS
5. **الأداء:** الفهارس محسنة للاستعلامات السريعة

---

## 🚀 **نظام التصويتات "نبض" جاهز للاستخدام!**

**جميع البيانات حقيقية ومتنوعة ويمكن لأي مستخدم نشرها والتفاعل معها.**
