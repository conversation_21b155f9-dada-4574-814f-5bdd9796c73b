-- إنشاء جدول مشاهدات المساحات
CREATE TABLE IF NOT EXISTS space_views (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    space_id UUID NOT NULL REFERENCES spaces(id) ON DELETE CASCADE,
    viewer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهرس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_space_views_space_id ON space_views(space_id);
CREATE INDEX IF NOT EXISTS idx_space_views_viewer_id ON space_views(viewer_id);
CREATE INDEX IF NOT EXISTS idx_space_views_created_at ON space_views(created_at);

-- إنشاء فهرس فريد لمنع التكرار في نفس اليوم (بدون استخدام DATE)
CREATE UNIQUE INDEX IF NOT EXISTS idx_space_views_unique_daily 
ON space_views(space_id, viewer_id, (created_at::date));

-- إنشاء RLS policies
ALTER TABLE space_views ENABLE ROW LEVEL SECURITY;

-- السماح للجميع بقراءة مشاهدات المساحات
CREATE POLICY "Allow read access to space_views" ON space_views
    FOR SELECT USING (true);

-- السماح للمستخدمين بإضافة مشاهداتهم
CREATE POLICY "Allow insert access to space_views" ON space_views
    FOR INSERT WITH CHECK (auth.uid() = viewer_id);

-- السماح للمستخدمين بحذف مشاهداتهم
CREATE POLICY "Allow delete access to space_views" ON space_views
    FOR DELETE USING (auth.uid() = viewer_id);

-- دالة لزيادة مشاهدات المساحة
CREATE OR REPLACE FUNCTION increment_space_views(space_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE spaces 
    SET views_count = COALESCE(views_count, 0) + 1,
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- منح الصلاحيات
GRANT EXECUTE ON FUNCTION increment_space_views TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated; 