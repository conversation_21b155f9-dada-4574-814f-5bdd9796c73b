# دليل تشخيص مشكلة عدد المنشورات في المساحات
# Space Posts Count Debug Guide

## المشكلة:
عدد المنشورات في المساحات يظهر صفر رغم وجود منشورات.

## خطوات التشخيص:

### 1. **التحقق من قاعدة البيانات:**
قم بتشغيل هذا الاستعلام في Supabase SQL Editor:

```sql
-- التحقق من وجود جدول space_posts
SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'space_posts'
);

-- عرض عدد المنشورات في كل مساحة
SELECT 
    s.name as space_name,
    s.id as space_id,
    COUNT(sp.id) as posts_count
FROM spaces s
LEFT JOIN space_posts sp ON s.id = sp.space_id
GROUP BY s.id, s.name
ORDER BY posts_count DESC;

-- عرض آخر 10 منشورات في المساحات
SELECT 
    sp.id,
    sp.content,
    sp.space_id,
    s.name as space_name,
    sp.created_at
FROM space_posts sp
JOIN spaces s ON sp.space_id = s.id
ORDER BY sp.created_at DESC
LIMIT 10;
```

### 2. **التحقق من Console في التطبيق:**
ابحث عن هذه الرسائل في console:

```
🔍 البحث عن منشورات المساحة: [space_id]
📋 المنشورات الموجودة: [count]
  - منشور: [id], المحتوى: [content]...
📊 عدد المنشورات في المساحة [space_id]: [count]
✅ تم إنشاء منشور جديد في المساحة: [space_id]
📝 محتوى المنشور: [content]...
🆔 معرف المنشور: [id]
📊 تم تحديث عداد المنشورات في المساحة: [space_id]
```

### 3. **اختبار إنشاء منشور جديد:**
1. اذهب إلى مساحة
2. أنشئ منشور جديد
3. تحقق من Console
4. تحقق من قاعدة البيانات

### 4. **التحقق من جدول space_posts:**
إذا لم يكن الجدول موجوداً، قم بإنشائه:

```sql
-- إنشاء جدول space_posts
CREATE TABLE IF NOT EXISTS space_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    space_id UUID NOT NULL REFERENCES spaces(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    author_name TEXT NOT NULL,
    content TEXT NOT NULL,
    media_urls TEXT[] DEFAULT '{}',
    link_url TEXT,
    link_title TEXT,
    link_description TEXT,
    link_image TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس
CREATE INDEX IF NOT EXISTS idx_space_posts_space_id ON space_posts(space_id);
CREATE INDEX IF NOT EXISTS idx_space_posts_author_id ON space_posts(author_id);
CREATE INDEX IF NOT EXISTS idx_space_posts_created_at ON space_posts(created_at);

-- RLS policies
ALTER TABLE space_posts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Allow read access to space_posts" ON space_posts
    FOR SELECT USING (true);

CREATE POLICY "Allow insert access to space_posts" ON space_posts
    FOR INSERT WITH CHECK (auth.uid() = author_id);

CREATE POLICY "Allow update access to space_posts" ON space_posts
    FOR UPDATE USING (auth.uid() = author_id);

CREATE POLICY "Allow delete access to space_posts" ON space_posts
    FOR DELETE USING (auth.uid() = author_id);
```

## النتائج المتوقعة:

### ✅ **إذا كان الجدول موجوداً:**
- يجب أن تظهر المنشورات في الاستعلام
- يجب أن يظهر العدد الصحيح

### ✅ **إذا لم يكن الجدول موجوداً:**
- قم بإنشائه باستخدام SQL أعلاه
- أعد تشغيل التطبيق

### ✅ **إذا كانت المنشورات موجودة ولكن العدد صفر:**
- المشكلة في الكود
- تحقق من Console للحصول على تفاصيل أكثر

## الحلول المحتملة:

### 1. **إنشاء جدول space_posts**
إذا لم يكن موجوداً

### 2. **إصلاح الكود**
إذا كان الجدول موجوداً ولكن الكود لا يعمل

### 3. **إعادة تشغيل التطبيق**
بعد إصلاح قاعدة البيانات

**اتبع هذه الخطوات بالترتيب لتشخيص وإصلاح المشكلة!** 