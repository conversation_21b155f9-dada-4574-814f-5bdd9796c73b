import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/marriage_chat.dart';

class MarriageChatService {
  final SupabaseClient _client = Supabase.instance.client;

  // جعل _client قابل للوصول
  SupabaseClient get client => _client;

  // الحصول على جميع محادثات المستخدم
  Future<List<MarriageChat>> getUserChats() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return [];

      final response = await _client
          .from('marriage_chats')
          .select('''
            *,
            last_message:marriage_messages(content, created_at)
          ''')
          .or('user1_id.eq.$userId,user2_id.eq.$userId')
          .eq('is_active', true)
          .order('last_message_at', ascending: false);

      List<MarriageChat> chats = [];
      
      for (var chatData in response) {
        // الحصول على معلومات المستخدم الآخر
        final otherUserId = chatData['user1_id'] == userId 
            ? chatData['user2_id'] 
            : chatData['user1_id'];
            
        final otherUserProfile = await _getOtherUserProfile(otherUserId);
        
        // إضافة آخر رسالة
        String? lastMessage;
        if (chatData['last_message'] != null && 
            (chatData['last_message'] as List).isNotEmpty) {
          lastMessage = chatData['last_message'][0]['content'];
        }

        chatData['last_message'] = lastMessage;
        chatData['other_user_name'] = otherUserProfile['name'];
        chatData['other_user_avatar'] = otherUserProfile['avatar'];
        
        chats.add(MarriageChat.fromJson(chatData));
      }

      return chats;
    } catch (e) {
      return [];
    }
  }

  // الحصول على معلومات المستخدم الآخر
  Future<Map<String, String?>> _getOtherUserProfile(String userId) async {
    try {
      final profile = await _client
          .from('marriage_profiles')
          .select('name, profile_image_url')
          .eq('user_id', userId)
          .maybeSingle();

      return {
        'name': profile?['name'] ?? 'مستخدم',
        'avatar': profile?['profile_image_url'],
      };
    } catch (e) {
      return {'name': 'مستخدم', 'avatar': null};
    }
  }

  // الحصول على محادثة محددة
  Future<MarriageChat?> getChat(String chatId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return null;

      final response = await _client
          .from('marriage_chats')
          .select('*')
          .eq('id', chatId)
          .or('user1_id.eq.$userId,user2_id.eq.$userId')
          .maybeSingle();

      if (response == null) return null;

      // الحصول على معلومات المستخدم الآخر
      final otherUserId = response['user1_id'] == userId 
          ? response['user2_id'] 
          : response['user1_id'];
          
      final otherUserProfile = await _getOtherUserProfile(otherUserId);
      
      response['other_user_name'] = otherUserProfile['name'];
      response['other_user_avatar'] = otherUserProfile['avatar'];

      return MarriageChat.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // الحصول على رسائل المحادثة
  Future<List<MarriageMessage>> getChatMessages(String chatId, {int limit = 50, int offset = 0}) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return [];

      // التحقق من أن المستخدم جزء من المحادثة
      final chat = await getChat(chatId);
      if (chat == null) return [];

      final response = await _client
          .from('marriage_messages')
          .select('*')
          .eq('chat_id', chatId)
          .eq('is_deleted', false)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List)
          .map((json) => MarriageMessage.fromJson(json))
          .toList()
          .reversed
          .toList();
    } catch (e) {
      return [];
    }
  }

  // إرسال رسالة
  Future<MarriageMessage?> sendMessage({
    required String chatId,
    required String content,
    MessageType messageType = MessageType.text,
    String? fileUrl,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // التحقق من أن المستخدم جزء من المحادثة
      final chat = await getChat(chatId);
      if (chat == null) throw Exception('المحادثة غير موجودة');

      final messageData = {
        'chat_id': chatId,
        'sender_id': userId,
        'content': content,
        'message_type': messageType.name,
        'file_url': fileUrl,
      };

      final response = await _client
          .from('marriage_messages')
          .insert(messageData)
          .select()
          .single();

      return MarriageMessage.fromJson(response);
    } catch (e) {
      throw Exception('فشل في إرسال الرسالة: $e');
    }
  }

  // تحديد الرسائل كمقروءة
  Future<void> markMessagesAsRead(String chatId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return;

      await _client
          .from('marriage_messages')
          .update({'is_read': true})
          .eq('chat_id', chatId)
          .neq('sender_id', userId)
          .eq('is_read', false);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  // حذف رسالة
  Future<void> deleteMessage(String messageId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('marriage_messages')
          .update({'is_deleted': true})
          .eq('id', messageId)
          .eq('sender_id', userId);
    } catch (e) {
      throw Exception('فشل في حذف الرسالة: $e');
    }
  }

  // الحصول على عدد الرسائل غير المقروءة
  Future<int> getUnreadMessagesCount() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return 0;

      // الحصول على جميع محادثات المستخدم
      final chats = await _client
          .from('marriage_chats')
          .select('id')
          .or('user1_id.eq.$userId,user2_id.eq.$userId')
          .eq('is_active', true);

      if (chats.isEmpty) return 0;

      final chatIds = (chats as List).map((chat) => chat['id']).toList();

      final response = await _client
          .from('marriage_messages')
          .select('id')
          .inFilter('chat_id', chatIds)
          .neq('sender_id', userId)
          .eq('is_read', false)
          .eq('is_deleted', false);

      return (response as List).length;
    } catch (e) {
      return 0;
    }
  }

  // البحث في الرسائل
  Future<List<MarriageMessage>> searchMessages(String chatId, String query) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return [];

      // التحقق من أن المستخدم جزء من المحادثة
      final chat = await getChat(chatId);
      if (chat == null) return [];

      final response = await _client
          .from('marriage_messages')
          .select('*')
          .eq('chat_id', chatId)
          .eq('is_deleted', false)
          .ilike('content', '%$query%')
          .order('created_at', ascending: false)
          .limit(20);

      return (response as List)
          .map((json) => MarriageMessage.fromJson(json))
          .toList();
    } catch (e) {
      return [];
    }
  }

  // الاستماع للرسائل الجديدة في الوقت الفعلي
  Stream<List<MarriageMessage>> subscribeToMessages(String chatId) {
    final userId = _client.auth.currentUser?.id;
    if (userId == null) return Stream.empty();

    return _client
        .from('marriage_messages')
        .stream(primaryKey: ['id'])
        .order('created_at')
        .map((data) => data
            .where((json) => json['chat_id'] == chatId && json['is_deleted'] == false)
            .map((json) => MarriageMessage.fromJson(json))
            .toList());
  }

  // إنهاء المحادثة
  Future<void> endChat(String chatId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('marriage_chats')
          .update({'is_active': false})
          .eq('id', chatId)
          .or('user1_id.eq.$userId,user2_id.eq.$userId');
    } catch (e) {
      throw Exception('فشل في إنهاء المحادثة: $e');
    }
  }
}
