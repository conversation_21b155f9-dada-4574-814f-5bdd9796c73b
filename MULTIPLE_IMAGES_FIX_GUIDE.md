# دليل إصلاح مشكلة الصور المتعددة
# Multiple Images Fix Guide

## المشكلة
الصور الواحدة تعمل بشكل صحيح، لكن الصور المتعددة (3-4 صور) لا تظهر في المنشور.

## الحل الشامل

### الخطوة 1: التحقق من قاعدة البيانات
1. اذهب إلى Supabase Dashboard
2. افتح SQL Editor
3. انسخ والصق محتوى ملف `CHECK_MEDIA_URLS_COLUMN.sql`
4. اضغط Run

### الخطوة 2: التحقق من النتائج
بعد تنفيذ SQL، يجب أن ترى:

#### في الجزء الأول (العمود):
```
column_name | data_type | is_nullable | column_default
------------|-----------|-------------|----------------
media_urls  | ARRAY     | YES         | NULL
```

#### في الجزء الرابع (المنشورات الأخيرة):
يجب أن ترى المنشورات مع `media_urls` إذا كانت موجودة.

#### في النهاية:
```
result
--------------------------------
تم التحقق من عمود media_urls بنجاح!
```

### الخطوة 3: اختبار التطبيق
1. افتح التطبيق
2. جرب إنشاء منشور مع 3-4 صور
3. راقب رسائل DEBUG في console

## رسائل DEBUG المتوقعة

عندما تعمل بشكل صحيح، يجب أن ترى:
```
DEBUG: _selectedMedia.length = 3
DEBUG: Starting upload of 3 media files
DEBUG: Uploading file 1/3: /path/to/image1.jpg
DEBUG: File 1 size: 123456 bytes
DEBUG: Generated filename for file 1: post_1234567890_0.jpg
DEBUG: File 1 uploaded to: https://...
DEBUG: Uploading file 2/3: /path/to/image2.jpg
DEBUG: File 2 size: 234567 bytes
DEBUG: Generated filename for file 2: post_1234567890_1.jpg
DEBUG: File 2 uploaded to: https://...
DEBUG: Uploading file 3/3: /path/to/image3.jpg
DEBUG: File 3 size: 345678 bytes
DEBUG: Generated filename for file 3: post_1234567890_2.jpg
DEBUG: File 3 uploaded to: https://...
DEBUG: All files uploaded successfully. Total URLs: 3
DEBUG: mediaUrls = [https://..., https://..., https://...]
DEBUG: Creating regular post...
DEBUG: Content: "نص المنشور"
DEBUG: Type: image
DEBUG: mediaUrls count: 3
DEBUG: mediaUrls: [https://..., https://..., https://...]
DEBUG: singleMediaUrl = null
DEBUG: multipleMediaUrls = [https://..., https://..., https://...]
DEBUG: Inserting data: {user_id: ..., content: ..., type: image, media_url: null, media_urls: [https://..., https://..., https://...], ...}
DEBUG: Post created successfully in database
```

## إذا استمرت المشكلة

### 1. تحقق من رسائل DEBUG
انظر إلى رسائل DEBUG لمعرفة:
- هل يتم رفع الملفات بنجاح؟
- هل يتم حفظ `mediaUrls` في قاعدة البيانات؟
- هل هناك أي أخطاء في العملية؟

### 2. تحقق من قاعدة البيانات
في Supabase SQL Editor، نفذ:
```sql
-- عرض المنشورات الأخيرة
SELECT 
    id,
    content,
    type,
    media_url,
    media_urls,
    array_length(media_urls, 1) as media_count
FROM posts 
ORDER BY created_at DESC 
LIMIT 5;
```

### 3. تحقق من عرض المنشورات
في `post_card.dart`، تأكد من أن الكود يتحقق من `mediaUrls`:
```dart
if (_post.mediaUrls != null && _post.mediaUrls!.isNotEmpty) {
  FeedMultiImage(urls: _post.mediaUrls!)
}
```

## المشاكل المحتملة وحلولها

### 1. عمود media_urls غير موجود
**الحل**: نفذ `CHECK_MEDIA_URLS_COLUMN.sql`

### 2. mediaUrls لا يتم حفظه
**الحل**: تحقق من دالة `createPost` في `supabase_service.dart`

### 3. mediaUrls لا يتم عرضه
**الحل**: تحقق من `post_card.dart` و `feed_media.dart`

### 4. مشكلة في رفع الملفات
**الحل**: تحقق من bucket `media` والسياسات

## التحقق النهائي

بعد تنفيذ جميع الخطوات، جرب:

1. **إنشاء منشور بصورة واحدة** - يجب أن يعمل
2. **إنشاء منشور بصورتين** - يجب أن يعمل
3. **إنشاء منشور بثلاث صور** - يجب أن يعمل
4. **إنشاء منشور بأربع صور** - يجب أن يعمل

إذا عملت جميع هذه الاختبارات، فالمشكلة محلولة!

## الدعم

إذا استمرت المشكلة:
1. انسخ رسائل DEBUG من التطبيق
2. انسخ نتائج SQL من Supabase
3. أرسل جميع المعلومات للمساعدة في التشخيص

**الآن نفذ `CHECK_MEDIA_URLS_COLUMN.sql` واختبر التطبيق! 🚀** 