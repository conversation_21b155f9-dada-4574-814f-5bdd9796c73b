-- نظام التصويتات "نبض" مع بيانات حقيقية

-- جدول التصويتات الرئيسي
CREATE TABLE IF NOT EXISTS polls (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    question TEXT NOT NULL,
    type VARCHAR(20) NOT NULL DEFAULT 'public', -- public, private
    category VARCHAR(20) NOT NULL DEFAULT 'general', -- general, sports, community, religion, entertainment, technology, health, education, business, politics
    duration VARCHAR(20) NOT NULL DEFAULT 'unlimited', -- oneHour, sixHours, twelveHours, oneDay, threeDays, oneWeek, unlimited
    allow_comments BOOLEAN NOT NULL DEFAULT true,
    allow_revote BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NULL
);

-- جدول خيارات التصويت
CREATE TABLE IF NOT EXISTS poll_options (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    text TEXT NOT NULL,
    votes INTEGER NOT NULL DEFAULT 0,
    option_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول أصوات المستخدمين
CREATE TABLE IF NOT EXISTS poll_votes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    option_id UUID NOT NULL REFERENCES poll_options(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- منع التصويت المتكرر من نفس المستخدم على نفس التصويت
    UNIQUE(poll_id, user_id)
);

-- جدول تعليقات التصويتات
CREATE TABLE IF NOT EXISTS poll_comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول مشاركات التصويتات
CREATE TABLE IF NOT EXISTS poll_shares (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    share_type VARCHAR(20) NOT NULL DEFAULT 'general', -- general, group, profile
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_polls_user_id ON polls(user_id);
CREATE INDEX IF NOT EXISTS idx_polls_category ON polls(category);
CREATE INDEX IF NOT EXISTS idx_polls_active ON polls(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_polls_created_at ON polls(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_polls_expires_at ON polls(expires_at) WHERE expires_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_poll_options_poll_id ON poll_options(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_options_order ON poll_options(poll_id, option_order);

CREATE INDEX IF NOT EXISTS idx_poll_votes_poll_id ON poll_votes(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_option_id ON poll_votes(option_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_user_id ON poll_votes(user_id);

CREATE INDEX IF NOT EXISTS idx_poll_comments_poll_id ON poll_comments(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_comments_user_id ON poll_comments(user_id);

-- دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إضافة triggers لتحديث updated_at
DROP TRIGGER IF EXISTS update_polls_updated_at ON polls;
CREATE TRIGGER update_polls_updated_at
    BEFORE UPDATE ON polls
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_poll_votes_updated_at ON poll_votes;
CREATE TRIGGER update_poll_votes_updated_at
    BEFORE UPDATE ON poll_votes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_poll_comments_updated_at ON poll_comments;
CREATE TRIGGER update_poll_comments_updated_at
    BEFORE UPDATE ON poll_comments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- دالة لتحديث عدد الأصوات تلقائياً
CREATE OR REPLACE FUNCTION update_poll_option_votes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE poll_options 
        SET votes = votes + 1 
        WHERE id = NEW.option_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE poll_options 
        SET votes = votes - 1 
        WHERE id = OLD.option_id;
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        -- إذا تم تغيير الخيار
        IF OLD.option_id != NEW.option_id THEN
            UPDATE poll_options 
            SET votes = votes - 1 
            WHERE id = OLD.option_id;
            
            UPDATE poll_options 
            SET votes = votes + 1 
            WHERE id = NEW.option_id;
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- إضافة trigger لتحديث عدد الأصوات
DROP TRIGGER IF EXISTS update_option_votes_trigger ON poll_votes;
CREATE TRIGGER update_option_votes_trigger
    AFTER INSERT OR UPDATE OR DELETE ON poll_votes
    FOR EACH ROW
    EXECUTE FUNCTION update_poll_option_votes();

-- دالة لإنهاء التصويتات المنتهية الصلاحية
CREATE OR REPLACE FUNCTION expire_polls()
RETURNS void AS $$
BEGIN
    UPDATE polls 
    SET is_active = false 
    WHERE expires_at IS NOT NULL 
    AND expires_at < NOW() 
    AND is_active = true;
END;
$$ LANGUAGE plpgsql;

-- تفعيل Row Level Security
ALTER TABLE polls ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_options ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_shares ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للتصويتات
DROP POLICY IF EXISTS "الجميع يمكنهم قراءة التصويتات العامة النشطة" ON polls;
CREATE POLICY "الجميع يمكنهم قراءة التصويتات العامة النشطة" ON polls
    FOR SELECT USING (is_active = true AND type = 'public');

DROP POLICY IF EXISTS "المستخدم يمكنه إنشاء تصويتاته" ON polls;
CREATE POLICY "المستخدم يمكنه إنشاء تصويتاته" ON polls
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "المستخدم يمكنه تحديث تصويتاته" ON polls;
CREATE POLICY "المستخدم يمكنه تحديث تصويتاته" ON polls
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "المستخدم يمكنه حذف تصويتاته" ON polls;
CREATE POLICY "المستخدم يمكنه حذف تصويتاته" ON polls
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان لخيارات التصويت
DROP POLICY IF EXISTS "الجميع يمكنهم قراءة خيارات التصويتات العامة" ON poll_options;
CREATE POLICY "الجميع يمكنهم قراءة خيارات التصويتات العامة" ON poll_options
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM polls 
            WHERE id = poll_options.poll_id AND is_active = true AND type = 'public'
        )
    );

DROP POLICY IF EXISTS "صاحب التصويت يمكنه إدارة الخيارات" ON poll_options;
CREATE POLICY "صاحب التصويت يمكنه إدارة الخيارات" ON poll_options
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM polls 
            WHERE id = poll_options.poll_id AND user_id = auth.uid()
        )
    );

-- سياسات الأمان للأصوات
DROP POLICY IF EXISTS "المستخدم يمكنه قراءة أصواته" ON poll_votes;
CREATE POLICY "المستخدم يمكنه قراءة أصواته" ON poll_votes
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "المستخدم يمكنه التصويت" ON poll_votes;
CREATE POLICY "المستخدم يمكنه التصويت" ON poll_votes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "المستخدم يمكنه تحديث تصويته" ON poll_votes;
CREATE POLICY "المستخدم يمكنه تحديث تصويته" ON poll_votes
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "المستخدم يمكنه حذف تصويته" ON poll_votes;
CREATE POLICY "المستخدم يمكنه حذف تصويته" ON poll_votes
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان للتعليقات
DROP POLICY IF EXISTS "الجميع يمكنهم قراءة تعليقات التصويتات العامة" ON poll_comments;
CREATE POLICY "الجميع يمكنهم قراءة تعليقات التصويتات العامة" ON poll_comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM polls 
            WHERE id = poll_comments.poll_id AND is_active = true AND type = 'public'
        )
        AND is_active = true
    );

DROP POLICY IF EXISTS "المستخدم يمكنه إضافة تعليقات" ON poll_comments;
CREATE POLICY "المستخدم يمكنه إضافة تعليقات" ON poll_comments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "المستخدم يمكنه تحديث تعليقاته" ON poll_comments;
CREATE POLICY "المستخدم يمكنه تحديث تعليقاته" ON poll_comments
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "المستخدم يمكنه حذف تعليقاته" ON poll_comments;
CREATE POLICY "المستخدم يمكنه حذف تعليقاته" ON poll_comments
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان للمشاركات
DROP POLICY IF EXISTS "المستخدم يمكنه إدارة مشاركاته" ON poll_shares;
CREATE POLICY "المستخدم يمكنه إدارة مشاركاته" ON poll_shares
    FOR ALL USING (auth.uid() = user_id);

-- منح الصلاحيات
GRANT ALL ON polls TO authenticated;
GRANT ALL ON poll_options TO authenticated;
GRANT ALL ON poll_votes TO authenticated;
GRANT ALL ON poll_comments TO authenticated;
GRANT ALL ON poll_shares TO authenticated;

GRANT EXECUTE ON FUNCTION expire_polls() TO authenticated;

-- إدراج بيانات حقيقية ومتنوعة

-- متغيرات للمستخدمين (استخدم IDs حقيقية من جدول profiles)
DO $$
DECLARE
    user1_id UUID := '11111111-1111-1111-1111-111111111111';
    user2_id UUID := '*************-2222-2222-************';
    user3_id UUID := '*************-3333-3333-************';
    user4_id UUID := '*************-4444-4444-************';
    user5_id UUID := '*************-5555-5555-************';

    -- متغيرات للتصويتات
    poll1_id UUID;
    poll2_id UUID;
    poll3_id UUID;
    poll4_id UUID;
    poll5_id UUID;
    poll6_id UUID;
    poll7_id UUID;
    poll8_id UUID;
    poll9_id UUID;
    poll10_id UUID;
    poll11_id UUID;
    poll12_id UUID;
    poll13_id UUID;
    poll14_id UUID;
    poll15_id UUID;

    -- متغيرات للخيارات
    option_id UUID;
BEGIN
    -- إدراج التصويتات الحقيقية

    -- 1. تصويت رياضي - كأس العالم
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (user1_id, 'من سيفوز بكأس العالم القادم في قطر؟', 'public', 'sports', 'oneWeek', true, false, NOW() + INTERVAL '7 days')
    RETURNING id INTO poll1_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll1_id, 'البرازيل', 0),
    (poll1_id, 'الأرجنتين', 1),
    (poll1_id, 'فرنسا', 2),
    (poll1_id, 'إنجلترا', 3),
    (poll1_id, 'إسبانيا', 4);

    -- 2. تصويت تقني - الذكاء الاصطناعي
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (user2_id, 'ما هو أفضل نموذج ذكاء اصطناعي حالياً؟', 'public', 'technology', 'unlimited', true, true)
    RETURNING id INTO poll2_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll2_id, 'ChatGPT من OpenAI', 0),
    (poll2_id, 'Claude من Anthropic', 1),
    (poll2_id, 'Gemini من Google', 2),
    (poll2_id, 'Copilot من Microsoft', 3);

    -- 3. تصويت مجتمعي - التعليم
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (user3_id, 'ما هو أهم تحدي يواجه التعليم في العالم العربي؟', 'public', 'education', 'threeDays', true, false, NOW() + INTERVAL '3 days')
    RETURNING id INTO poll3_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll3_id, 'نقص التمويل والموارد', 0),
    (poll3_id, 'عدم مواكبة التطور التقني', 1),
    (poll3_id, 'ضعف جودة المناهج', 2),
    (poll3_id, 'نقص المعلمين المؤهلين', 3),
    (poll3_id, 'عدم ربط التعليم بسوق العمل', 4);

    -- 4. تصويت صحي - نمط الحياة
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (user4_id, 'ما هو أفضل وقت لممارسة الرياضة؟', 'public', 'health', 'unlimited', true, true)
    RETURNING id INTO poll4_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll4_id, 'الصباح الباكر (5-8 ص)', 0),
    (poll4_id, 'الصباح المتأخر (8-12 ظ)', 1),
    (poll4_id, 'بعد الظهر (12-6 م)', 2),
    (poll4_id, 'المساء (6-10 م)', 3);

    -- 5. تصويت ترفيهي - الأفلام
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (user5_id, 'ما هو أفضل فيلم عربي في السنوات الأخيرة؟', 'public', 'entertainment', 'oneDay', true, false, NOW() + INTERVAL '1 day')
    RETURNING id INTO poll5_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll5_id, 'كيرة والجن', 0),
    (poll5_id, 'الفيل الأزرق 2', 1),
    (poll5_id, 'ولاد رزق 2', 2),
    (poll5_id, 'الممر', 3),
    (poll5_id, 'كازابلانكا', 4);

    -- 6. تصويت ديني - العبادات
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (user1_id, 'ما هو أفضل وقت لقراءة القرآن الكريم؟', 'public', 'religion', 'unlimited', true, false)
    RETURNING id INTO poll6_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll6_id, 'بعد صلاة الفجر', 0),
    (poll6_id, 'بين المغرب والعشاء', 1),
    (poll6_id, 'قبل النوم', 2),
    (poll6_id, 'في أي وقت متاح', 3);

    -- 7. تصويت أعمال - ريادة الأعمال
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (user2_id, 'ما هو أكبر تحدي يواجه رواد الأعمال الشباب؟', 'public', 'business', 'oneWeek', true, true, NOW() + INTERVAL '7 days')
    RETURNING id INTO poll7_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll7_id, 'الحصول على التمويل', 0),
    (poll7_id, 'إيجاد الفكرة المناسبة', 1),
    (poll7_id, 'بناء فريق العمل', 2),
    (poll7_id, 'التسويق والوصول للعملاء', 3),
    (poll7_id, 'الإجراءات الحكومية والقانونية', 4);

    -- 8. تصويت سياسي - القضايا العربية
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (user3_id, 'ما هي أولوية العالم العربي في الوقت الحالي؟', 'public', 'politics', 'unlimited', true, false)
    RETURNING id INTO poll8_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll8_id, 'تحقيق الأمن والاستقرار', 0),
    (poll8_id, 'التنمية الاقتصادية', 1),
    (poll8_id, 'التطوير التعليمي والثقافي', 2),
    (poll8_id, 'حل القضية الفلسطينية', 3),
    (poll8_id, 'مكافحة الفساد', 4);

    -- 9. تصويت عام - وسائل التواصل
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (user4_id, 'ما هي منصة التواصل الاجتماعي المفضلة لديك؟', 'public', 'general', 'threeDays', true, true, NOW() + INTERVAL '3 days')
    RETURNING id INTO poll9_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll9_id, 'تويتر (X)', 0),
    (poll9_id, 'إنستغرام', 1),
    (poll9_id, 'فيسبوك', 2),
    (poll9_id, 'تيك توك', 3),
    (poll9_id, 'سناب شات', 4),
    (poll9_id, 'لينكد إن', 5);

    -- 10. تصويت مجتمعي - البيئة
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (user5_id, 'ما هو أهم إجراء لحماية البيئة يمكن للفرد القيام به؟', 'public', 'community', 'unlimited', true, false)
    RETURNING id INTO poll10_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll10_id, 'تقليل استخدام البلاستيك', 0),
    (poll10_id, 'توفير الطاقة والمياه', 1),
    (poll10_id, 'استخدام وسائل النقل العام', 2),
    (poll10_id, 'إعادة التدوير', 3),
    (poll10_id, 'زراعة الأشجار', 4);

    -- 11. تصويت تقني - البرمجة
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (user1_id, 'ما هي أفضل لغة برمجة للمبتدئين؟', 'public', 'technology', 'oneWeek', true, true, NOW() + INTERVAL '7 days')
    RETURNING id INTO poll11_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll11_id, 'Python', 0),
    (poll11_id, 'JavaScript', 1),
    (poll11_id, 'Java', 2),
    (poll11_id, 'C++', 3),
    (poll11_id, 'Swift', 4);

    -- 12. تصويت رياضي - كرة القدم العربية
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (user2_id, 'من هو أفضل لاعب عربي في التاريخ؟', 'public', 'sports', 'unlimited', true, false)
    RETURNING id INTO poll12_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll12_id, 'محمد صلاح', 0),
    (poll12_id, 'رياض محرز', 1),
    (poll12_id, 'محمد أبو تريكة', 2),
    (poll12_id, 'ياسين الشيخاوي', 3),
    (poll12_id, 'علي ماهر', 4);

    -- 13. تصويت تعليمي - التعلم الإلكتروني
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (user3_id, 'ما هو أفضل موقع للتعلم الإلكتروني؟', 'public', 'education', 'oneDay', true, true, NOW() + INTERVAL '1 day')
    RETURNING id INTO poll13_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll13_id, 'Coursera', 0),
    (poll13_id, 'Udemy', 1),
    (poll13_id, 'edX', 2),
    (poll13_id, 'Khan Academy', 3),
    (poll13_id, 'Skillshare', 4);

    -- 14. تصويت صحي - التغذية
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (user4_id, 'ما هو أهم عنصر في النظام الغذائي الصحي؟', 'public', 'health', 'unlimited', true, false)
    RETURNING id INTO poll14_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll14_id, 'الخضروات والفواكه', 0),
    (poll14_id, 'البروتينات', 1),
    (poll14_id, 'الحبوب الكاملة', 2),
    (poll14_id, 'شرب الماء بكثرة', 3),
    (poll14_id, 'تجنب السكريات', 4);

    -- 15. تصويت ترفيهي - الألعاب
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (user5_id, 'ما هي أفضل لعبة فيديو في 2024؟', 'public', 'entertainment', 'threeDays', true, true, NOW() + INTERVAL '3 days')
    RETURNING id INTO poll15_id;

    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll15_id, 'Elden Ring', 0),
    (poll15_id, 'God of War Ragnarök', 1),
    (poll15_id, 'FIFA 24', 2),
    (poll15_id, 'Call of Duty', 3),
    (poll15_id, 'Fortnite', 4);

    -- إضافة أصوات حقيقية للتصويتات

    -- أصوات تصويت كأس العالم (poll1_id)
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll1_id, id, user2_id FROM poll_options WHERE poll_id = poll1_id AND option_order = 0; -- البرازيل
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll1_id, id, user3_id FROM poll_options WHERE poll_id = poll1_id AND option_order = 1; -- الأرجنتين
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll1_id, id, user4_id FROM poll_options WHERE poll_id = poll1_id AND option_order = 0; -- البرازيل
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll1_id, id, user5_id FROM poll_options WHERE poll_id = poll1_id AND option_order = 2; -- فرنسا

    -- أصوات تصويت الذكاء الاصطناعي (poll2_id)
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll2_id, id, user1_id FROM poll_options WHERE poll_id = poll2_id AND option_order = 0; -- ChatGPT
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll2_id, id, user3_id FROM poll_options WHERE poll_id = poll2_id AND option_order = 1; -- Claude
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll2_id, id, user4_id FROM poll_options WHERE poll_id = poll2_id AND option_order = 0; -- ChatGPT
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll2_id, id, user5_id FROM poll_options WHERE poll_id = poll2_id AND option_order = 1; -- Claude

    -- أصوات تصويت التعليم (poll3_id)
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll3_id, id, user1_id FROM poll_options WHERE poll_id = poll3_id AND option_order = 1; -- التطور التقني
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll3_id, id, user2_id FROM poll_options WHERE poll_id = poll3_id AND option_order = 0; -- نقص التمويل
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll3_id, id, user4_id FROM poll_options WHERE poll_id = poll3_id AND option_order = 4; -- ربط بسوق العمل
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll3_id, id, user5_id FROM poll_options WHERE poll_id = poll3_id AND option_order = 1; -- التطور التقني

    -- أصوات تصويت الرياضة (poll4_id)
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll4_id, id, user1_id FROM poll_options WHERE poll_id = poll4_id AND option_order = 0; -- الصباح الباكر
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll4_id, id, user2_id FROM poll_options WHERE poll_id = poll4_id AND option_order = 3; -- المساء
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll4_id, id, user3_id FROM poll_options WHERE poll_id = poll4_id AND option_order = 0; -- الصباح الباكر
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll4_id, id, user5_id FROM poll_options WHERE poll_id = poll4_id AND option_order = 3; -- المساء

    -- أصوات تصويت الأفلام (poll5_id)
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll5_id, id, user1_id FROM poll_options WHERE poll_id = poll5_id AND option_order = 3; -- الممر
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll5_id, id, user2_id FROM poll_options WHERE poll_id = poll5_id AND option_order = 0; -- كيرة والجن
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll5_id, id, user3_id FROM poll_options WHERE poll_id = poll5_id AND option_order = 3; -- الممر
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll5_id, id, user4_id FROM poll_options WHERE poll_id = poll5_id AND option_order = 1; -- الفيل الأزرق 2

    -- أصوات تصويت القرآن (poll6_id)
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll6_id, id, user2_id FROM poll_options WHERE poll_id = poll6_id AND option_order = 0; -- بعد الفجر
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll6_id, id, user3_id FROM poll_options WHERE poll_id = poll6_id AND option_order = 0; -- بعد الفجر
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll6_id, id, user4_id FROM poll_options WHERE poll_id = poll6_id AND option_order = 1; -- بين المغرب والعشاء
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll6_id, id, user5_id FROM poll_options WHERE poll_id = poll6_id AND option_order = 0; -- بعد الفجر

    -- أصوات تصويت ريادة الأعمال (poll7_id)
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll7_id, id, user1_id FROM poll_options WHERE poll_id = poll7_id AND option_order = 0; -- التمويل
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll7_id, id, user3_id FROM poll_options WHERE poll_id = poll7_id AND option_order = 3; -- التسويق
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll7_id, id, user4_id FROM poll_options WHERE poll_id = poll7_id AND option_order = 0; -- التمويل
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll7_id, id, user5_id FROM poll_options WHERE poll_id = poll7_id AND option_order = 2; -- بناء الفريق

    -- أصوات تصويت العالم العربي (poll8_id)
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll8_id, id, user1_id FROM poll_options WHERE poll_id = poll8_id AND option_order = 1; -- التنمية الاقتصادية
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll8_id, id, user2_id FROM poll_options WHERE poll_id = poll8_id AND option_order = 0; -- الأمن والاستقرار
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll8_id, id, user4_id FROM poll_options WHERE poll_id = poll8_id AND option_order = 3; -- القضية الفلسطينية
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll8_id, id, user5_id FROM poll_options WHERE poll_id = poll8_id AND option_order = 1; -- التنمية الاقتصادية

    -- أصوات تصويت وسائل التواصل (poll9_id)
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll9_id, id, user1_id FROM poll_options WHERE poll_id = poll9_id AND option_order = 0; -- تويتر
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll9_id, id, user2_id FROM poll_options WHERE poll_id = poll9_id AND option_order = 1; -- إنستغرام
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll9_id, id, user3_id FROM poll_options WHERE poll_id = poll9_id AND option_order = 3; -- تيك توك
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll9_id, id, user5_id FROM poll_options WHERE poll_id = poll9_id AND option_order = 1; -- إنستغرام

    -- أصوات تصويت البيئة (poll10_id)
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll10_id, id, user1_id FROM poll_options WHERE poll_id = poll10_id AND option_order = 0; -- تقليل البلاستيك
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll10_id, id, user2_id FROM poll_options WHERE poll_id = poll10_id AND option_order = 3; -- إعادة التدوير
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll10_id, id, user3_id FROM poll_options WHERE poll_id = poll10_id AND option_order = 4; -- زراعة الأشجار
    INSERT INTO poll_votes (poll_id, option_id, user_id)
    SELECT poll10_id, id, user4_id FROM poll_options WHERE poll_id = poll10_id AND option_order = 1; -- توفير الطاقة

    -- تحديث عدد الأصوات في الخيارات (سيتم تحديثها تلقائياً بواسطة الـ triggers)

    -- إضافة تعليقات على بعض التصويتات
    INSERT INTO poll_comments (poll_id, user_id, content) VALUES
    (poll1_id, user2_id, 'البرازيل لديها أقوى منتخب حالياً 🇧🇷'),
    (poll1_id, user3_id, 'الأرجنتين مع ميسي ستكون قوية جداً'),
    (poll2_id, user1_id, 'ChatGPT غير مجال الذكاء الاصطناعي تماماً'),
    (poll2_id, user4_id, 'Claude أكثر دقة في الإجابات المعقدة'),
    (poll3_id, user5_id, 'التعليم يحتاج ثورة حقيقية في المناهج'),
    (poll6_id, user1_id, 'بعد الفجر وقت مبارك للقراءة والتدبر'),
    (poll8_id, user3_id, 'الأمن والاستقرار أساس أي تقدم'),
    (poll9_id, user4_id, 'تويتر الأفضل للأخبار والنقاشات الجادة'),
    (poll10_id, user2_id, 'كل فرد يجب أن يساهم في حماية البيئة');

END $$;

-- رسالة نجاح
SELECT 'تم إنشاء نظام التصويتات "نبض" مع بيانات حقيقية ومتنوعة!' as message;
