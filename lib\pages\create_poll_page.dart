import 'package:flutter/material.dart';
import '../models/poll.dart';
import '../services/poll_service.dart';

class CreatePollPage extends StatefulWidget {
  const CreatePollPage({super.key});

  @override
  State<CreatePollPage> createState() => _CreatePollPageState();
}

class _CreatePollPageState extends State<CreatePollPage> {
  final _formKey = GlobalKey<FormState>();
  final PollService _pollService = PollService();
  
  final TextEditingController _questionController = TextEditingController();
  final List<TextEditingController> _optionControllers = [
    TextEditingController(),
    TextEditingController(),
  ];
  
  PollType _selectedType = PollType.public;
  PollCategory _selectedCategory = PollCategory.general;
  PollDuration _selectedDuration = PollDuration.unlimited;
  bool _allowComments = true;
  bool _allowRevote = false;
  bool _loading = false;

  @override
  void dispose() {
    _questionController.dispose();
    for (var controller in _optionControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء تصويت'),
        backgroundColor: Colors.purple[600],
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _loading ? null : _createPoll,
            child: _loading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2),
                  )
                : const Text('نشر', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // السؤال الرئيسي
            _buildSection(
              'السؤال الرئيسي',
              Icons.help_outline,
              [
                TextFormField(
                  controller: _questionController,
                  decoration: const InputDecoration(
                    hintText: 'اكتب سؤالك هنا...',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  maxLength: 200,
                  validator: (value) {
                    if (value?.trim().isEmpty == true) {
                      return 'السؤال مطلوب';
                    }
                    if (value!.trim().length < 10) {
                      return 'السؤال قصير جداً (10 أحرف على الأقل)';
                    }
                    return null;
                  },
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // خيارات التصويت
            _buildSection(
              'خيارات التصويت',
              Icons.list,
              [
                ..._buildOptionFields(),
                const SizedBox(height: 16),
                Row(
                  children: [
                    if (_optionControllers.length < 6)
                      ElevatedButton.icon(
                        onPressed: _addOption,
                        icon: const Icon(Icons.add),
                        label: const Text('إضافة خيار'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green[600],
                          foregroundColor: Colors.white,
                        ),
                      ),
                    const SizedBox(width: 12),
                    if (_optionControllers.length > 2)
                      ElevatedButton.icon(
                        onPressed: _removeOption,
                        icon: const Icon(Icons.remove),
                        label: const Text('حذف خيار'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red[600],
                          foregroundColor: Colors.white,
                        ),
                      ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // نوع التصويت
            _buildSection(
              'نوع التصويت',
              Icons.visibility,
              [
                ...PollType.values.map((type) {
                  return RadioListTile<PollType>(
                    title: Row(
                      children: [
                        Icon(type.icon),
                        const SizedBox(width: 8),
                        Text(type.arabicName),
                      ],
                    ),
                    value: type,
                    groupValue: _selectedType,
                    onChanged: (value) => setState(() => _selectedType = value!),
                  );
                }),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // فئة التصويت
            _buildSection(
              'فئة التصويت',
              Icons.category,
              [
                DropdownButtonFormField<PollCategory>(
                  value: _selectedCategory,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                  items: PollCategory.values.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Row(
                        children: [
                          Icon(category.icon, color: category.color),
                          const SizedBox(width: 8),
                          Text(category.arabicName),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) => setState(() => _selectedCategory = value!),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // مدة التصويت
            _buildSection(
              'مدة التصويت',
              Icons.timer,
              [
                DropdownButtonFormField<PollDuration>(
                  value: _selectedDuration,
                  decoration: const InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                  items: PollDuration.values.map((duration) {
                    return DropdownMenuItem(
                      value: duration,
                      child: Text(duration.arabicName),
                    );
                  }).toList(),
                  onChanged: (value) => setState(() => _selectedDuration = value!),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // إعدادات إضافية
            _buildSection(
              'إعدادات إضافية',
              Icons.settings,
              [
                SwitchListTile(
                  title: const Text('السماح بالتعليقات'),
                  subtitle: const Text('يمكن للمستخدمين التعليق على التصويت'),
                  value: _allowComments,
                  onChanged: (value) => setState(() => _allowComments = value),
                  activeColor: Colors.purple[600],
                ),
                SwitchListTile(
                  title: const Text('السماح بإعادة التصويت'),
                  subtitle: const Text('يمكن للمستخدمين تغيير تصويتهم'),
                  value: _allowRevote,
                  onChanged: (value) => setState(() => _allowRevote = value),
                  activeColor: Colors.purple[600],
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // زر النشر
            SizedBox(
              height: 50,
              child: ElevatedButton(
                onPressed: _loading ? null : _createPoll,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple[600],
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _loading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : const Text(
                        'نشر التصويت',
                        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                      ),
              ),
            ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: Colors.purple[600]),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.purple[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  List<Widget> _buildOptionFields() {
    return _optionControllers.asMap().entries.map((entry) {
      final index = entry.key;
      final controller = entry.value;
      
      return Padding(
        padding: const EdgeInsets.only(bottom: 12),
        child: TextFormField(
          controller: controller,
          decoration: InputDecoration(
            labelText: 'الخيار ${index + 1}',
            border: const OutlineInputBorder(),
            prefixIcon: CircleAvatar(
              radius: 12,
              backgroundColor: Colors.purple[600],
              child: Text(
                '${index + 1}',
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ),
          ),
          maxLength: 100,
          validator: (value) {
            if (value?.trim().isEmpty == true) {
              return 'الخيار ${index + 1} مطلوب';
            }
            if (value!.trim().length < 2) {
              return 'الخيار قصير جداً';
            }
            return null;
          },
        ),
      );
    }).toList();
  }

  void _addOption() {
    if (_optionControllers.length < 6) {
      setState(() {
        _optionControllers.add(TextEditingController());
      });
    }
  }

  void _removeOption() {
    if (_optionControllers.length > 2) {
      setState(() {
        _optionControllers.removeLast().dispose();
      });
    }
  }

  Future<void> _createPoll() async {
    if (!_formKey.currentState!.validate()) return;

    // التحقق من وجود خيارات صالحة
    final validOptions = _optionControllers
        .where((controller) => controller.text.trim().isNotEmpty)
        .toList();

    if (validOptions.length < 2) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يجب إضافة خيارين على الأقل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _loading = true);

    try {
      final options = validOptions.asMap().entries.map((entry) {
        return PollOption(
          id: '', // سيتم إنشاؤه في الخادم
          text: entry.value.text.trim(),
          votes: 0,
          percentage: 0.0,
        );
      }).toList();

      final poll = Poll(
        id: '', // سيتم إنشاؤه في الخادم
        userId: '', // سيتم تعيينه في الخدمة
        question: _questionController.text.trim(),
        options: options,
        type: _selectedType,
        category: _selectedCategory,
        duration: _selectedDuration,
        allowComments: _allowComments,
        allowRevote: _allowRevote,
        isActive: true,
        totalVotes: 0,
        createdAt: DateTime.now(),
      );

      await _pollService.createPoll(poll);

      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء التصويت بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إنشاء التصويت: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
