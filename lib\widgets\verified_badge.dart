import 'package:flutter/material.dart';

class VerifiedBadge extends StatelessWidget {
  final double size;
  final Color? color;
  final String? tooltipText;
  
  const VerifiedBadge({
    super.key,
    this.size = 16.0,
    this.color,
    this.tooltipText,
  });

  @override
  Widget build(BuildContext context) {
    // شارة التحقق الحقيقية - أيقونة التحقق مباشرة باللون الأزرق
    return Tooltip(
      message: tooltipText ?? _getDefaultTooltip(context),
      child: Icon(
        Icons.verified, // أيقونة التحقق الحقيقية
        size: size,
        color: color ?? const Color(0xFF1877F2), // لون فيسبوك الأزرق
        weight: 900, // سمك الخط
      ),
    );
  }

  String _getDefaultTooltip(BuildContext context) {
    // تحديد اللغة بناءً على إعدادات التطبيق
    final locale = Localizations.localeOf(context);
    if (locale.languageCode == 'ar') {
      return 'حساب موثق\nهذا الحساب تم التحقق من هويته من قبل فريقنا';
    } else {
      return 'Verified Account\nThis account has been verified by our team';
    }
  }
}

// نسخة بديلة مع أيقونة مختلفة
class AdvancedVerifiedBadge extends StatelessWidget {
  final double size;
  final Color? color;
  final String? tooltipText;
  
  const AdvancedVerifiedBadge({
    super.key,
    this.size = 16.0,
    this.color,
    this.tooltipText,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltipText ?? _getDefaultTooltip(context),
      child: Icon(
        Icons.check_circle, // أيقونة بديلة
        size: size,
        color: color ?? const Color(0xFF1877F2),
        weight: 900,
      ),
    );
  }

  String _getDefaultTooltip(BuildContext context) {
    final locale = Localizations.localeOf(context);
    if (locale.languageCode == 'ar') {
      return 'حساب موثق\nهذا الحساب تم التحقق من هويته من قبل فريقنا';
    } else {
      return 'Verified Account\nThis account has been verified by our team';
    }
  }
}

// نسخة مع تأثير ظل خفيف
class PremiumVerifiedBadge extends StatelessWidget {
  final double size;
  final Color? color;
  final String? tooltipText;
  
  const PremiumVerifiedBadge({
    super.key,
    this.size = 16.0,
    this.color,
    this.tooltipText,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltipText ?? _getDefaultTooltip(context),
      child: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              spreadRadius: 0,
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Icon(
          Icons.verified,
          size: size,
          color: color ?? const Color(0xFF1877F2),
          weight: 900,
        ),
      ),
    );
  }

  String _getDefaultTooltip(BuildContext context) {
    final locale = Localizations.localeOf(context);
    if (locale.languageCode == 'ar') {
      return 'حساب موثق\nهذا الحساب تم التحقق من هويته من قبل فريقنا';
    } else {
      return 'Verified Account\nThis account has been verified by our team';
    }
  }
}

// نسخة مع تأثير متقدم
class CustomVerifiedBadge extends StatelessWidget {
  final double size;
  final Color? color;
  final String? tooltipText;
  
  const CustomVerifiedBadge({
    super.key,
    this.size = 16.0,
    this.color,
    this.tooltipText,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltipText ?? _getDefaultTooltip(context),
      child: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.15),
              spreadRadius: 0,
              blurRadius: 3,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Icon(
          Icons.verified,
          size: size,
          color: color ?? const Color(0xFF1877F2),
          weight: 900,
        ),
      ),
    );
  }

  String _getDefaultTooltip(BuildContext context) {
    final locale = Localizations.localeOf(context);
    if (locale.languageCode == 'ar') {
      return 'حساب موثق\nهذا الحساب تم التحقق من هويته من قبل فريقنا';
    } else {
      return 'Verified Account\nThis account has been verified by our team';
    }
  }
} 