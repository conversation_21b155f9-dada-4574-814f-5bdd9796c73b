-- استعلامات إدارة وتحليل التصويتات

-- 1. عرض جميع التصويتات مع تفاصيلها
CREATE OR REPLACE VIEW poll_details_view AS
SELECT 
    p.id,
    p.question,
    p.type,
    p.category,
    p.duration,
    p.allow_comments,
    p.allow_revote,
    p.is_active,
    p.created_at,
    p.expires_at,
    CASE 
        WHEN p.expires_at IS NULL THEN 'مفتوح'
        WHEN p.expires_at > NOW() THEN 'نشط'
        ELSE 'منتهي'
    END as status,
    (SELECT COUNT(*) FROM poll_votes WHERE poll_id = p.id) as total_votes,
    (SELECT COUNT(*) FROM poll_comments WHERE poll_id = p.id) as total_comments,
    (SELECT COUNT(*) FROM poll_options WHERE poll_id = p.id) as options_count
FROM polls p
ORDER BY p.created_at DESC;

-- 2. عرض أفضل التصويتات (الأكثر تفاعلاً)
CREATE OR REPLACE VIEW trending_polls_view AS
SELECT 
    p.id,
    p.question,
    p.category,
    (SELECT COUNT(*) FROM poll_votes WHERE poll_id = p.id) as votes_count,
    (SELECT COUNT(*) FROM poll_comments WHERE poll_id = p.id) as comments_count,
    ((SELECT COUNT(*) FROM poll_votes WHERE poll_id = p.id) + 
     (SELECT COUNT(*) FROM poll_comments WHERE poll_id = p.id)) as engagement_score,
    p.created_at
FROM polls p
WHERE p.is_active = true
ORDER BY engagement_score DESC, votes_count DESC;

-- 3. إحصائيات شاملة للتصويتات
CREATE OR REPLACE VIEW poll_statistics_view AS
SELECT 
    'إجمالي التصويتات' as metric,
    COUNT(*)::text as value
FROM polls
UNION ALL
SELECT 
    'التصويتات النشطة' as metric,
    COUNT(*)::text as value
FROM polls WHERE is_active = true
UNION ALL
SELECT 
    'إجمالي الأصوات' as metric,
    COUNT(*)::text as value
FROM poll_votes
UNION ALL
SELECT 
    'إجمالي التعليقات' as metric,
    COUNT(*)::text as value
FROM poll_comments
UNION ALL
SELECT 
    'المستخدمون المشاركون' as metric,
    COUNT(DISTINCT user_id)::text as value
FROM poll_votes;

-- 4. توزيع التصويتات حسب الفئات
CREATE OR REPLACE VIEW category_distribution_view AS
SELECT 
    category,
    COUNT(*) as polls_count,
    ROUND((COUNT(*)::float / (SELECT COUNT(*) FROM polls WHERE is_active = true)) * 100, 1) as percentage,
    (SELECT COUNT(*) FROM poll_votes pv JOIN polls p ON pv.poll_id = p.id WHERE p.category = polls.category) as total_votes
FROM polls 
WHERE is_active = true
GROUP BY category
ORDER BY polls_count DESC;

-- 5. التصويتات المنتهية الصلاحية
CREATE OR REPLACE VIEW expired_polls_view AS
SELECT 
    p.id,
    p.question,
    p.category,
    p.expires_at,
    (SELECT COUNT(*) FROM poll_votes WHERE poll_id = p.id) as total_votes,
    p.created_at
FROM polls p
WHERE p.expires_at IS NOT NULL 
AND p.expires_at < NOW() 
AND p.is_active = true
ORDER BY p.expires_at DESC;

-- 6. أكثر المستخدمين نشاطاً في التصويت
CREATE OR REPLACE VIEW most_active_voters_view AS
SELECT 
    pv.user_id,
    COUNT(*) as votes_count,
    COUNT(DISTINCT pv.poll_id) as polls_participated,
    MIN(pv.created_at) as first_vote,
    MAX(pv.created_at) as last_vote
FROM poll_votes pv
GROUP BY pv.user_id
ORDER BY votes_count DESC
LIMIT 20;

-- 7. التصويتات الأكثر جدلاً (توزيع متقارب في الأصوات)
CREATE OR REPLACE VIEW controversial_polls_view AS
WITH poll_stats AS (
    SELECT 
        p.id,
        p.question,
        p.category,
        COUNT(pv.id) as total_votes,
        MAX(po.votes) as max_votes,
        MIN(po.votes) as min_votes,
        (MAX(po.votes) - MIN(po.votes)) as vote_difference
    FROM polls p
    JOIN poll_options po ON p.id = po.poll_id
    LEFT JOIN poll_votes pv ON p.id = pv.poll_id
    WHERE p.is_active = true
    GROUP BY p.id, p.question, p.category
    HAVING COUNT(pv.id) >= 5  -- على الأقل 5 أصوات
)
SELECT 
    id,
    question,
    category,
    total_votes,
    vote_difference,
    ROUND((vote_difference::float / total_votes) * 100, 1) as controversy_percentage
FROM poll_stats
WHERE vote_difference <= (total_votes * 0.3)  -- الفرق أقل من 30% من إجمالي الأصوات
ORDER BY controversy_percentage ASC, total_votes DESC;

-- استعلامات مفيدة للإدارة

-- البحث في التصويتات
CREATE OR REPLACE FUNCTION search_polls(search_term TEXT)
RETURNS TABLE(
    id UUID,
    question TEXT,
    category VARCHAR(20),
    total_votes BIGINT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.question,
        p.category,
        (SELECT COUNT(*) FROM poll_votes WHERE poll_id = p.id) as total_votes,
        p.created_at
    FROM polls p
    WHERE p.is_active = true
    AND (
        p.question ILIKE '%' || search_term || '%'
        OR EXISTS (
            SELECT 1 FROM poll_options po 
            WHERE po.poll_id = p.id 
            AND po.text ILIKE '%' || search_term || '%'
        )
    )
    ORDER BY p.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- الحصول على تفاصيل تصويت محدد مع النتائج
CREATE OR REPLACE FUNCTION get_poll_results(poll_uuid UUID)
RETURNS TABLE(
    question TEXT,
    option_text TEXT,
    votes INTEGER,
    percentage NUMERIC,
    option_order INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.question,
        po.text as option_text,
        po.votes,
        ROUND(
            CASE 
                WHEN (SELECT SUM(votes) FROM poll_options WHERE poll_id = poll_uuid) > 0 
                THEN (po.votes::numeric / (SELECT SUM(votes) FROM poll_options WHERE poll_id = poll_uuid)) * 100
                ELSE 0 
            END, 1
        ) as percentage,
        po.option_order
    FROM polls p
    JOIN poll_options po ON p.id = po.poll_id
    WHERE p.id = poll_uuid
    ORDER BY po.option_order;
END;
$$ LANGUAGE plpgsql;

-- تنظيف التصويتات المنتهية الصلاحية
CREATE OR REPLACE FUNCTION cleanup_expired_polls()
RETURNS INTEGER AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    UPDATE polls 
    SET is_active = false 
    WHERE expires_at IS NOT NULL 
    AND expires_at < NOW() 
    AND is_active = true;
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    RETURN updated_count;
END;
$$ LANGUAGE plpgsql;

-- إحصائيات يومية للتصويتات
CREATE OR REPLACE FUNCTION daily_poll_stats(target_date DATE DEFAULT CURRENT_DATE)
RETURNS TABLE(
    metric TEXT,
    count BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 'تصويتات جديدة'::TEXT, COUNT(*) 
    FROM polls 
    WHERE DATE(created_at) = target_date
    
    UNION ALL
    
    SELECT 'أصوات جديدة'::TEXT, COUNT(*) 
    FROM poll_votes 
    WHERE DATE(created_at) = target_date
    
    UNION ALL
    
    SELECT 'تعليقات جديدة'::TEXT, COUNT(*) 
    FROM poll_comments 
    WHERE DATE(created_at) = target_date;
END;
$$ LANGUAGE plpgsql;

-- منح الصلاحيات للدوال والعروض
GRANT SELECT ON poll_details_view TO authenticated;
GRANT SELECT ON trending_polls_view TO authenticated;
GRANT SELECT ON poll_statistics_view TO authenticated;
GRANT SELECT ON category_distribution_view TO authenticated;
GRANT SELECT ON expired_polls_view TO authenticated;
GRANT SELECT ON most_active_voters_view TO authenticated;
GRANT SELECT ON controversial_polls_view TO authenticated;

GRANT EXECUTE ON FUNCTION search_polls(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_poll_results(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_expired_polls() TO authenticated;
GRANT EXECUTE ON FUNCTION daily_poll_stats(DATE) TO authenticated;

-- رسالة نجاح
SELECT 'تم إنشاء جميع استعلامات إدارة التصويتات بنجاح!' as message;
