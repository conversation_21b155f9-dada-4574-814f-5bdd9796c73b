-- إصلاح بسيط للمشاهدات والصور

-- 1. حذف الدوال الموجودة
DROP FUNCTION IF EXISTS increment_property_views(UUID);
DROP FUNCTION IF EXISTS increment_property_views(property_uuid UUID);

-- 2. إنشاء دالة جديدة لتحديث المشاهدات
CREATE FUNCTION increment_property_views(property_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE real_estate_properties 
  SET views_count = views_count + 1,
      updated_at = NOW()
  WHERE id = property_id AND is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. منح الصلاحيات
GRANT EXECUTE ON FUNCTION increment_property_views(UUID) TO authenticated;

-- 4. إضافة سياسة تحديث المشاهدات
DROP POLICY IF EXISTS "تحديث المشاهدات للجميع" ON real_estate_properties;

CREATE POLICY "تحديث المشاهدات للجميع" ON real_estate_properties
    FOR UPDATE USING (is_active = true)
    WITH CHECK (is_active = true);

-- 5. التأكد من وجود bucket للصور
DO $$
BEGIN
    INSERT INTO storage.buckets (id, name, public)
    VALUES ('property-images', 'property-images', true);
EXCEPTION WHEN unique_violation THEN
    -- البucket موجود بالفعل
    NULL;
END $$;

-- 6. سياسات التخزين للصور
DROP POLICY IF EXISTS "Users can upload property images" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can view property images" ON storage.objects;
DROP POLICY IF EXISTS "Users can delete their property images" ON storage.objects;

-- السماح برفع الصور للمستخدمين المسجلين
CREATE POLICY "Users can upload property images" ON storage.objects
    FOR INSERT WITH CHECK (bucket_id = 'property-images' AND auth.role() = 'authenticated');

-- السماح بعرض الصور للجميع
CREATE POLICY "Anyone can view property images" ON storage.objects
    FOR SELECT USING (bucket_id = 'property-images');

-- السماح بحذف الصور للمالك
CREATE POLICY "Users can delete their property images" ON storage.objects
    FOR DELETE USING (bucket_id = 'property-images' AND auth.role() = 'authenticated');

-- رسالة نجاح
SELECT 'تم الإصلاح بنجاح!' as result;
