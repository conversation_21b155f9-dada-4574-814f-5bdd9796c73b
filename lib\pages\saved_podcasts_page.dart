import 'package:flutter/material.dart';
import '../models/podcast.dart';
import '../services/podcast_service.dart';
import '../widgets/podcast_card.dart';
import '../widgets/podcast_skeleton.dart';

class SavedPodcastsPage extends StatefulWidget {
  const SavedPodcastsPage({super.key});

  @override
  State<SavedPodcastsPage> createState() => _SavedPodcastsPageState();
}

class _SavedPodcastsPageState extends State<SavedPodcastsPage> {
  final PodcastService _podcastService = PodcastService();
  final ScrollController _scrollController = ScrollController();
  
  List<Podcast> _podcasts = [];
  bool _loading = true;
  bool _loadingMore = false;
  bool _hasMore = true;
  String? _error;
  
  final int _limit = 20;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadSavedPodcasts();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 300) {
      _loadMorePodcasts();
    }
  }

  Future<void> _loadSavedPodcasts() async {
    setState(() {
      _loading = true;
      _error = null;
    });

    try {
      final podcasts = await _podcastService.getSavedPodcasts(
        limit: _limit,
        offset: 0,
      );

      setState(() {
        _podcasts = podcasts;
        _hasMore = podcasts.length == _limit;
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'فشل في تحميل البودكاستات المحفوظة: $e';
        _loading = false;
      });
    }
  }

  Future<void> _loadMorePodcasts() async {
    if (_loadingMore || !_hasMore) return;

    setState(() => _loadingMore = true);

    try {
      final morePodcasts = await _podcastService.getSavedPodcasts(
        limit: _limit,
        offset: _podcasts.length,
      );

      setState(() {
        _podcasts.addAll(morePodcasts);
        _hasMore = morePodcasts.length == _limit;
        _loadingMore = false;
      });
    } catch (e) {
      setState(() => _loadingMore = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل المزيد: $e')),
        );
      }
    }
  }

  Future<void> _onRefresh() async {
    await _loadSavedPodcasts();
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: 6,
        itemBuilder: (context, index) => const PodcastSkeleton(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadSavedPodcasts,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_podcasts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bookmark_border, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد بودكاستات محفوظة',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'احفظ البودكاستات المفضلة لديك للاستماع إليها لاحقاً',
              style: TextStyle(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: _podcasts.length + (_loadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _podcasts.length) {
            return const Padding(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          return PodcastCard(
            podcast: _podcasts[index],
            onRefresh: _loadSavedPodcasts,
          );
        },
      ),
    );
  }
}
