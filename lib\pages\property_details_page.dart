import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/real_estate_property.dart';
import '../services/real_estate_service.dart';
import '../services/property_chat_service.dart';
import 'property_chat_page.dart';
import 'property_settings_page.dart';

class PropertyDetailsPage extends StatefulWidget {
  final RealEstateProperty property;

  const PropertyDetailsPage({
    super.key,
    required this.property,
  });

  @override
  State<PropertyDetailsPage> createState() => _PropertyDetailsPageState();
}

class _PropertyDetailsPageState extends State<PropertyDetailsPage> {
  final RealEstateService _realEstateService = RealEstateService();
  final PropertyChatService _chatService = PropertyChatService();
  late RealEstateProperty _property;
  bool _isFavorite = false;
  int _currentImageIndex = 0;
  bool _isOwner = false;

  @override
  void initState() {
    super.initState();
    _property = widget.property;
    _isFavorite = _property.isFavorite;
    _checkIfOwner();
    _incrementViews();
  }

  void _checkIfOwner() {
    final currentUserId = Supabase.instance.client.auth.currentUser?.id;
    _isOwner = currentUserId == _property.userId;
  }

  Future<void> _incrementViews() async {
    // لا نزيد المشاهدات إذا كان المستخدم هو المالك
    if (!_isOwner) {
      try {
        await _realEstateService.incrementViewsCount(_property.id);
        // تحديث العدد محلياً
        setState(() {
          _property = RealEstateProperty(
            id: _property.id,
            userId: _property.userId,
            title: _property.title,
            description: _property.description,
            propertyType: _property.propertyType,
            purpose: _property.purpose,
            category: _property.category,
            country: _property.country,
            city: _property.city,
            district: _property.district,
            address: _property.address,
            latitude: _property.latitude,
            longitude: _property.longitude,
            price: _property.price,
            currency: _property.currency,
            area: _property.area,
            bedrooms: _property.bedrooms,
            bathrooms: _property.bathrooms,
            floors: _property.floors,
            parkingSpaces: _property.parkingSpaces,
            features: _property.features,
            amenities: _property.amenities,
            contactPhone: _property.contactPhone,
            contactWhatsapp: _property.contactWhatsapp,
            allowAppMessages: _property.allowAppMessages,
            isActive: _property.isActive,
            isFeatured: _property.isFeatured,
            isVerified: _property.isVerified,
            viewsCount: _property.viewsCount + 1, // زيادة المشاهدات
            createdAt: _property.createdAt,
            updatedAt: _property.updatedAt,
            expiresAt: _property.expiresAt,
            images: _property.images,
            isFavorite: _property.isFavorite,
            ownerName: _property.ownerName,
            ownerAvatar: _property.ownerAvatar,
          );
        });
      } catch (e) {
        // تجاهل الخطأ
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // صور العقار
          _buildImageSliver(),
          
          // تفاصيل العقار
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // العنوان والسعر
                  _buildTitleAndPrice(),
                  
                  const SizedBox(height: 16),
                  
                  // الموقع
                  _buildLocation(),
                  
                  const SizedBox(height: 24),
                  
                  // تفاصيل العقار
                  _buildPropertyDetails(),
                  
                  const SizedBox(height: 24),
                  
                  // الوصف
                  _buildDescription(),
                  
                  const SizedBox(height: 24),
                  
                  // الميزات
                  if (_property.features.isNotEmpty) ...[
                    _buildFeatures(),
                    const SizedBox(height: 24),
                  ],
                  
                  // الخدمات القريبة
                  if (_property.amenities.isNotEmpty) ...[
                    _buildAmenities(),
                    const SizedBox(height: 24),
                  ],
                  
                  // معلومات المالك
                  _buildOwnerInfo(),
                  
                  const SizedBox(height: 24),
                  
                  // أزرار التواصل
                  _buildContactButtons(),
                  
                  const SizedBox(height: 100), // مساحة للأزرار السفلية
                ],
              ),
            ),
          ),
        ],
      ),
      
      // أزرار سفلية
      bottomNavigationBar: _buildBottomActions(),
    );
  }

  Widget _buildImageSliver() {
    final images = _property.images;
    
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      backgroundColor: Colors.white,
      foregroundColor: Colors.black,
      actions: [
        // زر المفضلة (إذا لم يكن المالك)
        if (!_isOwner)
          IconButton(
            onPressed: _toggleFavorite,
            icon: Icon(
              _isFavorite ? Icons.favorite : Icons.favorite_border,
              color: _isFavorite ? Colors.red : Colors.white,
            ),
            style: IconButton.styleFrom(
              backgroundColor: Colors.black.withValues(alpha: 0.5),
            ),
          ),

        // زر المشاركة
        IconButton(
          onPressed: _shareProperty,
          icon: const Icon(Icons.share, color: Colors.white),
          style: IconButton.styleFrom(
            backgroundColor: Colors.black.withValues(alpha: 0.5),
          ),
        ),

        // قائمة الخيارات
        PopupMenuButton(
          icon: const Icon(Icons.more_vert, color: Colors.white),
          itemBuilder: (context) => [
            if (_isOwner) ...[
              PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings, color: Colors.blue[600]),
                    const SizedBox(width: 8),
                    const Text('إعدادات العقار'),
                  ],
                ),
              ),
            ] else ...[
              PopupMenuItem(
                value: 'report',
                child: Row(
                  children: [
                    Icon(Icons.report, color: Colors.red[600]),
                    const SizedBox(width: 8),
                    const Text('إبلاغ عن العقار'),
                  ],
                ),
              ),
            ],
          ],
          onSelected: (value) {
            if (value == 'report') {
              _showReportDialog();
            } else if (value == 'settings') {
              _openPropertySettings();
            }
          },
        ),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: images.isNotEmpty
            ? PageView.builder(
                itemCount: images.length,
                onPageChanged: (index) {
                  setState(() => _currentImageIndex = index);
                },
                itemBuilder: (context, index) {
                  return Image.network(
                    images[index].imageUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[300],
                        child: const Center(
                          child: Icon(Icons.error, size: 50),
                        ),
                      );
                    },
                  );
                },
              )
            : Container(
                color: Colors.grey[300],
                child: const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.home, size: 80, color: Colors.grey),
                      SizedBox(height: 8),
                      Text('لا توجد صور', style: TextStyle(color: Colors.grey)),
                    ],
                  ),
                ),
              ),
      ),
      bottom: images.length > 1
          ? PreferredSize(
              preferredSize: const Size.fromHeight(30),
              child: Container(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    images.length,
                    (index) => Container(
                      margin: const EdgeInsets.symmetric(horizontal: 2),
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: index == _currentImageIndex
                            ? Colors.white
                            : Colors.white.withValues(alpha: 0.5),
                      ),
                    ),
                  ),
                ),
              ),
            )
          : null,
    );
  }

  Widget _buildTitleAndPrice() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _property.title,
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getPurposeColor().withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: _getPurposeColor()),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(_property.purpose.icon),
                        const SizedBox(width: 4),
                        Text(
                          _property.purpose.arabicName,
                          style: TextStyle(
                            color: _getPurposeColor(),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Colors.blue[50],
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: Colors.blue[200]!),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(_property.propertyType.icon),
                        const SizedBox(width: 4),
                        Text(
                          _property.propertyType.arabicName,
                          style: TextStyle(
                            color: Colors.blue[700],
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              _property.getFormattedPrice(),
              style: TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.green[600],
              ),
            ),
            if (_property.area != null)
              Text(
                _property.getFormattedArea(),
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
          ],
        ),
      ],
    );
  }

  Widget _buildLocation() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Icon(Icons.location_on, color: Colors.red[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'الموقع',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _property.getFullAddress(),
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          if (_property.latitude != null && _property.longitude != null)
            IconButton(
              onPressed: _openMap,
              icon: Icon(Icons.map, color: Colors.blue[600]),
              tooltip: 'عرض على الخريطة',
            ),
        ],
      ),
    );
  }

  Widget _buildPropertyDetails() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تفاصيل العقار',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.blue[700],
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              if (_property.bedrooms > 0) ...[
                _buildDetailItem(Icons.bed, '${_property.bedrooms} غرف نوم'),
                const SizedBox(width: 20),
              ],
              if (_property.bathrooms > 0) ...[
                _buildDetailItem(Icons.bathroom, '${_property.bathrooms} حمام'),
                const SizedBox(width: 20),
              ],
              if (_property.floors > 1) ...[
                _buildDetailItem(Icons.layers, '${_property.floors} طوابق'),
              ],
            ],
          ),
          if (_property.parkingSpaces > 0) ...[
            const SizedBox(height: 12),
            _buildDetailItem(Icons.local_parking, '${_property.parkingSpaces} موقف سيارة'),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 20, color: Colors.blue[600]),
        const SizedBox(width: 6),
        Text(
          text,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Colors.blue[700],
          ),
        ),
      ],
    );
  }

  Widget _buildDescription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الوصف',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 12),
        Text(
          _property.description,
          style: TextStyle(
            fontSize: 16,
            height: 1.6,
            color: Colors.grey[700],
          ),
        ),
      ],
    );
  }

  Widget _buildFeatures() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الميزات',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _property.features.map((feature) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.check_circle, size: 16, color: Colors.green[600]),
                  const SizedBox(width: 6),
                  Text(
                    feature,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.green[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildAmenities() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الخدمات القريبة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: _property.amenities.map((amenity) {
            return Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.location_on, size: 16, color: Colors.orange[600]),
                  const SizedBox(width: 6),
                  Text(
                    amenity,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.orange[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildOwnerInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: Colors.grey[300],
            backgroundImage: _property.ownerAvatar != null
                ? NetworkImage(_property.ownerAvatar!)
                : null,
            child: _property.ownerAvatar == null
                ? Icon(Icons.person, size: 30, color: Colors.grey[600])
                : null,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      _property.ownerName ?? 'مالك العقار',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (_property.isVerified) ...[
                      const SizedBox(width: 8),
                      Icon(Icons.verified, size: 20, color: Colors.blue[600]),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  'مالك العقار',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Column(
            children: [
              Icon(Icons.visibility, size: 16, color: Colors.grey[500]),
              const SizedBox(height: 4),
              Text(
                '${_property.viewsCount}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildContactButtons() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التواصل',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            // اتصال
            if (_property.contactPhone != null) ...[
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _makePhoneCall(_property.contactPhone!),
                  icon: const Icon(Icons.phone),
                  label: const Text('اتصال'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[600],
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 12),
            ],
            
            // واتساب
            if (_property.contactWhatsapp != null) ...[
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _openWhatsApp(_property.contactWhatsapp!),
                  icon: const Icon(Icons.chat),
                  label: const Text('واتساب'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green[700],
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 12),
            ],
            
            // رسالة في التطبيق
            if (_property.allowAppMessages) ...[
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _sendAppMessage,
                  icon: const Icon(Icons.message),
                  label: const Text('رسالة'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[600],
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.3),
            spreadRadius: 1,
            blurRadius: 5,
          ),
        ],
      ),
      child: Row(
        children: [
          // زر المفضلة
          IconButton(
            onPressed: _toggleFavorite,
            icon: Icon(
              _isFavorite ? Icons.favorite : Icons.favorite_border,
              color: _isFavorite ? Colors.red : Colors.grey[600],
            ),
            style: IconButton.styleFrom(
              backgroundColor: Colors.grey[100],
              padding: const EdgeInsets.all(12),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // زر التواصل الرئيسي
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _primaryContact,
              icon: const Icon(Icons.phone),
              label: const Text('تواصل الآن'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getPurposeColor() {
    switch (_property.purpose) {
      case PropertyPurpose.sale:
        return Colors.green;
      case PropertyPurpose.rent:
        return Colors.blue;
      case PropertyPurpose.exchange:
        return Colors.orange;
    }
  }

  Future<void> _toggleFavorite() async {
    try {
      await _realEstateService.toggleFavorite(_property.id);
      setState(() => _isFavorite = !_isFavorite);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isFavorite ? 'تم إضافة العقار للمفضلة' : 'تم إزالة العقار من المفضلة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث المفضلة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _shareProperty() {
    final shareText = '''
🏠 ${_property.title}

${_property.purpose.arabicName} - ${_property.propertyType.arabicName}
💰 ${_property.getFormattedPrice()}
📍 ${_property.getFullAddress()}

${_property.description}

تطبيق أرزاوو - منصة العقارات الموثوقة
    ''';

    Share.share(shareText);
  }

  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إبلاغ عن العقار'),
        content: const Text('هل تريد الإبلاغ عن هذا العقار؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _reportProperty();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('إبلاغ', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _reportProperty() async {
    try {
      await _realEstateService.reportProperty(_property.id, 'inappropriate', null);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال البلاغ بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إرسال البلاغ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _openMap() {
    if (_property.latitude != null && _property.longitude != null) {
      final url = 'https://www.google.com/maps/search/?api=1&query=${_property.latitude},${_property.longitude}';
      launchUrl(Uri.parse(url));
    }
  }

  void _makePhoneCall(String phoneNumber) {
    launchUrl(Uri.parse('tel:$phoneNumber'));
  }

  void _openWhatsApp(String phoneNumber) {
    final url = 'https://wa.me/$phoneNumber';
    launchUrl(Uri.parse(url));
  }

  Future<void> _sendAppMessage() async {
    if (_isOwner) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('لا يمكنك مراسلة نفسك')),
      );
      return;
    }

    try {
      final chatId = await _chatService.createOrGetPropertyChat(
        propertyId: _property.id,
        sellerId: _property.userId,
      );

      if (mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => PropertyChatPage(
              chatId: chatId,
              propertyTitle: _property.title,
              otherUserName: _property.ownerName ?? 'مالك العقار',
              otherUserAvatar: _property.ownerAvatar,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في فتح المحادثة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _primaryContact() {
    if (_property.contactPhone != null) {
      _makePhoneCall(_property.contactPhone!);
    } else if (_property.contactWhatsapp != null) {
      _openWhatsApp(_property.contactWhatsapp!);
    } else if (_property.allowAppMessages) {
      _sendAppMessage();
    }
  }

  Future<void> _openPropertySettings() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => PropertySettingsPage(property: _property),
      ),
    );

    // إذا تم حذف العقار، العودة للصفحة السابقة
    if (result == true && mounted) {
      Navigator.pop(context);
    }
  }
}
