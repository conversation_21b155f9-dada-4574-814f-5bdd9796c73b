# إعداد نظام المساحات بالكامل - الحل الشامل

## 🚨 المشكلة
```
ERROR: 42P01: relation "space_posts" does not exist
```

هذا يعني أن جدول `space_posts` غير موجود في قاعدة البيانات.

## ✅ الحل الشامل

### الخطوة 1: إنشاء جميع الجداول المطلوبة

قم بتنفيذ `CREATE_SPACE_POSTS_TABLE.sql` في Supabase SQL Editor أولاً:

```sql
-- هذا الملف سينشئ جميع الجداول المطلوبة:
-- 1. جدول المساحات (spaces)
-- 2. جدول منشورات المساحات (space_posts) 
-- 3. جدول متابعي المساحات (space_followers)
-- 4. جدول تفاعلات المنشورات (space_post_reactions)
-- 5. جدول تعليقات المنشورات (space_post_comments)
```

### الخطوة 2: إنشاء bucket للصور

بعد إنشاء الجداول، نفذ `ADD_SPACE_IMAGES_BUCKET.sql`:

```sql
-- إنشاء bucket لصور منشورات المساحات
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'space-images',
  'space-images', 
  true,  -- عام للجميع
  52428800,  -- 50MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 52428800,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];

-- إنشاء سياسات الأمان
CREATE POLICY "space_images_select_policy"
ON storage.objects FOR SELECT
USING (bucket_id = 'space-images');

CREATE POLICY "space_images_insert_policy"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'space-images' 
  AND auth.uid() IS NOT NULL
);
```

### الخطوة 3: التحقق من نجاح الإعداد

نفذ هذا الاستعلام للتحقق:

```sql
-- التحقق من وجود الجداول
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('spaces', 'space_posts', 'space_followers', 'space_post_reactions', 'space_post_comments');

-- التحقق من وجود bucket
SELECT * FROM storage.buckets WHERE id = 'space-images';

-- التحقق من وجود عمود media_urls
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'space_posts' AND column_name = 'media_urls';
```

### الخطوة 4: اختبار النظام

1. **أعد بناء التطبيق:**
   ```bash
   flutter clean
   flutter pub get
   flutter build apk --release
   ```

2. **اختبر الوظائف:**
   - افتح تطبيق Arzawo
   - اذهب إلى قسم المساحات
   - تأكد من وجود مساحات تجريبية
   - جرب إنشاء منشور جديد مع صور

## 📋 الملفات المطلوبة بالترتيب

### 1. `CREATE_SPACE_POSTS_TABLE.sql`
- ينشئ جميع الجداول المطلوبة
- ينشئ الفهارس والدوال المساعدة
- ينشئ سياسات الأمان
- يدرج بيانات تجريبية

### 2. `ADD_SPACE_IMAGES_BUCKET.sql`
- ينشئ bucket للصور
- ينشئ سياسات التخزين

### 3. ملفات التطبيق المحدثة:
- `lib/widgets/new_space_post_sheet.dart` - واجهة إنشاء المنشور
- `lib/supabase_service.dart` - خدمة رفع الصور
- `lib/services/space_posts_service.dart` - خدمة منشورات المساحات
- `lib/widgets/space_post_card.dart` - عرض المنشورات

## 🔍 استكشاف الأخطاء

### إذا ظهر خطأ "relation does not exist":
1. تأكد من تنفيذ `CREATE_SPACE_POSTS_TABLE.sql` أولاً
2. تحقق من وجود الجداول باستخدام الاستعلام أعلاه

### إذا لم تظهر الصور:
1. تحقق من وجود bucket `space-images`
2. تحقق من سياسات التخزين
3. تحقق من اتصال الإنترنت

### إذا لم تظهر المساحات:
1. تحقق من وجود بيانات تجريبية في جدول `spaces`
2. تحقق من سياسات الأمان

## 🎉 النتيجة المتوقعة

بعد تنفيذ جميع الخطوات:

- ✅ جدول `space_posts` موجود مع عمود `media_urls`
- ✅ bucket `space-images` موجود مع سياسات صحيحة
- ✅ يمكن اختيار حتى 4 صور للنشر
- ✅ معاينة الصور قبل النشر
- ✅ عرض الصور في المنشورات
- ✅ التمرير بين الصور المتعددة

## 📞 الدعم

إذا واجهت أي مشاكل:

1. تحقق من رسائل الخطأ في Supabase SQL Editor
2. تأكد من تنفيذ الملفات بالترتيب الصحيح
3. تحقق من وجود جميع الجداول المطلوبة
4. أعد بناء التطبيق بعد التحديثات 