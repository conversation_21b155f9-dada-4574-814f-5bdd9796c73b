import 'package:flutter/material.dart';
import '../models/job_seeker.dart';
import '../services/job_seekers_service.dart';
import '../widgets/job_seeker_card.dart';
import 'create_job_seeker_page.dart';
import 'job_seeker_details_page.dart';
import 'job_seekers_settings_page.dart';

class JobSeekersPage extends StatefulWidget {
  const JobSeekersPage({super.key});

  @override
  State<JobSeekersPage> createState() => _JobSeekersPageState();
}

class _JobSeekersPageState extends State<JobSeekersPage> {
  final JobSeekersService _jobSeekersService = JobSeekersService();
  final TextEditingController _searchController = TextEditingController();
  
  List<JobSeeker> _jobSeekers = [];
  bool _loading = true;
  
  String _selectedCity = 'الكل';
  String _selectedCategory = 'الكل';
  String _selectedJobType = 'الكل';
  String _selectedCountry = 'الكل';
  
  Map<String, int> _stats = {
    'total': 0,
    'new_this_week': 0,
    'top_category_count': 0,
  };

  final List<String> _cities = [
    'الكل', 'الرياض', 'جدة', 'مكة', 'المدينة', 'الدمام', 'الخبر', 'تبوك', 'أبها', 'جازان'
  ];

  final List<String> _countries = [
    'الكل', 'السعودية', 'الإمارات', 'الكويت', 'قطر', 'البحرين', 'عمان', 'الأردن', 'لبنان', 'مصر'
  ];

  @override
  void initState() {
    super.initState();
    _loadJobSeekers();
    _loadStats();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadJobSeekers() async {
    setState(() => _loading = true);
    try {
      final jobSeekers = await _jobSeekersService.searchJobSeekers(
        searchTerm: _searchController.text.isEmpty ? null : _searchController.text,
        city: _selectedCity == 'الكل' ? null : _selectedCity,
        category: _selectedCategory == 'الكل' ? null : _selectedCategory,
        jobType: _selectedJobType == 'الكل' ? null : _selectedJobType,
        country: _selectedCountry == 'الكل' ? null : _selectedCountry,
      );
      
      setState(() {
        _jobSeekers = jobSeekers;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadStats() async {
    try {
      final stats = await _jobSeekersService.getJobSeekersStats();
      setState(() => _stats = stats);
    } catch (e) {
      debugPrint('خطأ في تحميل الإحصائيات: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'البحث عن عمل',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.indigo[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _openSettings(),
            tooltip: 'الإعدادات',
          ),
          IconButton(
            icon: const Icon(Icons.person_add),
            onPressed: () => _checkUserProfile(),
            tooltip: 'إنشاء ملف مهني',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الإحصائيات
          _buildStatsBar(),
          
          // شريط البحث
          _buildSearchBar(),
          
          // شريط التصفية
          _buildFilterBar(),
          
          // قائمة الباحثين عن عمل
          Expanded(
            child: _buildJobSeekersList(),
          ),
        ],
      ),
      floatingActionButton: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          FloatingActionButton(
            heroTag: "settings",
            onPressed: () => _openSettings(),
            backgroundColor: Colors.grey[600],
            child: const Icon(Icons.settings, color: Colors.white),
          ),
          const SizedBox(height: 8),
          FloatingActionButton.extended(
            heroTag: "create",
            onPressed: () => _checkUserProfile(),
            backgroundColor: Colors.indigo[600],
            icon: const Icon(Icons.add, color: Colors.white),
            label: const Text(
              'أنشئ ملفك المهني',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.indigo[600],
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem('إجمالي الباحثين', '${_stats['total']}', Icons.people, Colors.white),
          _buildStatItem('جديد هذا الأسبوع', '${_stats['new_this_week']}', Icons.new_releases, Colors.white),
          _buildStatItem('أكثر المهن طلباً', '${_stats['top_category_count']}', Icons.trending_up, Colors.white),
        ],
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          title,
          style: TextStyle(
            color: color,
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'ابحث عن مهنة أو مهارة...',
          prefixIcon: Icon(Icons.search, color: Colors.indigo[600]),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _loadJobSeekers();
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.indigo[200]!),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.indigo[600]!),
          ),
          filled: true,
          fillColor: Colors.grey[50],
        ),
        onSubmitted: (_) => _loadJobSeekers(),
      ),
    );
  }

  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildFilterDropdown(
              'المدينة',
              _selectedCity,
              _cities,
              (value) => setState(() => _selectedCity = value!),
            ),
            const SizedBox(width: 12),
            _buildFilterDropdown(
              'المهنة',
              _selectedCategory,
              ['الكل'] + JobCategory.values.map((c) => c.arabicName).toList(),
              (value) => setState(() => _selectedCategory = value!),
            ),
            const SizedBox(width: 12),
            _buildFilterDropdown(
              'نوع العمل',
              _selectedJobType,
              ['الكل'] + JobType.values.map((t) => t.arabicName).toList(),
              (value) => setState(() => _selectedJobType = value!),
            ),
            const SizedBox(width: 12),
            _buildFilterDropdown(
              'البلد',
              _selectedCountry,
              _countries,
              (value) => setState(() => _selectedCountry = value!),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterDropdown(
    String label,
    String value,
    List<String> items,
    void Function(String?) onChanged,
  ) {
    return Container(
      width: 120,
      child: DropdownButtonFormField<String>(
        value: value,
        decoration: InputDecoration(
          labelText: label,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        ),
        items: items.map((item) => DropdownMenuItem(
          value: item,
          child: Text(item, style: const TextStyle(fontSize: 12)),
        )).toList(),
        onChanged: (newValue) {
          onChanged(newValue);
          _loadJobSeekers();
        },
      ),
    );
  }

  Widget _buildJobSeekersList() {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_jobSeekers.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.person_search,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد ملفات مهنية',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'كن أول من ينشئ ملفه المهني',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () => _checkUserProfile(),
              icon: const Icon(Icons.add),
              label: const Text('أنشئ ملفك المهني'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo[600],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadJobSeekers,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _jobSeekers.length,
        itemBuilder: (context, index) {
          return JobSeekerCard(
            jobSeeker: _jobSeekers[index],
            onTap: () => _openJobSeekerDetails(_jobSeekers[index]),
          );
        },
      ),
    );
  }

  void _checkUserProfile() async {
    try {
      final userProfile = await _jobSeekersService.getUserJobSeeker();
      if (userProfile != null) {
        // المستخدم لديه ملف مهني، اذهب لصفحة التفاصيل
        _openJobSeekerDetails(userProfile);
      } else {
        // المستخدم ليس لديه ملف مهني، اذهب لصفحة الإنشاء
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => const CreateJobSeekerPage()),
        ).then((_) => _loadJobSeekers());
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _openJobSeekerDetails(JobSeeker jobSeeker) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => JobSeekerDetailsPage(jobSeeker: jobSeeker),
      ),
    ).then((_) => _loadJobSeekers());
  }

  void _openSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const JobSeekersSettingsPage(),
      ),
    ).then((_) => _loadJobSeekers());
  }
}
