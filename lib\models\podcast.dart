import 'package:flutter/material.dart';

enum PodcastCategory {
  religion,
  selfDevelopment,
  story,
  children,
  talent,
  education,
  news,
  entertainment,
  health,
  technology,
  other,
}

extension PodcastCategoryExtension on PodcastCategory {
  String get displayName {
    switch (this) {
      case PodcastCategory.religion:
        return 'ديني';
      case PodcastCategory.selfDevelopment:
        return 'تطوير ذات';
      case PodcastCategory.story:
        return 'قصة';
      case PodcastCategory.children:
        return 'أطفال';
      case PodcastCategory.talent:
        return 'مواهب';
      case PodcastCategory.education:
        return 'تعليمي';
      case PodcastCategory.news:
        return 'أخبار';
      case PodcastCategory.entertainment:
        return 'ترفيه';
      case PodcastCategory.health:
        return 'صحة';
      case PodcastCategory.technology:
        return 'تقنية';
      case PodcastCategory.other:
        return 'أخرى';
    }
  }

  Color get color {
    switch (this) {
      case PodcastCategory.religion:
        return Colors.green;
      case PodcastCategory.selfDevelopment:
        return Colors.blue;
      case PodcastCategory.story:
        return Colors.purple;
      case PodcastCategory.children:
        return Colors.orange;
      case PodcastCategory.talent:
        return Colors.pink;
      case PodcastCategory.education:
        return Colors.indigo;
      case PodcastCategory.news:
        return Colors.red;
      case PodcastCategory.entertainment:
        return Colors.amber;
      case PodcastCategory.health:
        return Colors.teal;
      case PodcastCategory.technology:
        return Colors.cyan;
      case PodcastCategory.other:
        return Colors.grey;
    }
  }

  IconData get icon {
    switch (this) {
      case PodcastCategory.religion:
        return Icons.mosque;
      case PodcastCategory.selfDevelopment:
        return Icons.psychology;
      case PodcastCategory.story:
        return Icons.auto_stories;
      case PodcastCategory.children:
        return Icons.child_care;
      case PodcastCategory.talent:
        return Icons.star;
      case PodcastCategory.education:
        return Icons.school;
      case PodcastCategory.news:
        return Icons.newspaper;
      case PodcastCategory.entertainment:
        return Icons.theater_comedy;
      case PodcastCategory.health:
        return Icons.health_and_safety;
      case PodcastCategory.technology:
        return Icons.computer;
      case PodcastCategory.other:
        return Icons.more_horiz;
    }
  }
}

class Podcast {
  final String id;
  final String userId;
  final String userName;
  final String userAvatar;
  final String title;
  final String? description;
  final String audioUrl;
  final String? coverImageUrl;
  final PodcastCategory category;
  final Duration duration;
  final DateTime createdAt;
  final DateTime? updatedAt;
  int likesCount;
  int commentsCount;
  int playsCount;
  int sharesCount;
  int downloadsCount;
  bool isLiked;
  bool isSaved;
  bool isDownloaded;
  final bool isVerified; // حالة توثيق المستخدم
  final String? tags; // الكلمات المفتاحية
  final bool allowDownload; // السماح بالتحميل
  final bool allowComments; // السماح بالتعليقات

  Podcast({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userAvatar,
    required this.title,
    this.description,
    required this.audioUrl,
    this.coverImageUrl,
    required this.category,
    required this.duration,
    required this.createdAt,
    this.updatedAt,
    this.likesCount = 0,
    this.commentsCount = 0,
    this.playsCount = 0,
    this.sharesCount = 0,
    this.downloadsCount = 0,
    this.isLiked = false,
    this.isSaved = false,
    this.isDownloaded = false,
    this.isVerified = false,
    this.tags,
    this.allowDownload = true,
    this.allowComments = true,
  });

  factory Podcast.fromMap(Map<String, dynamic> map) {
    return Podcast(
      id: map['id'] ?? '',
      userId: map['user_id'] ?? '',
      userName: map['user_name'] ?? '',
      userAvatar: map['user_avatar'] ?? '',
      title: map['title'] ?? '',
      description: map['description'],
      audioUrl: map['audio_url'] ?? '',
      coverImageUrl: map['cover_image_url'],
      category: PodcastCategory.values.firstWhere(
        (e) => e.name == map['category'],
        orElse: () => PodcastCategory.other,
      ),
      duration: Duration(seconds: map['duration_seconds'] ?? 0),
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      likesCount: map['likes_count'] ?? 0,
      commentsCount: map['comments_count'] ?? 0,
      playsCount: map['plays_count'] ?? 0,
      sharesCount: map['shares_count'] ?? 0,
      downloadsCount: map['downloads_count'] ?? 0,
      isLiked: map['is_liked'] ?? false,
      isSaved: map['is_saved'] ?? false,
      isDownloaded: map['is_downloaded'] ?? false,
      isVerified: map['is_verified'] ?? false,
      tags: map['tags'],
      allowDownload: map['allow_download'] ?? true,
      allowComments: map['allow_comments'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'user_name': userName,
      'user_avatar': userAvatar,
      'title': title,
      'description': description,
      'audio_url': audioUrl,
      'cover_image_url': coverImageUrl,
      'category': category.name,
      'duration_seconds': duration.inSeconds,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'likes_count': likesCount,
      'comments_count': commentsCount,
      'plays_count': playsCount,
      'shares_count': sharesCount,
      'downloads_count': downloadsCount,
      'is_liked': isLiked,
      'is_saved': isSaved,
      'is_downloaded': isDownloaded,
      'is_verified': isVerified,
      'tags': tags,
      'allow_download': allowDownload,
      'allow_comments': allowComments,
    };
  }

  String get formattedDuration {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  String get formattedPlaysCount {
    if (playsCount < 1000) return playsCount.toString();
    if (playsCount < 1000000) return '${(playsCount / 1000).toStringAsFixed(1)}ك';
    return '${(playsCount / 1000000).toStringAsFixed(1)}م';
  }

  Podcast copyWith({
    String? id,
    String? userId,
    String? userName,
    String? userAvatar,
    String? title,
    String? description,
    String? audioUrl,
    String? coverImageUrl,
    PodcastCategory? category,
    Duration? duration,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? likesCount,
    int? commentsCount,
    int? playsCount,
    int? sharesCount,
    int? downloadsCount,
    bool? isLiked,
    bool? isSaved,
    bool? isDownloaded,
    bool? isVerified,
    String? tags,
    bool? allowDownload,
    bool? allowComments,
  }) {
    return Podcast(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      userName: userName ?? this.userName,
      userAvatar: userAvatar ?? this.userAvatar,
      title: title ?? this.title,
      description: description ?? this.description,
      audioUrl: audioUrl ?? this.audioUrl,
      coverImageUrl: coverImageUrl ?? this.coverImageUrl,
      category: category ?? this.category,
      duration: duration ?? this.duration,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      playsCount: playsCount ?? this.playsCount,
      sharesCount: sharesCount ?? this.sharesCount,
      downloadsCount: downloadsCount ?? this.downloadsCount,
      isLiked: isLiked ?? this.isLiked,
      isSaved: isSaved ?? this.isSaved,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      isVerified: isVerified ?? this.isVerified,
      tags: tags ?? this.tags,
      allowDownload: allowDownload ?? this.allowDownload,
      allowComments: allowComments ?? this.allowComments,
    );
  }
}
