-- إدراج بيانات تجريبية لنظام التصويتات
-- يجب تشغيل polls_final.sql أولاً

-- التحقق من وجود مستخدمين
DO $$
DECLARE
    user_count INTEGER;
    sample_user_id UUID;
    poll_id UUID;
BEGIN
    -- عد المستخدمين الموجودين
    SELECT COUNT(*) INTO user_count FROM profiles;
    
    IF user_count = 0 THEN
        RAISE NOTICE 'لا يوجد مستخدمين في النظام. يجب إنشاء مستخدم أولاً.';
        RETURN;
    END IF;
    
    -- الحصول على أول مستخدم
    SELECT id INTO sample_user_id FROM profiles LIMIT 1;
    
    RAISE NOTICE 'سيتم استخدام المستخدم: %', sample_user_id;

    -- 1. تصويت كأس العالم
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (sample_user_id, 'أي منتخب تتوقع أن يفوز بكأس العالم 2026؟', 'public', 'sports', 'oneWeek', true, false, NOW() + INTERVAL '7 days')
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'البرازيل 🇧🇷', 0),
    (poll_id, 'الأرجنتين 🇦🇷', 1),
    (poll_id, 'فرنسا 🇫🇷', 2),
    (poll_id, 'إنجلترا 🏴󠁧󠁢󠁥󠁮󠁧󠁿', 3),
    (poll_id, 'إسبانيا 🇪🇸', 4);

    -- 2. تصويت الذكاء الاصطناعي
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (sample_user_id, 'ما هو أفضل نموذج ذكاء اصطناعي؟', 'public', 'technology', 'unlimited', true, true)
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'ChatGPT', 0),
    (poll_id, 'Claude', 1),
    (poll_id, 'Gemini', 2),
    (poll_id, 'Copilot', 3);

    -- 3. تصويت وسائل التواصل
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (sample_user_id, 'ما هي منصة التواصل المفضلة لديك؟', 'public', 'general', 'threeDays', true, true, NOW() + INTERVAL '3 days')
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'تويتر (X) 🐦', 0),
    (poll_id, 'إنستغرام 📸', 1),
    (poll_id, 'تيك توك 🎵', 2),
    (poll_id, 'يوتيوب 📺', 3);

    -- 4. تصويت صحي
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (sample_user_id, 'ما هو أهم عنصر في النظام الغذائي؟', 'public', 'health', 'unlimited', true, false)
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'الخضروات والفواكه 🥗', 0),
    (poll_id, 'البروتينات 🥩', 1),
    (poll_id, 'الحبوب الكاملة 🌾', 2),
    (poll_id, 'شرب الماء 💧', 3);

    -- 5. تصويت تعليمي
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (sample_user_id, 'ما هو أفضل موقع للتعلم الإلكتروني؟', 'public', 'education', 'unlimited', true, true)
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'يوتيوب', 0),
    (poll_id, 'كورسيرا', 1),
    (poll_id, 'يوديمي', 2),
    (poll_id, 'خان أكاديمي', 3);

    -- 6. تصويت ترفيهي
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (sample_user_id, 'ما هو نوع الأفلام المفضل لديك؟', 'public', 'entertainment', 'unlimited', true, true)
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'الأكشن والمغامرات 🎬', 0),
    (poll_id, 'الكوميديا والضحك 😂', 1),
    (poll_id, 'الدراما والرومانسية 💕', 2),
    (poll_id, 'الخيال العلمي 🚀', 3);

    -- 7. تصويت أعمال
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (sample_user_id, 'ما هو أفضل نموذج عمل؟', 'public', 'business', 'oneDay', true, false, NOW() + INTERVAL '1 day')
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'العمل من المكتب 🏢', 0),
    (poll_id, 'العمل عن بُعد 🏠', 1),
    (poll_id, 'العمل المختلط ⚖️', 2),
    (poll_id, 'العمل المرن 📋', 3);

    -- 8. تصويت ديني
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (sample_user_id, 'ما هو أفضل شكل للعمل الخيري؟', 'public', 'religion', 'unlimited', true, false)
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'التبرع المالي 💰', 0),
    (poll_id, 'التطوع بالوقت ⏰', 1),
    (poll_id, 'الخدمات المهنية 🛠️', 2),
    (poll_id, 'نشر الوعي 📢', 3);

    -- 9. تصويت سياسي
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (sample_user_id, 'ما هي أولوية العالم العربي؟', 'public', 'politics', 'unlimited', true, false)
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'الأمن والاستقرار 🕊️', 0),
    (poll_id, 'التنمية الاقتصادية 📈', 1),
    (poll_id, 'التطوير التعليمي 📚', 2),
    (poll_id, 'القضية الفلسطينية 🇵🇸', 3);

    -- 10. تصويت مجتمعي
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (sample_user_id, 'ما هو أهم إجراء لحماية البيئة؟', 'public', 'community', 'threeDays', true, true, NOW() + INTERVAL '3 days')
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'تقليل البلاستيك ♻️', 0),
    (poll_id, 'توفير الطاقة 💡', 1),
    (poll_id, 'النقل العام 🚌', 2),
    (poll_id, 'إعادة التدوير 🔄', 3);

    RAISE NOTICE 'تم إدراج 10 تصويتات تجريبية بنجاح!';
    
END $$;

-- عرض النتائج
SELECT 
    question,
    category,
    duration,
    (SELECT COUNT(*) FROM poll_options WHERE poll_id = polls.id) as options_count,
    created_at
FROM polls 
ORDER BY created_at DESC;

-- رسالة نجاح
SELECT 'تم إدراج البيانات التجريبية بنجاح!' as message;
