-- إنشاء جدول saved_posts للمنشورات المحفوظة
-- قم بتنفيذ هذه الأوامر في Supabase SQL Editor

-- إنشاء جدول saved_posts إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS saved_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, post_id)
);

-- إنشاء RLS (Row Level Security)
ALTER TABLE saved_posts ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسة الأمان - المستخدم يمكنه رؤية وتعديل محفوظاته فقط
CREATE POLICY "Users can view their own saved posts" ON saved_posts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own saved posts" ON saved_posts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own saved posts" ON saved_posts
    FOR DELETE USING (auth.uid() = user_id);

-- إنشاء indexes لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_saved_posts_user_id ON saved_posts(user_id);
CREATE INDEX IF NOT EXISTS idx_saved_posts_post_id ON saved_posts(post_id);
CREATE INDEX IF NOT EXISTS idx_saved_posts_created_at ON saved_posts(created_at);

-- التحقق من إنشاء الجدول
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'saved_posts' 
ORDER BY column_name; 