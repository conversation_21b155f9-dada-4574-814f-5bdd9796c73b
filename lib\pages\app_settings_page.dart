import 'package:flutter/material.dart';
import '../supabase_service.dart';
import '../main.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'blocked_users_page.dart';
import 'watch_later_page.dart';

class AppSettingsPage extends StatefulWidget {
  const AppSettingsPage({super.key});

  @override
  State<AppSettingsPage> createState() => _AppSettingsPageState();
}

class _AppSettingsPageState extends State<AppSettingsPage> {
  bool notifyFollow = true;
  bool notifyLike = true;
  bool notifyComment = true;
  bool notifyChat = true;
  bool notifyApp = true;

  bool darkMode = themeModeNotifier.value == ThemeMode.dark;

  String defaultVisibility = 'public';
  String commentsFrom = 'everyone';

  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    final s = await SupabaseService().fetchSettings();
    setState(() {
      notifyFollow = s['notify_follow'] ?? true;
      notifyLike = s['notify_like'] ?? true;
      notifyComment = s['notify_comment'] ?? true;
      notifyChat = s['notify_chat'] ?? true;
      notifyApp = s['notify_app'] ?? true;
      defaultVisibility = s['default_visibility'] ?? 'public';
      commentsFrom = s['comments_from'] ?? 'everyone';
      _loading = false;
    });
  }

  bool _isSaving = false;

  Future<void> _save() async {
    if (_isSaving) return; // منع الضغط المتكرر
    
    setState(() {
      _isSaving = true;
    });

    try {
      print('=== بدء حفظ إعدادات التطبيق ===');
      print('notifyFollow: $notifyFollow');
      print('notifyLike: $notifyLike');
      print('notifyComment: $notifyComment');
      print('notifyChat: $notifyChat');
      print('notifyApp: $notifyApp');
      print('defaultVisibility: $defaultVisibility');
      print('commentsFrom: $commentsFrom');

      // إظهار مؤشر التحميل
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 16),
                Text('جاري حفظ الإعدادات...'),
              ],
            ),
            duration: Duration(seconds: 2),
          ),
        );
      }

      final settingsData = {
        'notify_follow': notifyFollow,
        'notify_like': notifyLike,
        'notify_comment': notifyComment,
        'notify_chat': notifyChat,
        'notify_app': notifyApp,
        'default_visibility': defaultVisibility,
        'comments_from': commentsFrom,
      };
      
      print('بيانات الإعدادات: $settingsData');
      
      await SupabaseService().updateSettings(settingsData);
      
      print('تم حفظ إعدادات التطبيق بنجاح!');

      // إظهار رسالة نجاح
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ الإعدادات بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      print('خطأ في حفظ إعدادات التطبيق: $e');
      // إظهار رسالة خطأ
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ الإعدادات: $e'),
            backgroundColor: Colors.red,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } finally {
      print('=== انتهاء حفظ إعدادات التطبيق ===');
      // إعادة تعيين حالة التحميل
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) return const Center(child: CircularProgressIndicator());
    return Scaffold(
      appBar: AppBar(title: const Text('إعدادات التطبيق')),
      body: ListView(
        children: [
          const ListTile(title: Text('المظهر', style: TextStyle(fontWeight: FontWeight.bold))),
          SwitchListTile(
            title: const Text('الوضع الداكن'),
            value: darkMode,
            onChanged: (v) async {
              setState(() => darkMode = v);
              themeModeNotifier.value = v ? ThemeMode.dark : ThemeMode.light;
              final prefs = await SharedPreferences.getInstance();
              await prefs.setString('theme_mode', v ? 'dark' : 'light');
            },
          ),
          ListTile( // NEW: اختيار اللغة
            title: const Text('اللغة'),
            trailing: DropdownButton<String>(
              value: localeNotifier.value.languageCode,
              items: const [
                DropdownMenuItem(value: 'ar', child: Text('العربية')),
                DropdownMenuItem(value: 'en', child: Text('English')),
              ],
              onChanged: (v) async {
                if (v == null) return;
                localeNotifier.value = Locale(v, '');
                final prefs = await SharedPreferences.getInstance();
                await prefs.setString('locale', v);
                setState(() {});
              },
            ),
          ),
          const ListTile(title: Text('الخصوصية', style: TextStyle(fontWeight: FontWeight.bold))),
          ListTile(
            title: const Text('من يمكنه رؤية منشوراتى الجديدة؟'),
            trailing: DropdownButton<String>(
              value: defaultVisibility,
              items: const [
                DropdownMenuItem(value: 'public', child: Text('الجميع')),
                DropdownMenuItem(value: 'followers', child: Text('المتابعون فقط')),
                DropdownMenuItem(value: 'private', child: Text('أنا فقط')),
              ],
              onChanged: (v) => setState(() => defaultVisibility = v!),
            ),
          ),
          ListTile(
            title: const Text('من يمكنه التعليق على منشوراتى؟'),
            trailing: DropdownButton<String>(
              value: commentsFrom,
              items: const [
                DropdownMenuItem(value: 'everyone', child: Text('الجميع')),
                DropdownMenuItem(value: 'followers', child: Text('المتابعون فقط')),
              ],
              onChanged: (v) => setState(() => commentsFrom = v!),
            ),
          ),
          const Divider(),
          const ListTile(title: Text('الإشعارات', style: TextStyle(fontWeight: FontWeight.bold))),
          SwitchListTile(title: const Text('إشعار المتابعين'), value: notifyFollow, onChanged: (v)=>setState(()=>notifyFollow=v)),
          SwitchListTile(title: const Text('إشعار الإعجابات'), value: notifyLike, onChanged: (v)=>setState(()=>notifyLike=v)),
          SwitchListTile(title: const Text('إشعار التعليقات'), value: notifyComment, onChanged: (v)=>setState(()=>notifyComment=v)),
          SwitchListTile(title: const Text('إشعار الدردشة'), value: notifyChat, onChanged: (v)=>setState(()=>notifyChat=v)),
          SwitchListTile(title: const Text('إشعارات التطبيق العامة'), value: notifyApp, onChanged: (v)=>setState(()=>notifyApp=v)),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ElevatedButton(
              onPressed: _isSaving ? null : _save,
              child: _isSaving 
                ? const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                      SizedBox(width: 8),
                      Text('جاري الحفظ...'),
                    ],
                  )
                : const Text('حفظ الإعدادات'),
            ),
          ),
          const Divider(),
          const ListTile(title: Text('الحساب', style: TextStyle(fontWeight: FontWeight.bold))),
          ListTile(
            leading: const Icon(Icons.block),
            title: const Text('المستخدمون المحظورون'),
            onTap: () {
              Navigator.push(context, MaterialPageRoute(builder: (_) => const BlockedUsersPage()));
            },
          ),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.red),
            title: const Text('تسجيل الخروج'),
            onTap: () async {
              await SupabaseService().signOut();
              if (context.mounted) {
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (_) => const LoginPage()),
                  (route) => false,
                );
              }
            },
          ),
          ListTile(
            leading: const Icon(Icons.pause_circle_filled, color: Colors.orange),
            title: const Text('تعطيل الحساب مؤقتًا'),
            subtitle: const Text('إخفاء حسابك مؤقتاً من البحث'),
            onTap: () => _showDeactivateOptions(),
          ),
          ListTile(
            leading: const Icon(Icons.delete_forever, color: Colors.red),
            title: const Text('حذف الحساب نهائيًا'),
            subtitle: const Text('سيتم جدولة حذف حسابك نهائياً'),
            onTap: () => _showDeleteOptions(),
          ),
          const Divider(),
          const ListTile(title: Text('وثائق قانونية', style: TextStyle(fontWeight: FontWeight.bold))),
          ListTile(
            leading: const Icon(Icons.privacy_tip),
            title: const Text('سياسة الخصوصية'),
            onTap: () => _openUrl('https://arzawo.com/privacy.html'),
          ),
          ListTile(
            leading: const Icon(Icons.description),
            title: const Text('شروط الاستخدام'),
            onTap: () => _openUrl('https://arzawo.com/terms.html'),
          ),
          ListTile(
            leading: const Icon(Icons.contact_mail),
            title: const Text('تواصل معنا'),
            onTap: () => _openUrl('https://arzawo.com/contact.html'),
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.history),
            title: const Text('للمشاهدة لاحقًا'),
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(builder: (_) => const WatchLaterPage()),
            ),
          ),
        ],
      ),
    );
  }

  Future<bool> _confirm(String title, String message) async {
    final res = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('إلغاء')),
          TextButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('تأكيد')),
        ],
      ),
    );
    return res == true;
  }

  Future<void> _openUrl(String url) async {
    final uri = Uri.parse(url);
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تعذر فتح الرابط')));
      }
    }
  }

  void _showDeactivateOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.pause_circle_filled, color: Colors.orange),
            SizedBox(width: 8),
            Text('تعطيل الحساب مؤقتاً'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'اختر مدة تعطيل حسابك:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            Text(
              '• سيتم إخفاء حسابك من البحث\n'
              '• لن تتمكن من تسجيل الدخول خلال فترة التعطيل\n'
              '• يمكنك إعادة التفعيل في أي وقت',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _showDeactivateDuration(),
            child: const Text('متابعة'),
          ),
        ],
      ),
    );
  }

  void _showDeactivateDuration() {
    Navigator.pop(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر مدة التعطيل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDurationOption('3 أيام', 3),
            _buildDurationOption('أسبوع واحد', 7),
            _buildDurationOption('أسبوعين', 14),
            _buildDurationOption('شهر واحد', 30),
            _buildDurationOption('3 أشهر', 90),
            _buildDurationOption('6 أشهر', 180),
            _buildDurationOption('سنة واحدة', 365),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Widget _buildDurationOption(String title, int days) {
    return ListTile(
      title: Text(title),
      trailing: const Icon(Icons.chevron_right),
      onTap: () => _confirmDeactivation(days),
    );
  }

  Future<void> _confirmDeactivation(int days) async {
    Navigator.pop(context);
    
    final durationText = days == 1 ? 'يوم واحد' : 
                        days == 3 ? '3 أيام' :
                        days == 7 ? 'أسبوع واحد' :
                        days == 14 ? 'أسبوعين' :
                        days == 30 ? 'شهر واحد' :
                        days == 90 ? '3 أشهر' :
                        days == 180 ? '6 أشهر' :
                        days == 365 ? 'سنة واحدة' : '$days يوم';

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد تعطيل الحساب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('سيتم تعطيل حسابك لمدة: $durationText'),
            const SizedBox(height: 16),
            const Text(
              'خلال هذه الفترة:\n'
              '• لن يظهر حسابك في البحث\n'
              '• لن تتمكن من تسجيل الدخول\n'
              '• لن تتلقى إشعارات\n'
              '• يمكنك إعادة التفعيل في أي وقت',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
            child: const Text('تعطيل الحساب'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _deactivateAccount(days);
    }
  }

  Future<void> _deactivateAccount(int days) async {
    try {
      // إظهار مؤشر التحميل
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 16),
                Text('جاري تعطيل الحساب...'),
              ],
            ),
            duration: Duration(seconds: 2),
          ),
        );
      }

      await SupabaseService().deactivateAccount(duration: days);
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تعطيل الحساب لمدة $days يوم'),
            backgroundColor: Colors.orange,
          ),
        );
        
        await SupabaseService().signOut();
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (_) => const LoginPage()),
          (route) => false,
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تعطيل الحساب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showDeleteOptions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.delete_forever, color: Colors.red),
            SizedBox(width: 8),
            Text('حذف الحساب نهائياً'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تحذير: هذا الإجراء نهائي ولا يمكن التراجع عنه!',
              style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red),
            ),
            SizedBox(height: 16),
            Text(
              'عند حذف حسابك:\n'
              '• سيتم حذف جميع بياناتك نهائياً\n'
              '• لن تتمكن من استرداد أي محتوى\n'
              '• سيتم حذف جميع المنشورات والتعليقات\n'
              '• لن تتمكن من تسجيل الدخول مرة أخرى',
              style: TextStyle(fontSize: 14),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => _showDeleteDuration(),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('متابعة'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDuration() {
    Navigator.pop(context);
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('اختر مدة التأخير'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'يمكنك تأخير حذف الحساب لفرصة إعادة النظر:',
              style: TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 16),
            _buildDeleteOption('حذف فوري', 0),
            _buildDeleteOption('بعد 7 أيام', 7),
            _buildDeleteOption('بعد 14 يوم', 14),
            _buildDeleteOption('بعد 30 يوم', 30),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
        ],
      ),
    );
  }

  Widget _buildDeleteOption(String title, int days) {
    return ListTile(
      title: Text(title),
      subtitle: days == 0 
        ? const Text('حذف فوري - لا يمكن التراجع')
        : Text('سيتم الحذف بعد $days يوم'),
      trailing: const Icon(Icons.chevron_right),
      onTap: () => _confirmDelete(days),
    );
  }

  Future<void> _confirmDelete(int days) async {
    Navigator.pop(context);
    
    final delayText = days == 0 ? 'فورياً' : 
                     days == 7 ? 'بعد 7 أيام' :
                     days == 14 ? 'بعد 14 يوم' :
                     days == 30 ? 'بعد 30 يوم' : 'بعد $days يوم';

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد حذف الحساب'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('سيتم حذف حسابك $delayText'),
            const SizedBox(height: 16),
            const Text(
              'هذا الإجراء نهائي:\n'
              '• سيتم حذف جميع بياناتك\n'
              '• لن تتمكن من استرداد أي محتوى\n'
              '• سيتم حذف جميع المنشورات والتعليقات\n'
              '• لن تتمكن من تسجيل الدخول مرة أخرى',
              style: TextStyle(fontSize: 14),
            ),
            if (days > 0) ...[
              const SizedBox(height: 16),
              Text(
                'يمكنك إلغاء الحذف خلال $days يوم من خلال تسجيل الدخول مرة أخرى.',
                style: TextStyle(fontSize: 14, color: Colors.blue),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف الحساب'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _deleteAccount(days);
    }
  }

  Future<void> _deleteAccount(int days) async {
    try {
      // إظهار مؤشر التحميل
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 16),
                Text('جاري جدولة حذف الحساب...'),
              ],
            ),
            duration: Duration(seconds: 2),
          ),
        );
      }

      await SupabaseService().deleteAccount(delayDays: days);
      
      if (context.mounted) {
        final delayText = days == 0 ? 'فورياً' : 
                         days == 7 ? 'بعد 7 أيام' :
                         days == 14 ? 'بعد 14 يوم' :
                         days == 30 ? 'بعد 30 يوم' : 'بعد $days يوم';
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم جدولة حذف الحساب $delayText'),
            backgroundColor: Colors.red,
          ),
        );
        
        await SupabaseService().signOut();
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (_) => const LoginPage()),
          (route) => false,
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف الحساب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
} 