import 'package:flutter/material.dart';
import '../models/community.dart';
import '../supabase_service.dart';
import 'create_community_page.dart';
import 'community_detail_page.dart';
import '../app_theme.dart';

class CommunityPage extends StatefulWidget {
  const CommunityPage({super.key});

  @override
  State<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends State<CommunityPage> {
  late Future<List<Community>> _future;

  @override
  void initState() {
    super.initState();
    _future = SupabaseService().fetchCommunities();
  }

  Future<void> _refresh() async {
    setState(() {
      _future = SupabaseService().fetchCommunities();
    });
  }

  void _openCreate() async {
    final created = await Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => const CreateCommunityPage()),
    );
    if (created == true) _refresh();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('المجتمعات')),
      body: RefreshIndicator(
        onRefresh: _refresh,
        child: FutureBuilder<List<Community>>(
          future: _future,
          builder: (context, snapshot) {
            if (snapshot.hasError) {
              return Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.error, color: Colors.red),
                    const SizedBox(height: 8),
                    Text('خطأ: ${snapshot.error}')
                  ],
                ),
              );
            }
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Center(child: CircularProgressIndicator());
            }
            final items = snapshot.data ?? [];
            if (items.isEmpty) {
              return const Center(child: Text('لا توجد مجتمعات بعد'));
            }
            return ListView.builder(
              padding: const EdgeInsets.fromLTRB(12, 12, 12, 100), // إضافة مساحة للأسفل
              itemCount: items.length,
              itemBuilder: (context, index) {
                final c = items[index];
                return InkWell(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(builder: (_) => CommunityDetailPage(community: c)),
                    );
                  },
                  child: Card(
                    elevation: 1,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // صورة المجتمع (أولوية للصورة الشخصية ثم الغلاف)
                          if (c.avatarUrl != null && c.avatarUrl!.isNotEmpty)
                            Hero(
                              tag: 'community_avatar_${c.id}',
                              child: CircleAvatar(
                                radius: 36,
                                backgroundImage: NetworkImage(c.avatarUrl!),
                              ),
                            )
                          else if (c.coverUrl != null && c.coverUrl!.isNotEmpty)
                            Hero(
                              tag: 'community_cover_${c.id}',
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: Image.network(c.coverUrl!, width: 72, height: 72, fit: BoxFit.cover),
                              ),
                            )
                          else
                            Container(
                              width: 72,
                              height: 72,
                              decoration: BoxDecoration(
                                color: AppTheme.primaryColor.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              alignment: Alignment.center,
                              child: Text(
                                c.name.isNotEmpty ? c.name.substring(0, 1).toUpperCase() : 'M',
                                style: const TextStyle(fontSize: 28, fontWeight: FontWeight.bold, color: Colors.white),
                              ),
                            ),
                          const SizedBox(width: 12),
                          // المعلومات
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(c.name, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                                const SizedBox(height: 6),
                                if (c.category != null && c.category!.isNotEmpty)
                                  Container(
                                    margin: const EdgeInsets.only(bottom: 4),
                                    child: Chip(
                                      padding: EdgeInsets.zero,
                                      label: Text(c.category!, style: const TextStyle(fontSize: 12)),
                                    ),
                                  ),
                                if (c.description != null && c.description!.isNotEmpty)
                                  Text(
                                    c.description!,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(color: Colors.grey[700]),
                                  ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 8),
                          // عدد الأعضاء
                          Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(Icons.group, size: 18, color: Colors.grey),
                              const SizedBox(height: 2),
                              Text(
                                '${c.membersCount}',
                                style: const TextStyle(fontSize: 12, color: Colors.grey),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
      floatingActionButton: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          gradient: LinearGradient(
            colors: [Colors.red.shade400, Colors.red.shade600],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.red.withValues(alpha: 0.3),
              spreadRadius: 2,
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: FloatingActionButton(
          onPressed: _openCreate,
          backgroundColor: Colors.transparent,
          elevation: 0,
          child: const Icon(
            Icons.add,
            color: Colors.white,
            size: 32,
          ),
        ),
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
} 