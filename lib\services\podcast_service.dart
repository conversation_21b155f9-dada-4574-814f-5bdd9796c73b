import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/podcast.dart';
import 'dart:io';

class PodcastService {
  final SupabaseClient _client = Supabase.instance.client;

  // جلب البودكاستات مع الترقيم
  Future<List<Podcast>> fetchPodcasts({
    int limit = 20,
    int offset = 0,
    PodcastCategory? category,
    String? searchQuery,
    String? userId,
  }) async {
    try {
      // استعلام بسيط بدون فلترة متقدمة لتجنب مشاكل الإصدار
      final response = await _client
          .from('podcasts')
          .select('*')
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return (response as List).map((item) {
        return Podcast.fromMap({
          ...item,
          'user_name': 'مستخدم',
          'user_avatar': '',
          'is_verified': false,
          'is_liked': false,
          'is_saved': false,
        });
      }).toList();
    } catch (e) {
      print('خطأ في جلب البودكاستات: $e');
      return [];
    }
  }

  // إنشاء بودكاست جديد
  Future<String> createPodcast({
    required String title,
    String? description,
    required File audioFile,
    File? coverImage,
    required PodcastCategory category,
    required Duration duration,
    String? tags,
    bool allowDownload = true,
    bool allowComments = true,
  }) async {
    final userId = _client.auth.currentUser?.id;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    // رفع الملف الصوتي
    final audioFileName = 'podcast_${DateTime.now().millisecondsSinceEpoch}.mp3';
    await _client.storage
        .from('podcasts')
        .upload('audio/$audioFileName', audioFile);

    final audioUrl = _client.storage
        .from('podcasts')
        .getPublicUrl('audio/$audioFileName');

    // رفع صورة الغلاف إن وجدت
    String? coverImageUrl;
    if (coverImage != null) {
      final coverFileName = 'cover_${DateTime.now().millisecondsSinceEpoch}.jpg';
      await _client.storage
          .from('podcasts')
          .upload('covers/$coverFileName', coverImage);
      
      coverImageUrl = _client.storage
          .from('podcasts')
          .getPublicUrl('covers/$coverFileName');
    }

    // إنشاء البودكاست في قاعدة البيانات
    final response = await _client.from('podcasts').insert({
      'user_id': userId,
      'title': title,
      'description': description,
      'audio_url': audioUrl,
      'cover_image_url': coverImageUrl,
      'category': category.name,
      'duration_seconds': duration.inSeconds,
      'tags': tags,
      'allow_download': allowDownload,
      'allow_comments': allowComments,
      'created_at': DateTime.now().toIso8601String(),
    }).select().single();

    return response['id'];
  }

  // تحديث بودكاست
  Future<void> updatePodcast({
    required String podcastId,
    String? title,
    String? description,
    PodcastCategory? category,
    String? tags,
    bool? allowDownload,
    bool? allowComments,
    File? newCoverImage,
  }) async {
    final userId = _client.auth.currentUser?.id;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final updateData = <String, dynamic>{
      'updated_at': DateTime.now().toIso8601String(),
    };

    if (title != null) updateData['title'] = title;
    if (description != null) updateData['description'] = description;
    if (category != null) updateData['category'] = category.name;
    if (tags != null) updateData['tags'] = tags;
    if (allowDownload != null) updateData['allow_download'] = allowDownload;
    if (allowComments != null) updateData['allow_comments'] = allowComments;

    // رفع صورة غلاف جديدة إن وجدت
    if (newCoverImage != null) {
      final coverFileName = 'cover_${DateTime.now().millisecondsSinceEpoch}.jpg';
      await _client.storage
          .from('podcasts')
          .upload('covers/$coverFileName', newCoverImage);
      
      updateData['cover_image_url'] = _client.storage
          .from('podcasts')
          .getPublicUrl('covers/$coverFileName');
    }

    await _client
        .from('podcasts')
        .update(updateData)
        .eq('id', podcastId)
        .eq('user_id', userId);
  }

  // حذف بودكاست
  Future<void> deletePodcast(String podcastId) async {
    final userId = _client.auth.currentUser?.id;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    // حذف الملفات من التخزين (تم تعطيله مؤقتاً)
    // final podcast = await _client
    //     .from('podcasts')
    //     .select('audio_url, cover_image_url')
    //     .eq('id', podcastId)
    //     .eq('user_id', userId)
    //     .single();

    // حذف من قاعدة البيانات
    await _client
        .from('podcasts')
        .delete()
        .eq('id', podcastId)
        .eq('user_id', userId);

    // حذف الملفات (اختياري - يمكن الاحتفاظ بها لفترة)
    // يمكن إضافة منطق حذف الملفات هنا
  }

  // إعجاب/إلغاء إعجاب
  Future<void> toggleLike(String podcastId) async {
    final userId = _client.auth.currentUser?.id;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    // فحص حالة الإعجاب الحالية
    final existingLike = await _client
        .from('podcast_likes')
        .select()
        .eq('podcast_id', podcastId)
        .eq('user_id', userId)
        .maybeSingle();

    if (existingLike != null) {
      // إلغاء الإعجاب
      await _client
          .from('podcast_likes')
          .delete()
          .eq('podcast_id', podcastId)
          .eq('user_id', userId);
    } else {
      // إضافة إعجاب
      await _client.from('podcast_likes').insert({
        'podcast_id': podcastId,
        'user_id': userId,
        'created_at': DateTime.now().toIso8601String(),
      });
    }
  }

  // حفظ/إلغاء حفظ
  Future<void> toggleSave(String podcastId) async {
    final userId = _client.auth.currentUser?.id;
    if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

    final existingSave = await _client
        .from('podcast_saves')
        .select()
        .eq('podcast_id', podcastId)
        .eq('user_id', userId)
        .maybeSingle();

    if (existingSave != null) {
      await _client
          .from('podcast_saves')
          .delete()
          .eq('podcast_id', podcastId)
          .eq('user_id', userId);
    } else {
      await _client.from('podcast_saves').insert({
        'podcast_id': podcastId,
        'user_id': userId,
        'created_at': DateTime.now().toIso8601String(),
      });
    }
  }

  // تسجيل تشغيل
  Future<void> recordPlay(String podcastId) async {
    final userId = _client.auth.currentUser?.id;
    
    // تحديث عداد التشغيل
    await _client.rpc('increment_podcast_plays', params: {
      'podcast_id': podcastId,
    });

    // تسجيل في سجل التشغيل للمستخدم المسجل
    if (userId != null) {
      await _client.from('podcast_plays').insert({
        'podcast_id': podcastId,
        'user_id': userId,
        'played_at': DateTime.now().toIso8601String(),
      });
    }
  }

  // جلب البودكاستات المحفوظة
  Future<List<Podcast>> getSavedPodcasts({int limit = 20, int offset = 0}) async {
    final userId = _client.auth.currentUser?.id;
    if (userId == null) return [];

    final response = await _client
        .from('podcast_saves')
        .select('''
          podcasts(
            *,
            profiles!podcasts_user_id_fkey(name, username, avatar_url, is_verified)
          )
        ''')
        .eq('user_id', userId)
        .order('created_at', ascending: false)
        .range(offset, offset + limit - 1);

    return (response as List).map((item) {
      final podcast = item['podcasts'] as Map<String, dynamic>;
      final profile = podcast['profiles'] as Map<String, dynamic>?;
      
      return Podcast.fromMap({
        ...podcast,
        'user_name': profile?['name'] ?? 'مستخدم',
        'user_avatar': profile?['avatar_url'] ?? '',
        'is_verified': profile?['is_verified'] ?? false,
        'is_saved': true,
      });
    }).toList();
  }

  // جلب بودكاستات المستخدم
  Future<List<Podcast>> getUserPodcasts(String userId, {int limit = 20, int offset = 0}) async {
    return await fetchPodcasts(
      limit: limit,
      offset: offset,
      userId: userId,
    );
  }

  // البحث في البودكاستات
  Future<List<Podcast>> searchPodcasts(String query, {int limit = 20, int offset = 0}) async {
    return await fetchPodcasts(
      limit: limit,
      offset: offset,
      searchQuery: query,
    );
  }

  // جلب البودكاستات حسب الفئة
  Future<List<Podcast>> getPodcastsByCategory(PodcastCategory category, {int limit = 20, int offset = 0}) async {
    return await fetchPodcasts(
      limit: limit,
      offset: offset,
      category: category,
    );
  }

  // جلب الإحصائيات
  Future<Map<String, int>> getPodcastStats(String podcastId) async {
    final stats = await _client
        .from('podcasts')
        .select('likes_count, comments_count, plays_count, shares_count, downloads_count')
        .eq('id', podcastId)
        .single();

    return {
      'likes': stats['likes_count'] ?? 0,
      'comments': stats['comments_count'] ?? 0,
      'plays': stats['plays_count'] ?? 0,
      'shares': stats['shares_count'] ?? 0,
      'downloads': stats['downloads_count'] ?? 0,
    };
  }
}
