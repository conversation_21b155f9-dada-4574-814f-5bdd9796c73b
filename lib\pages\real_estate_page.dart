import 'package:flutter/material.dart';
import '../models/real_estate_property.dart';
import '../services/real_estate_service.dart';
import '../widgets/property_card.dart';
import 'add_property_page.dart';
import 'property_details_page.dart';
import 'my_properties_page.dart';
import 'favorite_properties_page.dart';

class RealEstatePage extends StatefulWidget {
  const RealEstatePage({super.key});

  @override
  State<RealEstatePage> createState() => _RealEstatePageState();
}

class _RealEstatePageState extends State<RealEstatePage> with TickerProviderStateMixin {
  final RealEstateService _realEstateService = RealEstateService();
  final TextEditingController _searchController = TextEditingController();
  
  List<RealEstateProperty> _properties = [];
  bool _loading = true;
  bool _searching = false;
  
  // فلاتر البحث
  PropertyType? _selectedType;
  PropertyPurpose? _selectedPurpose;
  String? _selectedCity;
  double? _minPrice;
  double? _maxPrice;
  String _sortBy = 'created_at';
  
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadProperties();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadProperties() async {
    setState(() => _loading = true);
    try {
      final properties = await _realEstateService.getProperties(
        propertyType: _selectedType,
        purpose: _selectedPurpose,
        city: _selectedCity,
        minPrice: _minPrice,
        maxPrice: _maxPrice,
        sortBy: _sortBy,
        ascending: false,
      );
      setState(() {
        _properties = properties;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل العقارات: $e')),
        );
      }
    }
  }

  Future<void> _searchProperties() async {
    if (_searchController.text.trim().isEmpty) {
      _loadProperties();
      return;
    }

    setState(() => _searching = true);
    try {
      final properties = await _realEstateService.searchProperties(_searchController.text.trim());
      setState(() {
        _properties = properties;
        _searching = false;
      });
    } catch (e) {
      setState(() => _searching = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في البحث: $e')),
        );
      }
    }
  }

  void _showFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => _buildFiltersSheet(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('العقارات'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: const [
            Tab(text: 'جميع العقارات'),
            Tab(text: 'عقاراتي'),
            Tab(text: 'المفضلة'),
            Tab(text: 'إضافة عقار'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllPropertiesTab(),
          const MyPropertiesPage(),
          const FavoritePropertiesPage(),
          const AddPropertyPage(),
        ],
      ),
    );
  }

  Widget _buildAllPropertiesTab() {
    return Column(
      children: [
        // شريط البحث والفلاتر
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey[50],
          child: Column(
            children: [
              // شريط البحث
              Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: 'ابحث عن عقار...',
                        prefixIcon: const Icon(Icons.search),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(25),
                          borderSide: BorderSide.none,
                        ),
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: const EdgeInsets.symmetric(horizontal: 20),
                      ),
                      onSubmitted: (_) => _searchProperties(),
                    ),
                  ),
                  const SizedBox(width: 12),
                  // زر الفلاتر
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.blue[600],
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: IconButton(
                      onPressed: _showFilters,
                      icon: const Icon(Icons.tune, color: Colors.white),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // فلاتر سريعة
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    _buildQuickFilter('الكل', null, null),
                    _buildQuickFilter('شقق', PropertyType.apartment, null),
                    _buildQuickFilter('منازل', PropertyType.house, null),
                    _buildQuickFilter('أراضي', PropertyType.land, null),
                    _buildQuickFilter('للبيع', null, PropertyPurpose.sale),
                    _buildQuickFilter('للإيجار', null, PropertyPurpose.rent),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        // قائمة العقارات
        Expanded(
          child: _loading || _searching
              ? const Center(child: CircularProgressIndicator())
              : _properties.isEmpty
                  ? _buildEmptyState()
                  : RefreshIndicator(
                      onRefresh: _loadProperties,
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _properties.length,
                        itemBuilder: (context, index) {
                          final property = _properties[index];
                          return PropertyCard(
                            property: property,
                            onTap: () => _openPropertyDetails(property),
                            onFavoriteToggle: () => _toggleFavorite(property.id),
                          );
                        },
                      ),
                    ),
        ),
      ],
    );
  }

  Widget _buildQuickFilter(String label, PropertyType? type, PropertyPurpose? purpose) {
    final isSelected = (_selectedType == type && _selectedPurpose == purpose) ||
                      (type == null && purpose == null && _selectedType == null && _selectedPurpose == null);
    
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          setState(() {
            if (type == null && purpose == null) {
              _selectedType = null;
              _selectedPurpose = null;
            } else {
              _selectedType = type;
              _selectedPurpose = purpose;
            }
          });
          _loadProperties();
        },
        selectedColor: Colors.blue[100],
        checkmarkColor: Colors.blue[600],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.home_outlined, size: 80, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'لا توجد عقارات',
            style: TextStyle(fontSize: 20, color: Colors.grey[600]),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير الفلاتر أو البحث',
            style: TextStyle(fontSize: 14, color: Colors.grey[500]),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _tabController.animateTo(3),
            icon: const Icon(Icons.add),
            label: const Text('أضف عقار جديد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFiltersSheet() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'فلاتر البحث',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedType = null;
                    _selectedPurpose = null;
                    _selectedCity = null;
                    _minPrice = null;
                    _maxPrice = null;
                  });
                  Navigator.pop(context);
                  _loadProperties();
                },
                child: const Text('مسح الكل'),
              ),
            ],
          ),
          
          const SizedBox(height: 20),
          
          // نوع العقار
          const Text('نوع العقار:', style: TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: PropertyType.values.map((type) {
              return FilterChip(
                label: Text(type.arabicName),
                selected: _selectedType == type,
                onSelected: (selected) {
                  setState(() {
                    _selectedType = selected ? type : null;
                  });
                },
              );
            }).toList(),
          ),
          
          const SizedBox(height: 20),
          
          // الغرض
          const Text('الغرض:', style: TextStyle(fontWeight: FontWeight.w600)),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: PropertyPurpose.values.map((purpose) {
              return FilterChip(
                label: Text(purpose.arabicName),
                selected: _selectedPurpose == purpose,
                onSelected: (selected) {
                  setState(() {
                    _selectedPurpose = selected ? purpose : null;
                  });
                },
              );
            }).toList(),
          ),
          
          const SizedBox(height: 30),
          
          // أزرار التطبيق
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إلغاء'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _loadProperties();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[600],
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('تطبيق'),
                ),
              ),
            ],
          ),
          
          SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
        ],
      ),
    );
  }

  void _openPropertyDetails(RealEstateProperty property) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => PropertyDetailsPage(property: property),
      ),
    ).then((_) {
      // إعادة تحميل العقارات عند العودة لتحديث المشاهدات
      _loadProperties();
    });
  }

  Future<void> _toggleFavorite(String propertyId) async {
    try {
      await _realEstateService.toggleFavorite(propertyId);
      _loadProperties(); // إعادة تحميل لتحديث حالة المفضلة
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحديث المفضلة: $e')),
        );
      }
    }
  }
}
