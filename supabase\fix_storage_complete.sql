-- =============================================================
--  حل شامل لمشكلة رفع الصور والغلاف
--  Complete Storage Fix for Community Images
-- =============================================================

-- 1) إنشاء أو تحديث bucket للصور
-- -------------------------------------------------------

-- حذف bucket إذا كان موجود (لإعادة إنشائه بإعدادات صحيحة)
DELETE FROM storage.buckets WHERE id = 'community-images';

-- إنشاء bucket جديد بإعدادات صحيحة
INSERT INTO storage.buckets (
  id, 
  name, 
  public, 
  file_size_limit, 
  allowed_mime_types,
  created_at,
  updated_at
) VALUES (
  'community-images',
  'community-images', 
  true,  -- عام للجميع
  52428800,  -- 50MB حد أقصى
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'],
  NOW(),
  NOW()
);

-- 2) حذف جميع السياسات القديمة
-- -------------------------------------------------------

DROP POLICY IF EXISTS "Public can view community images" ON storage.objects;
DROP POLICY IF EXISTS "Community owners can upload images" ON storage.objects;
DROP POLICY IF EXISTS "Community owners can update images" ON storage.objects;
DROP POLICY IF EXISTS "Community owners can delete images" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can upload community images" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can update community images" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can delete community images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can upload community images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can update community images" ON storage.objects;
DROP POLICY IF EXISTS "Authenticated users can delete community images" ON storage.objects;
DROP POLICY IF EXISTS "Allow all operations" ON storage.objects;

-- 3) إنشاء سياسات مبسطة وقوية
-- -------------------------------------------------------

-- السماح للجميع بقراءة الصور (لأن bucket عام)
CREATE POLICY "community_images_select_policy"
ON storage.objects FOR SELECT
USING (bucket_id = 'community-images');

-- السماح للمستخدمين المسجلين برفع الصور
CREATE POLICY "community_images_insert_policy"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'community-images' 
  AND auth.uid() IS NOT NULL
  AND (storage.foldername(name))[1] IS NOT NULL  -- يجب أن يكون في مجلد
);

-- السماح للمستخدمين المسجلين بتحديث الصور
CREATE POLICY "community_images_update_policy"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'community-images' 
  AND auth.uid() IS NOT NULL
);

-- السماح للمستخدمين المسجلين بحذف الصور
CREATE POLICY "community_images_delete_policy"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'community-images' 
  AND auth.uid() IS NOT NULL
);

-- 4) التأكد من تفعيل RLS
-- -------------------------------------------------------

ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- 5) إنشاء دالة مساعدة للتحقق من ملكية المجتمع (اختياري)
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION public.is_community_owner(community_id TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM communities 
    WHERE id = community_id 
    AND owner_id = auth.uid()::text
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6) سياسة متقدمة للتحقق من ملكية المجتمع (اختياري)
-- -------------------------------------------------------

-- يمكن استبدال السياسات أعلاه بهذه السياسة المتقدمة
-- DROP POLICY IF EXISTS "community_images_insert_policy" ON storage.objects;
-- 
-- CREATE POLICY "community_images_owner_only_policy"
-- ON storage.objects FOR ALL
-- USING (
--   bucket_id = 'community-images' 
--   AND auth.uid() IS NOT NULL
--   AND (
--     -- السماح بالقراءة للجميع
--     current_setting('request.method', true) = 'GET'
--     OR
--     -- السماح بالكتابة فقط لمالك المجتمع
--     public.is_community_owner((storage.foldername(name))[1])
--   )
-- );

-- 7) إنشاء فهارس لتحسين الأداء
-- -------------------------------------------------------

CREATE INDEX IF NOT EXISTS idx_storage_objects_bucket_name 
ON storage.objects (bucket_id, name);

CREATE INDEX IF NOT EXISTS idx_communities_owner_id 
ON communities (owner_id);

-- 8) إعطاء صلاحيات للمستخدمين المسجلين
-- -------------------------------------------------------

-- السماح للمستخدمين المسجلين بالوصول لـ storage
GRANT USAGE ON SCHEMA storage TO authenticated;
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.buckets TO authenticated;

-- 9) تنظيف الملفات القديمة (اختياري)
-- -------------------------------------------------------

-- حذف جميع الملفات القديمة في bucket (احذر: هذا سيحذف جميع الصور!)
-- DELETE FROM storage.objects WHERE bucket_id = 'community-images';

-- 10) اختبار السياسات
-- -------------------------------------------------------

-- للتحقق من السياسات المطبقة:
-- SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
-- FROM pg_policies 
-- WHERE tablename = 'objects' AND schemaname = 'storage'
-- AND policyname LIKE '%community_images%';

-- للتحقق من bucket:
-- SELECT * FROM storage.buckets WHERE id = 'community-images';

-- للتحقق من الملفات:
-- SELECT name, bucket_id, created_at FROM storage.objects 
-- WHERE bucket_id = 'community-images' LIMIT 10;

-- =============================================================
--  تعليمات ما بعد التشغيل
-- =============================================================

-- 1. شغل هذا السكريبت في SQL Editor في Supabase
-- 2. تأكد من ظهور bucket "community-images" في Storage
-- 3. تأكد من أن bucket مُعلم كـ "Public"
-- 4. أعد بناء التطبيق: flutter build apk --release
-- 5. اختبر رفع الصور

-- =============================================================
--  استكشاف الأخطاء
-- =============================================================

-- إذا استمرت المشاكل، شغل هذه الاستعلامات للتشخيص:

-- التحقق من bucket:
-- SELECT id, name, public, file_size_limit, allowed_mime_types 
-- FROM storage.buckets WHERE id = 'community-images';

-- التحقق من السياسات:
-- SELECT policyname, cmd, qual FROM pg_policies 
-- WHERE tablename = 'objects' AND schemaname = 'storage';

-- التحقق من صلاحيات المستخدم الحالي:
-- SELECT auth.uid(), auth.role();

-- =============================================================
--  انتهى السكريبت
-- =============================================================
