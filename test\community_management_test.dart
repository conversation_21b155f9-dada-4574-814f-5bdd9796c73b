import 'package:flutter_test/flutter_test.dart';
import 'package:arzawo/models/community.dart';
import 'package:arzawo/supabase_service.dart';

void main() {
  group('Community Management Tests', () {
    late SupabaseService service;
    late Community testCommunity;

    setUpAll(() {
      service = SupabaseService();
      testCommunity = Community(
        id: 'test-community-id',
        name: 'Test Community',
        description: 'Test Description',
        category: 'Test Category',
        ownerId: 'test-owner-id',
        createdAt: DateTime.now(),
        isArchived: false,
        isDisabled: false,
        isPrivate: false,
        allowMemberPosts: true,
        requireApproval: false,
        allowComments: true,
        allowInvites: true,
        postPermission: 'all',
        joinType: 'open',
      );
    });

    group('Archive Community Tests', () {
      test('should validate archive community parameters', () {
        expect(() => service.archiveCommunity('', true), throwsA(isA<String>()));
        expect(() => service.archiveCommunity('valid-id', true), returnsNormally);
      });

      test('should handle archive status correctly', () {
        // Test initial state
        expect(testCommunity.isArchived, false);

        // Test creating archived community
        final archivedCommunity = Community(
          id: testCommunity.id,
          name: testCommunity.name,
          description: testCommunity.description,
          category: testCommunity.category,
          ownerId: testCommunity.ownerId,
          createdAt: testCommunity.createdAt,
          isArchived: true, // Changed to archived
          isDisabled: testCommunity.isDisabled,
          isPrivate: testCommunity.isPrivate,
          allowMemberPosts: testCommunity.allowMemberPosts,
          requireApproval: testCommunity.requireApproval,
          allowComments: testCommunity.allowComments,
          allowInvites: testCommunity.allowInvites,
          postPermission: testCommunity.postPermission,
          joinType: testCommunity.joinType,
        );
        expect(archivedCommunity.isArchived, true);
      });
    });

    group('Disable Community Tests', () {
      test('should validate disable community parameters', () {
        expect(() => service.disableCommunity('', true), throwsA(isA<String>()));
        expect(() => service.disableCommunity('valid-id', true), returnsNormally);
      });

      test('should handle disable status correctly', () {
        // Test initial state
        expect(testCommunity.isDisabled, false);

        // Test creating disabled community
        final disabledCommunity = Community(
          id: testCommunity.id,
          name: testCommunity.name,
          description: testCommunity.description,
          category: testCommunity.category,
          ownerId: testCommunity.ownerId,
          createdAt: testCommunity.createdAt,
          isArchived: testCommunity.isArchived,
          isDisabled: true, // Changed to disabled
          isPrivate: testCommunity.isPrivate,
          allowMemberPosts: testCommunity.allowMemberPosts,
          requireApproval: testCommunity.requireApproval,
          allowComments: testCommunity.allowComments,
          allowInvites: testCommunity.allowInvites,
          postPermission: testCommunity.postPermission,
          joinType: testCommunity.joinType,
        );
        expect(disabledCommunity.isDisabled, true);
      });
    });

    group('Delete Community Tests', () {
      test('should validate delete community parameters', () {
        expect(() => service.deleteCommunity(''), throwsA(isA<String>()));
        expect(() => service.deleteCommunity('valid-id'), returnsNormally);
      });

      test('should handle community deletion validation', () {
        // Test that community exists before deletion
        expect(testCommunity.id, isNotEmpty);
        expect(testCommunity.ownerId, isNotEmpty);
      });
    });

    group('Community Status Tests', () {
      test('should correctly identify community states', () {
        // Normal community
        expect(testCommunity.isArchived, false);
        expect(testCommunity.isDisabled, false);

        // Test archived community creation
        final archivedCommunity = Community(
          id: 'archived-test',
          name: 'Archived Test',
          ownerId: 'test-owner',
          createdAt: DateTime.now(),
          isArchived: true,
          isDisabled: false,
          isPrivate: false,
          allowMemberPosts: true,
          requireApproval: false,
          allowComments: true,
          allowInvites: true,
          postPermission: 'all',
          joinType: 'open',
        );
        expect(archivedCommunity.isArchived, true);
        expect(archivedCommunity.isDisabled, false);

        // Test disabled community creation
        final disabledCommunity = Community(
          id: 'disabled-test',
          name: 'Disabled Test',
          ownerId: 'test-owner',
          createdAt: DateTime.now(),
          isArchived: false,
          isDisabled: true,
          isPrivate: false,
          allowMemberPosts: true,
          requireApproval: false,
          allowComments: true,
          allowInvites: true,
          postPermission: 'all',
          joinType: 'open',
        );
        expect(disabledCommunity.isArchived, false);
        expect(disabledCommunity.isDisabled, true);
      });
    });

    group('Error Handling Tests', () {
      test('should handle empty community ID', () {
        expect(() => service.archiveCommunity('', true), throwsA(contains('المستخدم غير مسجل')));
        expect(() => service.disableCommunity('', true), throwsA(contains('المستخدم غير مسجل')));
        expect(() => service.deleteCommunity(''), throwsA(contains('المستخدم غير مسجل')));
      });

      test('should handle null values gracefully', () {
        final communityWithNulls = Community(
          id: 'test-id',
          name: 'Test',
          ownerId: 'owner-id',
          createdAt: DateTime.now(),
          description: null,
          category: null,
          avatarUrl: null,
          coverUrl: null,
          isArchived: false,
          isDisabled: false,
          isPrivate: false,
          allowMemberPosts: true,
          requireApproval: false,
          allowComments: true,
          allowInvites: true,
          postPermission: 'all',
          joinType: 'open',
        );

        expect(communityWithNulls.description, isNull);
        expect(communityWithNulls.category, isNull);
        expect(communityWithNulls.avatarUrl, isNull);
        expect(communityWithNulls.coverUrl, isNull);
      });
    });

    group('Community Properties Tests', () {
      test('should have correct initial properties', () {
        expect(testCommunity.id, 'test-community-id');
        expect(testCommunity.name, 'Test Community');
        expect(testCommunity.ownerId, 'test-owner-id');
        expect(testCommunity.isArchived, false);
        expect(testCommunity.isDisabled, false);
        expect(testCommunity.isPrivate, false);
        expect(testCommunity.allowMemberPosts, true);
        expect(testCommunity.requireApproval, false);
        expect(testCommunity.allowComments, true);
        expect(testCommunity.allowInvites, true);
        expect(testCommunity.postPermission, 'all');
        expect(testCommunity.joinType, 'open');
      });
    });
  });
}
