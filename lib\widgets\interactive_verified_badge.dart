import 'package:flutter/material.dart';
import 'verified_badge.dart';
import '../pages/verify_account_screen.dart'; // إصلاح المسار

class InteractiveVerifiedBadge extends StatelessWidget {
  final double size;
  final Color? color;
  final String? userName;
  final VoidCallback? onTap;
  
  const InteractiveVerifiedBadge({
    super.key,
    this.size = 16.0,
    this.color,
    this.userName,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap ?? () => _showVerificationInfo(context),
      child: Tooltip(
        message: _getTooltipMessage(context),
        child: VerifiedBadge(
          size: size,
          color: color,
        ),
      ),
    );
  }

  String _getTooltipMessage(BuildContext context) {
    final locale = Localizations.localeOf(context);
    final name = userName ?? 'هذا المستخدم';
    
    if (locale.languageCode == 'ar') {
      return '$name هنا شارة تحقق\nانقر لمعرفة المزيد';
    } else {
      return '$name here are verified\nClick to learn more';
    }
  }

  void _showVerificationInfo(BuildContext context) {
    final locale = Localizations.localeOf(context);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            VerifiedBadge(size: 24, color: color),
            const SizedBox(width: 8),
            Text(
              locale.languageCode == 'ar' ? 'حساب موثق' : 'Verified Account',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              locale.languageCode == 'ar' 
                ? 'هذا الحساب تم التحقق من هويته من قبل فريقنا'
                : 'This account has been verified by our team',
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 16),
            Text(
              locale.languageCode == 'ar'
                ? 'شروط الحصول على شارة التحقق:'
                : 'Requirements for verification:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            _buildRequirement(
              context,
              locale.languageCode == 'ar' ? '50,000 متابع على الأقل' : 'At least 50,000 followers',
              '50,000+',
            ),
            _buildRequirement(
              context,
              locale.languageCode == 'ar' ? '100,000 مشاهدة إجمالية' : '100,000 total views',
              '100,000+',
            ),
            const SizedBox(height: 16),
            Text(
              locale.languageCode == 'ar'
                ? 'شارة التحقق تعني أن هذا الحساب موثوق وتم التحقق من هويته.'
                : 'The verification badge means this account is trusted and verified.',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              locale.languageCode == 'ar' ? 'إغلاق' : 'Close',
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _navigateToVerificationPage(context);
            },
            child: Text(
              locale.languageCode == 'ar' ? 'طلب التوثيق' : 'Request Verification',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRequirement(BuildContext context, String text, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            size: 16,
            color: Colors.green,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(text),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: Colors.blue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              value,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.blue[700],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToVerificationPage(BuildContext context) {
    // التنقل إلى صفحة طلب التوثيق
    Navigator.push(context, MaterialPageRoute(builder: (_) => const VerifyAccountScreen()));
  }
}

// نسخة مبسطة للاستخدام السريع
class SimpleVerifiedBadge extends StatelessWidget {
  final double size;
  final Color? color;
  final String? userName;
  
  const SimpleVerifiedBadge({
    super.key,
    this.size = 16.0,
    this.color,
    this.userName,
  });

  @override
  Widget build(BuildContext context) {
    return InteractiveVerifiedBadge(
      size: size,
      color: color,
      userName: userName,
    );
  }
} 