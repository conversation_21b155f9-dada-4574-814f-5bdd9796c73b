-- حذف وإعادة إنشاء جداول الزواج الشرعي

-- حذف الجداول والدوال القديمة
DROP TABLE IF EXISTS marriage_reports CASCADE;
DROP TABLE IF EXISTS daily_contact_limits CASCADE;
DROP TABLE IF EXISTS contact_requests CASCADE;
DROP TABLE IF EXISTS marriage_profiles CASCADE;

DROP FUNCTION IF EXISTS increment_sent_requests(UUID);
DROP FUNCTION IF EXISTS increment_received_requests(UUID);
DROP FUNCTION IF EXISTS check_daily_limit(UUID);
DROP FUNCTION IF EXISTS update_daily_limit(UUID);
DROP FUNCTION IF EXISTS trigger_contact_request_insert();

-- إنشاء جدول ملفات الزواج الشرعي
CREATE TABLE marriage_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    name TEXT NOT NULL,
    gender TEXT NOT NULL CHECK (gender IN ('male', 'female')),
    age INTEGER NOT NULL CHECK (age >= 18 AND age <= 100),
    city TEXT NOT NULL,
    country TEXT NOT NULL,
    profession TEXT NOT NULL,
    marital_status TEXT NOT NULL CHECK (marital_status IN ('single', 'divorced', 'widowed')),
    description TEXT NOT NULL CHECK (length(description) >= 50 AND length(description) <= 250),
    goal TEXT NOT NULL CHECK (goal IN ('marriage', 'engagement', 'seriousRelationship')),
    desired_partner_specs TEXT NOT NULL,
    profile_image_url TEXT,
    hide_image_until_approval BOOLEAN DEFAULT TRUE,
    hide_name_until_approval BOOLEAN DEFAULT TRUE,
    contact_methods JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    received_requests_count INTEGER DEFAULT 0,
    sent_requests_count INTEGER DEFAULT 0,
    UNIQUE(user_id)
);

-- إنشاء جدول طلبات التواصل
CREATE TABLE contact_requests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    sender_id UUID NOT NULL,
    receiver_id UUID NOT NULL,
    message TEXT,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected')),
    responded_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(sender_id, receiver_id)
);

-- إنشاء جدول الإبلاغات
CREATE TABLE marriage_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    reporter_id UUID NOT NULL,
    reported_profile_id UUID NOT NULL REFERENCES marriage_profiles(id) ON DELETE CASCADE,
    reason TEXT NOT NULL,
    details TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول تتبع طلبات التواصل اليومية
CREATE TABLE daily_contact_limits (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    date DATE DEFAULT CURRENT_DATE,
    requests_sent INTEGER DEFAULT 0,
    UNIQUE(user_id, date)
);

-- إنشاء الفهارس
CREATE INDEX idx_marriage_profiles_user_id ON marriage_profiles(user_id);
CREATE INDEX idx_marriage_profiles_gender ON marriage_profiles(gender);
CREATE INDEX idx_marriage_profiles_age ON marriage_profiles(age);
CREATE INDEX idx_marriage_profiles_city ON marriage_profiles(city);
CREATE INDEX idx_marriage_profiles_country ON marriage_profiles(country);
CREATE INDEX idx_marriage_profiles_marital_status ON marriage_profiles(marital_status);
CREATE INDEX idx_marriage_profiles_goal ON marriage_profiles(goal);
CREATE INDEX idx_marriage_profiles_is_active ON marriage_profiles(is_active);
CREATE INDEX idx_marriage_profiles_created_at ON marriage_profiles(created_at DESC);

CREATE INDEX idx_contact_requests_sender_id ON contact_requests(sender_id);
CREATE INDEX idx_contact_requests_receiver_id ON contact_requests(receiver_id);
CREATE INDEX idx_contact_requests_status ON contact_requests(status);
CREATE INDEX idx_contact_requests_sent_at ON contact_requests(sent_at DESC);

CREATE INDEX idx_marriage_reports_reporter_id ON marriage_reports(reporter_id);
CREATE INDEX idx_marriage_reports_reported_profile_id ON marriage_reports(reported_profile_id);

CREATE INDEX idx_daily_contact_limits_user_id ON daily_contact_limits(user_id);
CREATE INDEX idx_daily_contact_limits_date ON daily_contact_limits(date);

-- إنشاء الدوال
CREATE FUNCTION increment_sent_requests(profile_user_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE marriage_profiles 
    SET sent_requests_count = sent_requests_count + 1 
    WHERE user_id = profile_user_id;
END;
$$ LANGUAGE plpgsql;

CREATE FUNCTION increment_received_requests(profile_user_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE marriage_profiles 
    SET received_requests_count = received_requests_count + 1 
    WHERE user_id = profile_user_id;
END;
$$ LANGUAGE plpgsql;

CREATE FUNCTION check_daily_limit(p_user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
    current_count INTEGER;
BEGIN
    SELECT requests_sent INTO current_count
    FROM daily_contact_limits
    WHERE user_id = p_user_id 
    AND date = CURRENT_DATE;
    
    IF current_count IS NULL THEN
        INSERT INTO daily_contact_limits (user_id, requests_sent) 
        VALUES (p_user_id, 0);
        RETURN TRUE;
    END IF;
    
    RETURN current_count < 3;
END;
$$ LANGUAGE plpgsql;

CREATE FUNCTION update_daily_limit(p_user_id UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO daily_contact_limits (user_id, requests_sent) 
    VALUES (p_user_id, 1)
    ON CONFLICT (user_id, date) 
    DO UPDATE SET requests_sent = daily_contact_limits.requests_sent + 1;
END;
$$ LANGUAGE plpgsql;

CREATE FUNCTION trigger_contact_request_insert()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM increment_sent_requests(NEW.sender_id);
    PERFORM increment_received_requests(NEW.receiver_id);
    PERFORM update_daily_limit(NEW.sender_id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء التريجر
CREATE TRIGGER trigger_contact_request_insert
    AFTER INSERT ON contact_requests
    FOR EACH ROW
    EXECUTE FUNCTION trigger_contact_request_insert();

-- تفعيل Row Level Security
ALTER TABLE marriage_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE contact_requests ENABLE ROW LEVEL SECURITY;
ALTER TABLE marriage_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_contact_limits ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان لملفات الزواج
CREATE POLICY "الجميع يمكنهم قراءة الملفات النشطة" ON marriage_profiles
    FOR SELECT USING (is_active = true);

CREATE POLICY "المستخدم يمكنه إنشاء ملفه" ON marriage_profiles
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه تحديث ملفه" ON marriage_profiles
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه حذف ملفه" ON marriage_profiles
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان لطلبات التواصل
CREATE POLICY "المستخدم يمكنه قراءة طلباته" ON contact_requests
    FOR SELECT USING (auth.uid() = sender_id OR auth.uid() = receiver_id);

CREATE POLICY "المستخدم يمكنه إرسال طلبات" ON contact_requests
    FOR INSERT WITH CHECK (auth.uid() = sender_id);

CREATE POLICY "المستخدم يمكنه تحديث طلباته المستلمة" ON contact_requests
    FOR UPDATE USING (auth.uid() = receiver_id);

CREATE POLICY "المرسل يمكنه حذف طلباته المرسلة" ON contact_requests
    FOR DELETE USING (auth.uid() = sender_id AND status = 'pending');

-- سياسات الأمان للإبلاغات
CREATE POLICY "المستخدم يمكنه الإبلاغ" ON marriage_reports
    FOR INSERT WITH CHECK (auth.uid() = reporter_id);

-- سياسات الأمان للحدود اليومية
CREATE POLICY "المستخدم يمكنه قراءة حدوده اليومية" ON daily_contact_limits
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "النظام يمكنه إدارة الحدود اليومية" ON daily_contact_limits
    FOR ALL USING (true);

-- منح الصلاحيات
GRANT ALL ON marriage_profiles TO authenticated;
GRANT ALL ON contact_requests TO authenticated;
GRANT ALL ON marriage_reports TO authenticated;
GRANT ALL ON daily_contact_limits TO authenticated;

GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON FUNCTION increment_sent_requests(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION increment_received_requests(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION check_daily_limit(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION update_daily_limit(UUID) TO authenticated;

-- رسالة نجاح
SELECT 'تم إنشاء جداول الزواج الشرعي بنجاح!' as message;
