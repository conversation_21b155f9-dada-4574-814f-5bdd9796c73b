-- إنشاء نظام تخزين الصور للمساحات (نسخة من نظام المجتمعات)
-- تنفيذ هذا الملف لإنشاء bucket وسياسات الأمان للمساحات

-- 1) إنشاء bucket لصور المساحات
-- -------------------------------------------------------

-- إنشاء bucket لصور المساحات (مثل المجتمعات)
INSERT INTO storage.buckets (id, name, public)
VALUES ('space-images', 'space-images', true)
ON CONFLICT (id) DO NOTHING;

-- 2) إعداد سياسات الأمان (RLS) للصور
-- -------------------------------------------------------

-- حذف السياسات القديمة إن وجدت
DROP POLICY IF EXISTS "Public can view space images" ON storage.objects;
DROP POLICY IF EXISTS "Space owners can upload images" ON storage.objects;
DROP POLICY IF EXISTS "Space owners can update images" ON storage.objects;
DROP POLICY IF EXISTS "Space owners can delete images" ON storage.objects;
DROP POLICY IF EXISTS "space_images_select_policy" ON storage.objects;
DROP POLICY IF EXISTS "space_images_insert_policy" ON storage.objects;
DROP POLICY IF EXISTS "space_images_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "space_images_delete_policy" ON storage.objects;

-- السماح للجميع بقراءة صور المساحات
CREATE POLICY "space_images_select_policy"
ON storage.objects FOR SELECT
USING (bucket_id = 'space-images');

-- السماح لمالكي المساحات برفع الصور
CREATE POLICY "space_images_insert_policy"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'space-images' 
  AND auth.uid() IS NOT NULL
  AND EXISTS (
    SELECT 1 FROM public.spaces 
    WHERE owner_id = auth.uid()
    AND id::text = split_part(name, '/', 1)
  )
);

-- السماح لمالكي المساحات بتحديث الصور
CREATE POLICY "space_images_update_policy"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'space-images' 
  AND auth.uid() IS NOT NULL
  AND EXISTS (
    SELECT 1 FROM public.spaces 
    WHERE owner_id = auth.uid()
    AND id::text = split_part(name, '/', 1)
  )
);

-- السماح لمالكي المساحات بحذف الصور
CREATE POLICY "space_images_delete_policy"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'space-images' 
  AND auth.uid() IS NOT NULL
  AND EXISTS (
    SELECT 1 FROM public.spaces 
    WHERE owner_id = auth.uid()
    AND id::text = split_part(name, '/', 1)
  )
);

-- 3) إنشاء دالة للتحقق من صلاحيات المساحة (مثل المجتمعات)
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION public.check_space_permission(
  p_space_id UUID,
  p_user_id UUID,
  p_operation TEXT
) RETURNS BOOLEAN AS $$
BEGIN
  -- التحقق من وجود المساحة
  IF NOT EXISTS (SELECT 1 FROM public.spaces WHERE id = p_space_id) THEN
    RETURN FALSE;
  END IF;

  -- التحقق من الملكية
  IF p_operation IN ('update', 'delete', 'upload') THEN
    RETURN EXISTS (
      SELECT 1 FROM public.spaces 
      WHERE id = p_space_id AND owner_id = p_user_id
    );
  END IF;

  -- للعمليات الأخرى (مثل القراءة)
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4) إنشاء دالة مساعدة لرفع صور المساحات
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION public.upload_space_image(
  p_space_id UUID,
  p_image_type TEXT,
  p_file_name TEXT
) RETURNS TEXT AS $$
DECLARE
  v_user_id UUID;
  v_file_path TEXT;
BEGIN
  -- الحصول على معرف المستخدم الحالي
  v_user_id := auth.uid();
  
  -- التحقق من تسجيل الدخول
  IF v_user_id IS NULL THEN
    RAISE EXCEPTION 'يجب تسجيل الدخول أولاً';
  END IF;

  -- التحقق من صلاحيات التعديل
  IF NOT public.check_space_permission(p_space_id, v_user_id, 'upload') THEN
    RAISE EXCEPTION 'ليس لديك صلاحية لتعديل هذه المساحة';
  END IF;

  -- إنشاء مسار الملف
  v_file_path := p_space_id::text || '/' || p_image_type || '_' || 
                 extract(epoch from now())::bigint || '_' || p_file_name;

  RETURN v_file_path;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- رسالة نجاح
SELECT 'تم إنشاء نظام تخزين الصور للمساحات بنجاح!' as result;
