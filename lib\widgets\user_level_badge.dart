import 'package:flutter/material.dart';
import '../models/user_level.dart';
import '../supabase_service.dart';
import 'verified_badge.dart';
import 'interactive_verified_badge.dart';

class UserLevelBadge extends StatelessWidget {
  final UserLevel level;
  final double size;
  final bool showText;
  final EdgeInsetsGeometry? margin;

  const UserLevelBadge({
    super.key,
    required this.level,
    this.size = 16,
    this.showText = false,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    final levelInfo = UserLevelSystem.getLevelInfo(level);
    
    return Container(
      margin: margin ?? const EdgeInsets.only(left: 4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: levelInfo.color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(4),
              border: Border.all(
                color: levelInfo.color,
                width: 1,
              ),
            ),
            child: Icon(
              levelInfo.icon,
              size: size,
              color: levelInfo.color,
            ),
          ),
          if (showText) ...[
            const SizedBox(width: 4),
            Text(
              levelInfo.name,
              style: TextStyle(
                fontSize: size * 0.75,
                color: levelInfo.color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

// Widget للاستخدام مع اسم المستخدم
class UserNameWithLevel extends StatelessWidget {
  final String name;
  final String userId;
  final bool isVerified;
  final TextStyle? textStyle;
  final double badgeSize;
  final bool showLevelText;
  final MainAxisAlignment alignment;

  const UserNameWithLevel({
    super.key,
    required this.name,
    required this.userId,
    this.isVerified = false,
    this.textStyle,
    this.badgeSize = 16,
    this.showLevelText = false,
    this.alignment = MainAxisAlignment.start,
  });

  @override
  Widget build(BuildContext context) {
    final userLevel = _getUserLevel(userId);
    
    // Debug print لمعرفة حالة التحقق
    print('UserNameWithLevel - Name: $name, Is Verified: $isVerified, User ID: $userId');
    
    return Row(
      mainAxisAlignment: alignment,
      mainAxisSize: MainAxisSize.min,
      children: [
        Flexible(
          child: Text(
            name,
            style: textStyle,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        
        // شارة التوثيق
        if (isVerified) ...[
          const SizedBox(width: 4),
          InteractiveVerifiedBadge(
            size: badgeSize,
            userName: name,
          ),
        ],
        
        // شارة المستوى
        const SizedBox(width: 4),
        UserLevelBadge(
          level: userLevel,
          size: badgeSize,
          showText: showLevelText,
        ),
      ],
    );
  }

  // حساب مستوى المستخدم (مؤقت - سيتم ربطه بقاعدة البيانات لاحقاً)
  UserLevel _getUserLevel(String userId) {
    // بيانات تجريبية - في التطبيق الحقيقي ستأتي من قاعدة البيانات
    final mockUserStats = _getMockUserStats(userId);
    
    return UserLevelSystem.calculateUserLevel(
      followersCount: mockUserStats['followers'] ?? 0,
      postsCount: mockUserStats['posts'] ?? 0,
      totalViews: mockUserStats['views'] ?? 0,
    );
  }

  // الحصول على الإحصائيات الحقيقية للمستخدم
  Map<String, int> _getMockUserStats(String userId) {
    // هنا يجب جلب البيانات الحقيقية من قاعدة البيانات
    // لكن مؤقتاً سنستخدم بيانات تجريبية واقعية أكثر

    // بيانات تجريبية واقعية للمستخدمين
    final userStats = {
      // حسابك الحقيقي - بناءً على متابعيك الفعليين
      '62fb0b2e-8cdd-4226-878f-3eec5131952c': {
        'followers': 2, // عدد المتابعين الحقيقي
        'posts': 5,     // عدد المنشورات التقريبي
        'views': 50,    // عدد المشاهدات التقريبي
      },

      // مستخدم آخر موثق
      '5c3675a3-ab2b-4248-bdbf-8bf0dc1de485': {
        'followers': 25,
        'posts': 15,
        'views': 500,
      },
    };

    // للمستخدمين الآخرين، استخدم بيانات عشوائية منخفضة
    return userStats[userId] ?? {
      'followers': (userId.hashCode % 20).abs(), // 0-19 متابع
      'posts': (userId.hashCode % 10).abs(),     // 0-9 منشورات
      'views': (userId.hashCode % 200).abs(),    // 0-199 مشاهدة
    };
  }
}

// Widget مبسط للاستخدام السريع
class QuickUserLevel extends StatelessWidget {
  final String userId;
  final String name;
  final bool isVerified;
  final TextStyle? textStyle;
  final double badgeSize;
  final bool showLevelText;

  const QuickUserLevel({
    super.key,
    required this.userId,
    required this.name,
    this.isVerified = false,
    this.textStyle,
    this.badgeSize = 16,
    this.showLevelText = false,
  });

  @override
  Widget build(BuildContext context) {
    // Debug print لمعرفة حالة التحقق في QuickUserLevel
    print('QuickUserLevel - Name: $name, Is Verified: $isVerified, User ID: $userId');
    
    return FutureBuilder<Map<String, dynamic>?>(
      future: SupabaseService().fetchProfile(userId),
      builder: (context, snapshot) {
        final isUserVerified = snapshot.hasData && (snapshot.data?['is_verified'] ?? false);
        final finalIsVerified = isVerified || isUserVerified;
        
        print('QuickUserLevel FutureBuilder - Name: $name, Is Verified: $finalIsVerified, User ID: $userId');
        
        return UserNameWithLevel(
          name: name,
          userId: userId,
          isVerified: finalIsVerified,
          textStyle: textStyle,
          badgeSize: badgeSize,
          showLevelText: showLevelText,
        );
      },
    );
  }
}

// Widget لعرض تفاصيل المستوى
class UserLevelDetails extends StatelessWidget {
  final String userId;
  final bool showProgress;

  const UserLevelDetails({
    super.key,
    required this.userId,
    this.showProgress = true,
  });

  @override
  Widget build(BuildContext context) {
    final userLevel = _getUserLevel(userId);
    final levelInfo = UserLevelSystem.getLevelInfo(userLevel);
    final mockStats = _getMockUserStats(userId);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: levelInfo.color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: levelInfo.color.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(levelInfo.icon, color: levelInfo.color, size: 20),
              const SizedBox(width: 8),
              Text(
                levelInfo.name,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: levelInfo.color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            levelInfo.description,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          if (showProgress) ...[
            const SizedBox(height: 8),
            _buildProgressToNextLevel(userLevel, mockStats),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressToNextLevel(UserLevel currentLevel, Map<String, int> stats) {
    final nextLevel = UserLevelSystem.getNextLevel(currentLevel);
    if (nextLevel == null) {
      return Text(
        'أعلى مستوى! 🏆',
        style: TextStyle(
          fontSize: 12,
          color: Colors.amber[700],
          fontWeight: FontWeight.w600,
        ),
      );
    }

    final progress = UserLevelSystem.calculateProgress(
      currentLevel: currentLevel,
      followersCount: stats['followers'] ?? 0,
      postsCount: stats['posts'] ?? 0,
      totalViews: stats['views'] ?? 0,
    );

    final nextLevelInfo = UserLevelSystem.getLevelInfo(nextLevel);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التقدم للمستوى التالي: ${nextLevelInfo.name}',
          style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: Colors.grey[300],
          valueColor: AlwaysStoppedAnimation<Color>(nextLevelInfo.color),
        ),
        const SizedBox(height: 4),
        Text(
          '${(progress * 100).toInt()}% مكتمل',
          style: TextStyle(fontSize: 10, color: Colors.grey[600]),
        ),
      ],
    );
  }

  UserLevel _getUserLevel(String userId) {
    final mockUserStats = _getMockUserStats(userId);
    return UserLevelSystem.calculateUserLevel(
      followersCount: mockUserStats['followers'] ?? 0,
      postsCount: mockUserStats['posts'] ?? 0,
      totalViews: mockUserStats['views'] ?? 0,
    );
  }

  Map<String, int> _getMockUserStats(String userId) {
    // نفس البيانات المستخدمة في الدالة الأخرى
    final userStats = {
      '62fb0b2e-8cdd-4226-878f-3eec5131952c': {
        'followers': 2, // عدد المتابعين الحقيقي
        'posts': 5,     // عدد المنشورات التقريبي
        'views': 50,    // عدد المشاهدات التقريبي
      },

      '5c3675a3-ab2b-4248-bdbf-8bf0dc1de485': {
        'followers': 25,
        'posts': 15,
        'views': 500,
      },
    };

    return userStats[userId] ?? {
      'followers': (userId.hashCode % 20).abs(),
      'posts': (userId.hashCode % 10).abs(),
      'views': (userId.hashCode % 200).abs(),
    };
  }
}
