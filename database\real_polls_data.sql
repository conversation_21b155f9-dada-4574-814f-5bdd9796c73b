-- بيانات تصويتات حقيقية يمكن لأي مستخدم نشرها
-- استخدم auth.uid() للحصول على المستخدم الحالي

-- إدراج التصويتات الحقيقية والمتنوعة

-- 1. تصويت رياضي - كأس العالم 2026
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(), 
    'أي منتخب تتوقع أن يفوز بكأس العالم 2026؟', 
    'public', 
    'sports', 
    'oneWeek', 
    true, 
    false, 
    NOW() + INTERVAL '7 days'
);

-- خيارات التصويت
INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'أي منتخب تتوقع أن يفوز بكأس العالم 2026؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('البرازيل 🇧🇷', 0),
    ('الأرجنتين 🇦🇷', 1),
    ('فرنسا 🇫🇷', 2),
    ('إنجلترا 🏴󠁧󠁢󠁥󠁮󠁧󠁿', 3),
    ('إسبانيا 🇪🇸', 4),
    ('ألمانيا 🇩🇪', 5)
) AS options(option_text, option_order);

-- 2. تصويت تقني - الذكاء الاصطناعي
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(), 
    'ما هو أفضل نموذج ذكاء اصطناعي للاستخدام اليومي؟', 
    'public', 
    'technology', 
    'unlimited', 
    true, 
    true
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو أفضل نموذج ذكاء اصطناعي للاستخدام اليومي؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('ChatGPT من OpenAI', 0),
    ('Claude من Anthropic', 1),
    ('Gemini من Google', 2),
    ('Copilot من Microsoft', 3),
    ('Meta AI من فيسبوك', 4)
) AS options(option_text, option_order);

-- 3. تصويت مجتمعي - وسائل التواصل
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(), 
    'ما هي منصة التواصل الاجتماعي المفضلة لديك؟', 
    'public', 
    'general', 
    'threeDays', 
    true, 
    true, 
    NOW() + INTERVAL '3 days'
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هي منصة التواصل الاجتماعي المفضلة لديك؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('تويتر (X) 🐦', 0),
    ('إنستغرام 📸', 1),
    ('تيك توك 🎵', 2),
    ('يوتيوب 📺', 3),
    ('سناب شات 👻', 4),
    ('لينكد إن 💼', 5)
) AS options(option_text, option_order);

-- 4. تصويت صحي - النظام الغذائي
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(), 
    'ما هو أهم عنصر في النظام الغذائي الصحي؟', 
    'public', 
    'health', 
    'unlimited', 
    true, 
    false
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو أهم عنصر في النظام الغذائي الصحي؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('الخضروات والفواكه 🥗', 0),
    ('البروتينات 🥩', 1),
    ('الحبوب الكاملة 🌾', 2),
    ('شرب الماء بكثرة 💧', 3),
    ('تجنب السكريات 🚫🍭', 4)
) AS options(option_text, option_order);

-- 5. تصويت تعليمي - التعلم الإلكتروني
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(), 
    'ما هو أفضل موقع للتعلم الإلكتروني والدورات المجانية؟', 
    'public', 
    'education', 
    'oneWeek', 
    true, 
    true, 
    NOW() + INTERVAL '7 days'
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو أفضل موقع للتعلم الإلكتروني والدورات المجانية؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('يوتيوب YouTube', 0),
    ('كورسيرا Coursera', 1),
    ('يوديمي Udemy', 2),
    ('خان أكاديمي Khan Academy', 3),
    ('إدكس edX', 4),
    ('رواق (عربي)', 5)
) AS options(option_text, option_order);

-- 6. تصويت ترفيهي - الأفلام
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(), 
    'ما هو نوع الأفلام المفضل لديك؟', 
    'public', 
    'entertainment', 
    'unlimited', 
    true, 
    true
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو نوع الأفلام المفضل لديك؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('الأكشن والمغامرات 🎬', 0),
    ('الكوميديا والضحك 😂', 1),
    ('الدراما والرومانسية 💕', 2),
    ('الخيال العلمي 🚀', 3),
    ('الرعب والإثارة 😱', 4),
    ('الوثائقيات 📚', 5)
) AS options(option_text, option_order);

-- 7. تصويت أعمال - العمل عن بُعد
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(), 
    'ما هو أفضل نموذج عمل في عصر التكنولوجيا؟', 
    'public', 
    'business', 
    'oneDay', 
    true, 
    false, 
    NOW() + INTERVAL '1 day'
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو أفضل نموذج عمل في عصر التكنولوجيا؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('العمل من المكتب 🏢', 0),
    ('العمل عن بُعد 🏠', 1),
    ('العمل المختلط (هجين) ⚖️', 2),
    ('العمل المرن حسب المشروع 📋', 3)
) AS options(option_text, option_order);

-- 8. تصويت ديني - الأعمال الخيرية
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(), 
    'ما هو أفضل شكل للعمل الخيري والمساعدة؟', 
    'public', 
    'religion', 
    'unlimited', 
    true, 
    false
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو أفضل شكل للعمل الخيري والمساعدة؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('التبرع المالي 💰', 0),
    ('التطوع بالوقت والجهد ⏰', 1),
    ('تقديم الخدمات المهنية مجاناً 🛠️', 2),
    ('نشر الوعي والتثقيف 📢', 3),
    ('كفالة الأيتام والمحتاجين 🤝', 4)
) AS options(option_text, option_order);

-- 9. تصويت سياسي - القضايا العربية
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(), 
    'ما هي أولوية العالم العربي في الوقت الحالي؟', 
    'public', 
    'politics', 
    'unlimited', 
    true, 
    false
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هي أولوية العالم العربي في الوقت الحالي؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('تحقيق الأمن والاستقرار 🕊️', 0),
    ('التنمية الاقتصادية 📈', 1),
    ('التطوير التعليمي والثقافي 📚', 2),
    ('حل القضية الفلسطينية 🇵🇸', 3),
    ('مكافحة الفساد ⚖️', 4),
    ('حماية البيئة 🌍', 5)
) AS options(option_text, option_order);

-- 10. تصويت مجتمعي - البيئة
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(), 
    'ما هو أهم إجراء لحماية البيئة يمكن للفرد القيام به؟', 
    'public', 
    'community', 
    'threeDays', 
    true, 
    true, 
    NOW() + INTERVAL '3 days'
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو أهم إجراء لحماية البيئة يمكن للفرد القيام به؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('تقليل استخدام البلاستيك ♻️', 0),
    ('توفير الطاقة والمياه 💡', 1),
    ('استخدام وسائل النقل العام 🚌', 2),
    ('إعادة التدوير 🔄', 3),
    ('زراعة الأشجار 🌳', 4),
    ('شراء منتجات صديقة للبيئة 🌱', 5)
) AS options(option_text, option_order);

-- 11. تصويت تقني - البرمجة
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(), 
    'ما هي أفضل لغة برمجة للمبتدئين في 2024؟', 
    'public', 
    'technology', 
    'unlimited', 
    true, 
    true
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هي أفضل لغة برمجة للمبتدئين في 2024؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('Python 🐍', 0),
    ('JavaScript 🌐', 1),
    ('Java ☕', 2),
    ('C++ ⚡', 3),
    ('Swift 📱', 4),
    ('Kotlin 🤖', 5)
) AS options(option_text, option_order);

-- 12. تصويت صحي - الرياضة
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(), 
    'ما هو أفضل وقت لممارسة الرياضة؟', 
    'public', 
    'health', 
    'oneDay', 
    true, 
    true, 
    NOW() + INTERVAL '1 day'
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو أفضل وقت لممارسة الرياضة؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('الصباح الباكر (5-8 ص) 🌅', 0),
    ('الصباح المتأخر (8-12 ظ) ☀️', 1),
    ('بعد الظهر (12-6 م) 🌤️', 2),
    ('المساء (6-10 م) 🌆', 3)
) AS options(option_text, option_order);

-- رسالة نجاح
SELECT 'تم إدراج التصويتات الحقيقية بنجاح! يمكن لأي مستخدم مسجل نشرها.' as message;
