class NumberFormatUtil {
  static String prettyCount(int n) {
    if (n >= 1000000000) {
      final v = n / 1000000000;
      return _format(v, ' مليار');
    } else if (n >= 1000000) {
      final v = n / 1000000;
      return _format(v, ' مليون');
    } else if (n >= 1000) {
      final v = n / 1000;
      return _format(v, ' ألف');
    } else {
      return n.toString();
    }
  }

  static String _format(double v, String suffix) {
    final isInt = v == v.roundToDouble();
    final str = isInt ? v.toStringAsFixed(0) : v.toStringAsFixed(1);
    return '$str$suffix';
  }
} 