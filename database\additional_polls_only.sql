-- تصويتات إضافية حقيقية (يجب تشغيل complete_polls_system.sql أولاً)

-- 13. تصويت رياضي - كرة القدم العربية
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(), 
    'من هو أفضل لاعب عربي في كرة القدم حالياً؟', 
    'public', 
    'sports', 
    'unlimited', 
    true, 
    false
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'من هو أفضل لاعب عربي في كرة القدم حالياً؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('محمد صلاح (مصر) ⚽', 0),
    ('رياض محرز (الجزائر) 🇩🇿', 1),
    ('أشرف حكيمي (المغرب) 🇲🇦', 2),
    ('سالم الدوسري (السعودية) 🇸🇦', 3),
    ('علي مبخوت (الإمارات) 🇦🇪', 4)
) AS options(option_text, option_order);

-- 14. تصويت ترفيهي - المسلسلات
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(), 
    'ما هو أفضل مسلسل عربي شاهدته مؤخراً؟', 
    'public', 
    'entertainment', 
    'oneWeek', 
    true, 
    true, 
    NOW() + INTERVAL '7 days'
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو أفضل مسلسل عربي شاهدته مؤخراً؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('جعفر العمدة 👑', 0),
    ('الاختيار 🎖️', 1),
    ('نسل الأغراب 🏛️', 2),
    ('رشاش 🏜️', 3),
    ('مدرسة الروابي للبنات 🎓', 4),
    ('الملك 👨‍👑', 5)
) AS options(option_text, option_order);

-- 15. تصويت تعليمي - التخصصات الجامعية
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(), 
    'ما هو أهم تخصص جامعي للمستقبل؟', 
    'public', 
    'education', 
    'unlimited', 
    true, 
    false
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو أهم تخصص جامعي للمستقبل؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('علوم الحاسوب والذكاء الاصطناعي 🤖', 0),
    ('الطب والعلوم الصحية 🏥', 1),
    ('الهندسة والتكنولوجيا ⚙️', 2),
    ('إدارة الأعمال والاقتصاد 💼', 3),
    ('العلوم البيئية والطاقة المتجددة 🌱', 4),
    ('التصميم والإبداع الرقمي 🎨', 5)
) AS options(option_text, option_order);

-- 16. تصويت مجتمعي - المرأة في العمل
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(), 
    'ما هو أهم عامل لتمكين المرأة في سوق العمل؟', 
    'public', 
    'community', 
    'threeDays', 
    true, 
    false, 
    NOW() + INTERVAL '3 days'
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو أهم عامل لتمكين المرأة في سوق العمل؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('توفير حضانات في أماكن العمل 👶', 0),
    ('المرونة في ساعات العمل ⏰', 1),
    ('المساواة في الأجور 💰', 2),
    ('فرص التدريب والتطوير 📈', 3),
    ('بيئة عمل آمنة ومحترمة 🛡️', 4)
) AS options(option_text, option_order);

-- 17. تصويت تقني - العملات الرقمية
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(), 
    'ما رأيك في مستقبل العملات الرقمية؟', 
    'public', 
    'technology', 
    'unlimited', 
    true, 
    true
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما رأيك في مستقبل العملات الرقمية؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('ستحل محل العملات التقليدية 🚀', 0),
    ('ستكون مكملة للعملات التقليدية ⚖️', 1),
    ('ستبقى للاستثمار فقط 📊', 2),
    ('ستختفي تدريجياً 📉', 3),
    ('غير متأكد 🤷‍♂️', 4)
) AS options(option_text, option_order);

-- 18. تصويت صحي - النوم
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(), 
    'كم ساعة نوم تحتاجها يومياً للشعور بالراحة؟', 
    'public', 
    'health', 
    'oneDay', 
    true, 
    true, 
    NOW() + INTERVAL '1 day'
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'كم ساعة نوم تحتاجها يومياً للشعور بالراحة؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('6 ساعات أو أقل 😴', 0),
    ('7-8 ساعات 😊', 1),
    ('9-10 ساعات 😌', 2),
    ('أكثر من 10 ساعات 😪', 3)
) AS options(option_text, option_order);

-- 19. تصويت أعمال - ريادة الأعمال
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(), 
    'ما هو أكبر تحدي يواجه رواد الأعمال الشباب؟', 
    'public', 
    'business', 
    'unlimited', 
    true, 
    false
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو أكبر تحدي يواجه رواد الأعمال الشباب؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('الحصول على التمويل 💰', 0),
    ('إيجاد الفكرة المناسبة 💡', 1),
    ('بناء فريق العمل 👥', 2),
    ('التسويق والوصول للعملاء 📢', 3),
    ('الإجراءات الحكومية والقانونية 📋', 4)
) AS options(option_text, option_order);

-- 20. تصويت ديني - قراءة القرآن
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(), 
    'ما هو أفضل وقت لقراءة القرآن الكريم؟', 
    'public', 
    'religion', 
    'oneWeek', 
    true, 
    false, 
    NOW() + INTERVAL '7 days'
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو أفضل وقت لقراءة القرآن الكريم؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('بعد صلاة الفجر 🌅', 0),
    ('بين المغرب والعشاء 🌆', 1),
    ('قبل النوم 🌙', 2),
    ('في أي وقت متاح ⏰', 3)
) AS options(option_text, option_order);

-- 21. تصويت ترفيهي - الألعاب
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(), 
    'ما هو نوع الألعاب المفضل لديك؟', 
    'public', 
    'entertainment', 
    'unlimited', 
    true, 
    true
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو نوع الألعاب المفضل لديك؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('ألعاب الأكشن والقتال ⚔️', 0),
    ('ألعاب الرياضة ⚽', 1),
    ('ألعاب الاستراتيجية 🧠', 2),
    ('ألعاب المغامرات 🗺️', 3),
    ('ألعاب السباق 🏎️', 4),
    ('ألعاب الألغاز 🧩', 5)
) AS options(option_text, option_order);

-- 22. تصويت عام - الطقس
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(), 
    'ما هو فصل السنة المفضل لديك؟', 
    'public', 
    'general', 
    'threeDays', 
    true, 
    true, 
    NOW() + INTERVAL '3 days'
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هو فصل السنة المفضل لديك؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('الربيع 🌸', 0),
    ('الصيف ☀️', 1),
    ('الخريف 🍂', 2),
    ('الشتاء ❄️', 3)
) AS options(option_text, option_order);

-- 23. تصويت تعليمي - طرق التعلم
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
VALUES (
    auth.uid(), 
    'ما هي أفضل طريقة للتعلم بالنسبة لك؟', 
    'public', 
    'education', 
    'unlimited', 
    true, 
    false
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هي أفضل طريقة للتعلم بالنسبة لك؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('القراءة والكتب 📚', 0),
    ('مشاهدة الفيديوهات 📹', 1),
    ('التطبيق العملي 🛠️', 2),
    ('النقاش والحوار 💬', 3),
    ('الدورات التفاعلية 🎯', 4)
) AS options(option_text, option_order);

-- 24. تصويت مجتمعي - المواصلات
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
VALUES (
    auth.uid(), 
    'ما هي وسيلة المواصلات المفضلة لديك؟', 
    'public', 
    'community', 
    'oneDay', 
    true, 
    true, 
    NOW() + INTERVAL '1 day'
);

INSERT INTO poll_options (poll_id, text, option_order) 
SELECT 
    (SELECT id FROM polls WHERE question = 'ما هي وسيلة المواصلات المفضلة لديك؟' ORDER BY created_at DESC LIMIT 1),
    option_text,
    option_order
FROM (VALUES 
    ('السيارة الشخصية 🚗', 0),
    ('المواصلات العامة 🚌', 1),
    ('الدراجة الهوائية 🚴‍♂️', 2),
    ('المشي 🚶‍♂️', 3),
    ('تطبيقات النقل (أوبر/كريم) 📱', 4)
) AS options(option_text, option_order);

-- رسالة نجاح
SELECT 'تم إدراج 12 تصويت إضافي حقيقي!' as message;
