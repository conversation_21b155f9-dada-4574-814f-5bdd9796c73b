-- إنشاء جداول إحصائيات القصص
-- نفذ هذا السكريبت في Supabase SQL Editor

-- جدول مشاهدات القصص (كل مستخدم يحسب مرة واحدة فقط)
CREATE TABLE IF NOT EXISTS story_views (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    story_id UUID REFERENCES stories(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(story_id, user_id)
);

-- جدول تفاعلات القصص (الإعجابات، إلخ)
CREATE TABLE IF NOT EXISTS story_reactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    story_id UUID REFERENCES stories(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('like', 'love', 'laugh', 'wow', 'sad', 'angry')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(story_id, user_id, type)
);

-- تفعيل RLS
ALTER TABLE story_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE story_reactions ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان لجدول مشاهدات القصص
CREATE POLICY "Users can view their own story views" ON story_views
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own story views" ON story_views
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Story owners can view all views" ON story_views
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM stories 
            WHERE stories.id = story_views.story_id 
            AND stories.user_id = auth.uid()
        )
    );

-- سياسات الأمان لجدول تفاعلات القصص
CREATE POLICY "Users can view their own story reactions" ON story_reactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own story reactions" ON story_reactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own story reactions" ON story_reactions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own story reactions" ON story_reactions
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Story owners can view all reactions" ON story_reactions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM stories 
            WHERE stories.id = story_reactions.story_id 
            AND stories.user_id = auth.uid()
        )
    );

-- إنشاء indexes لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_story_views_story_id ON story_views(story_id);
CREATE INDEX IF NOT EXISTS idx_story_views_user_id ON story_views(user_id);
CREATE INDEX IF NOT EXISTS idx_story_views_created_at ON story_views(created_at);

CREATE INDEX IF NOT EXISTS idx_story_reactions_story_id ON story_reactions(story_id);
CREATE INDEX IF NOT EXISTS idx_story_reactions_user_id ON story_reactions(user_id);
CREATE INDEX IF NOT EXISTS idx_story_reactions_type ON story_reactions(type);
CREATE INDEX IF NOT EXISTS idx_story_reactions_created_at ON story_reactions(created_at);

-- التحقق من إنشاء الجداول
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('story_views', 'story_reactions')
ORDER BY table_name, column_name; 