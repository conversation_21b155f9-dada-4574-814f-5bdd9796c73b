-- إضافة الأعمدة المفقودة لإعدادات التطبيق في جدول user_settings
-- قم بتنفيذ هذه الأوامر في Supabase SQL Editor

-- إضافة عمود notify_like إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'notify_like') THEN
        ALTER TABLE user_settings ADD COLUMN notify_like BOOLEAN DEFAULT true;
    END IF;
END $$;

-- إضافة عمود notify_comment إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'notify_comment') THEN
        ALTER TABLE user_settings ADD COLUMN notify_comment BOOLEAN DEFAULT true;
    END IF;
END $$;

-- إضافة عمود default_visibility إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'default_visibility') THEN
        ALTER TABLE user_settings ADD COLUMN default_visibility TEXT DEFAULT 'public';
    END IF;
END $$;

-- إضافة عمود comments_from إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'comments_from') THEN
        ALTER TABLE user_settings ADD COLUMN comments_from TEXT DEFAULT 'everyone';
    END IF;
END $$;

-- التحقق من جميع الأعمدة المطلوبة لإعدادات التطبيق
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'user_settings' 
AND column_name IN (
    'notify_follow', 'notify_like', 'notify_comment', 'notify_chat', 'notify_app',
    'default_visibility', 'comments_from', 'post_visibility', 'comment_permission'
)
ORDER BY column_name; 