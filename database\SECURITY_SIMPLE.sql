-- ===================================
-- إعدادات الأمان المبسطة والمضمونة
-- تشغل بعد PRODUCTION_SETUP.sql
-- ===================================

-- 1. إضافة قيود على الجداول
ALTER TABLE job_seekers 
ADD CONSTRAINT check_age CHECK (age >= 16 AND age <= 70);

ALTER TABLE job_seekers 
ADD CONSTRAINT check_experience CHECK (experience_years >= 0 AND experience_years <= 50);

ALTER TABLE job_seekers 
ADD CONSTRAINT check_gender CHECK (gender IN ('ذكر', 'أنثى'));

ALTER TABLE job_seekers 
ADD CONSTRAINT check_marital_status CHECK (marital_status IN ('single', 'married', 'divorced', 'widowed'));

ALTER TABLE job_seekers 
ADD CONSTRAINT check_category CHECK (category IN (
    'construction', 'teaching', 'driving', 'barbering', 'programming', 
    'delivery', 'design', 'carpentry', 'blacksmithing', 'tailoring', 
    'painting', 'plastering', 'electrical', 'mechanics', 'cleaning', 
    'cooking', 'healthcare', 'sales', 'accounting', 'security', 'other'
));

ALTER TABLE job_seekers 
ADD CONSTRAINT check_job_type CHECK (preferred_job_type IN (
    'fullTime', 'partTime', 'remote', 'freelance', 'contract'
));

-- 2. إضافة قيد فريد لمنع تكرار الملف المهني
ALTER TABLE job_seekers 
ADD CONSTRAINT unique_user_profile UNIQUE (user_id);

-- 3. إضافة قيود فريدة للإعجابات والحفظ
ALTER TABLE job_seeker_likes 
ADD CONSTRAINT unique_user_like UNIQUE (seeker_id, user_id);

ALTER TABLE job_seeker_saves 
ADD CONSTRAINT unique_user_save UNIQUE (seeker_id, user_id);

ALTER TABLE job_seeker_user_settings 
ADD CONSTRAINT unique_user_settings UNIQUE (user_id);

-- 4. دالة التحقق من رقم الهاتف السعودي
CREATE OR REPLACE FUNCTION is_valid_saudi_phone(phone TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN phone ~ '^05[0-9]{8}$';
END;
$$ LANGUAGE plpgsql;

-- 5. دالة التحقق من البريد الإلكتروني
CREATE OR REPLACE FUNCTION is_valid_email(email TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    IF email IS NULL OR email = '' THEN
        RETURN TRUE; -- البريد اختياري
    END IF;
    RETURN email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$';
END;
$$ LANGUAGE plpgsql;

-- 6. محفز التحقق من البيانات
CREATE OR REPLACE FUNCTION validate_job_seeker()
RETURNS TRIGGER AS $$
BEGIN
    -- التحقق من الاسم
    IF LENGTH(TRIM(NEW.full_name)) < 3 THEN
        RAISE EXCEPTION 'الاسم قصير جداً (3 أحرف على الأقل)';
    END IF;
    
    -- التحقق من رقم الهاتف
    IF NOT is_valid_saudi_phone(NEW.phone_number) THEN
        RAISE EXCEPTION 'رقم الهاتف غير صحيح. يجب أن يبدأ بـ 05 ويتكون من 10 أرقام';
    END IF;
    
    -- التحقق من البريد الإلكتروني
    IF NOT is_valid_email(NEW.email) THEN
        RAISE EXCEPTION 'صيغة البريد الإلكتروني غير صحيحة';
    END IF;
    
    -- التحقق من الوصف
    IF LENGTH(TRIM(NEW.description)) < 10 THEN
        RAISE EXCEPTION 'الوصف قصير جداً (10 أحرف على الأقل)';
    END IF;
    
    -- التحقق من المهارات
    IF array_length(NEW.skills, 1) IS NULL OR array_length(NEW.skills, 1) = 0 THEN
        RAISE EXCEPTION 'يجب إضافة مهارة واحدة على الأقل';
    END IF;
    
    -- التحقق من اللغات
    IF array_length(NEW.languages, 1) IS NULL OR array_length(NEW.languages, 1) = 0 THEN
        RAISE EXCEPTION 'يجب إضافة لغة واحدة على الأقل';
    END IF;
    
    -- تحديث updated_at
    NEW.updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 7. تطبيق المحفز
DROP TRIGGER IF EXISTS validate_job_seeker_trigger ON job_seekers;
CREATE TRIGGER validate_job_seeker_trigger
    BEFORE INSERT OR UPDATE ON job_seekers
    FOR EACH ROW EXECUTE FUNCTION validate_job_seeker();

-- 8. دالة منع الإعجاب بالملف الشخصي
CREATE OR REPLACE FUNCTION prevent_self_like()
RETURNS TRIGGER AS $$
BEGIN
    -- منع الإعجاب بالملف الشخصي
    IF EXISTS (
        SELECT 1 FROM job_seekers 
        WHERE id = NEW.seeker_id AND user_id = NEW.user_id
    ) THEN
        RAISE EXCEPTION 'لا يمكنك الإعجاب بملفك الشخصي';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 9. تطبيق محفز منع الإعجاب الذاتي
DROP TRIGGER IF EXISTS prevent_self_like_trigger ON job_seeker_likes;
CREATE TRIGGER prevent_self_like_trigger
    BEFORE INSERT ON job_seeker_likes
    FOR EACH ROW EXECUTE FUNCTION prevent_self_like();

-- 10. دالة منع حفظ الملف الشخصي
CREATE OR REPLACE FUNCTION prevent_self_save()
RETURNS TRIGGER AS $$
BEGIN
    -- منع حفظ الملف الشخصي
    IF EXISTS (
        SELECT 1 FROM job_seekers 
        WHERE id = NEW.seeker_id AND user_id = NEW.user_id
    ) THEN
        RAISE EXCEPTION 'لا يمكنك حفظ ملفك الشخصي';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 11. تطبيق محفز منع الحفظ الذاتي
DROP TRIGGER IF EXISTS prevent_self_save_trigger ON job_seeker_saves;
CREATE TRIGGER prevent_self_save_trigger
    BEFORE INSERT ON job_seeker_saves
    FOR EACH ROW EXECUTE FUNCTION prevent_self_save();

-- 12. دالة تنظيف البيانات القديمة
CREATE OR REPLACE FUNCTION cleanup_old_job_data()
RETURNS TEXT AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- حذف الملفات غير النشطة لأكثر من 6 أشهر
    DELETE FROM job_seekers 
    WHERE is_active = false 
    AND updated_at < NOW() - INTERVAL '6 months';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN format('تم حذف %s ملف غير نشط قديم', deleted_count);
END;
$$ LANGUAGE plpgsql;

-- 13. دالة إعادة حساب الإحصائيات
CREATE OR REPLACE FUNCTION recalculate_job_stats()
RETURNS TEXT AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    -- إعادة حساب عدد الإعجابات
    UPDATE job_seekers 
    SET likes_count = (
        SELECT COUNT(*) 
        FROM job_seeker_likes 
        WHERE seeker_id = job_seekers.id
    );
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    RETURN format('تم تحديث إحصائيات %s ملف مهني', updated_count);
END;
$$ LANGUAGE plpgsql;

-- 14. دالة البحث الآمن
CREATE OR REPLACE FUNCTION search_job_seekers_safe(
    search_term TEXT DEFAULT NULL,
    filter_city TEXT DEFAULT NULL,
    filter_category TEXT DEFAULT NULL,
    limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
    id UUID,
    full_name TEXT,
    category TEXT,
    current_city TEXT,
    experience_years INTEGER,
    description TEXT,
    phone_number TEXT,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    -- تحديد حد أقصى للنتائج
    IF limit_count > 100 THEN
        limit_count := 100;
    END IF;
    
    RETURN QUERY
    SELECT 
        js.id,
        js.full_name,
        js.category,
        js.current_city,
        js.experience_years,
        js.description,
        js.phone_number,
        js.created_at
    FROM job_seekers js
    WHERE js.is_active = true
    AND (search_term IS NULL OR (
        LOWER(js.full_name) LIKE LOWER('%' || search_term || '%') OR
        LOWER(js.description) LIKE LOWER('%' || search_term || '%')
    ))
    AND (filter_city IS NULL OR js.current_city = filter_city)
    AND (filter_category IS NULL OR js.category = filter_category)
    ORDER BY js.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 15. منح الصلاحيات
GRANT EXECUTE ON FUNCTION is_valid_saudi_phone(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION is_valid_email(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_old_job_data() TO authenticated;
GRANT EXECUTE ON FUNCTION recalculate_job_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION search_job_seekers_safe(TEXT, TEXT, TEXT, INTEGER) TO anon, authenticated;

-- 16. تم تطبيق الأمان بنجاح
SELECT 
    'تم تطبيق إعدادات الأمان بنجاح!' as message,
    'جميع القيود والتحققات مفعلة.' as status;
