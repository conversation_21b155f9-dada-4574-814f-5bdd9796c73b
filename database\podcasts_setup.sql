-- إن<PERSON><PERSON>ء جدول البودكاستات
CREATE TABLE IF NOT EXISTS podcasts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    audio_url TEXT NOT NULL,
    cover_image_url TEXT,
    category TEXT NOT NULL DEFAULT 'other',
    duration_seconds INTEGER NOT NULL DEFAULT 0,
    tags TEXT,
    allow_download BOOLEAN DEFAULT true,
    allow_comments BOOLEAN DEFAULT true,
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    plays_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,
    downloads_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول إعجابات البودكاست
CREATE TABLE IF NOT EXISTS podcast_likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    podcast_id UUID REFERENCES podcasts(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(podcast_id, user_id)
);

-- إنشاء جدول حفظ البودكاست
CREATE TABLE IF NOT EXISTS podcast_saves (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    podcast_id UUID REFERENCES podcasts(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(podcast_id, user_id)
);

-- إنشاء جدول تشغيل البودكاست
CREATE TABLE IF NOT EXISTS podcast_plays (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    podcast_id UUID REFERENCES podcasts(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    played_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول تعليقات البودكاست
CREATE TABLE IF NOT EXISTS podcast_comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    podcast_id UUID REFERENCES podcasts(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    content TEXT NOT NULL,
    parent_id UUID REFERENCES podcast_comments(id) ON DELETE CASCADE,
    likes_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول إعجابات تعليقات البودكاست
CREATE TABLE IF NOT EXISTS podcast_comment_likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    comment_id UUID REFERENCES podcast_comments(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(comment_id, user_id)
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_podcasts_user_id ON podcasts(user_id);
CREATE INDEX IF NOT EXISTS idx_podcasts_category ON podcasts(category);
CREATE INDEX IF NOT EXISTS idx_podcasts_created_at ON podcasts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_podcast_likes_podcast_id ON podcast_likes(podcast_id);
CREATE INDEX IF NOT EXISTS idx_podcast_likes_user_id ON podcast_likes(user_id);
CREATE INDEX IF NOT EXISTS idx_podcast_saves_podcast_id ON podcast_saves(podcast_id);
CREATE INDEX IF NOT EXISTS idx_podcast_saves_user_id ON podcast_saves(user_id);
CREATE INDEX IF NOT EXISTS idx_podcast_plays_podcast_id ON podcast_plays(podcast_id);
CREATE INDEX IF NOT EXISTS idx_podcast_comments_podcast_id ON podcast_comments(podcast_id);
CREATE INDEX IF NOT EXISTS idx_podcast_comments_parent_id ON podcast_comments(parent_id);

-- إنشاء دالة لزيادة عداد التشغيل
CREATE OR REPLACE FUNCTION increment_podcast_plays(podcast_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE podcasts 
    SET plays_count = plays_count + 1 
    WHERE id = podcast_id;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لتحديث عداد الإعجابات
CREATE OR REPLACE FUNCTION update_podcast_likes_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE podcasts 
        SET likes_count = likes_count + 1 
        WHERE id = NEW.podcast_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE podcasts 
        SET likes_count = likes_count - 1 
        WHERE id = OLD.podcast_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لتحديث عداد التعليقات
CREATE OR REPLACE FUNCTION update_podcast_comments_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE podcasts 
        SET comments_count = comments_count + 1 
        WHERE id = NEW.podcast_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE podcasts 
        SET comments_count = comments_count - 1 
        WHERE id = OLD.podcast_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لتحديث عداد إعجابات التعليقات
CREATE OR REPLACE FUNCTION update_podcast_comment_likes_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE podcast_comments 
        SET likes_count = likes_count + 1 
        WHERE id = NEW.comment_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE podcast_comments 
        SET likes_count = likes_count - 1 
        WHERE id = OLD.comment_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- إنشاء المحفزات (Triggers)
DROP TRIGGER IF EXISTS trigger_podcast_likes_count ON podcast_likes;
CREATE TRIGGER trigger_podcast_likes_count
    AFTER INSERT OR DELETE ON podcast_likes
    FOR EACH ROW EXECUTE FUNCTION update_podcast_likes_count();

DROP TRIGGER IF EXISTS trigger_podcast_comments_count ON podcast_comments;
CREATE TRIGGER trigger_podcast_comments_count
    AFTER INSERT OR DELETE ON podcast_comments
    FOR EACH ROW EXECUTE FUNCTION update_podcast_comments_count();

DROP TRIGGER IF EXISTS trigger_podcast_comment_likes_count ON podcast_comment_likes;
CREATE TRIGGER trigger_podcast_comment_likes_count
    AFTER INSERT OR DELETE ON podcast_comment_likes
    FOR EACH ROW EXECUTE FUNCTION update_podcast_comment_likes_count();

-- إنشاء bucket للتخزين في Supabase Storage
INSERT INTO storage.buckets (id, name, public) 
VALUES ('podcasts', 'podcasts', true)
ON CONFLICT (id) DO NOTHING;

-- إعداد سياسات الأمان (RLS Policies)
ALTER TABLE podcasts ENABLE ROW LEVEL SECURITY;
ALTER TABLE podcast_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE podcast_saves ENABLE ROW LEVEL SECURITY;
ALTER TABLE podcast_plays ENABLE ROW LEVEL SECURITY;
ALTER TABLE podcast_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE podcast_comment_likes ENABLE ROW LEVEL SECURITY;

-- سياسات البودكاستات
CREATE POLICY "البودكاستات مرئية للجميع" ON podcasts FOR SELECT USING (true);
CREATE POLICY "المستخدمون يمكنهم إنشاء بودكاستات" ON podcasts FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "المستخدمون يمكنهم تحديث بودكاستاتهم" ON podcasts FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "المستخدمون يمكنهم حذف بودكاستاتهم" ON podcasts FOR DELETE USING (auth.uid() = user_id);

-- سياسات الإعجابات
CREATE POLICY "الإعجابات مرئية للجميع" ON podcast_likes FOR SELECT USING (true);
CREATE POLICY "المستخدمون يمكنهم إضافة إعجاب" ON podcast_likes FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "المستخدمون يمكنهم حذف إعجابهم" ON podcast_likes FOR DELETE USING (auth.uid() = user_id);

-- سياسات الحفظ
CREATE POLICY "المحفوظات مرئية للمالك فقط" ON podcast_saves FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "المستخدمون يمكنهم حفظ البودكاستات" ON podcast_saves FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "المستخدمون يمكنهم حذف المحفوظات" ON podcast_saves FOR DELETE USING (auth.uid() = user_id);

-- سياسات التشغيل
CREATE POLICY "التشغيلات مرئية للجميع" ON podcast_plays FOR SELECT USING (true);
CREATE POLICY "يمكن تسجيل التشغيل" ON podcast_plays FOR INSERT WITH CHECK (true);

-- سياسات التعليقات
CREATE POLICY "التعليقات مرئية للجميع" ON podcast_comments FOR SELECT USING (true);
CREATE POLICY "المستخدمون يمكنهم إضافة تعليقات" ON podcast_comments FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "المستخدمون يمكنهم تحديث تعليقاتهم" ON podcast_comments FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "المستخدمون يمكنهم حذف تعليقاتهم" ON podcast_comments FOR DELETE USING (auth.uid() = user_id);

-- سياسات إعجابات التعليقات
CREATE POLICY "إعجابات التعليقات مرئية للجميع" ON podcast_comment_likes FOR SELECT USING (true);
CREATE POLICY "المستخدمون يمكنهم إعجاب التعليقات" ON podcast_comment_likes FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "المستخدمون يمكنهم حذف إعجاب التعليق" ON podcast_comment_likes FOR DELETE USING (auth.uid() = user_id);

-- سياسات التخزين
CREATE POLICY "يمكن للجميع رؤية ملفات البودكاست" ON storage.objects FOR SELECT USING (bucket_id = 'podcasts');
CREATE POLICY "المستخدمون المسجلون يمكنهم رفع ملفات البودكاست" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'podcasts' AND auth.role() = 'authenticated');
CREATE POLICY "المستخدمون يمكنهم تحديث ملفاتهم" ON storage.objects FOR UPDATE USING (bucket_id = 'podcasts' AND auth.uid()::text = (storage.foldername(name))[1]);
CREATE POLICY "المستخدمون يمكنهم حذف ملفاتهم" ON storage.objects FOR DELETE USING (bucket_id = 'podcasts' AND auth.uid()::text = (storage.foldername(name))[1]);

-- إدراج بيانات تجريبية (اختياري)
-- يمكن إزالة هذا القسم في الإنتاج
/*
INSERT INTO podcasts (user_id, title, description, audio_url, category, duration_seconds, tags) VALUES
((SELECT id FROM profiles LIMIT 1), 'مقدمة في البودكاست', 'هذا بودكاست تجريبي لاختبار النظام', 'https://example.com/audio1.mp3', 'education', 300, 'تعليم,تجربة'),
((SELECT id FROM profiles LIMIT 1), 'قصة ملهمة', 'قصة تحفيزية رائعة', 'https://example.com/audio2.mp3', 'story', 600, 'قصة,تحفيز');
*/
