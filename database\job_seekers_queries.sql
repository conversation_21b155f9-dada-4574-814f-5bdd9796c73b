-- ===================================
-- استعلامات متقدمة لقسم البحث عن عمل
-- ===================================

-- ===================================
-- 1. استعلامات البحث والتصفية
-- ===================================

-- البحث في جميع الباحثين عن عمل النشطين
CREATE OR REPLACE VIEW active_job_seekers AS
SELECT 
    js.*,
    CASE 
        WHEN js.experience_years = 0 THEN 'بدون خبرة'
        WHEN js.experience_years = 1 THEN 'سنة واحدة'
        WHEN js.experience_years = 2 THEN 'سنتان'
        WHEN js.experience_years <= 10 THEN js.experience_years || ' سنوات'
        ELSE js.experience_years || ' سنة'
    END as formatted_experience,
    js.age || ' سنة' as formatted_age,
    array_to_string(js.skills, ' • ') as skills_text,
    array_to_string(js.languages, ' • ') as languages_text
FROM job_seekers js
WHERE js.is_active = true
ORDER BY js.created_at DESC;

-- البحث بالنص في الاسم والوصف والمهارات
CREATE OR REPLACE FUNCTION search_job_seekers(search_term TEXT)
RETURNS TABLE (
    id UUID,
    full_name TEXT,
    category TEXT,
    current_city TEXT,
    current_country TEXT,
    experience_years INTEGER,
    description TEXT,
    skills TEXT[],
    languages TEXT[],
    phone_number TEXT,
    email TEXT,
    views_count INTEGER,
    likes_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        js.id,
        js.full_name,
        js.category,
        js.current_city,
        js.current_country,
        js.experience_years,
        js.description,
        js.skills,
        js.languages,
        js.phone_number,
        js.email,
        js.views_count,
        js.likes_count,
        js.created_at
    FROM job_seekers js
    WHERE js.is_active = true
    AND (
        LOWER(js.full_name) LIKE LOWER('%' || search_term || '%')
        OR LOWER(js.description) LIKE LOWER('%' || search_term || '%')
        OR EXISTS (
            SELECT 1 FROM unnest(js.skills) skill 
            WHERE LOWER(skill) LIKE LOWER('%' || search_term || '%')
        )
        OR LOWER(js.current_city) LIKE LOWER('%' || search_term || '%')
        OR LOWER(js.nationality) LIKE LOWER('%' || search_term || '%')
    )
    ORDER BY js.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- التصفية المتقدمة
CREATE OR REPLACE FUNCTION filter_job_seekers(
    filter_city TEXT DEFAULT NULL,
    filter_category TEXT DEFAULT NULL,
    filter_job_type TEXT DEFAULT NULL,
    filter_country TEXT DEFAULT NULL,
    min_experience INTEGER DEFAULT NULL,
    max_experience INTEGER DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    full_name TEXT,
    category TEXT,
    current_city TEXT,
    current_country TEXT,
    experience_years INTEGER,
    preferred_job_type TEXT,
    description TEXT,
    skills TEXT[],
    languages TEXT[],
    phone_number TEXT,
    email TEXT,
    views_count INTEGER,
    likes_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        js.id,
        js.full_name,
        js.category,
        js.current_city,
        js.current_country,
        js.experience_years,
        js.preferred_job_type,
        js.description,
        js.skills,
        js.languages,
        js.phone_number,
        js.email,
        js.views_count,
        js.likes_count,
        js.created_at
    FROM job_seekers js
    WHERE js.is_active = true
    AND (filter_city IS NULL OR js.current_city = filter_city)
    AND (filter_category IS NULL OR js.category = filter_category)
    AND (filter_job_type IS NULL OR js.preferred_job_type = filter_job_type)
    AND (filter_country IS NULL OR js.current_country = filter_country)
    AND (min_experience IS NULL OR js.experience_years >= min_experience)
    AND (max_experience IS NULL OR js.experience_years <= max_experience)
    ORDER BY js.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- 2. استعلامات الإحصائيات
-- ===================================

-- إحصائيات عامة
CREATE OR REPLACE VIEW job_seekers_stats AS
SELECT 
    COUNT(*) as total_seekers,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as new_this_week,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '30 days') as new_this_month,
    COUNT(DISTINCT category) as total_categories,
    COUNT(DISTINCT current_city) as total_cities,
    COUNT(DISTINCT current_country) as total_countries,
    AVG(experience_years)::NUMERIC(4,1) as avg_experience,
    MAX(views_count) as max_views,
    SUM(views_count) as total_views,
    SUM(likes_count) as total_likes
FROM job_seekers 
WHERE is_active = true;

-- إحصائيات حسب الفئة
CREATE OR REPLACE VIEW category_stats AS
SELECT 
    category,
    COUNT(*) as count,
    AVG(experience_years)::NUMERIC(4,1) as avg_experience,
    SUM(views_count) as total_views,
    SUM(likes_count) as total_likes,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as new_this_week
FROM job_seekers 
WHERE is_active = true
GROUP BY category
ORDER BY count DESC;

-- إحصائيات حسب المدينة
CREATE OR REPLACE VIEW city_stats AS
SELECT 
    current_city,
    current_country,
    COUNT(*) as count,
    COUNT(DISTINCT category) as categories_count
FROM job_seekers 
WHERE is_active = true
GROUP BY current_city, current_country
ORDER BY count DESC;

-- أكثر المهارات طلباً
CREATE OR REPLACE VIEW popular_skills AS
SELECT 
    skill,
    COUNT(*) as count
FROM job_seekers js,
LATERAL unnest(js.skills) as skill
WHERE js.is_active = true
GROUP BY skill
ORDER BY count DESC
LIMIT 20;

-- ===================================
-- 3. استعلامات التفاعل
-- ===================================

-- الحصول على الملفات المحفوظة للمستخدم
CREATE OR REPLACE FUNCTION get_user_saved_profiles(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    full_name TEXT,
    category TEXT,
    current_city TEXT,
    experience_years INTEGER,
    description TEXT,
    phone_number TEXT,
    saved_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        js.id,
        js.full_name,
        js.category,
        js.current_city,
        js.experience_years,
        js.description,
        js.phone_number,
        jss.created_at as saved_at
    FROM job_seekers js
    INNER JOIN job_seeker_saves jss ON js.id = jss.seeker_id
    WHERE jss.user_id = user_uuid
    AND js.is_active = true
    ORDER BY jss.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- الحصول على الملفات المعجب بها للمستخدم
CREATE OR REPLACE FUNCTION get_user_liked_profiles(user_uuid UUID)
RETURNS TABLE (
    id UUID,
    full_name TEXT,
    category TEXT,
    current_city TEXT,
    experience_years INTEGER,
    description TEXT,
    phone_number TEXT,
    liked_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        js.id,
        js.full_name,
        js.category,
        js.current_city,
        js.experience_years,
        js.description,
        js.phone_number,
        jsl.created_at as liked_at
    FROM job_seekers js
    INNER JOIN job_seeker_likes jsl ON js.id = jsl.seeker_id
    WHERE jsl.user_id = user_uuid
    AND js.is_active = true
    ORDER BY jsl.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- 4. استعلامات التوصيات
-- ===================================

-- توصيات بناءً على المدينة والفئة
CREATE OR REPLACE FUNCTION get_recommended_profiles(
    user_city TEXT DEFAULT NULL,
    user_category TEXT DEFAULT NULL,
    limit_count INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    full_name TEXT,
    category TEXT,
    current_city TEXT,
    experience_years INTEGER,
    description TEXT,
    skills TEXT[],
    phone_number TEXT,
    views_count INTEGER,
    likes_count INTEGER,
    match_score INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        js.id,
        js.full_name,
        js.category,
        js.current_city,
        js.experience_years,
        js.description,
        js.skills,
        js.phone_number,
        js.views_count,
        js.likes_count,
        (
            CASE WHEN js.current_city = user_city THEN 3 ELSE 0 END +
            CASE WHEN js.category = user_category THEN 2 ELSE 0 END +
            CASE WHEN js.views_count > 10 THEN 1 ELSE 0 END
        ) as match_score
    FROM job_seekers js
    WHERE js.is_active = true
    ORDER BY match_score DESC, js.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- 5. استعلامات الصيانة والتنظيف
-- ===================================

-- تنظيف البيانات القديمة غير النشطة
CREATE OR REPLACE FUNCTION cleanup_inactive_profiles()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    -- حذف الملفات غير النشطة لأكثر من 6 أشهر
    DELETE FROM job_seekers 
    WHERE is_active = false 
    AND updated_at < NOW() - INTERVAL '6 months';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- تنظيف الإعجابات والحفظ للملفات المحذوفة
    DELETE FROM job_seeker_likes 
    WHERE seeker_id NOT IN (SELECT id FROM job_seekers);
    
    DELETE FROM job_seeker_saves 
    WHERE seeker_id NOT IN (SELECT id FROM job_seekers);
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- إعادة حساب الإحصائيات
CREATE OR REPLACE FUNCTION recalculate_stats()
RETURNS VOID AS $$
BEGIN
    -- إعادة حساب عدد الإعجابات
    UPDATE job_seekers 
    SET likes_count = (
        SELECT COUNT(*) 
        FROM job_seeker_likes 
        WHERE seeker_id = job_seekers.id
    );
    
    -- تحديث updated_at للملفات المحدثة
    UPDATE job_seekers 
    SET updated_at = NOW() 
    WHERE updated_at < created_at;
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- 6. فهارس البحث النصي المتقدم
-- ===================================

-- إنشاء فهرس البحث النصي للأسماء والأوصاف
CREATE INDEX IF NOT EXISTS idx_job_seekers_search_text 
ON job_seekers 
USING gin(to_tsvector('arabic', full_name || ' ' || description));

-- إنشاء فهرس للمهارات
CREATE INDEX IF NOT EXISTS idx_job_seekers_skills_gin 
ON job_seekers 
USING gin(skills);

-- إنشاء فهرس للغات
CREATE INDEX IF NOT EXISTS idx_job_seekers_languages_gin 
ON job_seekers 
USING gin(languages);

-- ===================================
-- 7. استعلامات الأمان والمراقبة
-- ===================================

-- مراقبة النشاط المشبوه
CREATE OR REPLACE VIEW suspicious_activity AS
SELECT 
    user_id,
    COUNT(*) as profile_count,
    MAX(created_at) as last_created,
    array_agg(DISTINCT current_city) as cities,
    array_agg(DISTINCT phone_number) as phone_numbers
FROM job_seekers
GROUP BY user_id
HAVING COUNT(*) > 1  -- أكثر من ملف لنفس المستخدم
ORDER BY profile_count DESC;

-- إحصائيات الاستخدام اليومية
CREATE OR REPLACE VIEW daily_usage_stats AS
SELECT 
    DATE(created_at) as date,
    COUNT(*) as new_profiles,
    COUNT(DISTINCT user_id) as unique_users
FROM job_seekers
WHERE created_at >= NOW() - INTERVAL '30 days'
GROUP BY DATE(created_at)
ORDER BY date DESC;

-- ===================================
-- تم إنشاء جميع الاستعلامات بنجاح
-- ===================================

SELECT 'تم إنشاء جميع الاستعلامات المتقدمة بنجاح!' as message;
