import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../models/community.dart';
import '../supabase_service.dart';
import '../widgets/profile_avatar.dart';

class CommunitySettingsPage extends StatefulWidget {
  final Community community;
  const CommunitySettingsPage({super.key, required this.community});

  @override
  State<CommunitySettingsPage> createState() => _CommunitySettingsPageState();
}

class _CommunitySettingsPageState extends State<CommunitySettingsPage> with TickerProviderStateMixin {
  late Community _comm;
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameCtrl;
  late TextEditingController _descCtrl;
  late TextEditingController _catCtrl;
  bool _saving = false;
  late TabController _tabController;

  // إعدادات المجتمع
  late bool _allowMemberPosts;
  late bool _requireApproval;
  late bool _allowComments;
  late bool _allowInvites;
  late bool _isPrivate;
  late String _postPermission;
  late String _joinType;

  @override
  void initState() {
    super.initState();
    _comm = widget.community;
    _nameCtrl = TextEditingController(text: _comm.name);
    _descCtrl = TextEditingController(text: _comm.description ?? '');
    _catCtrl = TextEditingController(text: _comm.category ?? '');

    // تهيئة الإعدادات
    _allowMemberPosts = _comm.allowMemberPosts;
    _requireApproval = _comm.requireApproval;
    _allowComments = _comm.allowComments;
    _allowInvites = _comm.allowInvites;
    _isPrivate = _comm.isPrivate;
    _postPermission = _comm.postPermission;
    _joinType = _comm.joinType;

    _tabController = TabController(length: 4, vsync: this);
    _loadMembers();
  }

  @override
  void dispose() {
    _nameCtrl.dispose();
    _descCtrl.dispose();
    _catCtrl.dispose();
    _tabController.dispose();
    super.dispose();
  }

  List<Map<String, dynamic>> _members = [];
  Future<void> _loadMembers() async {
    _members = await SupabaseService().fetchCommunityMembers(_comm.id);
    if (mounted) setState(() {});
  }

  /// إعادة تحميل بيانات المجتمع من قاعدة البيانات
  Future<void> _refreshCommunityData() async {
    try {
      // إضافة تأخير قصير للتأكد من تحديث قاعدة البيانات
      await Future.delayed(const Duration(milliseconds: 500));

      final updatedCommunity = await SupabaseService().getCommunity(_comm.id);
      if (updatedCommunity != null && mounted) {
        setState(() {
          _comm = updatedCommunity;
          // تحديث controllers بالبيانات الجديدة
          _nameCtrl.text = _comm.name;
          _descCtrl.text = _comm.description ?? '';
          _catCtrl.text = _comm.category ?? '';

          // تحديث الإعدادات
          _allowMemberPosts = _comm.allowMemberPosts;
          _requireApproval = _comm.requireApproval;
          _allowComments = _comm.allowComments;
          _allowInvites = _comm.allowInvites;
          _isPrivate = _comm.isPrivate;
          _postPermission = _comm.postPermission;
          _joinType = _comm.joinType;
        });

        // إجبار إعادة بناء الواجهة مرة أخرى
        if (mounted) {
          setState(() {});
        }
      } else {
        throw 'فشل في الحصول على بيانات المجتمع المحدثة';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث البيانات: $e'),
            backgroundColor: Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> _saveBasicInfo() async {
    if (!_formKey.currentState!.validate()) return;
    setState(() => _saving = true);

    try {
      // التحقق من البيانات قبل الحفظ
      final name = _nameCtrl.text.trim();
      final description = _descCtrl.text.trim();
      final category = _catCtrl.text.trim();

      if (name.isEmpty) {
        throw 'اسم المجتمع مطلوب';
      }

      // محاولة تحديث المعلومات
      await SupabaseService().updateCommunityInfo(
        communityId: _comm.id,
        name: name,
        description: description.isEmpty ? null : description,
        category: category.isEmpty ? null : category,
      );

      // إعادة تحميل بيانات المجتمع المحدثة
      await _refreshCommunityData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ تم حفظ المعلومات الأساسية بنجاح'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'خطأ في الحفظ';
        String errorDetails = e.toString().toLowerCase();

        if (errorDetails.contains('اسم المجتمع مطلوب')) {
          errorMessage = 'اسم المجتمع مطلوب';
        } else if (errorDetails.contains('المستخدم غير مسجل')) {
          errorMessage = 'يجب تسجيل الدخول أولاً';
        } else if (errorDetails.contains('المجتمع غير موجود')) {
          errorMessage = 'المجتمع غير موجود';
        } else if (errorDetails.contains('فقط مالك المجتمع')) {
          errorMessage = 'ليس لديك صلاحية لتعديل هذا المجتمع';
        } else if (errorDetails.contains('فشل في تحديث')) {
          errorMessage = 'فشل في حفظ التغييرات - تحقق من الاتصال';
        } else if (errorDetails.contains('unauthorized') ||
                   errorDetails.contains('403') ||
                   errorDetails.contains('forbidden')) {
          errorMessage = 'ليس لديك صلاحية لتعديل هذا المجتمع';
        } else if (errorDetails.contains('not found') ||
                   errorDetails.contains('404')) {
          errorMessage = 'المجتمع غير موجود';
        } else if (errorDetails.contains('network') ||
                   errorDetails.contains('connection')) {
          errorMessage = 'مشكلة في الاتصال - تحقق من الإنترنت';
        } else {
          errorMessage = 'خطأ في الحفظ: ${e.toString()}';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ $errorMessage'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _saveBasicInfo(),
            ),
          ),
        );
      }
    }
    if (mounted) setState(() => _saving = false);
  }

  Future<void> _saveSettings() async {
    setState(() => _saving = true);
    try {
      await SupabaseService().updateCommunitySettings(
        communityId: _comm.id,
        allowMemberPosts: _allowMemberPosts,
        requireApproval: _requireApproval,
        allowComments: _allowComments,
        allowInvites: _allowInvites,
        isPrivate: _isPrivate,
        postPermission: _postPermission,
        joinType: _joinType,
      );
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم حفظ الإعدادات')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في الحفظ: $e')),
        );
      }
    }
    if (mounted) setState(() => _saving = false);
  }

  Future<void> _removeMember(String userId) async {
    await SupabaseService().removeCommunityMember(_comm.id, userId);
    await _loadMembers();
  }

  Future<void> _pickAndUploadImage(String imageType) async {
    final ImagePicker picker = ImagePicker();
    final XFile? image = await picker.pickImage(
      source: ImageSource.gallery,
      maxWidth: imageType == 'avatar' ? 400 : 1200,
      maxHeight: imageType == 'avatar' ? 400 : 600,
      imageQuality: 85,
    );

    if (image == null) return;

    setState(() => _saving = true);
    try {
      final bytes = await image.readAsBytes();

      // التحقق من حجم الملف
      if (bytes.length > 100 * 1024 * 1024) { // 100MB
        throw 'حجم الصورة كبير جداً (الحد الأقصى 100MB)';
      }

      // التحقق من نوع الملف
      final fileName = image.name;
      if (!fileName.toLowerCase().contains(RegExp(r'\.(jpg|jpeg|png|webp|gif)$'))) {
        throw 'نوع الملف غير مدعوم - استخدم JPG أو PNG أو WebP';
      }

      final url = await SupabaseService().uploadCommunityImage(
        communityId: _comm.id,
        imageBytes: bytes,
        fileName: fileName,
        imageType: imageType,
      );

      // التحقق من نجاح الرفع
      if (url.isEmpty) {
        throw 'فشل في رفع الصورة - لم يتم إرجاع رابط';
      }

      // إعادة تحميل بيانات المجتمع وإجبار تحديث الصور
      await _refreshCommunityData();

      // إجبار إعادة بناء الواجهة لإظهار الصور الجديدة
      if (mounted) {
        setState(() {
          // تحديث الواجهة لإظهار الصور الجديدة
        });

        // تأخير إضافي لضمان ظهور الصور
        await Future.delayed(const Duration(milliseconds: 300));

        if (mounted) {
          setState(() {
            // إعادة بناء مرة أخرى لضمان ظهور الصور
          });
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('✅ تم رفع ${imageType == 'avatar' ? 'الصورة الشخصية' : 'صورة الغلاف'} بنجاح'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 3),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = e.toString();

        // إذا كانت رسالة خطأ واضحة، استخدمها كما هي
        if (errorMessage.contains('المستخدم غير مسجل') ||
            errorMessage.contains('المجتمع غير موجود') ||
            errorMessage.contains('فقط مالك المجتمع') ||
            errorMessage.contains('حجم الصورة') ||
            errorMessage.contains('نوع الملف') ||
            errorMessage.contains('نوع الصورة')) {
          // استخدم الرسالة كما هي
        } else {
          // رسائل خطأ عامة
          errorMessage = 'خطأ في رفع الصورة: $errorMessage';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ $errorMessage'),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _pickAndUploadImage(imageType),
            ),
          ),
        );
      }
    }
    if (mounted) setState(() => _saving = false);
  }

  Future<void> _archiveCommunity() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_comm.isArchived ? 'إلغاء أرشفة المجتمع' : 'أرشفة المجتمع'),
        content: Text(_comm.isArchived
          ? 'هل تريد إلغاء أرشفة هذا المجتمع؟ سيصبح مرئياً للجميع مرة أخرى.'
          : 'هل تريد أرشفة هذا المجتمع؟ سيصبح غير مرئي للأعضاء الجدد.'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('إلغاء')),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(_comm.isArchived ? 'إلغاء الأرشفة' : 'أرشفة'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      setState(() => _saving = true);
      try {
        final newArchiveStatus = !_comm.isArchived;
        await SupabaseService().archiveCommunity(_comm.id, newArchiveStatus);

        // إعادة تحميل بيانات المجتمع المحدثة
        await _refreshCommunityData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(newArchiveStatus ? 'تم أرشفة المجتمع بنجاح' : 'تم إلغاء أرشفة المجتمع بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تحديث حالة الأرشفة: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
      if (mounted) setState(() => _saving = false);
    }
  }

  Future<void> _disableCommunity() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_comm.isDisabled ? 'تفعيل المجتمع' : 'تعطيل المجتمع'),
        content: Text(_comm.isDisabled
          ? 'هل تريد تفعيل هذا المجتمع؟ سيتمكن الأعضاء من النشر والتفاعل مرة أخرى.'
          : 'هل تريد تعطيل هذا المجتمع؟ سيتم منع جميع الأنشطة فيه.'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('إلغاء')),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text(_comm.isDisabled ? 'تفعيل' : 'تعطيل'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      setState(() => _saving = true);
      try {
        final newDisableStatus = !_comm.isDisabled;
        await SupabaseService().disableCommunity(_comm.id, newDisableStatus);

        // إعادة تحميل بيانات المجتمع المحدثة
        await _refreshCommunityData();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(newDisableStatus ? 'تم تعطيل المجتمع بنجاح' : 'تم تفعيل المجتمع بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في تحديث حالة التعطيل: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
      if (mounted) setState(() => _saving = false);
    }
  }

  Future<void> _deleteCommunity() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المجتمع'),
        content: const Text('تحذير: هذا الإجراء لا يمكن التراجع عنه. سيتم حذف المجتمع وجميع منشوراته وتعليقاته نهائياً.'),
        actions: [
          TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('إلغاء')),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف نهائياً'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      setState(() => _saving = true);
      try {
        await SupabaseService().deleteCommunity(_comm.id);
        if (mounted) {
          // إغلاق صفحة الإعدادات والعودة للصفحة الرئيسية
          Navigator.of(context).popUntil((route) => route.isFirst);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف المجتمع نهائياً بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('خطأ في حذف المجتمع: $e'),
              backgroundColor: Colors.red,
              duration: const Duration(seconds: 5),
            ),
          );
        }
      }
      if (mounted) setState(() => _saving = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات المجتمع'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'المعلومات'),
            Tab(text: 'الصور'),
            Tab(text: 'الإعدادات'),
            Tab(text: 'الإدارة'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildBasicInfoTab(),
          _buildImagesTab(),
          _buildSettingsTab(),
          _buildManagementTab(),
        ],
      ),
    );
  }

  Widget _buildBasicInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextFormField(
              controller: _nameCtrl,
              decoration: const InputDecoration(
                labelText: 'اسم المجتمع',
                border: OutlineInputBorder(),
              ),
              validator: (v) => (v == null || v.isEmpty) ? 'مطلوب' : null,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _descCtrl,
              decoration: const InputDecoration(
                labelText: 'الوصف',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _catCtrl,
              decoration: const InputDecoration(
                labelText: 'الفئة',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _saving ? null : _saveBasicInfo,
                child: _saving
                  ? const CircularProgressIndicator()
                  : const Text('حفظ المعلومات الأساسية'),
              ),
            ),
            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 16),
            Row(
              children: [
                const Text('الأعضاء', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                const Spacer(),
                IconButton(onPressed: _loadMembers, icon: const Icon(Icons.refresh)),
              ],
            ),
            const SizedBox(height: 8),
            _members.isEmpty
                ? const Card(
                    child: Padding(
                      padding: EdgeInsets.all(16),
                      child: Text('لا يوجد أعضاء', textAlign: TextAlign.center),
                    ),
                  )
                : ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _members.length,
                    itemBuilder: (ctx, i) {
                      final m = _members[i];
                      final isOwner = m['is_owner'] == true;
                      final isAdmin = m['role'] == 'admin';

                      return Card(
                        child: ListTile(
                          leading: ProfileAvatar(userId: m['id']),
                          title: Text(m['name']),
                          subtitle: Text(isOwner ? 'مالك المجتمع' : (isAdmin ? 'مدير' : 'عضو')),
                          trailing: isOwner
                              ? const Chip(
                                  label: Text('مالك'),
                                  backgroundColor: Colors.amber,
                                )
                              : isAdmin
                                  ? const Chip(
                                      label: Text('مدير'),
                                      backgroundColor: Colors.blue,
                                    )
                                  : IconButton(
                                      icon: const Icon(Icons.remove_circle, color: Colors.red),
                                      onPressed: () => _removeMember(m['id']),
                                    ),
                        ),
                      );
                    },
                  ),
          ],
        ),
      ),
    );
  }

  Widget _buildImagesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('الصورة الشخصية', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          Center(
            child: Stack(
              children: [
                CircleAvatar(
                  radius: 60,
                  backgroundImage: _comm.avatarUrl != null && _comm.avatarUrl!.isNotEmpty
                      ? NetworkImage('${_comm.avatarUrl!}?t=${DateTime.now().millisecondsSinceEpoch}')
                      : null,
                  child: _comm.avatarUrl == null || _comm.avatarUrl!.isEmpty
                      ? const Icon(Icons.group, size: 60)
                      : null,
                ),
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: FloatingActionButton.small(
                    onPressed: () => _pickAndUploadImage('avatar'),
                    child: const Icon(Icons.camera_alt),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),
          const Text('صورة الغلاف', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            height: 200,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: _comm.coverUrl != null && _comm.coverUrl!.isNotEmpty
                ? ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Stack(
                      children: [
                        Image.network(
                          '${_comm.coverUrl!}?t=${DateTime.now().millisecondsSinceEpoch}',
                          width: double.infinity,
                          height: 200,
                          fit: BoxFit.cover,
                        ),
                        Positioned(
                          bottom: 8,
                          right: 8,
                          child: FloatingActionButton.small(
                            onPressed: () => _pickAndUploadImage('cover'),
                            child: const Icon(Icons.camera_alt),
                          ),
                        ),
                      ],
                    ),
                  )
                : Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.image, size: 60, color: Colors.grey),
                        const SizedBox(height: 8),
                        const Text('لا توجد صورة غلاف'),
                        const SizedBox(height: 8),
                        ElevatedButton.icon(
                          onPressed: () => _pickAndUploadImage('cover'),
                          icon: const Icon(Icons.upload),
                          label: const Text('رفع صورة'),
                        ),
                      ],
                    ),
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('إعدادات الخصوصية', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('مجتمع خاص'),
                  subtitle: const Text('يتطلب موافقة للانضمام'),
                  value: _isPrivate,
                  onChanged: (value) => setState(() => _isPrivate = value),
                ),
                const Divider(height: 1),
                ListTile(
                  title: const Text('نوع الانضمام'),
                  subtitle: Text(_getJoinTypeText(_joinType)),
                  trailing: DropdownButton<String>(
                    value: _joinType,
                    items: const [
                      DropdownMenuItem(value: 'open', child: Text('مفتوح')),
                      DropdownMenuItem(value: 'approval', child: Text('بموافقة')),
                      DropdownMenuItem(value: 'invite_only', child: Text('بدعوة فقط')),
                    ],
                    onChanged: (value) => setState(() => _joinType = value!),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          const Text('إعدادات المحتوى', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          Card(
            child: Column(
              children: [
                SwitchListTile(
                  title: const Text('السماح للأعضاء بالنشر'),
                  subtitle: const Text('يمكن للأعضاء إنشاء منشورات جديدة'),
                  value: _allowMemberPosts,
                  onChanged: (value) => setState(() => _allowMemberPosts = value),
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('تتطلب موافقة على المنشورات'),
                  subtitle: const Text('يجب موافقة المدير على المنشورات قبل نشرها'),
                  value: _requireApproval,
                  onChanged: (value) => setState(() => _requireApproval = value),
                ),
                const Divider(height: 1),
                SwitchListTile(
                  title: const Text('السماح بالتعليقات'),
                  subtitle: const Text('يمكن للأعضاء التعليق على المنشورات'),
                  value: _allowComments,
                  onChanged: (value) => setState(() => _allowComments = value),
                ),
                const Divider(height: 1),
                ListTile(
                  title: const Text('صلاحيات النشر'),
                  subtitle: Text(_getPostPermissionText(_postPermission)),
                  trailing: DropdownButton<String>(
                    value: _postPermission,
                    items: const [
                      DropdownMenuItem(value: 'all', child: Text('الجميع')),
                      DropdownMenuItem(value: 'members', child: Text('الأعضاء فقط')),
                      DropdownMenuItem(value: 'admins', child: Text('المدراء فقط')),
                    ],
                    onChanged: (value) => setState(() => _postPermission = value!),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          const Text('إعدادات التفاعل', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          Card(
            child: SwitchListTile(
              title: const Text('السماح بالدعوات'),
              subtitle: const Text('يمكن للأعضاء دعوة أشخاص آخرين'),
              value: _allowInvites,
              onChanged: (value) => setState(() => _allowInvites = value),
            ),
          ),
          const SizedBox(height: 24),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _saving ? null : _saveSettings,
              child: _saving
                ? const CircularProgressIndicator()
                : const Text('حفظ الإعدادات'),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildManagementTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('إدارة المجتمع', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
          const SizedBox(height: 16),
          Card(
            child: Column(
              children: [
                ListTile(
                  leading: Icon(
                    _comm.isArchived ? Icons.unarchive : Icons.archive,
                    color: Colors.orange,
                  ),
                  title: Text(_comm.isArchived ? 'إلغاء أرشفة المجتمع' : 'أرشفة المجتمع'),
                  subtitle: Text(_comm.isArchived
                    ? 'المجتمع مؤرشف حالياً - اضغط لإلغاء الأرشفة'
                    : 'إخفاء المجتمع من القائمة العامة'),
                  onTap: _archiveCommunity,
                ),
                const Divider(height: 1),
                ListTile(
                  leading: Icon(
                    _comm.isDisabled ? Icons.play_arrow : Icons.pause,
                    color: Colors.blue,
                  ),
                  title: Text(_comm.isDisabled ? 'تفعيل المجتمع' : 'تعطيل المجتمع'),
                  subtitle: Text(_comm.isDisabled
                    ? 'المجتمع معطل حالياً - اضغط للتفعيل'
                    : 'منع جميع الأنشطة في المجتمع'),
                  onTap: _disableCommunity,
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          const Text('المنطقة الخطرة', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold, color: Colors.red)),
          const SizedBox(height: 16),
          Card(
            color: Colors.red.shade50,
            child: ListTile(
              leading: const Icon(Icons.delete_forever, color: Colors.red),
              title: const Text('حذف المجتمع نهائياً', style: TextStyle(color: Colors.red)),
              subtitle: const Text('تحذير: لا يمكن التراجع عن هذا الإجراء'),
              onTap: _deleteCommunity,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('معلومات المجتمع', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                Text('تاريخ الإنشاء: ${_comm.createdAt.day}/${_comm.createdAt.month}/${_comm.createdAt.year}'),
                Text('عدد الأعضاء: ${_comm.membersCount}'),
                Text('الحالة: ${_getStatusText()}'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getJoinTypeText(String type) {
    switch (type) {
      case 'open': return 'مفتوح للجميع';
      case 'approval': return 'يتطلب موافقة';
      case 'invite_only': return 'بدعوة فقط';
      default: return 'غير محدد';
    }
  }

  String _getPostPermissionText(String permission) {
    switch (permission) {
      case 'all': return 'الجميع';
      case 'members': return 'الأعضاء فقط';
      case 'admins': return 'المدراء فقط';
      default: return 'غير محدد';
    }
  }

  String _getStatusText() {
    if (_comm.isDisabled) return 'معطل';
    if (_comm.isArchived) return 'مؤرشف';
    return 'نشط';
  }
}