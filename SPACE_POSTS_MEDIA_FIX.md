# إصلاح مشكلة الوسائط في منشورات المساحات
# Fix for Media in Space Posts

## المشكلة:
عند إنشاء منشور مع صور في المساحات، يتم نشر منشور فارغ بدون الصور.

## السبب:
1. **استخدام NewPostSheet بدلاً من NewSpacePostSheet**
2. **عدم جلب media_urls من جدول space_posts**
3. **استخدام bucket خاطئ لرفع الصور**

## الإصلاحات المطبقة:

### ✅ **1. إصلاح صفحة تفاصيل المساحة:**
```dart
// في space_details_page.dart
import '../widgets/new_space_post_sheet.dart'; // تغيير الاستيراد

// في _openNewPostSheet()
builder: (context) => NewSpacePostSheet( // تغيير من NewPostSheet
  spaceId: _space.id,
  spaceName: _space.name,
  onPostCreated: () {
    _loadSpacePosts();
    _loadSpaceDetails();
  },
),
```

### ✅ **2. إصلاح رفع الصور:**
```dart
// في supabase_service.dart - uploadSpaceImage()
await _client.storage.from('posts-images').uploadBinary( // تغيير من 'space-images'
  path,
  bytes,
  fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
);

String url = _client.storage.from('posts-images').getPublicUrl(path); // تغيير من 'space-images'
```

### ✅ **3. إصلاح جلب المنشورات:**
```dart
// في supabase_service.dart - fetchPostsBySpace()
final rows = await _client
    .from('space_posts')
    .select('''
      id,
      author_id,
      content,
      created_at,
      media_urls, // إضافة هذا العمود
      link_url,
      link_title,
      link_description,
      link_image
    ''')
    .eq('space_id', spaceId)
    .order('created_at', ascending: false);

// في إنشاء Post object
mediaUrls: row['media_urls'] != null ? List<String>.from(row['media_urls']) : null,
```

### ✅ **4. إضافة رسائل تشخيص:**
```dart
// في fetchPostsBySpace()
if (row['media_urls'] != null && (row['media_urls'] as List).isNotEmpty) {
  print('📸 المنشور يحتوي على ${(row['media_urls'] as List).length} صورة');
}
```

## خطوات الاختبار:

### 1. **اختبار إنشاء منشور مع صور:**
- افتح التطبيق
- اذهب إلى مساحة
- أنشئ منشور جديد مع صور
- تحقق من Console للحصول على الرسائل:
  ```
  ✅ تم إنشاء منشور جديد في المساحة: [space_id]
  📝 محتوى المنشور: [content]...
  🆔 معرف المنشور: [id]
  📊 تم تحديث عداد المنشورات في المساحة: [space_id]
  ```

### 2. **اختبار عرض الصور:**
- تحقق من أن الصور تظهر في المنشور
- تحقق من Console للحصول على الرسائل:
  ```
  📸 المنشور يحتوي على [number] صورة
  ```

### 3. **التحقق من قاعدة البيانات:**
```sql
-- عرض منشورات المساحة مع الوسائط
SELECT 
    id,
    content,
    media_urls,
    created_at
FROM space_posts 
WHERE space_id = '[space_id]'
ORDER BY created_at DESC;
```

## النتائج المتوقعة:

### 🎯 **رفع الصور بنجاح:**
- رفع الصور إلى bucket `posts-images`
- حفظ URLs في عمود `media_urls`

### 🎯 **عرض الصور في المنشورات:**
- عرض الصور في `PostCard` باستخدام `FeedMultiImage`
- دعم الصور المتعددة (حتى 4 صور)

### 🎯 **تحديث عداد المنشورات:**
- تحديث عدد المنشورات عند إضافة منشورات جديدة
- عرض العدد الصحيح في جميع الصفحات

## إذا لم تنجح:

### 1. **تحقق من Console:**
ابحث عن رسائل الخطأ في رفع الصور

### 2. **تحقق من bucket:**
تأكد من وجود bucket `posts-images` في Supabase Storage

### 3. **تحقق من RLS:**
تأكد من أن RLS policies تسمح بالقراءة من `space_posts`

**الآن منشورات المساحات ستدعم الوسائط بشكل صحيح!** 