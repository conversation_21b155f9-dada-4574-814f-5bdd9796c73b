import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import '../models/job.dart';
import '../services/jobs_service.dart';

class JobApplicationPage extends StatefulWidget {
  final Job job;

  const JobApplicationPage({super.key, required this.job});

  @override
  State<JobApplicationPage> createState() => _JobApplicationPageState();
}

class _JobApplicationPageState extends State<JobApplicationPage> {
  final _formKey = GlobalKey<FormState>();
  final _coverLetterController = TextEditingController();
  final _resumeLinkController = TextEditingController();
  
  String? _selectedResumeFile;
  bool _loading = false;
  bool _useResumeLink = true;

  @override
  void dispose() {
    _coverLetterController.dispose();
    _resumeLinkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقديم للوظيفة'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // معلومات الوظيفة
            _buildJobSummary(),
            
            const SizedBox(height: 24),
            
            // السيرة الذاتية
            _buildResumeSection(),
            
            const SizedBox(height: 24),
            
            // رسالة التقديم
            _buildCoverLetterSection(),
            
            const SizedBox(height: 32),
            
            // معلومات إضافية
            _buildInfoSection(),
            
            const SizedBox(height: 32),
            
            // زر التقديم
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _loading ? null : _submitApplication,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _loading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Text(
                        'تقديم الطلب',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildJobSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.work, color: Colors.blue[600]),
              const SizedBox(width: 8),
              Text(
                'تقديم للوظيفة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            widget.job.title,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'في ${widget.job.companyName}',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                widget.job.location,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(width: 16),
              Icon(Icons.work_outline, size: 16, color: Colors.grey[600]),
              const SizedBox(width: 4),
              Text(
                widget.job.jobTypeText,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildResumeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'السيرة الذاتية',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'يرجى إرفاق سيرتك الذاتية أو رابط لها',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 16),
        
        // خيارات السيرة الذاتية
        Row(
          children: [
            Expanded(
              child: RadioListTile<bool>(
                title: const Text('رابط السيرة الذاتية'),
                value: true,
                groupValue: _useResumeLink,
                onChanged: (value) => setState(() => _useResumeLink = value!),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            Expanded(
              child: RadioListTile<bool>(
                title: const Text('رفع ملف'),
                value: false,
                groupValue: _useResumeLink,
                onChanged: (value) => setState(() => _useResumeLink = value!),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        if (_useResumeLink) ...[
          TextFormField(
            controller: _resumeLinkController,
            decoration: const InputDecoration(
              labelText: 'رابط السيرة الذاتية',
              hintText: 'https://example.com/my-resume.pdf',
              border: OutlineInputBorder(),
              prefixIcon: Icon(Icons.link),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'يرجى إدخال رابط السيرة الذاتية';
              }
              final uri = Uri.tryParse(value);
              if (uri == null || !(uri.hasAbsolutePath && uri.hasScheme)) {
                return 'يرجى إدخال رابط صحيح';
              }
              return null;
            },
          ),
        ] else ...[
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                if (_selectedResumeFile == null) ...[
                  Icon(Icons.upload_file, size: 48, color: Colors.grey[400]),
                  const SizedBox(height: 8),
                  Text(
                    'اختر ملف السيرة الذاتية',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  const SizedBox(height: 8),
                  ElevatedButton.icon(
                    onPressed: _pickResumeFile,
                    icon: const Icon(Icons.folder_open),
                    label: const Text('اختيار ملف'),
                  ),
                ] else ...[
                  Icon(Icons.description, size: 48, color: Colors.green[600]),
                  const SizedBox(height: 8),
                  Text(
                    _selectedResumeFile!,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      TextButton.icon(
                        onPressed: _pickResumeFile,
                        icon: const Icon(Icons.edit),
                        label: const Text('تغيير'),
                      ),
                      TextButton.icon(
                        onPressed: () => setState(() => _selectedResumeFile = null),
                        icon: const Icon(Icons.delete, color: Colors.red),
                        label: const Text('حذف', style: TextStyle(color: Colors.red)),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildCoverLetterSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'رسالة التقديم (اختياري)',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'اكتب رسالة قصيرة تعبر فيها عن اهتمامك بالوظيفة',
          style: TextStyle(
            color: Colors.grey[600],
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _coverLetterController,
          decoration: const InputDecoration(
            hintText: 'أعبر عن اهتمامك بالوظيفة ولماذا تعتقد أنك مناسب لها...',
            border: OutlineInputBorder(),
            alignLabelWithHint: true,
          ),
          maxLines: 6,
          maxLength: 500,
        ),
      ],
    );
  }

  Widget _buildInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.amber[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.amber[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.amber[700]),
              const SizedBox(width: 8),
              Text(
                'معلومات مهمة',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.amber[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '• تأكد من أن سيرتك الذاتية محدثة ومناسبة للوظيفة\n'
            '• اكتب رسالة تقديم مخصصة لهذه الوظيفة\n'
            '• سيتم إرسال طلبك إلى صاحب العمل مباشرة\n'
            '• يمكنك متابعة حالة طلبك من قسم "طلباتي"',
            style: TextStyle(
              color: Colors.amber[700],
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickResumeFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx'],
      );

      if (result != null) {
        setState(() {
          _selectedResumeFile = result.files.single.name;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في اختيار الملف: $e')),
        );
      }
    }
  }

  Future<void> _submitApplication() async {
    if (!_formKey.currentState!.validate()) return;

    // التحقق من السيرة الذاتية
    if (_useResumeLink && _resumeLinkController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى إدخال رابط السيرة الذاتية')),
      );
      return;
    }

    if (!_useResumeLink && _selectedResumeFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى اختيار ملف السيرة الذاتية')),
      );
      return;
    }

    setState(() => _loading = true);

    try {
      String? resumeUrl;
      
      if (_useResumeLink) {
        resumeUrl = _resumeLinkController.text.trim();
      } else {
        // TODO: رفع الملف إلى التخزين السحابي
        // resumeUrl = await uploadResumeFile(_selectedResumeFile!);
        resumeUrl = 'uploaded_file_url'; // مؤقت
      }

      await JobsService().applyToJob(
        jobId: widget.job.id,
        resumeUrl: resumeUrl,
        coverLetter: _coverLetterController.text.trim().isEmpty 
            ? null : _coverLetterController.text.trim(),
      );

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تقديم طلبك بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تقديم الطلب: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _loading = false);
      }
    }
  }
}
