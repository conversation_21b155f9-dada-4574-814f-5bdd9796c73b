import 'package:flutter/material.dart';
import '../models/note.dart';
import '../services/notes_service.dart';
import 'create_note_page.dart';

class NoteDetailsPage extends StatefulWidget {
  final Note note;

  const NoteDetailsPage({super.key, required this.note});

  @override
  State<NoteDetailsPage> createState() => _NoteDetailsPageState();
}

class _NoteDetailsPageState extends State<NoteDetailsPage> {
  final NotesService _notesService = NotesService();
  late Note _note;
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    _note = widget.note;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          _note.title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: _note.type.color,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _editNote(),
            tooltip: 'تعديل',
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'pin',
                child: Row(
                  children: [
                    Icon(
                      _note.isPinned ? Icons.push_pin_outlined : Icons.push_pin,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(_note.isPinned ? 'إلغاء التثبيت' : 'تثبيت'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'archive',
                child: Row(
                  children: [
                    Icon(
                      _note.isArchived ? Icons.unarchive : Icons.archive,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(_note.isArchived ? 'إلغاء الأرشفة' : 'أرشفة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 16, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المذكرة
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: _note.type.color.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            _note.type.icon,
                            color: _note.type.color,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _note.type.arabicName,
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: _note.type.color,
                                ),
                              ),
                              Text(
                                _note.category.arabicName,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (_note.isPinned)
                          Icon(
                            Icons.push_pin,
                            color: Colors.amber[600],
                          ),
                      ],
                    ),
                    
                    const SizedBox(height: 16),
                    
                    // الإحصائيات
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        _buildStatItem('كلمة', _note.wordCount.toString()),
                        _buildStatItem('حرف', _note.characterCount.toString()),
                        _buildStatItem('تم الإنشاء', _note.formattedCreatedDate),
                        _buildStatItem('آخر تحديث', _note.formattedUpdatedDate),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // المحتوى
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المحتوى',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey[800],
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      _note.content,
                      style: const TextStyle(
                        fontSize: 16,
                        height: 1.6,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // الوسوم
            if (_note.tags.isNotEmpty) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الوسوم',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[800],
                        ),
                      ),
                      const SizedBox(height: 12),
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: _note.tags.map((tag) {
                          return Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: Colors.blue[50],
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(color: Colors.blue[200]!),
                            ),
                            child: Text(
                              '#$tag',
                              style: TextStyle(
                                color: Colors.blue[700],
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ],
                  ),
                ),
              ),
            ],
            
            // حالة المهمة
            if (_note.isTask) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'حالة المهمة',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[800],
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Icon(
                            _note.taskStatus?.icon ?? TaskStatus.pending.icon,
                            color: _note.taskStatus?.color ?? TaskStatus.pending.color,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _note.taskStatus?.arabicName ?? TaskStatus.pending.arabicName,
                            style: TextStyle(
                              fontSize: 16,
                              color: _note.taskStatus?.color ?? TaskStatus.pending.color,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
            
            // التذكيرات والمواعيد
            if (_note.hasReminder || _note.hasDueDate) ...[
              const SizedBox(height: 16),
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'المواعيد والتذكيرات',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[800],
                        ),
                      ),
                      const SizedBox(height: 12),
                      
                      if (_note.hasReminder) ...[
                        Row(
                          children: [
                            Icon(
                              Icons.alarm,
                              color: Colors.orange[600],
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'تذكير: ${_formatDateTime(_note.reminderDate!)}',
                              style: TextStyle(
                                fontSize: 16,
                                color: Colors.orange[600],
                              ),
                            ),
                          ],
                        ),
                      ],
                      
                      if (_note.hasReminder && _note.hasDueDate)
                        const SizedBox(height: 8),
                      
                      if (_note.hasDueDate) ...[
                        Row(
                          children: [
                            Icon(
                              Icons.schedule,
                              color: _note.isOverdue ? Colors.red[600] : Colors.blue[600],
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'موعد نهائي: ${_formatDateTime(_note.dueDate!)}',
                              style: TextStyle(
                                fontSize: 16,
                                color: _note.isOverdue ? Colors.red[600] : Colors.blue[600],
                                fontWeight: _note.isOverdue ? FontWeight.bold : null,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _editNote() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateNotePage(editNote: _note),
      ),
    ).then((result) {
      if (result != null) {
        setState(() => _note = result);
      }
    });
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'pin':
        _togglePin();
        break;
      case 'archive':
        _toggleArchive();
        break;
      case 'delete':
        _deleteNote();
        break;
    }
  }

  void _togglePin() async {
    setState(() => _loading = true);
    try {
      final updatedNote = await _notesService.togglePin(_note.id);
      setState(() => _note = updatedNote);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_note.isPinned ? 'تم التثبيت' : 'تم إلغاء التثبيت'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }

  void _toggleArchive() async {
    setState(() => _loading = true);
    try {
      await _notesService.toggleArchive(_note.id);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_note.isArchived ? 'تم إلغاء الأرشفة' : 'تم الأرشفة'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }

  void _deleteNote() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المذكرة'),
        content: Text('هل تريد حذف "${_note.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              setState(() => _loading = true);
              try {
                await _notesService.deleteNote(_note.id);
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف المذكرة'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  Navigator.pop(context);
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في الحذف: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } finally {
                setState(() => _loading = false);
              }
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
