import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:typed_data';
import '../models/space.dart';
import '../models/post.dart';
import '../services/spaces_service.dart';
import '../supabase_service.dart';
import '../utils/number_format.dart';
import '../widgets/post_card.dart';
import '../widgets/new_space_post_sheet.dart'; // تغيير الاستيراد
import 'edit_space_page.dart';

class SpaceDetailsPage extends StatefulWidget {
  final Space space;

  const SpaceDetailsPage({super.key, required this.space});

  @override
  State<SpaceDetailsPage> createState() => _SpaceDetailsPageState();
}

class _SpaceDetailsPageState extends State<SpaceDetailsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _spacesService = SpacesService();
  final _supabaseService = SupabaseService();

  late Space _space;
  List<Post> _posts = [];
  bool _isFollowing = false;
  bool _isLoading = false;
  bool _isLoadingPosts = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this); // تقليل عدد التبويبات إلى 2
    _space = widget.space;
    _isFollowing = _space.isFollowing;
    _loadSpaceDetails();
    _loadSpacePosts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadSpaceDetails() async {
    try {
      final spaceDetails = await _spacesService.getSpaceDetails(_space.id);
      if (spaceDetails != null && mounted) {
        setState(() {
          _space = spaceDetails;
          _isFollowing = spaceDetails.isFollowing;
        });
      }
    } catch (e) {
      // Handle error silently
    }
  }

  Future<void> _loadSpacePosts() async {
    setState(() {
      _isLoadingPosts = true;
    });

    try {
      final posts = await _supabaseService.fetchPostsBySpace(_space.id);
      if (mounted) {
        setState(() {
          _posts = posts;
          _isLoadingPosts = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoadingPosts = false;
      });
    }
  }

  void _openNewPostSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => NewSpacePostSheet(
        spaceId: _space.id,
        spaceName: _space.name,
        onPostCreated: () {
          _loadSpacePosts();
          _loadSpaceDetails(); // لتحديث عداد المنشورات
        },
      ),
    );
  }

  void _openEditSpace() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditSpacePage(space: _space),
      ),
    ).then((_) {
      _loadSpaceDetails();
    });
  }

  void _openSpaceSettings() {
    // مؤقتاً - سنضيف صفحة الإعدادات لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('صفحة الإعدادات قيد التطوير')),
    );
  }

  void _showFollowers() {
    // مؤقتاً - سنضيف صفحة المتابعين لاحقاً
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('صفحة المتابعين قيد التطوير')),
    );
  }

  // تحديث صورة الملف الشخصي
  Future<void> _updateAvatar() async {
    if (!_space.isOwner) return;

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 500,
        maxHeight: 500,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _isLoading = true;
        });

        final Uint8List imageBytes = await image.readAsBytes();
        final String imageUrl = await _spacesService.updateSpaceAvatar(_space.id, imageBytes);

        setState(() {
          _space = _space.copyWith(profileImage: imageUrl);
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تحديث صورة الملف الشخصي بنجاح')),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      String errorMessage = 'فشل في تحديث صورة الملف الشخصي';
      if (e.toString().contains('Bucket not found')) {
        errorMessage = 'مشكلة في التخزين. يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.';
      } else if (e.toString().contains('Unauthorized')) {
        errorMessage = 'ليس لديك صلاحية لتعديل هذه المساحة.';
      } else if (e.toString().contains('Network')) {
        errorMessage = 'مشكلة في الاتصال. تحقق من الإنترنت وحاول مرة أخرى.';
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  // تحديث صورة الغلاف
  Future<void> _updateCover() async {
    if (!_space.isOwner) return;

    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1200,
        maxHeight: 600,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _isLoading = true;
        });

        final Uint8List imageBytes = await image.readAsBytes();
        final String imageUrl = await _spacesService.updateSpaceCover(_space.id, imageBytes);

        setState(() {
          _space = _space.copyWith(coverImage: imageUrl);
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تحديث صورة الغلاف بنجاح')),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      String errorMessage = 'فشل في تحديث صورة الغلاف';
      if (e.toString().contains('Bucket not found')) {
        errorMessage = 'مشكلة في التخزين. يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.';
      } else if (e.toString().contains('Unauthorized')) {
        errorMessage = 'ليس لديك صلاحية لتعديل هذه المساحة.';
      } else if (e.toString().contains('Network')) {
        errorMessage = 'مشكلة في الاتصال. تحقق من الإنترنت وحاول مرة أخرى.';
      }

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(errorMessage),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  // تحديث المعرف الفريد
  void _updateUsername() {
    if (!_space.isOwner) return;

    showDialog(
      context: context,
      builder: (context) => _UsernameUpdateDialog(
        currentUsername: _space.username ?? '',
        spaceId: _space.id,
        onUsernameUpdated: (newUsername) {
          setState(() {
            _space = _space.copyWith(username: newUsername);
          });
        },
      ),
    );
  }

  Future<void> _toggleFollow() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final newFollowState = await _spacesService.toggleSpaceFollow(_space.id);
      setState(() {
        _isFollowing = newFollowState;
        _space = _space.copyWith(
          isFollowing: newFollowState,
          followersCount: _space.followersCount + (newFollowState ? 1 : -1),
        );
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(newFollowState ? 'تمت متابعة المساحة' : 'تم إلغاء متابعة المساحة'),
            backgroundColor: newFollowState ? Colors.green : Colors.orange,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحديث المتابعة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('فشل في فتح الرابط')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 300,
              pinned: true,
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
              flexibleSpace: FlexibleSpaceBar(
                background: _buildCoverSection(),
              ),
              actions: _space.isOwner ? [
                // زر النشر
                IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: _openNewPostSheet,
                  tooltip: 'منشور جديد',
                ),
                // زر التعديل
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: _openEditSpace,
                  tooltip: 'تعديل المساحة',
                ),
                // زر الإعدادات
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  onSelected: (value) {
                    switch (value) {
                      case 'settings':
                        _openSpaceSettings();
                        break;
                      case 'followers':
                        _showFollowers();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'settings',
                      child: Row(
                        children: [
                          Icon(Icons.settings),
                          SizedBox(width: 8),
                          Text('إعدادات المساحة'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'followers',
                      child: Row(
                        children: [
                          Icon(Icons.people),
                          SizedBox(width: 8),
                          Text('المتابعون'),
                        ],
                      ),
                    ),
                  ],
                ),
              ] : null,
            ),
          ];
        },
        body: Column(
          children: [
            // معلومات المساحة
            _buildSpaceInfo(),
            
            // التبويبات بتصميم عصري
            Container(
              color: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TabBar(
                controller: _tabController,
                labelColor: Colors.black,
                unselectedLabelColor: Colors.grey[600],
                indicatorColor: Colors.red[600],
                indicatorWeight: 3,
                labelStyle: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 15,
                ),
                tabs: const [
                  Tab(text: 'المنشورات'),
                  Tab(text: 'حول المساحة'),
                ],
              ),
            ),
            
            // محتوى التبويبات
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildPostsTab(),
                  _buildAboutTab(), // دمج نظرة عامة والمعلومات
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCoverSection() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            SpaceCategoryHelper.getCategoryColor(_space.category).withOpacity(0.7),
            SpaceCategoryHelper.getCategoryColor(_space.category),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Stack(
        children: [
          if (_space.coverImage != null)
            Container(
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: NetworkImage(_space.coverImage!),
                  fit: BoxFit.cover,
                ),
              ),
            ),
          
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.transparent,
                  Colors.black.withOpacity(0.3),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),

          // زر تحديث صورة الغلاف (للمالك فقط)
          if (_space.isOwner)
            Positioned(
              top: 20,
              right: 20,
              child: GestureDetector(
                onTap: _updateCover,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.camera_alt,
                    color: Colors.white,
                    size: 20,
                  ),
                ),
              ),
            ),

          Positioned(
            bottom: 20,
            left: 20,
            right: 20,
            child: Row(
              children: [
                // صورة الملف الشخصي
                Stack(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 3),
                        color: Colors.white,
                      ),
                      child: _space.profileImage != null
                          ? ClipOval(
                              child: Image.network(
                                _space.profileImage!,
                                fit: BoxFit.cover,
                              ),
                            )
                          : Icon(
                              SpaceCategoryHelper.getCategoryIcon(_space.category),
                              size: 40,
                              color: SpaceCategoryHelper.getCategoryColor(_space.category),
                            ),
                    ),

                    // زر تحديث الصورة (للمالك فقط)
                    if (_space.isOwner)
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: GestureDetector(
                          onTap: _updateAvatar,
                          child: Container(
                            width: 24,
                            height: 24,
                            decoration: BoxDecoration(
                              color: Colors.blue,
                              shape: BoxShape.circle,
                              border: Border.all(color: Colors.white, width: 2),
                            ),
                            child: const Icon(
                              Icons.camera_alt,
                              size: 12,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
                
                const SizedBox(width: 16),
                
                // معلومات أساسية مع خلفية واضحة
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.6),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Text(
                                  _space.name,
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                            if (_space.isOwner)
                              Container(
                                margin: const EdgeInsets.only(right: 8),
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.6),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: GestureDetector(
                                  onTap: _updateUsername,
                                  child: const Icon(
                                    Icons.edit,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                              ),
                          ],
                        ),

                        const SizedBox(height: 8),

                        // المعرف الفريد
                        if (_space.username != null && _space.username!.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              '@${_space.username}',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),

                        const SizedBox(height: 4),

                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Text(
                            'بواسطة ${_space.ownerName}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.white,
                            ),
                          ),
                        ),

                        const SizedBox(height: 8),

                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          decoration: BoxDecoration(
                            color: Colors.red[600],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            SpaceCategoryHelper.getCategoryName(_space.category),
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpaceInfo() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الوصف
          Text(
            _space.description,
            style: const TextStyle(fontSize: 16),
          ),
          
          const SizedBox(height: 16),
          
          // الإحصائيات بتصميم عصري
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[200]!),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildModernStatItem(
                  Icons.people_outline,
                  NumberFormatUtil.prettyCount(_space.followersCount),
                  'متابع',
                  Colors.red[600]!,
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: Colors.grey[300],
                ),
                _buildModernStatItem(
                  Icons.article_outlined,
                  NumberFormatUtil.prettyCount(_space.postsCount),
                  'منشور',
                  Colors.grey[700]!,
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: Colors.grey[300],
                ),
                _buildModernStatItem(
                  Icons.visibility_outlined,
                  NumberFormatUtil.prettyCount(_space.viewsCount),
                  'مشاهدة',
                  Colors.grey[700]!,
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 20),
          
          // زر المتابعة
          if (!_space.isOwner)
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _toggleFollow,
                style: ElevatedButton.styleFrom(
                  backgroundColor: _isFollowing ? Colors.grey[300] : Colors.red[600],
                  foregroundColor: _isFollowing ? Colors.black87 : Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(25),
                  ),
                  elevation: _isFollowing ? 0 : 3,
                ),
                child: _isLoading
                    ? const CircularProgressIndicator()
                    : Text(
                        _isFollowing ? 'إلغاء المتابعة' : 'متابعة',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String count, String label) {
    return Column(
      children: [
        Icon(icon, color: Colors.red[600], size: 24),
        const SizedBox(height: 4),
        Text(
          count,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  // دالة جديدة للإحصائيات بتصميم عصري
  Widget _buildModernStatItem(IconData icon, String count, String label, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          count,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: Colors.grey[800],
          ),
        ),
        const SizedBox(height: 2),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  // دمج نظرة عامة والمعلومات في تبويب واحد
  Widget _buildAboutTab() {
    return ListView(
      padding: const EdgeInsets.all(20),
      children: [
        // معلومات أساسية
        if (_space.goal != null) ...[
          _buildInfoSection('الهدف من المساحة', _space.goal!),
          const SizedBox(height: 20),
        ],

        if (_space.description.isNotEmpty) ...[
          _buildInfoSection('الوصف', _space.description),
          const SizedBox(height: 20),
        ],

        if (_space.profession != null) ...[
          _buildInfoSection('المهنة/التخصص', _space.profession!),
          const SizedBox(height: 20),
        ],

        _buildInfoSection('الفئة', SpaceCategoryHelper.getCategoryName(_space.category)),
        const SizedBox(height: 20),

        _buildInfoSection('مستوى الخصوصية', _space.privacy.name),
        const SizedBox(height: 20),

        _buildInfoSection(
          'تاريخ الإنشاء',
          '${_space.createdAt.day}/${_space.createdAt.month}/${_space.createdAt.year}',
        ),
        const SizedBox(height: 20),

        // الإحصائيات
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(Icons.people, '${_space.followersCount}', 'متابع'),
              _buildStatItem(Icons.article, '${_space.postsCount}', 'منشور'),
              _buildStatItem(Icons.visibility, '${_space.viewsCount}', 'مشاهدة'),
            ],
          ),
        ),
        const SizedBox(height: 20),

        // معلومات الاتصال
        if (_space.phoneNumber != null || _space.email != null || _space.website != null) ...[
          const Text(
            'معلومات الاتصال',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          if (_space.phoneNumber != null) ...[
            _buildContactItem(Icons.phone, 'الهاتف', _space.phoneNumber!),
            const SizedBox(height: 16),
          ],

          if (_space.email != null) ...[
            _buildContactItem(Icons.email, 'البريد الإلكتروني', _space.email!),
            const SizedBox(height: 16),
          ],

          if (_space.website != null) ...[
            _buildContactItem(Icons.language, 'الموقع الإلكتروني', _space.website!),
            const SizedBox(height: 20),
          ],
        ],

        // روابط التواصل
        if (_space.socialLinks.isNotEmpty) ...[
          const Text(
            'وسائل التواصل الاجتماعي',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: _space.socialLinks.entries.map((entry) {
              return _buildSocialButton(entry.key, entry.value);
            }).toList(),
          ),
        ],
      ],
    );
  }

  Widget _buildPostsTab() {
    if (_isLoadingPosts) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_posts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.article, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد منشورات بعد',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            if (_space.isOwner) ...[
              const SizedBox(height: 8),
              Text(
                'ابدأ بنشر أول منشور في مساحتك',
                style: TextStyle(color: Colors.grey[500]),
              ),
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: _openNewPostSheet,
                icon: const Icon(Icons.add),
                label: const Text('منشور جديد'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadSpacePosts,
      child: ListView.builder(
        padding: EdgeInsets.zero, // إزالة الهوامش لعرض كامل الشاشة
        itemCount: _posts.length,
        itemBuilder: (context, index) {
          return PostCard(
            post: _posts[index],
            onRefresh: () {
              _loadSpacePosts();
              _loadSpaceDetails();
            },
          );
        },
      ),
    );
  }



  Widget _buildInfoSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          content,
          style: const TextStyle(fontSize: 14),
        ),
      ],
    );
  }

  Widget _buildSocialButton(String platform, String url) {
    IconData icon;
    Color color;
    
    switch (platform.toLowerCase()) {
      case 'facebook':
        icon = Icons.facebook;
        color = Colors.blue;
        break;
      case 'instagram':
        icon = Icons.camera_alt;
        color = Colors.purple;
        break;
      case 'twitter':
        icon = Icons.alternate_email;
        color = Colors.lightBlue;
        break;
      case 'youtube':
        icon = Icons.play_circle;
        color = Colors.red;
        break;
      case 'linkedin':
        icon = Icons.work;
        color = Colors.indigo;
        break;
      default:
        icon = Icons.link;
        color = Colors.grey;
    }
    
    return ElevatedButton.icon(
      onPressed: () => _launchUrl(url),
      icon: Icon(icon, size: 20),
      label: Text(platform),
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withOpacity(0.1),
        foregroundColor: color,
        elevation: 0,
      ),
    );
  }

  Widget _buildContactItem(IconData icon, String label, String value) {
    return InkWell(
      onTap: () {
        if (label == 'الهاتف') {
          _launchUrl('tel:$value');
        } else if (label == 'البريد الإلكتروني') {
          _launchUrl('mailto:$value');
        } else if (label == 'الموقع الإلكتروني') {
          _launchUrl(value);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Row(
          children: [
            Icon(icon, color: Colors.blue[600]),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const Spacer(),
            Icon(Icons.open_in_new, color: Colors.grey[400], size: 20),
          ],
        ),
      ),
    );
  }
}

// حوار تحديث المعرف الفريد
class _UsernameUpdateDialog extends StatefulWidget {
  final String currentUsername;
  final String spaceId;
  final Function(String) onUsernameUpdated;

  const _UsernameUpdateDialog({
    required this.currentUsername,
    required this.spaceId,
    required this.onUsernameUpdated,
  });

  @override
  State<_UsernameUpdateDialog> createState() => _UsernameUpdateDialogState();
}

class _UsernameUpdateDialogState extends State<_UsernameUpdateDialog> {
  late TextEditingController _controller;
  bool _isLoading = false;
  bool _isAvailable = true;
  String? _errorMessage;
  final _spacesService = SpacesService();

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.currentUsername);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _checkAvailability() async {
    final username = _controller.text.trim();
    if (username.isEmpty || username == widget.currentUsername) {
      setState(() {
        _isAvailable = true;
        _errorMessage = null;
      });
      return;
    }

    if (!RegExp(r'^[a-zA-Z0-9_]{3,30}$').hasMatch(username)) {
      setState(() {
        _isAvailable = false;
        _errorMessage = 'المعرف يجب أن يحتوي على أحرف وأرقام فقط (3-30 حرف)';
      });
      return;
    }

    final isAvailable = await _spacesService.isUsernameAvailable(username);
    setState(() {
      _isAvailable = isAvailable;
      _errorMessage = isAvailable ? null : 'هذا المعرف مستخدم بالفعل';
    });
  }

  Future<void> _updateUsername() async {
    final username = _controller.text.trim();
    if (username.isEmpty) return;

    setState(() {
      _isLoading = true;
    });

    try {
      await _spacesService.updateSpaceUsername(widget.spaceId, username);
      widget.onUsernameUpdated(username);
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم تحديث المعرف الفريد بنجاح')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل في التحديث: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تحديث المعرف الفريد'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text('اختر معرفاً فريداً لمساحتك (مثل صفحات فيسبوك)'),
          const SizedBox(height: 16),
          TextField(
            controller: _controller,
            decoration: InputDecoration(
              labelText: 'المعرف الفريد',
              prefixText: '@',
              border: const OutlineInputBorder(),
              errorText: _errorMessage,
              suffixIcon: _isAvailable && _controller.text.trim().isNotEmpty
                  ? const Icon(Icons.check, color: Colors.green)
                  : null,
            ),
            onChanged: (_) => _checkAvailability(),
          ),
          const SizedBox(height: 8),
          Text(
            'يمكن أن يحتوي على أحرف وأرقام و _ فقط (3-30 حرف)',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading || !_isAvailable || _controller.text.trim().isEmpty
              ? null
              : _updateUsername,
          child: _isLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('حفظ'),
        ),
      ],
    );
  }
}
