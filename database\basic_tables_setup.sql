-- إعد<PERSON> الجداول الأساسية المطلوبة للتطبيق

-- تفعيل الامتدادات المطلوبة
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- إ<PERSON><PERSON><PERSON>ء جدول المستخدمين الأساسي (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE,
    full_name TEXT,
    avatar_url TEXT,
    bio TEXT,
    website TEXT,
    location TEXT,
    birth_date DATE,
    phone TEXT,
    email TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المساحات الأساسي (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS spaces (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT,
    owner_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    avatar_url TEXT,
    cover_url TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    followers_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المحادثات الأساسي (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS chats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user1_id UUID NOT NULL,
    user2_id UUID NOT NULL,
    type TEXT DEFAULT 'direct',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الرسائل الأساسي (إذا لم يكن موجوداً)
CREATE TABLE IF NOT EXISTS messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    chat_id UUID NOT NULL REFERENCES chats(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL,
    content TEXT,
    type TEXT DEFAULT 'text',
    file_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_read BOOLEAN DEFAULT FALSE
);

-- إنشاء الفهارس الأساسية
CREATE INDEX IF NOT EXISTS idx_profiles_username ON profiles(username);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);

CREATE INDEX IF NOT EXISTS idx_spaces_owner_id ON spaces(owner_id);
CREATE INDEX IF NOT EXISTS idx_spaces_category ON spaces(category);

CREATE INDEX IF NOT EXISTS idx_chats_user1_id ON chats(user1_id);
CREATE INDEX IF NOT EXISTS idx_chats_user2_id ON chats(user2_id);
CREATE INDEX IF NOT EXISTS idx_chats_updated_at ON chats(updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_messages_chat_id ON messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_messages_sender_id ON messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at DESC);

-- تفعيل Row Level Security للجداول الأساسية
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE spaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;

-- سياسات أمان أساسية للملفات الشخصية
DROP POLICY IF EXISTS "الجميع يمكنهم قراءة الملفات الشخصية" ON profiles;
DROP POLICY IF EXISTS "المستخدم يمكنه تحديث ملفه الشخصي" ON profiles;
DROP POLICY IF EXISTS "المستخدم يمكنه إنشاء ملفه الشخصي" ON profiles;

CREATE POLICY "الجميع يمكنهم قراءة الملفات الشخصية" ON profiles
    FOR SELECT USING (true);

CREATE POLICY "المستخدم يمكنه تحديث ملفه الشخصي" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "المستخدم يمكنه إنشاء ملفه الشخصي" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- سياسات أمان أساسية للمساحات
DROP POLICY IF EXISTS "الجميع يمكنهم قراءة المساحات" ON spaces;
DROP POLICY IF EXISTS "صاحب المساحة يمكنه تحديثها" ON spaces;
DROP POLICY IF EXISTS "المستخدم يمكنه إنشاء مساحة" ON spaces;

CREATE POLICY "الجميع يمكنهم قراءة المساحات" ON spaces
    FOR SELECT USING (true);

CREATE POLICY "صاحب المساحة يمكنه تحديثها" ON spaces
    FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "المستخدم يمكنه إنشاء مساحة" ON spaces
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

-- سياسات أمان أساسية للمحادثات
DROP POLICY IF EXISTS "المستخدم يمكنه قراءة محادثاته" ON chats;
DROP POLICY IF EXISTS "المستخدم يمكنه إنشاء محادثة" ON chats;

CREATE POLICY "المستخدم يمكنه قراءة محادثاته" ON chats
    FOR SELECT USING (auth.uid() = user1_id OR auth.uid() = user2_id);

CREATE POLICY "المستخدم يمكنه إنشاء محادثة" ON chats
    FOR INSERT WITH CHECK (auth.uid() = user1_id OR auth.uid() = user2_id);

-- سياسات أمان أساسية للرسائل
DROP POLICY IF EXISTS "المستخدم يمكنه قراءة رسائل محادثاته" ON messages;
DROP POLICY IF EXISTS "المستخدم يمكنه إرسال رسائل" ON messages;

CREATE POLICY "المستخدم يمكنه قراءة رسائل محادثاته" ON messages
    FOR SELECT USING (
        auth.uid() IN (
            SELECT user1_id FROM chats WHERE id = messages.chat_id
            UNION
            SELECT user2_id FROM chats WHERE id = messages.chat_id
        )
    );

CREATE POLICY "المستخدم يمكنه إرسال رسائل" ON messages
    FOR INSERT WITH CHECK (auth.uid() = sender_id);

-- دالة لإنشاء ملف شخصي تلقائياً عند التسجيل
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, username, full_name, email)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', 'user_' || substr(NEW.id::text, 1, 8)),
        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
        NEW.email
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تريجر لإنشاء ملف شخصي عند التسجيل
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- منح الصلاحيات الأساسية
GRANT ALL ON profiles TO authenticated;
GRANT ALL ON spaces TO authenticated;
GRANT ALL ON chats TO authenticated;
GRANT ALL ON messages TO authenticated;

GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON FUNCTION handle_new_user() TO authenticated;

-- رسالة نجاح
SELECT 'تم إنشاء الجداول الأساسية بنجاح!' as message;
