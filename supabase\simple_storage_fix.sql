-- =============================================================
--  حل مبسط لمشكلة رفع الصور - بدون صلاحيات إدارية
--  Simple Storage Fix - No Admin Rights Required
-- =============================================================

-- هذا السكريبت يعمل مع الصلاحيات العادية فقط

-- 1) إنشاء bucket للصور (إذا لم يكن موجود)
-- -------------------------------------------------------

INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'community-images',
  'community-images', 
  true,  -- عام للجميع
  52428800,  -- 50MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 52428800,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];

-- 2) إنشاء دالة مساعدة للتحقق من ملكية المجتمع
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION public.is_community_owner(community_id TEXT)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM communities 
    WHERE id = community_id 
    AND owner_id = auth.uid()::text
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3) التحقق من نجاح إنشاء bucket
-- -------------------------------------------------------

SELECT 
  'SUCCESS: Bucket created/updated' as status,
  id, 
  name, 
  public,
  file_size_limit,
  allowed_mime_types
FROM storage.buckets 
WHERE id = 'community-images';

-- =============================================================
--  تعليمات مهمة - اقرأ بعناية!
-- =============================================================

/*

المشكلة: لا تملك صلاحيات لتعديل سياسات storage.objects

الحل: استخدم واجهة Supabase بدلاً من SQL

خطوات الحل:

1. شغل السكريبت أعلاه أولاً (لإنشاء bucket)

2. اذهب إلى Storage في لوحة Supabase

3. تأكد من وجود bucket "community-images"

4. اضغط على bucket "community-images"

5. اذهب إلى تبويب "Policies"

6. اضغط "New Policy"

7. اختر "Custom policy"

8. املأ البيانات:
   - Policy name: community_images_policy
   - Allowed operation: All
   - Target roles: authenticated
   - Policy definition: true

9. اضغط Save

10. أعد بناء التطبيق: flutter build apk --release

*/

-- =============================================================
--  طريقة بديلة: تعطيل RLS مؤقتاً (غير آمن!)
-- =============================================================

/*

إذا كنت تريد حل سريع (غير آمن):

1. اذهب إلى Database > Tables
2. ابحث عن storage.objects
3. اضغط على الجدول
4. اذهب إلى Settings
5. عطل "Enable RLS"
6. احفظ التغييرات

تحذير: هذا يجعل جميع الملفات قابلة للوصول من الجميع!

*/

-- =============================================================
--  اختبار الحل
-- =============================================================

-- بعد تطبيق الحل، شغل هذا للتحقق:

SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images' AND public = true)
    THEN '✅ Bucket exists and is public'
    ELSE '❌ Bucket missing or not public'
  END as bucket_status;

-- =============================================================
--  انتهى السكريبت المبسط
-- =============================================================
