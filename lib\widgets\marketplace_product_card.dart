import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/marketplace_product.dart';

class MarketplaceProductCard extends StatelessWidget {
  final MarketplaceProduct product;
  final VoidCallback? onTap;
  final VoidCallback? onFavorite;

  const MarketplaceProductCard({
    super.key,
    required this.product,
    this.onTap,
    this.onFavorite,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // الصور
            _buildImageSection(),
            
            // المحتوى
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // العنوان والسعر
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              product.title,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              product.formattedPrice,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: product.isFree ? Colors.green : Colors.orange[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                      // زر المفضلة
                      IconButton(
                        onPressed: onFavorite,
                        icon: Icon(
                          product.isFavorited == true 
                              ? Icons.favorite 
                              : Icons.favorite_border,
                          color: product.isFavorited == true 
                              ? Colors.red 
                              : Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // الوصف
                  Text(
                    product.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // معلومات إضافية
                  Row(
                    children: [
                      // الفئة
                      if (product.categoryName != null) ...[
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            product.categoryName!,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.blue[700],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                      ],
                      
                      // الحالة
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: _getConditionColor().withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          product.conditionText,
                          style: TextStyle(
                            fontSize: 12,
                            color: _getConditionColor(),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                      
                      const Spacer(),
                      
                      // المدينة
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 14,
                            color: Colors.grey[500],
                          ),
                          const SizedBox(width: 2),
                          Text(
                            product.city,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // معلومات البائع
                  Row(
                    children: [
                      // صورة البائع
                      CircleAvatar(
                        radius: 16,
                        backgroundImage: product.userAvatar != null
                            ? NetworkImage(product.userAvatar!)
                            : null,
                        child: product.userAvatar == null
                            ? const Icon(Icons.person, size: 16)
                            : null,
                      ),
                      const SizedBox(width: 8),
                      
                      // اسم البائع
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              product.userName ?? 'مستخدم',
                              style: const TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            if (product.sellerRating != null)
                              Row(
                                children: [
                                  Icon(
                                    Icons.star,
                                    size: 12,
                                    color: Colors.amber[600],
                                  ),
                                  const SizedBox(width: 2),
                                  Text(
                                    product.sellerRating!.toStringAsFixed(1),
                                    style: TextStyle(
                                      fontSize: 11,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ),
                      
                      // إحصائيات
                      Row(
                        children: [
                          Icon(
                            Icons.visibility,
                            size: 14,
                            color: Colors.grey[500],
                          ),
                          const SizedBox(width: 2),
                          Text(
                            product.viewsCount.toString(),
                            style: TextStyle(
                              fontSize: 11,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // أزرار التواصل
                  _buildContactButtons(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageSection() {
    return Container(
      height: 200,
      width: double.infinity,
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
        child: product.images.isNotEmpty
            ? PageView.builder(
                itemCount: product.images.length,
                itemBuilder: (context, index) {
                  return Image.network(
                    product.images[index],
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Colors.grey[200],
                        child: const Icon(
                          Icons.image_not_supported,
                          size: 50,
                          color: Colors.grey,
                        ),
                      );
                    },
                  );
                },
              )
            : Container(
                color: Colors.grey[200],
                child: const Icon(
                  Icons.image,
                  size: 50,
                  color: Colors.grey,
                ),
              ),
      ),
    );
  }

  Widget _buildContactButtons() {
    return Row(
      children: [
        // زر الهاتف
        if (product.phoneEnabled && product.phoneNumber != null)
          Expanded(
            child: _buildContactButton(
              icon: Icons.phone,
              label: 'اتصال',
              color: Colors.green,
              onTap: () => _makePhoneCall(product.phoneNumber!),
            ),
          ),
        
        if (product.phoneEnabled && product.phoneNumber != null &&
            (product.whatsappEnabled || product.chatEnabled))
          const SizedBox(width: 8),
        
        // زر واتساب
        if (product.whatsappEnabled && product.whatsappNumber != null)
          Expanded(
            child: _buildContactButton(
              icon: Icons.chat,
              label: 'واتساب',
              color: Colors.green[700]!,
              onTap: () => _openWhatsApp(product.whatsappNumber!),
            ),
          ),
        
        if (product.whatsappEnabled && product.whatsappNumber != null &&
            product.chatEnabled)
          const SizedBox(width: 8),
        
        // زر الدردشة
        if (product.chatEnabled)
          Expanded(
            child: _buildContactButton(
              icon: Icons.message,
              label: 'دردشة',
              color: Colors.blue,
              onTap: () {
                // Open in-app chat
              },
            ),
          ),
      ],
    );
  }

  Widget _buildContactButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getConditionColor() {
    switch (product.condition) {
      case ProductCondition.newCondition:
        return Colors.green;
      case ProductCondition.used:
        return Colors.orange;
      case ProductCondition.refurbished:
        return Colors.blue;
    }
  }

  void _makePhoneCall(String phoneNumber) async {
    final uri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _openWhatsApp(String phoneNumber) async {
    final uri = Uri.parse('https://wa.me/$phoneNumber');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }
}
