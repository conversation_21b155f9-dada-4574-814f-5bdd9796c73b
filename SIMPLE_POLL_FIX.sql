-- إصلا<PERSON> مبسط لمشاكل التصويت في قسم النبض
-- Simple fix for poll voting issues

-- 1. إضافة فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_poll_votes_poll_id ON poll_votes(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_user_id ON poll_votes(user_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_option_id ON poll_votes(option_id);
CREATE INDEX IF NOT EXISTS idx_poll_options_poll_id ON poll_options(poll_id);

-- 2. إنشاء دالة لتحديث عدد الأصوات لخيار معين
CREATE OR REPLACE FUNCTION update_option_votes(p_option_id UUID)
RETURNS VOID AS $$
DECLARE
    votes_count INTEGER;
BEGIN
    -- حساب عدد الأصوات الفعلي من جدول poll_votes
    SELECT COUNT(*) INTO votes_count
    FROM poll_votes
    WHERE option_id = p_option_id;
    
    -- تحديث عدد الأصوات في جدول poll_options
    UPDATE poll_options
    SET votes = votes_count
    WHERE id = p_option_id;
END;
$$ LANGUAGE plpgsql;

-- 3. إنشاء trigger لتحديث الأصوات تلقائياً
CREATE OR REPLACE FUNCTION trigger_update_votes()
RETURNS TRIGGER AS $$
BEGIN
    -- عند إضافة تصويت جديد
    IF TG_OP = 'INSERT' THEN
        PERFORM update_option_votes(NEW.option_id);
        RETURN NEW;
    END IF;
    
    -- عند حذف تصويت
    IF TG_OP = 'DELETE' THEN
        PERFORM update_option_votes(OLD.option_id);
        RETURN OLD;
    END IF;
    
    -- عند تحديث تصويت (تغيير الخيار)
    IF TG_OP = 'UPDATE' THEN
        -- إذا تغير الخيار، حدث كلا الخيارين
        IF OLD.option_id != NEW.option_id THEN
            PERFORM update_option_votes(OLD.option_id);
        END IF;
        PERFORM update_option_votes(NEW.option_id);
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 4. إنشاء trigger
DROP TRIGGER IF EXISTS poll_votes_trigger ON poll_votes;
CREATE TRIGGER poll_votes_trigger
    AFTER INSERT OR UPDATE OR DELETE ON poll_votes
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_votes();

-- 5. تحديث الأصوات الموجودة
DO $$
DECLARE
    option_record RECORD;
BEGIN
    -- تحديث عدد الأصوات لجميع الخيارات
    FOR option_record IN 
        SELECT id FROM poll_options
    LOOP
        PERFORM update_option_votes(option_record.id);
    END LOOP;
END $$;

-- 6. التحقق من النتائج
SELECT 
    'Poll System Fixed' as status,
    COUNT(*) as total_polls,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_polls
FROM polls; 