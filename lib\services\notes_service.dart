import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/note.dart';

class NotesService {
  final SupabaseClient _client = Supabase.instance.client;

  // إنشاء مذكرة جديدة
  Future<Note> createNote({
    required String title,
    required String content,
    String? htmlContent,
    required NoteType type,
    required NoteCategory category,
    TaskStatus? taskStatus,
    List<String>? tags,
    bool isPinned = false,
    bool isPublic = false,
    DateTime? reminderDate,
    DateTime? dueDate,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final wordCount = _countWords(content);
      final characterCount = content.length;

      final noteData = {
        'user_id': userId,
        'title': title,
        'content': content,
        'html_content': htmlContent,
        'type': type.name,
        'category': category.name,
        'task_status': taskStatus?.name,
        'tags': tags ?? [],
        'is_pinned': isPinned,
        'is_public': isPublic,
        'is_archived': false,
        'reminder_date': reminderDate?.toIso8601String(),
        'due_date': dueDate?.toIso8601String(),
        'word_count': wordCount,
        'character_count': characterCount,
      };

      final response = await _client
          .from('notes')
          .insert(noteData)
          .select()
          .single();

      return Note.fromJson(response);
    } catch (e) {
      throw Exception('فشل في إنشاء المذكرة: $e');
    }
  }

  // الحصول على جميع مذكرات المستخدم
  Future<List<Note>> getUserNotes({
    NoteType? type,
    NoteCategory? category,
    bool? isPinned,
    bool? isArchived,
    String? searchQuery,
    List<String>? tags,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      var query = _client
          .from('notes')
          .select('*')
          .eq('user_id', userId);

      if (type != null) {
        query = query.eq('type', type.name);
      }

      if (category != null) {
        query = query.eq('category', category.name);
      }

      if (isPinned != null) {
        query = query.eq('is_pinned', isPinned);
      }

      if (isArchived != null) {
        query = query.eq('is_archived', isArchived);
      } else {
        // افتراضياً لا نعرض المؤرشفة
        query = query.eq('is_archived', false);
      }

      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or('title.ilike.%$searchQuery%,content.ilike.%$searchQuery%');
      }

      if (tags != null && tags.isNotEmpty) {
        query = query.overlaps('tags', tags);
      }

      final response = await query.order('is_pinned', ascending: false)
          .order('updated_at', ascending: false);

      return (response as List)
          .map((json) => Note.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب المذكرات: $e');
    }
  }

  // الحصول على مذكرة واحدة
  Future<Note?> getNote(String noteId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final response = await _client
          .from('notes')
          .select('*')
          .eq('id', noteId)
          .eq('user_id', userId)
          .maybeSingle();

      if (response == null) return null;
      return Note.fromJson(response);
    } catch (e) {
      throw Exception('فشل في جلب المذكرة: $e');
    }
  }

  // تحديث مذكرة
  Future<Note> updateNote(String noteId, {
    String? title,
    String? content,
    String? htmlContent,
    NoteType? type,
    NoteCategory? category,
    TaskStatus? taskStatus,
    List<String>? tags,
    bool? isPinned,
    bool? isPublic,
    bool? isArchived,
    DateTime? reminderDate,
    DateTime? dueDate,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final updateData = <String, dynamic>{
        'updated_at': DateTime.now().toIso8601String(),
      };

      if (title != null) updateData['title'] = title;
      if (content != null) {
        updateData['content'] = content;
        updateData['word_count'] = _countWords(content);
        updateData['character_count'] = content.length;
      }
      if (htmlContent != null) updateData['html_content'] = htmlContent;
      if (type != null) updateData['type'] = type.name;
      if (category != null) updateData['category'] = category.name;
      if (taskStatus != null) updateData['task_status'] = taskStatus.name;
      if (tags != null) updateData['tags'] = tags;
      if (isPinned != null) updateData['is_pinned'] = isPinned;
      if (isPublic != null) updateData['is_public'] = isPublic;
      if (isArchived != null) updateData['is_archived'] = isArchived;
      if (reminderDate != null) updateData['reminder_date'] = reminderDate.toIso8601String();
      if (dueDate != null) updateData['due_date'] = dueDate.toIso8601String();

      final response = await _client
          .from('notes')
          .update(updateData)
          .eq('id', noteId)
          .eq('user_id', userId)
          .select()
          .single();

      return Note.fromJson(response);
    } catch (e) {
      throw Exception('فشل في تحديث المذكرة: $e');
    }
  }

  // حذف مذكرة
  Future<void> deleteNote(String noteId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('notes')
          .delete()
          .eq('id', noteId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في حذف المذكرة: $e');
    }
  }

  // تبديل حالة التثبيت
  Future<Note> togglePin(String noteId) async {
    try {
      final note = await getNote(noteId);
      if (note == null) throw Exception('المذكرة غير موجودة');

      return await updateNote(noteId, isPinned: !note.isPinned);
    } catch (e) {
      throw Exception('فشل في تبديل التثبيت: $e');
    }
  }

  // تبديل حالة الأرشفة
  Future<Note> toggleArchive(String noteId) async {
    try {
      final note = await getNote(noteId);
      if (note == null) throw Exception('المذكرة غير موجودة');

      return await updateNote(noteId, isArchived: !note.isArchived);
    } catch (e) {
      throw Exception('فشل في تبديل الأرشفة: $e');
    }
  }

  // تبديل حالة المهمة
  Future<Note> toggleTaskStatus(String noteId, TaskStatus newStatus) async {
    try {
      return await updateNote(noteId, taskStatus: newStatus);
    } catch (e) {
      throw Exception('فشل في تحديث حالة المهمة: $e');
    }
  }

  // الحصول على المهام حسب التاريخ
  Future<List<Note>> getTasksByDate(DateTime date) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final response = await _client
          .from('notes')
          .select('*')
          .eq('user_id', userId)
          .eq('type', NoteType.task.name)
          .eq('is_archived', false)
          .gte('due_date', startOfDay.toIso8601String())
          .lt('due_date', endOfDay.toIso8601String())
          .order('due_date', ascending: true);

      return (response as List)
          .map((json) => Note.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب المهام: $e');
    }
  }

  // الحصول على التذكيرات القادمة
  Future<List<Note>> getUpcomingReminders() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final now = DateTime.now();
      final nextWeek = now.add(const Duration(days: 7));

      final response = await _client
          .from('notes')
          .select('*')
          .eq('user_id', userId)
          .eq('is_archived', false)
          .gte('reminder_date', now.toIso8601String())
          .lte('reminder_date', nextWeek.toIso8601String())
          .order('reminder_date', ascending: true);

      return (response as List)
          .map((json) => Note.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب التذكيرات: $e');
    }
  }

  // الحصول على إحصائيات المذكرات
  Future<Map<String, int>> getNotesStats() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final response = await _client
          .from('notes')
          .select('type, task_status, is_archived')
          .eq('user_id', userId);

      final notes = response as List;
      
      int totalNotes = notes.length;
      int totalTasks = notes.where((n) => n['type'] == NoteType.task.name).length;
      int completedTasks = notes.where((n) => 
          n['type'] == NoteType.task.name && 
          n['task_status'] == TaskStatus.completed.name).length;
      int archivedNotes = notes.where((n) => n['is_archived'] == true).length;
      int activeNotes = totalNotes - archivedNotes;

      return {
        'total_notes': totalNotes,
        'active_notes': activeNotes,
        'archived_notes': archivedNotes,
        'total_tasks': totalTasks,
        'completed_tasks': completedTasks,
        'pending_tasks': totalTasks - completedTasks,
      };
    } catch (e) {
      throw Exception('فشل في جلب الإحصائيات: $e');
    }
  }

  // البحث في المذكرات
  Future<List<Note>> searchNotes(String query) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final response = await _client
          .from('notes')
          .select('*')
          .eq('user_id', userId)
          .eq('is_archived', false)
          .or('title.ilike.%$query%,content.ilike.%$query%,tags.cs.{$query}')
          .order('updated_at', ascending: false);

      return (response as List)
          .map((json) => Note.fromJson(json))
          .toList();
    } catch (e) {
      throw Exception('فشل في البحث: $e');
    }
  }

  // الحصول على جميع الوسوم
  Future<List<String>> getAllTags() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final response = await _client
          .from('notes')
          .select('tags')
          .eq('user_id', userId)
          .eq('is_archived', false);

      final allTags = <String>{};
      for (final note in response as List) {
        final tags = List<String>.from(note['tags'] ?? []);
        allTags.addAll(tags);
      }

      return allTags.toList()..sort();
    } catch (e) {
      throw Exception('فشل في جلب الوسوم: $e');
    }
  }

  // حفظ تلقائي
  Future<void> autoSave(String noteId, String content, {String? htmlContent}) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return;

      await _client
          .from('notes')
          .update({
            'content': content,
            'html_content': htmlContent,
            'word_count': _countWords(content),
            'character_count': content.length,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', noteId)
          .eq('user_id', userId);
    } catch (e) {
      // تجاهل أخطاء الحفظ التلقائي
    }
  }

  // حساب عدد الكلمات
  int _countWords(String text) {
    if (text.trim().isEmpty) return 0;
    
    // إزالة HTML tags
    final plainText = text.replaceAll(RegExp(r'<[^>]*>'), '');
    
    // تقسيم النص إلى كلمات
    final words = plainText.trim().split(RegExp(r'\s+'));
    return words.where((word) => word.isNotEmpty).length;
  }

  // تصدير المذكرات
  Future<Map<String, dynamic>> exportNotes() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final notes = await getUserNotes(isArchived: null); // جميع المذكرات
      final stats = await getNotesStats();

      return {
        'export_date': DateTime.now().toIso8601String(),
        'user_id': userId,
        'notes_count': notes.length,
        'notes': notes.map((note) => note.toJson()).toList(),
        'statistics': stats,
      };
    } catch (e) {
      throw Exception('فشل في تصدير المذكرات: $e');
    }
  }

  // حذف جميع المذكرات
  Future<void> deleteAllNotes() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('notes')
          .delete()
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في حذف جميع المذكرات: $e');
    }
  }
}
