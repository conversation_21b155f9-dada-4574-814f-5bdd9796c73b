import 'package:flutter/material.dart';

enum NoteType {
  note,
  task,
  reminder,
  journal,
}

extension NoteTypeExtension on NoteType {
  String get arabicName {
    switch (this) {
      case NoteType.note:
        return 'ملاحظة';
      case NoteType.task:
        return 'مهمة';
      case NoteType.reminder:
        return 'تذكير';
      case NoteType.journal:
        return 'يومية';
    }
  }

  IconData get icon {
    switch (this) {
      case NoteType.note:
        return Icons.note;
      case NoteType.task:
        return Icons.task_alt;
      case NoteType.reminder:
        return Icons.alarm;
      case NoteType.journal:
        return Icons.book;
    }
  }

  Color get color {
    switch (this) {
      case NoteType.note:
        return Colors.blue;
      case NoteType.task:
        return Colors.green;
      case NoteType.reminder:
        return Colors.orange;
      case NoteType.journal:
        return Colors.purple;
    }
  }
}

enum NoteCategory {
  work,
  personal,
  goals,
  daily,
  study,
  health,
  finance,
  travel,
  other,
}

extension NoteCategoryExtension on NoteCategory {
  String get arabicName {
    switch (this) {
      case NoteCategory.work:
        return 'عمل';
      case NoteCategory.personal:
        return 'شخصية';
      case NoteCategory.goals:
        return 'أهداف';
      case NoteCategory.daily:
        return 'يوميات';
      case NoteCategory.study:
        return 'دراسة';
      case NoteCategory.health:
        return 'صحة';
      case NoteCategory.finance:
        return 'مالية';
      case NoteCategory.travel:
        return 'سفر';
      case NoteCategory.other:
        return 'أخرى';
    }
  }

  IconData get icon {
    switch (this) {
      case NoteCategory.work:
        return Icons.work;
      case NoteCategory.personal:
        return Icons.person;
      case NoteCategory.goals:
        return Icons.flag;
      case NoteCategory.daily:
        return Icons.today;
      case NoteCategory.study:
        return Icons.school;
      case NoteCategory.health:
        return Icons.health_and_safety;
      case NoteCategory.finance:
        return Icons.attach_money;
      case NoteCategory.travel:
        return Icons.flight;
      case NoteCategory.other:
        return Icons.more_horiz;
    }
  }

  Color get color {
    switch (this) {
      case NoteCategory.work:
        return Colors.indigo;
      case NoteCategory.personal:
        return Colors.teal;
      case NoteCategory.goals:
        return Colors.amber;
      case NoteCategory.daily:
        return Colors.cyan;
      case NoteCategory.study:
        return Colors.deepPurple;
      case NoteCategory.health:
        return Colors.red;
      case NoteCategory.finance:
        return Colors.green;
      case NoteCategory.travel:
        return Colors.blue;
      case NoteCategory.other:
        return Colors.grey;
    }
  }
}

enum TaskStatus {
  pending,
  inProgress,
  completed,
  cancelled,
}

extension TaskStatusExtension on TaskStatus {
  String get arabicName {
    switch (this) {
      case TaskStatus.pending:
        return 'في الانتظار';
      case TaskStatus.inProgress:
        return 'قيد التنفيذ';
      case TaskStatus.completed:
        return 'مكتملة';
      case TaskStatus.cancelled:
        return 'ملغية';
    }
  }

  IconData get icon {
    switch (this) {
      case TaskStatus.pending:
        return Icons.schedule;
      case TaskStatus.inProgress:
        return Icons.hourglass_empty;
      case TaskStatus.completed:
        return Icons.check_circle;
      case TaskStatus.cancelled:
        return Icons.cancel;
    }
  }

  Color get color {
    switch (this) {
      case TaskStatus.pending:
        return Colors.orange;
      case TaskStatus.inProgress:
        return Colors.blue;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.cancelled:
        return Colors.red;
    }
  }
}

class Note {
  final String id;
  final String userId;
  final String title;
  final String content;
  final String? htmlContent;
  final NoteType type;
  final NoteCategory category;
  final TaskStatus? taskStatus;
  final List<String> tags;
  final bool isPinned;
  final bool isPublic;
  final bool isArchived;
  final DateTime? reminderDate;
  final DateTime? dueDate;
  final int wordCount;
  final int characterCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  Note({
    required this.id,
    required this.userId,
    required this.title,
    required this.content,
    this.htmlContent,
    required this.type,
    required this.category,
    this.taskStatus,
    required this.tags,
    required this.isPinned,
    required this.isPublic,
    required this.isArchived,
    this.reminderDate,
    this.dueDate,
    required this.wordCount,
    required this.characterCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Note.fromJson(Map<String, dynamic> json) {
    return Note(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      title: json['title'] as String,
      content: json['content'] as String,
      htmlContent: json['html_content'] as String?,
      type: NoteType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => NoteType.note,
      ),
      category: NoteCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => NoteCategory.other,
      ),
      taskStatus: json['task_status'] != null
          ? TaskStatus.values.firstWhere(
              (e) => e.name == json['task_status'],
              orElse: () => TaskStatus.pending,
            )
          : null,
      tags: List<String>.from(json['tags'] ?? []),
      isPinned: json['is_pinned'] as bool? ?? false,
      isPublic: json['is_public'] as bool? ?? false,
      isArchived: json['is_archived'] as bool? ?? false,
      reminderDate: json['reminder_date'] != null
          ? DateTime.parse(json['reminder_date'])
          : null,
      dueDate: json['due_date'] != null
          ? DateTime.parse(json['due_date'])
          : null,
      wordCount: json['word_count'] as int? ?? 0,
      characterCount: json['character_count'] as int? ?? 0,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'title': title,
      'content': content,
      'html_content': htmlContent,
      'type': type.name,
      'category': category.name,
      'task_status': taskStatus?.name,
      'tags': tags,
      'is_pinned': isPinned,
      'is_public': isPublic,
      'is_archived': isArchived,
      'reminder_date': reminderDate?.toIso8601String(),
      'due_date': dueDate?.toIso8601String(),
      'word_count': wordCount,
      'character_count': characterCount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Note copyWith({
    String? id,
    String? userId,
    String? title,
    String? content,
    String? htmlContent,
    NoteType? type,
    NoteCategory? category,
    TaskStatus? taskStatus,
    List<String>? tags,
    bool? isPinned,
    bool? isPublic,
    bool? isArchived,
    DateTime? reminderDate,
    DateTime? dueDate,
    int? wordCount,
    int? characterCount,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Note(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      title: title ?? this.title,
      content: content ?? this.content,
      htmlContent: htmlContent ?? this.htmlContent,
      type: type ?? this.type,
      category: category ?? this.category,
      taskStatus: taskStatus ?? this.taskStatus,
      tags: tags ?? this.tags,
      isPinned: isPinned ?? this.isPinned,
      isPublic: isPublic ?? this.isPublic,
      isArchived: isArchived ?? this.isArchived,
      reminderDate: reminderDate ?? this.reminderDate,
      dueDate: dueDate ?? this.dueDate,
      wordCount: wordCount ?? this.wordCount,
      characterCount: characterCount ?? this.characterCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get formattedCreatedDate {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays == 0) {
      return 'اليوم ${createdAt.hour.toString().padLeft(2, '0')}:${createdAt.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'أمس ${createdAt.hour.toString().padLeft(2, '0')}:${createdAt.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} أيام';
    } else {
      return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
    }
  }

  String get formattedUpdatedDate {
    final now = DateTime.now();
    final difference = now.difference(updatedAt);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else {
      return '${difference.inDays} أيام';
    }
  }

  String get preview {
    final plainText = content.replaceAll(RegExp(r'<[^>]*>'), '');
    return plainText.length > 100 
        ? '${plainText.substring(0, 100)}...'
        : plainText;
  }

  bool get isTask => type == NoteType.task;
  bool get isReminder => type == NoteType.reminder;
  bool get hasReminder => reminderDate != null;
  bool get hasDueDate => dueDate != null;
  bool get isOverdue => hasDueDate && dueDate!.isBefore(DateTime.now());
  bool get isCompleted => taskStatus == TaskStatus.completed;
}
