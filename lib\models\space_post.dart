import '../models/reaction_type.dart';

class SpacePost {
  final String id;
  final String spaceId;
  final String spaceName;
  final String authorId;
  final String authorName;
  final String content;
  final List<String> mediaUrls;
  final String? linkUrl;
  final String? linkTitle;
  final String? linkDescription;
  final String? linkImage;
  
  // الإحصائيات
  final int likesCount;
  final int commentsCount;
  final int sharesCount;
  final int viewsCount;
  
  // التفاعل الحالي للمستخدم
  final ReactionType? currentUserReaction;
  final Map<ReactionType, int> reactionCounts;
  
  // التواريخ
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // حالات إضافية
  final bool isAuthor;
  final bool canEdit;
  final bool canDelete;
  final bool isAuthorVerified; // حالة توثيق المؤلف

  const SpacePost({
    required this.id,
    required this.spaceId,
    required this.spaceName,
    required this.authorId,
    required this.authorName,
    required this.content,
    this.mediaUrls = const [],
    this.linkUrl,
    this.linkTitle,
    this.linkDescription,
    this.linkImage,
    this.likesCount = 0,
    this.commentsCount = 0,
    this.sharesCount = 0,
    this.viewsCount = 0,
    this.currentUserReaction,
    this.reactionCounts = const {},
    required this.createdAt,
    required this.updatedAt,
    this.isAuthor = false,
    this.canEdit = false,
    this.canDelete = false,
    this.isAuthorVerified = false,
  });

  factory SpacePost.fromJson(Map<String, dynamic> json) {
    // تحويل reaction_counts من JSON إلى Map<ReactionType, int>
    final reactionCountsJson = json['reaction_counts'] as Map<String, dynamic>? ?? {};
    final reactionCounts = <ReactionType, int>{};
    
    for (final entry in reactionCountsJson.entries) {
      final reactionType = ReactionType.values.firstWhere(
        (type) => type.name == entry.key,
        orElse: () => ReactionType.like,
      );
      reactionCounts[reactionType] = entry.value as int? ?? 0;
    }

    // تحويل current_user_reaction
    ReactionType? currentUserReaction;
    if (json['current_user_reaction'] != null) {
      currentUserReaction = ReactionType.values.firstWhere(
        (type) => type.name == json['current_user_reaction'],
        orElse: () => ReactionType.like,
      );
    }

    return SpacePost(
      id: json['id'] ?? '',
      spaceId: json['space_id'] ?? '',
      spaceName: json['space_name'] ?? '',
      authorId: json['author_id'] ?? '',
      authorName: json['author_name'] ?? '',
      content: json['content'] ?? '',
      mediaUrls: List<String>.from(json['media_urls'] ?? []),
      linkUrl: json['link_url'],
      linkTitle: json['link_title'],
      linkDescription: json['link_description'],
      linkImage: json['link_image'],
      likesCount: json['likes_count'] ?? 0,
      commentsCount: json['comments_count'] ?? 0,
      sharesCount: json['shares_count'] ?? 0,
      viewsCount: json['views_count'] ?? 0,
      currentUserReaction: currentUserReaction,
      reactionCounts: reactionCounts,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      isAuthor: json['is_author'] ?? false,
      canEdit: json['can_edit'] ?? false,
      canDelete: json['can_delete'] ?? false,
      isAuthorVerified: json['is_author_verified'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    // تحويل reactionCounts إلى JSON
    final reactionCountsJson = <String, int>{};
    for (final entry in reactionCounts.entries) {
      reactionCountsJson[entry.key.name] = entry.value;
    }

    return {
      'id': id,
      'space_id': spaceId,
      'space_name': spaceName,
      'author_id': authorId,
      'author_name': authorName,
      'content': content,
      'media_urls': mediaUrls,
      'link_url': linkUrl,
      'link_title': linkTitle,
      'link_description': linkDescription,
      'link_image': linkImage,
      'likes_count': likesCount,
      'comments_count': commentsCount,
      'shares_count': sharesCount,
      'views_count': viewsCount,
      'current_user_reaction': currentUserReaction?.name,
      'reaction_counts': reactionCountsJson,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_author_verified': isAuthorVerified,
    };
  }

  SpacePost copyWith({
    String? id,
    String? spaceId,
    String? spaceName,
    String? authorId,
    String? authorName,
    String? content,
    List<String>? mediaUrls,
    String? linkUrl,
    String? linkTitle,
    String? linkDescription,
    String? linkImage,
    int? likesCount,
    int? commentsCount,
    int? sharesCount,
    int? viewsCount,
    ReactionType? currentUserReaction,
    Map<ReactionType, int>? reactionCounts,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isAuthor,
    bool? canEdit,
    bool? canDelete,
    bool? isAuthorVerified,
  }) {
    return SpacePost(
      id: id ?? this.id,
      spaceId: spaceId ?? this.spaceId,
      spaceName: spaceName ?? this.spaceName,
      authorId: authorId ?? this.authorId,
      authorName: authorName ?? this.authorName,
      content: content ?? this.content,
      mediaUrls: mediaUrls ?? this.mediaUrls,
      linkUrl: linkUrl ?? this.linkUrl,
      linkTitle: linkTitle ?? this.linkTitle,
      linkDescription: linkDescription ?? this.linkDescription,
      linkImage: linkImage ?? this.linkImage,
      likesCount: likesCount ?? this.likesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      sharesCount: sharesCount ?? this.sharesCount,
      viewsCount: viewsCount ?? this.viewsCount,
      currentUserReaction: currentUserReaction ?? this.currentUserReaction,
      reactionCounts: reactionCounts ?? this.reactionCounts,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isAuthor: isAuthor ?? this.isAuthor,
      canEdit: canEdit ?? this.canEdit,
      canDelete: canDelete ?? this.canDelete,
      isAuthorVerified: isAuthorVerified ?? this.isAuthorVerified,
    );
  }

  // دالة مساعدة للحصول على عدد تفاعل معين
  int getReactionCount(ReactionType type) {
    return reactionCounts[type] ?? 0;
  }

  // دالة مساعدة للتحقق من وجود تفاعل
  bool hasReaction(ReactionType type) {
    return currentUserReaction == type;
  }

  // دالة مساعدة للحصول على إجمالي التفاعلات
  int get totalReactions {
    return reactionCounts.values.fold(0, (sum, count) => sum + count);
  }

  // دالة مساعدة للتحقق من وجود وسائط
  bool get hasMedia {
    return mediaUrls.isNotEmpty;
  }

  // دالة مساعدة للتحقق من وجود رابط
  bool get hasLink {
    return linkUrl != null && linkUrl!.isNotEmpty;
  }

  // دالة مساعدة لتنسيق الوقت
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
