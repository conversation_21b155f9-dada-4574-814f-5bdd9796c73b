-- إنشاء جداول نظام المساحات

-- 1. جدول المساحات الرئيسي
CREATE TABLE IF NOT EXISTS public.spaces (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    owner_name TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    goal TEXT,
    category TEXT NOT NULL DEFAULT 'other',
    profession TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    privacy TEXT NOT NULL DEFAULT 'public',
    
    -- الصور
    cover_image TEXT,
    profile_image TEXT,
    
    -- معلومات التواصل
    phone_number TEXT,
    email TEXT,
    website TEXT,
    social_links JSONB DEFAULT '{}',
    
    -- الإحصائيات
    followers_count INTEGER DEFAULT 0,
    posts_count INTEGER DEFAULT 0,
    views_count INTEGER DEFAULT 0,
    
    -- التواريخ
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. جدول متابعي المساحات
CREATE TABLE IF NOT EXISTS public.space_followers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    space_id UUID NOT NULL REFERENCES public.spaces(id) ON DELETE CASCADE,
    follower_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    followed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- منع المتابعة المكررة
    UNIQUE(space_id, follower_id)
);

-- 3. جدول منشورات المساحات
CREATE TABLE IF NOT EXISTS public.space_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    space_id UUID NOT NULL REFERENCES public.spaces(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    media_urls TEXT[],
    link_url TEXT,
    link_title TEXT,
    link_description TEXT,
    link_image TEXT,
    
    -- الإحصائيات
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,
    views_count INTEGER DEFAULT 0,
    
    -- التواريخ
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. جدول تفاعلات منشورات المساحات
CREATE TABLE IF NOT EXISTS public.space_post_reactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES public.space_posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    reaction_type TEXT NOT NULL DEFAULT 'like',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- منع التفاعل المكرر
    UNIQUE(post_id, user_id)
);

-- 5. جدول تعليقات منشورات المساحات
CREATE TABLE IF NOT EXISTS public.space_post_comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES public.space_posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES public.space_post_comments(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    
    -- الإحصائيات
    likes_count INTEGER DEFAULT 0,
    replies_count INTEGER DEFAULT 0,
    
    -- التواريخ
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_spaces_owner_id ON public.spaces(owner_id);
CREATE INDEX IF NOT EXISTS idx_spaces_category ON public.spaces(category);
CREATE INDEX IF NOT EXISTS idx_spaces_status ON public.spaces(status);
CREATE INDEX IF NOT EXISTS idx_spaces_privacy ON public.spaces(privacy);
CREATE INDEX IF NOT EXISTS idx_spaces_created_at ON public.spaces(created_at);

CREATE INDEX IF NOT EXISTS idx_space_followers_space_id ON public.space_followers(space_id);
CREATE INDEX IF NOT EXISTS idx_space_followers_follower_id ON public.space_followers(follower_id);
CREATE INDEX IF NOT EXISTS idx_space_followers_followed_at ON public.space_followers(followed_at);

CREATE INDEX IF NOT EXISTS idx_space_posts_space_id ON public.space_posts(space_id);
CREATE INDEX IF NOT EXISTS idx_space_posts_author_id ON public.space_posts(author_id);
CREATE INDEX IF NOT EXISTS idx_space_posts_created_at ON public.space_posts(created_at);

CREATE INDEX IF NOT EXISTS idx_space_post_reactions_post_id ON public.space_post_reactions(post_id);
CREATE INDEX IF NOT EXISTS idx_space_post_reactions_user_id ON public.space_post_reactions(user_id);

CREATE INDEX IF NOT EXISTS idx_space_post_comments_post_id ON public.space_post_comments(post_id);
CREATE INDEX IF NOT EXISTS idx_space_post_comments_user_id ON public.space_post_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_space_post_comments_parent_id ON public.space_post_comments(parent_id);

-- إنشاء الدوال المساعدة

-- دالة زيادة عدد المتابعين
CREATE OR REPLACE FUNCTION increment_space_followers(space_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.spaces 
    SET followers_count = followers_count + 1,
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql;

-- دالة تقليل عدد المتابعين
CREATE OR REPLACE FUNCTION decrement_space_followers(space_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.spaces 
    SET followers_count = GREATEST(followers_count - 1, 0),
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql;

-- دالة زيادة عدد المشاهدات
CREATE OR REPLACE FUNCTION increment_space_views(space_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.spaces 
    SET views_count = views_count + 1,
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql;

-- دالة زيادة عدد المنشورات
CREATE OR REPLACE FUNCTION increment_space_posts(space_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.spaces 
    SET posts_count = posts_count + 1,
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql;

-- دالة تقليل عدد المنشورات
CREATE OR REPLACE FUNCTION decrement_space_posts(space_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.spaces 
    SET posts_count = GREATEST(posts_count - 1, 0),
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql;

-- تحديث تلقائي لـ updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إضافة المحفزات
CREATE TRIGGER update_spaces_updated_at 
    BEFORE UPDATE ON public.spaces 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_space_posts_updated_at 
    BEFORE UPDATE ON public.space_posts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_space_post_comments_updated_at 
    BEFORE UPDATE ON public.space_post_comments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إعداد Row Level Security (RLS)

-- تفعيل RLS على الجداول
ALTER TABLE public.spaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.space_followers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.space_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.space_post_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.space_post_comments ENABLE ROW LEVEL SECURITY;

-- حذف السياسات الموجودة إذا كانت موجودة لتجنب التكرار
DROP POLICY IF EXISTS "المساحات العامة قابلة للقراءة من الجميع" ON public.spaces;
DROP POLICY IF EXISTS "المالكون يمكنهم إدارة مساحاتهم" ON public.spaces;
DROP POLICY IF EXISTS "المستخدمون المسجلون يمكنهم إنشاء مساحات" ON public.spaces;
DROP POLICY IF EXISTS "Anyone can view public spaces" ON public.spaces;
DROP POLICY IF EXISTS "Authenticated users can create spaces" ON public.spaces;
DROP POLICY IF EXISTS "Space owners can update their spaces" ON public.spaces;
DROP POLICY IF EXISTS "Space owners can delete their spaces" ON public.spaces;

-- سياسات الأمان للمساحات
CREATE POLICY "spaces_select_policy" ON public.spaces
    FOR SELECT USING (
        privacy = 'public' OR
        auth.uid() = owner_id OR
        EXISTS (
            SELECT 1 FROM public.space_followers
            WHERE space_id = id AND follower_id = auth.uid()
        )
    );

CREATE POLICY "spaces_insert_policy" ON public.spaces
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "spaces_update_policy" ON public.spaces
    FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "spaces_delete_policy" ON public.spaces
    FOR DELETE USING (auth.uid() = owner_id);

-- سياسات الأمان لمتابعي المساحات
CREATE POLICY "الجميع يمكنهم رؤية المتابعين" ON public.space_followers
    FOR SELECT USING (true);

CREATE POLICY "المستخدمون يمكنهم متابعة المساحات" ON public.space_followers
    FOR INSERT WITH CHECK (auth.uid() = follower_id);

CREATE POLICY "المستخدمون يمكنهم إلغاء متابعة المساحات" ON public.space_followers
    FOR DELETE USING (auth.uid() = follower_id);

-- سياسات الأمان لمنشورات المساحات
CREATE POLICY "منشورات المساحات العامة قابلة للقراءة" ON public.space_posts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.spaces 
            WHERE id = space_id 
            AND (privacy = 'public' OR auth.uid() = owner_id)
        )
    );

CREATE POLICY "مالكو المساحات يمكنهم النشر" ON public.space_posts
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.spaces 
            WHERE id = space_id 
            AND auth.uid() = owner_id
        )
    );

CREATE POLICY "مالكو المساحات يمكنهم إدارة المنشورات" ON public.space_posts
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.spaces 
            WHERE id = space_id 
            AND auth.uid() = owner_id
        ) OR auth.uid() = author_id
    );

-- إدراج بيانات تجريبية
INSERT INTO public.spaces (
    owner_id,
    owner_name,
    name,
    description,
    goal,
    category,
    profession,
    privacy,
    followers_count,
    posts_count,
    views_count
) VALUES 
(
    '62fb0b2e-8cdd-4226-878f-3eec5131952c',
    'المستخدم الموثق',
    'شركة التقنية المتقدمة',
    'نحن شركة رائدة في مجال تطوير التطبيقات والحلول التقنية المبتكرة. نسعى لتقديم أفضل الخدمات لعملائنا.',
    'تطوير حلول تقنية مبتكرة تساعد الشركات على النمو والتطور',
    'technology',
    'مطور تطبيقات',
    'public',
    150,
    25,
    1500
),
(
    '5c3675a3-ab2b-4248-bdbf-8bf0dc1de485',
    'مستخدم آخر',
    'مطعم الأصالة',
    'مطعم يقدم أشهى الأطباق العربية الأصيلة بطعم لا يُنسى. نستخدم أجود المكونات الطبيعية.',
    'تقديم تجربة طعام استثنائية تعكس التراث العربي الأصيل',
    'food',
    'طاهي محترف',
    'public',
    89,
    12,
    890
),
(
    '62fb0b2e-8cdd-4226-878f-3eec5131952c',
    'المستخدم الموثق',
    'أكاديمية التعلم الذكي',
    'منصة تعليمية متخصصة في تقديم دورات تدريبية عالية الجودة في مختلف المجالات التقنية والمهنية.',
    'نشر المعرفة وتطوير المهارات للجيل القادم',
    'education',
    'مدرب معتمد',
    'public',
    234,
    45,
    3200
);

-- إضافة عمود space_id إلى جدول posts الموجود
ALTER TABLE public.posts ADD COLUMN IF NOT EXISTS space_id UUID REFERENCES public.spaces(id) ON DELETE CASCADE;

-- إنشاء فهرس للعمود الجديد
CREATE INDEX IF NOT EXISTS idx_posts_space_id ON public.posts(space_id);

-- إضافة المعرف الفريد للمساحات (الصور موجودة بالفعل)
ALTER TABLE public.spaces ADD COLUMN IF NOT EXISTS username TEXT UNIQUE;

-- تحديث أسماء الأعمدة إذا كانت مختلفة (للتوافق مع النموذج)
-- الجدول يستخدم cover_image و profile_image بالفعل

-- إنشاء فهرس للمعرف الفريد
CREATE INDEX IF NOT EXISTS idx_spaces_username ON public.spaces(username);

-- إضافة قيد للتأكد من أن المعرف الفريد يحتوي على أحرف وأرقام فقط
ALTER TABLE public.spaces ADD CONSTRAINT spaces_username_format
CHECK (username IS NULL OR username ~ '^[a-zA-Z0-9_]{3,30}$');

-- السياسات تم إنشاؤها بالفعل في الأعلى

-- إضافة بعض المتابعين التجريبيين
INSERT INTO public.space_followers (space_id, follower_id)
SELECT s.id, '5c3675a3-ab2b-4248-bdbf-8bf0dc1de485'
FROM public.spaces s
WHERE s.owner_id = '62fb0b2e-8cdd-4226-878f-3eec5131952c'
ON CONFLICT (space_id, follower_id) DO NOTHING;

INSERT INTO public.space_followers (space_id, follower_id)
SELECT s.id, '62fb0b2e-8cdd-4226-878f-3eec5131952c'
FROM public.spaces s
WHERE s.owner_id = '5c3675a3-ab2b-4248-bdbf-8bf0dc1de485'
ON CONFLICT (space_id, follower_id) DO NOTHING;
