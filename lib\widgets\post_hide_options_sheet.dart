import 'package:flutter/material.dart';
import '../models/post.dart';

enum HideReason {
  notInterested('لست مهتماً بهذا المحتوى'),
  spam('محتوى مزعج أو غير مرغوب فيه'),
  inappropriate('محتوى غير مناسب'),
  misleading('معلومات مضللة'),
  repetitive('محتوى متكرر'),
  hideUser('إخفاء كل منشورات {userName}'),
  hideUserTemporary('إخفاء منشورات {userName} لمدة 30 يوماً');

  const HideReason(this.arabicName);
  final String arabicName;

  // دالة للحصول على النص مع اسم المستخدم
  String getDisplayName(String userName) {
    return arabicName.replaceAll('{userName}', userName);
  }
}

class PostHideOptionsSheet extends StatefulWidget {
  final Post post;
  final Function(HideReason reason) onHide;
  final VoidCallback? onReport;

  const PostHideOptionsSheet({
    super.key,
    required this.post,
    required this.onHide,
    this.onReport,
  });

  @override
  State<PostHideOptionsSheet> createState() => _PostHideOptionsSheetState();
}

class _PostHideOptionsSheetState extends State<PostHideOptionsSheet> {
  HideReason? _selectedReason;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.visibility_off_rounded,
                  color: Colors.grey[700],
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'إخفاء هذا المنشور',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Colors.grey[800],
                        ),
                      ),
                      Text(
                        'لماذا تريد إخفاء هذا المنشور؟',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),
          
          const Divider(height: 1),
          
          // Options
          Flexible(
            child: ListView(
              shrinkWrap: true,
              children: [
                ...HideReason.values.map((reason) => _buildReasonOption(reason)),
                
                const Divider(),
                
                // Report option
                ListTile(
                  leading: Icon(
                    Icons.flag_rounded,
                    color: Colors.red[600],
                  ),
                  title: Text(
                    'الإبلاغ عن المنشور',
                    style: TextStyle(
                      color: Colors.red[600],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  subtitle: const Text('إبلاغ عن محتوى مخالف'),
                  onTap: () {
                    Navigator.pop(context);
                    widget.onReport?.call();
                  },
                ),
              ],
            ),
          ),
          
          // Action buttons
          if (_selectedReason != null) ...[
            const Divider(),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('إلغاء'),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    flex: 2,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        widget.onHide(_selectedReason!);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red[600],
                        foregroundColor: Colors.white,
                      ),
                      child: const Text('إخفاء المنشور'),
                    ),
                  ),
                ],
              ),
            ),
          ],
          
          // Safe area
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  Widget _buildReasonOption(HideReason reason) {
    final isSelected = _selectedReason == reason;
    
    return ListTile(
      leading: Radio<HideReason>(
        value: reason,
        groupValue: _selectedReason,
        onChanged: (value) {
          setState(() => _selectedReason = value);
        },
        activeColor: Colors.red[600],
      ),
      title: Text(
        reason.getDisplayName(widget.post.userName),
        style: TextStyle(
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          color: isSelected ? Colors.red[600] : Colors.grey[800],
        ),
      ),
      onTap: () {
        setState(() => _selectedReason = reason);
      },
    );
  }
}

// Helper function to show the sheet
void showPostHideOptions({
  required BuildContext context,
  required Post post,
  required Function(HideReason reason) onHide,
  VoidCallback? onReport,
}) {
  showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => PostHideOptionsSheet(
      post: post,
      onHide: onHide,
      onReport: onReport,
    ),
  );
}
