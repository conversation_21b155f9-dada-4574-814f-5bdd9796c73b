-- ===================================
-- الإعداد الشامل لقسم البحث عن عمل
-- تطبيق أرزاوو - نسخة الإنتاج
-- ===================================

-- تنفيذ هذا الملف في Supabase SQL Editor لإعداد القسم كاملاً

BEGIN;

-- ===================================
-- 1. إنشاء الجداول الأساسية
-- ===================================

-- جدول الباحثين عن عمل
CREATE TABLE IF NOT EXISTS job_seekers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL CHECK (LENGTH(TRIM(full_name)) >= 3),
    profile_image TEXT,
    age INTEGER NOT NULL CHECK (age >= 16 AND age <= 70),
    gender TEXT NOT NULL CHECK (gender IN ('ذكر', 'أنثى')),
    marital_status TEXT NOT NULL CHECK (marital_status IN ('single', 'married', 'divorced', 'widowed')),
    current_country TEXT NOT NULL,
    current_city TEXT NOT NULL,
    nationality TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('construction', 'teaching', 'driving', 'barbering', 'programming', 'delivery', 'design', 'carpentry', 'blacksmithing', 'tailoring', 'painting', 'plastering', 'electrical', 'mechanics', 'cleaning', 'cooking', 'healthcare', 'sales', 'accounting', 'security', 'other')),
    skills TEXT[] DEFAULT '{}' CHECK (array_length(skills, 1) > 0),
    languages TEXT[] DEFAULT '{}' CHECK (array_length(languages, 1) > 0),
    experience_years INTEGER DEFAULT 0 CHECK (experience_years >= 0 AND experience_years <= 50),
    description TEXT NOT NULL CHECK (LENGTH(TRIM(description)) >= 10 AND LENGTH(TRIM(description)) <= 1000),
    preferred_job_type TEXT NOT NULL CHECK (preferred_job_type IN ('fullTime', 'partTime', 'remote', 'freelance', 'contract')),
    preferred_location TEXT NOT NULL,
    phone_number TEXT NOT NULL CHECK (phone_number ~ '^05[0-9]{8}$'),
    email TEXT CHECK (email IS NULL OR email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    social_links TEXT,
    cv_url TEXT,
    portfolio_images TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    views_count INTEGER DEFAULT 0 CHECK (views_count >= 0),
    likes_count INTEGER DEFAULT 0 CHECK (likes_count >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- جدول إعجابات الباحثين عن عمل
CREATE TABLE IF NOT EXISTS job_seeker_likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    seeker_id UUID REFERENCES job_seekers(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(seeker_id, user_id)
);

-- جدول حفظ الباحثين عن عمل
CREATE TABLE IF NOT EXISTS job_seeker_saves (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    seeker_id UUID REFERENCES job_seekers(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(seeker_id, user_id)
);

-- جدول تتبع معدل الاستخدام
CREATE TABLE IF NOT EXISTS rate_limits (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    action_type TEXT NOT NULL,
    action_count INTEGER DEFAULT 1,
    window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, action_type)
);

-- جدول الأنشطة المشبوهة
CREATE TABLE IF NOT EXISTS suspicious_activities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    activity_type TEXT NOT NULL,
    description TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول إعدادات المستخدم لقسم البحث عن عمل
CREATE TABLE IF NOT EXISTS job_seeker_user_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    profile_notifications_enabled BOOLEAN DEFAULT TRUE,
    show_phone_number BOOLEAN DEFAULT TRUE,
    show_email BOOLEAN DEFAULT TRUE,
    allow_direct_contact BOOLEAN DEFAULT TRUE,
    show_online_status BOOLEAN DEFAULT TRUE,
    profile_visible_in_search BOOLEAN DEFAULT TRUE,
    email_notifications BOOLEAN DEFAULT TRUE,
    sms_notifications BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================
-- 2. إنشاء الفهارس لتحسين الأداء
-- ===================================

-- فهارس أساسية
CREATE INDEX IF NOT EXISTS idx_job_seekers_user_id ON job_seekers(user_id);
CREATE INDEX IF NOT EXISTS idx_job_seekers_category ON job_seekers(category);
CREATE INDEX IF NOT EXISTS idx_job_seekers_city ON job_seekers(current_city);
CREATE INDEX IF NOT EXISTS idx_job_seekers_country ON job_seekers(current_country);
CREATE INDEX IF NOT EXISTS idx_job_seekers_job_type ON job_seekers(preferred_job_type);
CREATE INDEX IF NOT EXISTS idx_job_seekers_active ON job_seekers(is_active);
CREATE INDEX IF NOT EXISTS idx_job_seekers_created_at ON job_seekers(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_job_seekers_updated_at ON job_seekers(updated_at DESC);

-- فهارس مركبة للبحث المتقدم
CREATE INDEX IF NOT EXISTS idx_job_seekers_active_category ON job_seekers(is_active, category);
CREATE INDEX IF NOT EXISTS idx_job_seekers_active_city ON job_seekers(is_active, current_city);
CREATE INDEX IF NOT EXISTS idx_job_seekers_city_category ON job_seekers(current_city, category);
CREATE INDEX IF NOT EXISTS idx_job_seekers_country_job_type ON job_seekers(current_country, preferred_job_type);

-- فهارس للتفاعل
CREATE INDEX IF NOT EXISTS idx_job_seeker_likes_seeker_id ON job_seeker_likes(seeker_id);
CREATE INDEX IF NOT EXISTS idx_job_seeker_likes_user_id ON job_seeker_likes(user_id);
CREATE INDEX IF NOT EXISTS idx_job_seeker_saves_seeker_id ON job_seeker_saves(seeker_id);
CREATE INDEX IF NOT EXISTS idx_job_seeker_saves_user_id ON job_seeker_saves(user_id);

-- فهارس البحث النصي
CREATE INDEX IF NOT EXISTS idx_job_seekers_search_text 
ON job_seekers USING gin(to_tsvector('arabic', full_name || ' ' || description));

CREATE INDEX IF NOT EXISTS idx_job_seekers_skills_gin 
ON job_seekers USING gin(skills);

CREATE INDEX IF NOT EXISTS idx_job_seekers_languages_gin 
ON job_seekers USING gin(languages);

-- فهارس الأمان
CREATE INDEX IF NOT EXISTS idx_rate_limits_user_action ON rate_limits(user_id, action_type, window_start);
CREATE INDEX IF NOT EXISTS idx_suspicious_activities_user ON suspicious_activities(user_id, created_at);

-- فهارس الإعدادات
CREATE INDEX IF NOT EXISTS idx_job_seeker_user_settings_user_id ON job_seeker_user_settings(user_id);

-- ===================================
-- 3. تفعيل Row Level Security
-- ===================================

ALTER TABLE job_seekers ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_seeker_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_seeker_saves ENABLE ROW LEVEL SECURITY;
ALTER TABLE rate_limits ENABLE ROW LEVEL SECURITY;
ALTER TABLE suspicious_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_seeker_user_settings ENABLE ROW LEVEL SECURITY;

-- ===================================
-- 4. إنشاء سياسات الأمان
-- ===================================

-- سياسات job_seekers
DROP POLICY IF EXISTS "Anyone can view active job seekers" ON job_seekers;
CREATE POLICY "Anyone can view active job seekers" ON job_seekers
    FOR SELECT USING (is_active = true);

DROP POLICY IF EXISTS "Users can insert one profile only" ON job_seekers;
CREATE POLICY "Users can insert one profile only" ON job_seekers
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        NOT EXISTS (
            SELECT 1 FROM job_seekers 
            WHERE user_id = auth.uid() AND is_active = true
        )
    );

DROP POLICY IF EXISTS "Users can update own profile" ON job_seekers;
CREATE POLICY "Users can update own profile" ON job_seekers
    FOR UPDATE USING (
        auth.uid() = user_id AND
        user_id = OLD.user_id AND
        created_at = OLD.created_at
    );

DROP POLICY IF EXISTS "Users can delete own profile" ON job_seekers;
CREATE POLICY "Users can delete own profile" ON job_seekers
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات job_seeker_likes
DROP POLICY IF EXISTS "Authenticated users can like" ON job_seeker_likes;
CREATE POLICY "Authenticated users can like" ON job_seeker_likes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can remove own likes" ON job_seeker_likes;
CREATE POLICY "Users can remove own likes" ON job_seeker_likes
    FOR DELETE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can view likes" ON job_seeker_likes;
CREATE POLICY "Users can view likes" ON job_seeker_likes
    FOR SELECT USING (true);

-- سياسات job_seeker_saves
DROP POLICY IF EXISTS "Authenticated users can save" ON job_seeker_saves;
CREATE POLICY "Authenticated users can save" ON job_seeker_saves
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can remove own saves" ON job_seeker_saves;
CREATE POLICY "Users can remove own saves" ON job_seeker_saves
    FOR DELETE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can view own saves" ON job_seeker_saves;
CREATE POLICY "Users can view own saves" ON job_seeker_saves
    FOR SELECT USING (auth.uid() = user_id);

-- سياسات rate_limits
DROP POLICY IF EXISTS "Users can view own rate limits" ON rate_limits;
CREATE POLICY "Users can view own rate limits" ON rate_limits
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "System can manage rate limits" ON rate_limits;
CREATE POLICY "System can manage rate limits" ON rate_limits
    FOR ALL USING (true);

-- سياسات suspicious_activities
DROP POLICY IF EXISTS "System can log suspicious activities" ON suspicious_activities;
CREATE POLICY "System can log suspicious activities" ON suspicious_activities
    FOR INSERT WITH CHECK (true);

-- ===================================
-- 5. إنشاء الدوال المساعدة
-- ===================================

-- دالة تحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة تحديث عدد الإعجابات
CREATE OR REPLACE FUNCTION update_job_seeker_likes_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE job_seekers 
        SET likes_count = likes_count + 1 
        WHERE id = NEW.seeker_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE job_seekers 
        SET likes_count = GREATEST(likes_count - 1, 0) 
        WHERE id = OLD.seeker_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- دالة زيادة عدد المشاهدات
CREATE OR REPLACE FUNCTION increment_job_seeker_views(seeker_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE job_seekers 
    SET views_count = views_count + 1 
    WHERE id = seeker_id;
END;
$$ LANGUAGE plpgsql;

-- دالة منع الإعجاب المتكرر
CREATE OR REPLACE FUNCTION prevent_duplicate_likes()
RETURNS TRIGGER AS $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM job_seeker_likes 
        WHERE seeker_id = NEW.seeker_id AND user_id = NEW.user_id
    ) THEN
        RAISE EXCEPTION 'لقد قمت بالإعجاب بهذا الملف مسبقاً';
    END IF;
    
    IF EXISTS (
        SELECT 1 FROM job_seekers 
        WHERE id = NEW.seeker_id AND user_id = NEW.user_id
    ) THEN
        RAISE EXCEPTION 'لا يمكنك الإعجاب بملفك الشخصي';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة منع الحفظ المتكرر
CREATE OR REPLACE FUNCTION prevent_duplicate_saves()
RETURNS TRIGGER AS $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM job_seeker_saves 
        WHERE seeker_id = NEW.seeker_id AND user_id = NEW.user_id
    ) THEN
        RAISE EXCEPTION 'لقد قمت بحفظ هذا الملف مسبقاً';
    END IF;
    
    IF EXISTS (
        SELECT 1 FROM job_seekers 
        WHERE id = NEW.seeker_id AND user_id = NEW.user_id
    ) THEN
        RAISE EXCEPTION 'لا يمكنك حفظ ملفك الشخصي';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- 6. إنشاء المحفزات
-- ===================================

-- محفز تحديث updated_at
DROP TRIGGER IF EXISTS update_job_seekers_updated_at ON job_seekers;
CREATE TRIGGER update_job_seekers_updated_at
    BEFORE UPDATE ON job_seekers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- محفز تحديث عدد الإعجابات
DROP TRIGGER IF EXISTS job_seeker_likes_count_trigger ON job_seeker_likes;
CREATE TRIGGER job_seeker_likes_count_trigger
    AFTER INSERT OR DELETE ON job_seeker_likes
    FOR EACH ROW EXECUTE FUNCTION update_job_seeker_likes_count();

-- محفز منع الإعجاب المتكرر
DROP TRIGGER IF EXISTS prevent_duplicate_likes_trigger ON job_seeker_likes;
CREATE TRIGGER prevent_duplicate_likes_trigger
    BEFORE INSERT ON job_seeker_likes
    FOR EACH ROW EXECUTE FUNCTION prevent_duplicate_likes();

-- محفز منع الحفظ المتكرر
DROP TRIGGER IF EXISTS prevent_duplicate_saves_trigger ON job_seeker_saves;
CREATE TRIGGER prevent_duplicate_saves_trigger
    BEFORE INSERT ON job_seeker_saves
    FOR EACH ROW EXECUTE FUNCTION prevent_duplicate_saves();

COMMIT;

-- ===================================
-- 7. إدراج البيانات التجريبية
-- ===================================

-- سيتم إدراج البيانات التجريبية في معاملة منفصلة
-- لضمان عدم تأثر الإعداد الأساسي في حالة وجود خطأ

DO $$
BEGIN
    -- التحقق من عدم وجود بيانات مسبقة
    IF NOT EXISTS (SELECT 1 FROM job_seekers LIMIT 1) THEN
        -- إدراج البيانات التجريبية هنا
        INSERT INTO job_seekers (
            user_id, full_name, age, gender, marital_status, current_country, 
            current_city, nationality, category, skills, languages, experience_years,
            description, preferred_job_type, preferred_location, phone_number, email
        ) VALUES 
        (
            gen_random_uuid(),
            'أحمد محمد الأحمد',
            28, 'ذكر', 'married', 'السعودية', 'الرياض', 'سعودي', 'programming',
            ARRAY['Flutter', 'Dart', 'Firebase', 'Supabase', 'React Native'],
            ARRAY['العربية', 'الإنجليزية'], 5,
            'مطور تطبيقات محترف مع خبرة 5 سنوات في تطوير تطبيقات الهاتف المحمول والويب.',
            'fullTime', 'الرياض أو عن بعد', '0501234567', '<EMAIL>'
        ),
        (
            gen_random_uuid(),
            'فاطمة علي السالم',
            25, 'أنثى', 'single', 'السعودية', 'جدة', 'سعودية', 'teaching',
            ARRAY['تدريس اللغة العربية', 'تدريس القرآن الكريم', 'التعليم الابتدائي'],
            ARRAY['العربية', 'الإنجليزية'], 3,
            'معلمة لغة عربية مع خبرة 3 سنوات في التدريس. حاصلة على بكالوريوس في اللغة العربية.',
            'partTime', 'جدة أو المدينة المنورة', '0509876543', '<EMAIL>'
        ),
        (
            gen_random_uuid(),
            'محمد عبدالله النجار',
            35, 'ذكر', 'married', 'السعودية', 'الدمام', 'سعودي', 'carpentry',
            ARRAY['نجارة الأثاث', 'نجارة المطابخ', 'الديكور الخشبي', 'إصلاح الأثاث'],
            ARRAY['العربية'], 12,
            'نجار محترف مع خبرة 12 سنة في صناعة وإصلاح الأثاث. أتقن جميع أنواع النجارة.',
            'freelance', 'المنطقة الشرقية', '0551234567', NULL
        );
        
        RAISE NOTICE 'تم إدراج البيانات التجريبية بنجاح';
    ELSE
        RAISE NOTICE 'البيانات موجودة مسبقاً، تم تخطي إدراج البيانات التجريبية';
    END IF;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'خطأ في إدراج البيانات التجريبية: %', SQLERRM;
END $$;

-- ===================================
-- 8. التحقق من نجاح الإعداد
-- ===================================

DO $$
DECLARE
    tables_count INTEGER;
    indexes_count INTEGER;
    policies_count INTEGER;
    functions_count INTEGER;
    triggers_count INTEGER;
    sample_data_count INTEGER;
BEGIN
    -- عد الجداول
    SELECT COUNT(*) INTO tables_count
    FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name LIKE '%job_seeker%';
    
    -- عد الفهارس
    SELECT COUNT(*) INTO indexes_count
    FROM pg_indexes 
    WHERE schemaname = 'public' 
    AND indexname LIKE '%job_seeker%';
    
    -- عد السياسات
    SELECT COUNT(*) INTO policies_count
    FROM pg_policies 
    WHERE schemaname = 'public' 
    AND tablename LIKE '%job_seeker%';
    
    -- عد الدوال
    SELECT COUNT(*) INTO functions_count
    FROM pg_proc p
    JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public'
    AND p.proname LIKE '%job_seeker%';
    
    -- عد المحفزات
    SELECT COUNT(*) INTO triggers_count
    FROM information_schema.triggers
    WHERE trigger_schema = 'public'
    AND (trigger_name LIKE '%job_seeker%' OR event_object_table LIKE '%job_seeker%');
    
    -- عد البيانات التجريبية
    SELECT COUNT(*) INTO sample_data_count FROM job_seekers;
    
    -- عرض النتائج
    RAISE NOTICE '=== تقرير الإعداد ===';
    RAISE NOTICE 'الجداول المنشأة: %', tables_count;
    RAISE NOTICE 'الفهارس المنشأة: %', indexes_count;
    RAISE NOTICE 'سياسات الأمان: %', policies_count;
    RAISE NOTICE 'الدوال المنشأة: %', functions_count;
    RAISE NOTICE 'المحفزات المنشأة: %', triggers_count;
    RAISE NOTICE 'البيانات التجريبية: %', sample_data_count;
    RAISE NOTICE '===================';
    
    IF tables_count >= 5 AND indexes_count >= 10 AND policies_count >= 8 THEN
        RAISE NOTICE '✅ تم إعداد قسم البحث عن عمل بنجاح!';
        RAISE NOTICE '🚀 القسم جاهز للاستخدام في الإنتاج';
    ELSE
        RAISE NOTICE '⚠️  قد يكون هناك نقص في الإعداد، يرجى مراجعة الأخطاء';
    END IF;
END $$;

-- ===================================
-- انتهى الإعداد الشامل
-- ===================================
