-- ===================================
-- إعداد قسم البحث عن عمل للإنتاج
-- بدون أي بيانات تجريبية - للمستخدمين الحقيقيين فقط
-- ===================================

-- 1. جدول الباحثين عن عمل
CREATE TABLE job_seekers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    profile_image TEXT,
    age INTEGER NOT NULL,
    gender TEXT NOT NULL,
    marital_status TEXT NOT NULL,
    current_country TEXT NOT NULL,
    current_city TEXT NOT NULL,
    nationality TEXT NOT NULL,
    category TEXT NOT NULL,
    skills TEXT[] DEFAULT '{}',
    languages TEXT[] DEFAULT '{}',
    experience_years INTEGER DEFAULT 0,
    description TEXT NOT NULL,
    preferred_job_type TEXT NOT NULL,
    preferred_location TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    email TEXT,
    social_links TEXT,
    cv_url TEXT,
    portfolio_images TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    views_count INTEGER DEFAULT 0,
    likes_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. جدول الإعجابات
CREATE TABLE job_seeker_likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    seeker_id UUID REFERENCES job_seekers(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. جدول الحفظ
CREATE TABLE job_seeker_saves (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    seeker_id UUID REFERENCES job_seekers(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. جدول إعدادات المستخدم
CREATE TABLE job_seeker_user_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    profile_notifications_enabled BOOLEAN DEFAULT TRUE,
    show_phone_number BOOLEAN DEFAULT TRUE,
    show_email BOOLEAN DEFAULT TRUE,
    allow_direct_contact BOOLEAN DEFAULT TRUE,
    show_online_status BOOLEAN DEFAULT TRUE,
    profile_visible_in_search BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. تفعيل Row Level Security
ALTER TABLE job_seekers ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_seeker_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_seeker_saves ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_seeker_user_settings ENABLE ROW LEVEL SECURITY;

-- 6. سياسات الأمان للباحثين عن عمل
CREATE POLICY "Anyone can view active job seekers" ON job_seekers
    FOR SELECT USING (is_active = true);

CREATE POLICY "Users can insert own profile" ON job_seekers
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own profile" ON job_seekers
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own profile" ON job_seekers
    FOR DELETE USING (auth.uid() = user_id);

-- 7. سياسات الإعجابات
CREATE POLICY "Anyone can view likes" ON job_seeker_likes
    FOR SELECT USING (true);

CREATE POLICY "Users can add likes" ON job_seeker_likes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can remove own likes" ON job_seeker_likes
    FOR DELETE USING (auth.uid() = user_id);

-- 8. سياسات الحفظ
CREATE POLICY "Users can view own saves" ON job_seeker_saves
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can add saves" ON job_seeker_saves
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can remove own saves" ON job_seeker_saves
    FOR DELETE USING (auth.uid() = user_id);

-- 9. سياسات الإعدادات
CREATE POLICY "Users can view own settings" ON job_seeker_user_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own settings" ON job_seeker_user_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own settings" ON job_seeker_user_settings
    FOR UPDATE USING (auth.uid() = user_id);

-- 10. إنشاء الفهارس الأساسية
CREATE INDEX idx_job_seekers_user_id ON job_seekers(user_id);
CREATE INDEX idx_job_seekers_category ON job_seekers(category);
CREATE INDEX idx_job_seekers_city ON job_seekers(current_city);
CREATE INDEX idx_job_seekers_active ON job_seekers(is_active);
CREATE INDEX idx_job_seeker_likes_seeker_id ON job_seeker_likes(seeker_id);
CREATE INDEX idx_job_seeker_likes_user_id ON job_seeker_likes(user_id);
CREATE INDEX idx_job_seeker_saves_user_id ON job_seeker_saves(user_id);

-- 11. دالة زيادة المشاهدات
CREATE OR REPLACE FUNCTION increment_job_seeker_views(seeker_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE job_seekers 
    SET views_count = views_count + 1 
    WHERE id = seeker_id;
END;
$$ LANGUAGE plpgsql;

-- 12. دالة تحديث عدد الإعجابات
CREATE OR REPLACE FUNCTION update_job_seeker_likes_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE job_seekers 
        SET likes_count = likes_count + 1 
        WHERE id = NEW.seeker_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE job_seekers 
        SET likes_count = GREATEST(likes_count - 1, 0) 
        WHERE id = OLD.seeker_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 13. محفز تحديث الإعجابات
CREATE TRIGGER job_seeker_likes_count_trigger
    AFTER INSERT OR DELETE ON job_seeker_likes
    FOR EACH ROW EXECUTE FUNCTION update_job_seeker_likes_count();

-- 14. دوال الحذف والإعدادات
CREATE OR REPLACE FUNCTION delete_user_job_data()
RETURNS TEXT AS $$
DECLARE
    current_user_id UUID;
    deleted_count INTEGER;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN 'خطأ: يجب تسجيل الدخول';
    END IF;
    
    -- حذف الإعجابات
    DELETE FROM job_seeker_likes WHERE user_id = current_user_id;
    
    -- حذف الحفظ
    DELETE FROM job_seeker_saves WHERE user_id = current_user_id;
    
    -- حذف الإعدادات
    DELETE FROM job_seeker_user_settings WHERE user_id = current_user_id;
    
    -- حذف الملف المهني
    DELETE FROM job_seekers WHERE user_id = current_user_id;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    IF deleted_count > 0 THEN
        RETURN 'تم حذف جميع بياناتك بنجاح';
    ELSE
        RETURN 'لا توجد بيانات للحذف';
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 15. دالة مسح البيانات المحفوظة فقط
CREATE OR REPLACE FUNCTION clear_saved_data()
RETURNS TEXT AS $$
DECLARE
    current_user_id UUID;
    deleted_likes INTEGER;
    deleted_saves INTEGER;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN 'خطأ: يجب تسجيل الدخول';
    END IF;
    
    -- حذف الإعجابات
    DELETE FROM job_seeker_likes WHERE user_id = current_user_id;
    GET DIAGNOSTICS deleted_likes = ROW_COUNT;
    
    -- حذف الحفظ
    DELETE FROM job_seeker_saves WHERE user_id = current_user_id;
    GET DIAGNOSTICS deleted_saves = ROW_COUNT;
    
    RETURN format('تم مسح %s إعجاب و %s حفظ', deleted_likes, deleted_saves);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 16. دالة إحصائيات عامة
CREATE OR REPLACE FUNCTION get_job_seekers_stats()
RETURNS JSON AS $$
BEGIN
    RETURN json_build_object(
        'total_seekers', (SELECT COUNT(*) FROM job_seekers WHERE is_active = true),
        'new_this_week', (
            SELECT COUNT(*) FROM job_seekers 
            WHERE is_active = true 
            AND created_at >= NOW() - INTERVAL '7 days'
        ),
        'total_categories', (
            SELECT COUNT(DISTINCT category) FROM job_seekers WHERE is_active = true
        ),
        'total_cities', (
            SELECT COUNT(DISTINCT current_city) FROM job_seekers WHERE is_active = true
        ),
        'generated_at', NOW()
    );
END;
$$ LANGUAGE plpgsql;

-- 17. منح الصلاحيات
GRANT EXECUTE ON FUNCTION increment_job_seeker_views(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION delete_user_job_data() TO authenticated;
GRANT EXECUTE ON FUNCTION clear_saved_data() TO authenticated;
GRANT EXECUTE ON FUNCTION get_job_seekers_stats() TO anon, authenticated;

-- 18. تم الإنشاء بنجاح
SELECT 
    'قسم البحث عن عمل جاهز للإنتاج! بدون أي بيانات تجريبية.' as message,
    'المستخدمون الحقيقيون يمكنهم الآن إنشاء ملفاتهم المهنية.' as note;
