class CommunityPost {
  final String id;
  final String communityId;
  final String userId;
  final String userName;
  final String userAvatar;
  final String content;
  final String? mediaUrl;
  final String mediaType; // text/image/video
  final DateTime createdAt;
  final int upVotes;
  final int downVotes;
  final int commentsCount;
  final int? userVote; // 1 up, -1 down, 0/ null none
  final bool isVerified; // حالة توثيق المستخدم

  CommunityPost({
    required this.id,
    required this.communityId,
    required this.userId,
    required this.userName,
    required this.userAvatar,
    required this.content,
    this.mediaUrl,
    required this.mediaType,
    required this.createdAt,
    required this.upVotes,
    required this.downVotes,
    required this.commentsCount,
    this.userVote,
    this.isVerified = false,
  });

  factory CommunityPost.fromMap(Map<String, dynamic> map) {
    final profile = map['profiles'] ?? {};
    return CommunityPost(
      id: map['id'].toString(),
      communityId: map['community_id'].toString(),
      userId: map['user_id'].toString(),
      userName: profile['name'] ?? 'مستخدم',
      userAvatar: profile['avatar_url'] ?? '',
      content: map['content'] ?? '',
      mediaUrl: map['media_url'],
      mediaType: map['media_type'] ?? 'text',
      createdAt: DateTime.parse(map['created_at']),
      upVotes: map['up_votes'] ?? 0,
      downVotes: map['down_votes'] ?? 0,
      commentsCount: map['comments_count'] ?? 0,
      userVote: map['current_vote'],
      isVerified: profile['is_verified'] ?? false,
    );
  }
} 