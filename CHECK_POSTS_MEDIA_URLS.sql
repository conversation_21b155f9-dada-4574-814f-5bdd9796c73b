-- التحقق من وجود عمود media_urls في جدول posts
-- Check if media_urls column exists in posts table

-- 1. التحقق من وجود العمود
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'posts' AND column_name = 'media_urls';

-- 2. إذا لم يكن موجوداً، أضفه
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' AND column_name = 'media_urls'
    ) THEN
        ALTER TABLE posts ADD COLUMN media_urls TEXT[];
        RAISE NOTICE 'تم إضافة عمود media_urls لجدول posts بنجاح';
    ELSE
        RAISE NOTICE 'عمود media_urls موجود بالفعل في جدول posts';
    END IF;
END $$;

-- 3. التحقق مرة أخرى
SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'posts' AND column_name = 'media_urls';

-- 4. عرض بعض المنشورات للتحقق
SELECT 
    id,
    content,
    type,
    media_url,
    media_urls,
    created_at
FROM posts 
WHERE media_urls IS NOT NULL 
ORDER BY created_at DESC 
LIMIT 5;

-- 5. إحصائيات المنشورات
SELECT 
    type,
    COUNT(*) as total_posts,
    COUNT(CASE WHEN media_url IS NOT NULL THEN 1 END) as single_media,
    COUNT(CASE WHEN media_urls IS NOT NULL THEN 1 END) as multi_media
FROM posts 
GROUP BY type; 