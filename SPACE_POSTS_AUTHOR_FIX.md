# إصلاح أسماء وصور المستخدمين في منشورات المساحات
# Fix for Author Names and Avatars in Space Posts

## المشكلة:
في منشورات المساحات، يتم عرض "مستخدم" بدلاً من الاسم الحقيقي، ولا تظهر صورة الملف الشخصي.

## السبب:
`fetchPostsBySpace` لا يجلب معلومات المستخدمين (الأسماء والصور) من جدول `profiles`.

## الإصلاحات المطبقة:

### ✅ **1. إضافة دالة لجلب معلومات المستخدمين:**
```dart
/// دالة مساعدة لجلب معلومات المستخدمين
Future<Map<String, Map<String, dynamic>>> _getUserProfiles(List<String> userIds) async {
  if (userIds.isEmpty) return {};
  
  try {
    final response = await _client
        .from('profiles')
        .select('id, name, username, avatar_url, is_verified')
        .inFilter('id', userIds);
    
    final Map<String, Map<String, dynamic>> profiles = {};
    for (final row in (response as List)) {
      profiles[row['id'] as String] = row as Map<String, dynamic>;
    }
    
    return profiles;
  } catch (e) {
    print('❌ خطأ في جلب معلومات المستخدمين: $e');
    return {};
  }
}
```

### ✅ **2. تحديث fetchPostsBySpace:**
```dart
// جلب معرفات المستخدمين الفريدة
final authorIds = (rows as List).map((row) => row['author_id'] as String).toSet().toList();

// جلب معلومات المستخدمين
final userProfiles = await _getUserProfiles(authorIds);
print('👥 تم جلب معلومات ${userProfiles.length} مستخدم');

// في إنشاء Post object
final authorId = row['author_id'] as String;
final userProfile = userProfiles[authorId];

final post = Post(
  id: row['id'],
  userId: authorId,
  userName: userProfile?['name'] ?? userProfile?['username'] ?? 'مستخدم',
  userAvatar: userProfile?['avatar_url'] ?? '',
  // ... باقي الحقول
);
```

### ✅ **3. إضافة رسائل تشخيص:**
```dart
print('👤 صاحب المنشور: ${post.userName}');
```

## خطوات الاختبار:

### 1. **اختبار إنشاء منشور في المساحة:**
- افتح التطبيق
- اذهب إلى مساحة
- أنشئ منشور جديد
- تحقق من Console للحصول على الرسائل:
  ```
  👥 تم جلب معلومات [number] مستخدم
  ✅ تم إضافة منشور: [post_id]
  👤 صاحب المنشور: [real_name]
  ```

### 2. **اختبار عرض المنشورات:**
- تحقق من أن الاسم الحقيقي يظهر بدلاً من "مستخدم"
- تحقق من أن صورة الملف الشخصي تظهر بجانب الاسم
- تحقق من أن شارة التحقق تظهر إذا كان المستخدم موثقاً

### 3. **التحقق من قاعدة البيانات:**
```sql
-- عرض منشورات المساحة مع معلومات المؤلفين
SELECT 
    sp.id,
    sp.content,
    sp.author_id,
    p.name as author_name,
    p.username as author_username,
    p.avatar_url as author_avatar,
    p.is_verified as author_verified
FROM space_posts sp
LEFT JOIN profiles p ON sp.author_id = p.id
WHERE sp.space_id = '[space_id]'
ORDER BY sp.created_at DESC;
```

## النتائج المتوقعة:

### 🎯 **عرض الأسماء الحقيقية:**
- عرض الاسم الحقيقي من `profiles.name`
- استخدام `profiles.username` كبديل إذا لم يكن هناك `name`

### 🎯 **عرض صور الملف الشخصي:**
- عرض صورة الملف الشخصي من `profiles.avatar_url`
- عرض أيقونة افتراضية إذا لم تكن هناك صورة

### 🎯 **عرض شارات التحقق:**
- عرض شارة التحقق إذا كان `profiles.is_verified = true`

## إذا لم تنجح:

### 1. **تحقق من Console:**
ابحث عن رسائل الخطأ في جلب معلومات المستخدمين

### 2. **تحقق من قاعدة البيانات:**
تأكد من وجود بيانات في جدول `profiles`

### 3. **تحقق من RLS:**
تأكد من أن RLS policies تسمح بالقراءة من `profiles`

**الآن منشورات المساحات ستعرض الأسماء والصور الحقيقية!** 