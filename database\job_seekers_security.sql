-- ===================================
-- إعدادات الأمان المتقدمة لقسم البحث عن عمل
-- ===================================

-- ===================================
-- 1. سياسات الأمان المحسنة
-- ===================================

-- سياسة عرض الملفات النشطة فقط مع إخفاء المعلومات الحساسة للغير مصرح لهم
DROP POLICY IF EXISTS "Public can view active job seekers with limited info" ON job_seekers;
CREATE POLICY "Public can view active job seekers with limited info" ON job_seekers
    FOR SELECT USING (
        is_active = true AND (
            -- المالك يرى جميع المعلومات
            auth.uid() = user_id OR
            -- الآخرون يرون المعلومات العامة فقط
            auth.uid() IS NOT NULL
        )
    );

-- سياسة منع إنشاء أكثر من ملف واحد لكل مستخدم
DROP POLICY IF EXISTS "Users can insert one job seeker profile only" ON job_seekers;
CREATE POLICY "Users can insert one job seeker profile only" ON job_seekers
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        NOT EXISTS (
            SELECT 1 FROM job_seekers 
            WHERE user_id = auth.uid() AND is_active = true
        )
    );

-- سياسة تحديث الملف الشخصي مع قيود إضافية
DROP POLICY IF EXISTS "Users can update own profile with restrictions" ON job_seekers;
CREATE POLICY "Users can update own profile with restrictions" ON job_seekers
    FOR UPDATE USING (auth.uid() = user_id);

-- ===================================
-- 2. دوال التحقق من صحة البيانات
-- ===================================

-- دالة التحقق من صحة رقم الهاتف السعودي
CREATE OR REPLACE FUNCTION validate_saudi_phone(phone TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- التحقق من أن الرقم يبدأ بـ 05 ويتكون من 10 أرقام
    RETURN phone ~ '^05[0-9]{8}$';
END;
$$ LANGUAGE plpgsql;

-- دالة التحقق من صحة البريد الإلكتروني
CREATE OR REPLACE FUNCTION validate_email(email TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- التحقق من صيغة البريد الإلكتروني الأساسية
    RETURN email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$';
END;
$$ LANGUAGE plpgsql;

-- دالة التحقق من المحتوى المناسب
CREATE OR REPLACE FUNCTION validate_content(content TEXT)
RETURNS BOOLEAN AS $$
BEGIN
    -- منع الكلمات غير المناسبة (يمكن توسيعها)
    RETURN NOT (
        LOWER(content) ~ '(كلمة_محظورة1|كلمة_محظورة2)' OR
        LENGTH(content) > 1000 OR
        LENGTH(content) < 10
    );
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- 3. محفزات التحقق من البيانات
-- ===================================

-- محفز التحقق من البيانات قبل الإدراج أو التحديث
CREATE OR REPLACE FUNCTION validate_job_seeker_data()
RETURNS TRIGGER AS $$
BEGIN
    -- التحقق من رقم الهاتف
    IF NOT validate_saudi_phone(NEW.phone_number) THEN
        RAISE EXCEPTION 'رقم الهاتف غير صحيح. يجب أن يبدأ بـ 05 ويتكون من 10 أرقام';
    END IF;
    
    -- التحقق من البريد الإلكتروني إذا تم إدخاله
    IF NEW.email IS NOT NULL AND NEW.email != '' THEN
        IF NOT validate_email(NEW.email) THEN
            RAISE EXCEPTION 'صيغة البريد الإلكتروني غير صحيحة';
        END IF;
    END IF;
    
    -- التحقق من المحتوى
    IF NOT validate_content(NEW.description) THEN
        RAISE EXCEPTION 'الوصف يحتوي على محتوى غير مناسب أو طوله غير صحيح';
    END IF;
    
    -- التحقق من الاسم
    IF LENGTH(TRIM(NEW.full_name)) < 3 THEN
        RAISE EXCEPTION 'الاسم قصير جداً';
    END IF;
    
    -- التحقق من العمر
    IF NEW.age < 16 OR NEW.age > 70 THEN
        RAISE EXCEPTION 'العمر يجب أن يكون بين 16 و 70 سنة';
    END IF;
    
    -- التحقق من سنوات الخبرة
    IF NEW.experience_years < 0 OR NEW.experience_years > 50 THEN
        RAISE EXCEPTION 'سنوات الخبرة غير صحيحة';
    END IF;
    
    -- التحقق من عدد المهارات
    IF array_length(NEW.skills, 1) IS NULL OR array_length(NEW.skills, 1) = 0 THEN
        RAISE EXCEPTION 'يجب إضافة مهارة واحدة على الأقل';
    END IF;
    
    -- التحقق من عدد اللغات
    IF array_length(NEW.languages, 1) IS NULL OR array_length(NEW.languages, 1) = 0 THEN
        RAISE EXCEPTION 'يجب إضافة لغة واحدة على الأقل';
    END IF;
    
    -- تحديث updated_at تلقائياً
    NEW.updated_at = NOW();
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق المحفز
DROP TRIGGER IF EXISTS validate_job_seeker_trigger ON job_seekers;
CREATE TRIGGER validate_job_seeker_trigger
    BEFORE INSERT OR UPDATE ON job_seekers
    FOR EACH ROW EXECUTE FUNCTION validate_job_seeker_data();

-- ===================================
-- 4. سياسات الحماية من الإساءة
-- ===================================

-- منع الإعجاب المتكرر من نفس المستخدم
CREATE OR REPLACE FUNCTION prevent_duplicate_likes()
RETURNS TRIGGER AS $$
BEGIN
    -- التحقق من عدم وجود إعجاب سابق
    IF EXISTS (
        SELECT 1 FROM job_seeker_likes 
        WHERE seeker_id = NEW.seeker_id AND user_id = NEW.user_id
    ) THEN
        RAISE EXCEPTION 'لقد قمت بالإعجاب بهذا الملف مسبقاً';
    END IF;
    
    -- منع الإعجاب بالملف الشخصي
    IF EXISTS (
        SELECT 1 FROM job_seekers 
        WHERE id = NEW.seeker_id AND user_id = NEW.user_id
    ) THEN
        RAISE EXCEPTION 'لا يمكنك الإعجاب بملفك الشخصي';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق المحفز على الإعجابات
DROP TRIGGER IF EXISTS prevent_duplicate_likes_trigger ON job_seeker_likes;
CREATE TRIGGER prevent_duplicate_likes_trigger
    BEFORE INSERT ON job_seeker_likes
    FOR EACH ROW EXECUTE FUNCTION prevent_duplicate_likes();

-- منع الحفظ المتكرر
CREATE OR REPLACE FUNCTION prevent_duplicate_saves()
RETURNS TRIGGER AS $$
BEGIN
    -- التحقق من عدم وجود حفظ سابق
    IF EXISTS (
        SELECT 1 FROM job_seeker_saves 
        WHERE seeker_id = NEW.seeker_id AND user_id = NEW.user_id
    ) THEN
        RAISE EXCEPTION 'لقد قمت بحفظ هذا الملف مسبقاً';
    END IF;
    
    -- منع حفظ الملف الشخصي
    IF EXISTS (
        SELECT 1 FROM job_seekers 
        WHERE id = NEW.seeker_id AND user_id = NEW.user_id
    ) THEN
        RAISE EXCEPTION 'لا يمكنك حفظ ملفك الشخصي';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق المحفز على الحفظ
DROP TRIGGER IF EXISTS prevent_duplicate_saves_trigger ON job_seeker_saves;
CREATE TRIGGER prevent_duplicate_saves_trigger
    BEFORE INSERT ON job_seeker_saves
    FOR EACH ROW EXECUTE FUNCTION prevent_duplicate_saves();

-- ===================================
-- 5. حدود معدل الاستخدام (Rate Limiting)
-- ===================================

-- جدول تتبع معدل الاستخدام
CREATE TABLE IF NOT EXISTS rate_limits (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    action_type TEXT NOT NULL,
    action_count INTEGER DEFAULT 1,
    window_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- فهرس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_rate_limits_user_action ON rate_limits(user_id, action_type, window_start);

-- دالة التحقق من حدود معدل الاستخدام
CREATE OR REPLACE FUNCTION check_rate_limit(
    user_uuid UUID,
    action TEXT,
    max_actions INTEGER,
    window_minutes INTEGER
)
RETURNS BOOLEAN AS $$
DECLARE
    current_count INTEGER;
    window_start TIMESTAMP WITH TIME ZONE;
BEGIN
    window_start := NOW() - INTERVAL '1 minute' * window_minutes;
    
    -- حساب عدد الإجراءات في النافزة الزمنية
    SELECT COALESCE(SUM(action_count), 0) INTO current_count
    FROM rate_limits
    WHERE user_id = user_uuid
    AND action_type = action
    AND window_start >= window_start;
    
    -- التحقق من تجاوز الحد المسموح
    IF current_count >= max_actions THEN
        RETURN FALSE;
    END IF;
    
    -- تسجيل الإجراء الجديد
    INSERT INTO rate_limits (user_id, action_type, action_count)
    VALUES (user_uuid, action, 1)
    ON CONFLICT (user_id, action_type) 
    DO UPDATE SET 
        action_count = rate_limits.action_count + 1,
        window_start = CASE 
            WHEN rate_limits.window_start < NOW() - INTERVAL '1 minute' * window_minutes 
            THEN NOW() 
            ELSE rate_limits.window_start 
        END;
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- 6. مراقبة النشاط المشبوه
-- ===================================

-- جدول تسجيل الأنشطة المشبوهة
CREATE TABLE IF NOT EXISTS suspicious_activities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    activity_type TEXT NOT NULL,
    description TEXT,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- دالة تسجيل النشاط المشبوه
CREATE OR REPLACE FUNCTION log_suspicious_activity(
    user_uuid UUID,
    activity TEXT,
    description_text TEXT DEFAULT NULL
)
RETURNS VOID AS $$
BEGIN
    INSERT INTO suspicious_activities (user_id, activity_type, description)
    VALUES (user_uuid, activity, description_text);
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- 7. تنظيف البيانات التلقائي
-- ===================================

-- دالة تنظيف البيانات القديمة
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS VOID AS $$
BEGIN
    -- حذف حدود معدل الاستخدام القديمة (أكثر من يوم)
    DELETE FROM rate_limits 
    WHERE created_at < NOW() - INTERVAL '1 day';
    
    -- حذف سجلات النشاط المشبوه القديمة (أكثر من شهر)
    DELETE FROM suspicious_activities 
    WHERE created_at < NOW() - INTERVAL '1 month';
    
    -- تنظيف الملفات غير النشطة القديمة (أكثر من 6 أشهر)
    UPDATE job_seekers 
    SET is_active = false 
    WHERE is_active = true 
    AND updated_at < NOW() - INTERVAL '6 months';
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- 8. إعدادات الأمان على مستوى قاعدة البيانات
-- ===================================

-- تفعيل تسجيل الاستعلامات البطيئة
-- ALTER SYSTEM SET log_min_duration_statement = 1000;

-- تفعيل تسجيل الاتصالات
-- ALTER SYSTEM SET log_connections = on;

-- تفعيل تسجيل قطع الاتصال
-- ALTER SYSTEM SET log_disconnections = on;

-- ===================================
-- 9. دوال المساعدة للأمان
-- ===================================

-- دالة التحقق من صلاحية المستخدم
CREATE OR REPLACE FUNCTION is_user_authorized(user_uuid UUID, seeker_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM job_seekers 
        WHERE id = seeker_id AND user_id = user_uuid
    );
END;
$$ LANGUAGE plpgsql;

-- دالة إخفاء المعلومات الحساسة
CREATE OR REPLACE FUNCTION sanitize_job_seeker_data(seeker_data JSONB, requesting_user UUID)
RETURNS JSONB AS $$
DECLARE
    is_owner BOOLEAN;
    sanitized_data JSONB;
BEGIN
    -- التحقق من ملكية الملف
    SELECT user_id = requesting_user INTO is_owner
    FROM job_seekers 
    WHERE id = (seeker_data->>'id')::UUID;
    
    sanitized_data := seeker_data;
    
    -- إخفاء المعلومات الحساسة للغير مالكين
    IF NOT is_owner THEN
        sanitized_data := sanitized_data - 'email' - 'social_links';
        -- إخفاء جزء من رقم الهاتف
        sanitized_data := jsonb_set(
            sanitized_data, 
            '{phone_number}', 
            to_jsonb(SUBSTRING(seeker_data->>'phone_number' FROM 1 FOR 6) || '****')
        );
    END IF;
    
    RETURN sanitized_data;
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- تم إنشاء جميع إعدادات الأمان بنجاح
-- ===================================

SELECT 'تم إنشاء جميع إعدادات الأمان المتقدمة بنجاح!' as message;
