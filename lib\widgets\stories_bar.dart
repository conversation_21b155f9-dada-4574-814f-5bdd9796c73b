import 'package:flutter/material.dart';
import '../models/story.dart';
import '../supabase_service.dart';
import '../pages/story_viewer_page.dart';
import 'add_story_sheet.dart';
import 'dart:typed_data';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'cached_image.dart';
import 'package:shimmer/shimmer.dart';

const _cardWidth = 110.0;
const _cardHeight = 190.0;

class StoriesBar extends StatelessWidget {
  const StoriesBar({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: _cardHeight,
      child: StreamBuilder<List<Story>>(
        stream: SupabaseService().storiesStream(),
        builder: (context, snapshot) {
          if (!snapshot.hasData) {
            return ListView.separated(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 12),
              itemCount: 4,
              separatorBuilder: (_, __) => const SizedBox(width: 8),
              itemBuilder: (_, __) => const _StorySkeleton(),
            );
          }
          final stories = snapshot.data!.where((s) => s.expiresAt.isAfter(DateTime.now())).toList();
          // تجميع حسب المستخدم
          final Map<String, Story> latestByUser = {};
          for (final s in stories) {
            latestByUser[s.userId] ??= s;
            if (s.createdAt.isAfter(latestByUser[s.userId]!.createdAt)) {
              latestByUser[s.userId] = s;
            }
          }
          final items = latestByUser.values.toList();
          return ListView.separated(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 12),
            itemCount: items.length + 1,
            separatorBuilder: (_, __) => const SizedBox(width: 8),
            itemBuilder: (context, idx) {
              if (idx == 0) {
                return _AddStoryCard(onTap: () async {
                  await showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(25))),
                    builder: (_) => const AddStorySheet(),
                  );
                });
              }
              final story = items[idx - 1];
              return _StoryCard(story: story);
            },
          );
        },
      ),
    );
  }
}

// --------------------------- Story Card --------------------------- //

class _StoryCard extends StatelessWidget {
  final Story story;
  const _StoryCard({required this.story});

  // Builds the appropriate preview widget based on story type
  Widget _buildPreview() {
    switch (story.type) {
      case StoryType.image:
        // Show the image itself
        if (story.mediaUrl == null) return const SizedBox.shrink();
        return CachedImage(url: story.mediaUrl!, fit: BoxFit.cover);
      case StoryType.video:
        // Generate and show thumbnail with a play icon overlay
        if (story.mediaUrl == null) {
          return const SizedBox.shrink();
        }
        return FutureBuilder<Uint8List?>(
          future: VideoThumbnail.thumbnailData(
            video: story.mediaUrl!,
            imageFormat: ImageFormat.PNG,
            maxHeight: _cardHeight.toInt(), // maintain aspect ratio based on height
            quality: 75,
          ),
          builder: (context, snapshot) {
            if (snapshot.hasData && snapshot.data != null) {
              return Stack(
                fit: StackFit.expand,
                children: [
                  Image.memory(snapshot.data!, fit: BoxFit.cover),
                  Positioned.fill(
                    child: Container(color: Colors.black26),
                  ),
                  const Center(child: Icon(Icons.play_circle_filled, color: Colors.white70, size: 48)),
                ],
              );
            }
            return const Center(child: CircularProgressIndicator(strokeWidth: 2));
          },
        );
      case StoryType.text:
      default:
        // Show text snippet over coloured background
        return Container(
          color: Colors.grey.shade700,
          alignment: Alignment.center,
          padding: const EdgeInsets.all(8),
          child: Text(
            story.text ?? '',
            maxLines: 5,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
            style: const TextStyle(color: Colors.white, fontSize: 14, height: 1.2),
          ),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (_) => StoryViewerPage(initialUserId: story.userId)),
        );
      },
      child: SizedBox(
        width: _cardWidth,
        child: Stack(
          children: [
            // Background preview
            ClipRRect(
              borderRadius: BorderRadius.circular(18),
              child: Container(
                height: _cardHeight,
                width: _cardWidth,
                child: _buildPreview(),
              ),
            ),
            // Dark overlay bottom gradient for text readability
            Positioned.fill(
              child: Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  height: 50,
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.vertical(bottom: Radius.circular(18)),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [Colors.transparent, Colors.black54],
                    ),
                  ),
                ),
              ),
            ),
            // User name
            Positioned(
              left: 8,
              right: 8,
              bottom: 10,
              child: Text(
                story.userName,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: const TextStyle(color: Colors.white, fontSize: 13, fontWeight: FontWeight.w500),
                textAlign: TextAlign.center,
              ),
            ),
            // Avatar with ring
            Positioned(
              top: 8,
              left: (_cardWidth / 2) - 24,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.green, width: 3),
                ),
                child: CircleAvatar(
                  radius: 21,
                  backgroundImage: story.userAvatar.isNotEmpty ? NetworkImage(story.userAvatar) : null,
                  child: story.userAvatar.isEmpty ? Text(story.userName[0]) : null,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// --------------------------- Add Story Card --------------------------- //

class _AddStoryCard extends StatelessWidget {
  final VoidCallback onTap;
  const _AddStoryCard({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: SizedBox(
        width: _cardWidth,
        child: Stack(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(18),
              child: Container(
                height: _cardHeight,
                width: _cardWidth,
                color: Colors.grey.shade700,
              ),
            ),
            const Center(
              child: Icon(Icons.add, size: 40, color: Colors.white),
            ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 10,
              child: const Text('إضافة حالة', textAlign: TextAlign.center, style: TextStyle(color: Colors.white, fontSize: 13)),
            ),
          ],
        ),
      ),
    );
  }
}

// --------------------------- Story Skeleton --------------------------- //

class _StorySkeleton extends StatelessWidget {
  const _StorySkeleton();

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: Colors.grey.shade300,
      highlightColor: Colors.grey.shade100,
      child: Container(
        width: _cardWidth,
        height: _cardHeight,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(18),
        ),
      ),
    );
  }
} 