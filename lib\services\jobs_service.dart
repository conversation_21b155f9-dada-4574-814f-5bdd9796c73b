import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/job.dart';

class JobsService {
  final SupabaseClient _client = Supabase.instance.client;

  // الحصول على جميع الوظائف
  Future<List<Job>> getAllJobs({
    JobCategory? category,
    JobType? jobType,
    String? location,
    String? searchQuery,
  }) async {
    try {
      // التحقق من وجود جدول الوظائف أولاً
      final tableExists = await _checkJobsTableExists();
      if (!tableExists) {
        // إرجاع قائمة فارغة إذا لم يكن الجدول موجوداً
        return [];
      }

      var query = _client
          .from('jobs')
          .select('*')
          .eq('is_active', true);

      // فلترة حسب الفئة
      if (category != null) {
        query = query.eq('category', category.name);
      }

      // فلترة حسب نوع العمل
      if (jobType != null) {
        query = query.eq('job_type', jobType.name);
      }

      // فلترة حسب الموقع
      if (location != null && location.isNotEmpty) {
        if (location == 'remote') {
          query = query.eq('is_remote', true);
        } else {
          query = query.ilike('location', '%$location%');
        }
      }

      // البحث النصي
      if (searchQuery != null && searchQuery.isNotEmpty) {
        query = query.or('title.ilike.%$searchQuery%,company_name.ilike.%$searchQuery%,job_title.ilike.%$searchQuery%');
      }

      final response = await query.order('created_at', ascending: false);

      return (response as List).map((json) {
        // إضافة بيانات افتراضية للناشر
        json['publisher_name'] = json['publisher_name'] ?? 'مستخدم';
        json['publisher_avatar'] = json['publisher_avatar'];
        json['space_name'] = json['space_name'];

        return Job.fromJson(json);
      }).toList();
    } catch (e) {
      // في حالة عدم وجود الجدول، إرجاع قائمة فارغة
      return [];
    }
  }

  // التحقق من وجود جدول الوظائف
  Future<bool> _checkJobsTableExists() async {
    try {
      await _client.from('jobs').select('id').limit(1);
      return true;
    } catch (e) {
      return false;
    }
  }

  // الحصول على وظيفة محددة
  Future<Job?> getJob(String jobId) async {
    try {
      final response = await _client
          .from('jobs')
          .select('''
            *,
            profiles:publisher_id(username, avatar_url),
            spaces:space_id(name)
          ''')
          .eq('id', jobId)
          .single();

      // إضافة بيانات الناشر والمساحة
      response['publisher_name'] = response['profiles']?['username'] ?? '';
      response['publisher_avatar'] = response['profiles']?['avatar_url'];
      response['space_name'] = response['spaces']?['name'];

      return Job.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // نشر وظيفة جديدة
  Future<String> createJob(Job job) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // التحقق من وجود ملف شخصي للمستخدم
      await _ensureUserProfile(userId);

      final jobData = job.toJson();
      jobData['publisher_id'] = userId;
      jobData.remove('id'); // إزالة المعرف ليتم إنشاؤه تلقائياً
      jobData.remove('publisher_name');
      jobData.remove('publisher_avatar');
      jobData.remove('space_name');

      final response = await _client
          .from('jobs')
          .insert(jobData)
          .select()
          .single();

      return response['id'];
    } catch (e) {
      if (e.toString().contains('row level security')) {
        throw Exception('تحتاج إلى إعداد ملفك الشخصي أولاً');
      }
      throw Exception('فشل في نشر الوظيفة: $e');
    }
  }

  // التأكد من وجود ملف شخصي للمستخدم (اختياري)
  Future<void> _ensureUserProfile(String userId) async {
    // لا نحتاج للتحقق من الملف الشخصي في الوقت الحالي
    // يمكن إضافة هذا لاحقاً عند الحاجة
    return;
  }

  // تحديث وظيفة
  Future<void> updateJob(String jobId, Job job) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final jobData = job.toJson();
      jobData.remove('id');
      jobData.remove('publisher_id');
      jobData.remove('created_at');

      await _client
          .from('jobs')
          .update(jobData)
          .eq('id', jobId)
          .eq('publisher_id', userId);
    } catch (e) {
      throw Exception('فشل في تحديث الوظيفة: $e');
    }
  }

  // حذف وظيفة
  Future<void> deleteJob(String jobId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('jobs')
          .delete()
          .eq('id', jobId)
          .eq('publisher_id', userId);
    } catch (e) {
      throw Exception('فشل في حذف الوظيفة: $e');
    }
  }

  // الحصول على وظائف المستخدم
  Future<List<Job>> getMyJobs() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return [];

      final tableExists = await _checkJobsTableExists();
      if (!tableExists) return [];

      final response = await _client
          .from('jobs')
          .select('*')
          .eq('publisher_id', userId)
          .order('created_at', ascending: false);

      return (response as List).map((json) {
        json['publisher_name'] = json['publisher_name'] ?? 'أنت';
        json['publisher_avatar'] = json['publisher_avatar'];
        json['space_name'] = json['space_name'];

        return Job.fromJson(json);
      }).toList();
    } catch (e) {
      return [];
    }
  }

  // حفظ وظيفة
  Future<void> saveJob(String jobId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client.from('saved_jobs').insert({
        'user_id': userId,
        'job_id': jobId,
      });
    } catch (e) {
      throw Exception('فشل في حفظ الوظيفة: $e');
    }
  }

  // إلغاء حفظ وظيفة
  Future<void> unsaveJob(String jobId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('saved_jobs')
          .delete()
          .eq('user_id', userId)
          .eq('job_id', jobId);
    } catch (e) {
      throw Exception('فشل في إلغاء حفظ الوظيفة: $e');
    }
  }

  // الحصول على الوظائف المحفوظة
  Future<List<Job>> getSavedJobs() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return [];

      final tableExists = await _checkJobsTableExists();
      if (!tableExists) return [];

      // محاولة الحصول على الوظائف المحفوظة
      try {
        final response = await _client
            .from('saved_jobs')
            .select('job_id')
            .eq('user_id', userId);

        final jobIds = (response as List).map((item) => item['job_id']).toList();

        if (jobIds.isEmpty) return [];

        final jobsResponse = await _client
            .from('jobs')
            .select('*')
            .inFilter('id', jobIds)
            .order('created_at', ascending: false);

        return (jobsResponse as List).map((json) {
          json['publisher_name'] = json['publisher_name'] ?? 'مستخدم';
          json['publisher_avatar'] = json['publisher_avatar'];
          json['space_name'] = json['space_name'];

          return Job.fromJson(json);
        }).toList();
      } catch (e) {
        return [];
      }
    } catch (e) {
      return [];
    }
  }

  // التقديم على وظيفة
  Future<void> applyToJob({
    required String jobId,
    String? resumeUrl,
    String? coverLetter,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // التحقق من عدم التقديم مسبقاً
      final existingApplication = await _client
          .from('job_applications')
          .select()
          .eq('job_id', jobId)
          .eq('applicant_id', userId)
          .maybeSingle();

      if (existingApplication != null) {
        throw Exception('لقد تقدمت لهذه الوظيفة مسبقاً');
      }

      // إنشاء طلب التقديم
      await _client.from('job_applications').insert({
        'job_id': jobId,
        'applicant_id': userId,
        'resume_url': resumeUrl,
        'cover_letter': coverLetter,
        'status': 'pending',
      });

      // تحديث عدد المتقدمين
      await _client.rpc('increment_applications_count', params: {'job_id': jobId});
    } catch (e) {
      throw Exception('فشل في التقديم للوظيفة: $e');
    }
  }

  // الحصول على طلبات التقديم لوظيفة
  Future<List<JobApplication>> getJobApplications(String jobId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // التحقق من أن المستخدم هو صاحب الوظيفة
      final job = await _client
          .from('jobs')
          .select('publisher_id')
          .eq('id', jobId)
          .single();

      if (job['publisher_id'] != userId) {
        throw Exception('غير مصرح لك بعرض طلبات التقديم');
      }

      final response = await _client
          .from('job_applications')
          .select('''
            *,
            profiles:applicant_id(username, avatar_url)
          ''')
          .eq('job_id', jobId)
          .order('applied_at', ascending: false);

      return (response as List).map((json) {
        json['applicant_name'] = json['profiles']?['username'] ?? '';
        json['applicant_avatar'] = json['profiles']?['avatar_url'];
        
        return JobApplication.fromJson(json);
      }).toList();
    } catch (e) {
      throw Exception('فشل في تحميل طلبات التقديم: $e');
    }
  }

  // تحديث حالة طلب التقديم
  Future<void> updateApplicationStatus(String applicationId, ApplicationStatus status) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('job_applications')
          .update({'status': status.name})
          .eq('id', applicationId);
    } catch (e) {
      throw Exception('فشل في تحديث حالة الطلب: $e');
    }
  }

  // التحقق من التقديم لوظيفة
  Future<bool> hasAppliedToJob(String jobId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return false;

      final response = await _client
          .from('job_applications')
          .select()
          .eq('job_id', jobId)
          .eq('applicant_id', userId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      return false;
    }
  }

  // الحصول على وظائف مقترحة حسب اهتمامات المستخدم
  Future<List<Job>> getRecommendedJobs() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return [];

      // يمكن تحسين هذا لاحقاً بناءً على ملف المستخدم ومساحته
      return await getAllJobs();
    } catch (e) {
      return [];
    }
  }

  // الإبلاغ عن وظيفة
  Future<void> reportJob(String jobId, String reason) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client.from('job_reports').insert({
        'job_id': jobId,
        'reporter_id': userId,
        'reason': reason,
      });
    } catch (e) {
      throw Exception('فشل في الإبلاغ عن الوظيفة: $e');
    }
  }

  // الحصول على إحصائيات الوظائف
  Future<Map<String, int>> getJobStats() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return {};

      final myJobs = await _client
          .from('jobs')
          .select('id')
          .eq('publisher_id', userId);

      final applications = await _client
          .from('job_applications')
          .select('id')
          .eq('applicant_id', userId);

      final savedJobs = await _client
          .from('saved_jobs')
          .select('id')
          .eq('user_id', userId);

      return {
        'my_jobs': (myJobs as List).length,
        'applications': (applications as List).length,
        'saved_jobs': (savedJobs as List).length,
      };
    } catch (e) {
      return {};
    }
  }
}
