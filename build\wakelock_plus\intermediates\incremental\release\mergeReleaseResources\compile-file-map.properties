#Mon Jul 28 21:33:48 WEST 2025
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-anydpi-v21/ic_call_answer.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_answer.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-anydpi-v21/ic_call_answer_low.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_answer_low.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-anydpi-v21/ic_call_answer_video.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_answer_video.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-anydpi-v21/ic_call_answer_video_low.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_answer_video_low.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-anydpi-v21/ic_call_decline.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_decline.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-anydpi-v21/ic_call_decline_low.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-anydpi-v21\\ic_call_decline_low.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-hdpi-v4/ic_call_answer.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_answer.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-hdpi-v4/ic_call_answer_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_answer_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-hdpi-v4/ic_call_answer_video.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_answer_video.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-hdpi-v4/ic_call_answer_video_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_answer_video_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-hdpi-v4/ic_call_decline.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_decline.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-hdpi-v4/ic_call_decline_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\ic_call_decline_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-hdpi-v4/notification_bg_low_normal.9.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_low_normal.9.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-hdpi-v4/notification_bg_low_pressed.9.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_low_pressed.9.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-hdpi-v4/notification_bg_normal.9.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_normal.9.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-hdpi-v4/notification_bg_normal_pressed.9.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_bg_normal_pressed.9.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-hdpi-v4/notification_oversize_large_icon_bg.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notification_oversize_large_icon_bg.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-hdpi-v4/notify_panel_notification_icon_bg.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-hdpi-v4\\notify_panel_notification_icon_bg.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-ldpi-v4/ic_call_answer.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_answer.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-ldpi-v4/ic_call_answer_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_answer_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-ldpi-v4/ic_call_answer_video.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_answer_video.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-ldpi-v4/ic_call_answer_video_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_answer_video_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-ldpi-v4/ic_call_decline.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_decline.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-ldpi-v4/ic_call_decline_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-ldpi-v4\\ic_call_decline_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-mdpi-v4/ic_call_answer.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_answer.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-mdpi-v4/ic_call_answer_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_answer_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-mdpi-v4/ic_call_answer_video.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_answer_video.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-mdpi-v4/ic_call_answer_video_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_answer_video_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-mdpi-v4/ic_call_decline.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_decline.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-mdpi-v4/ic_call_decline_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\ic_call_decline_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-mdpi-v4/notification_bg_low_normal.9.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_low_normal.9.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-mdpi-v4/notification_bg_low_pressed.9.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_low_pressed.9.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-mdpi-v4/notification_bg_normal.9.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_normal.9.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-mdpi-v4/notification_bg_normal_pressed.9.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notification_bg_normal_pressed.9.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-mdpi-v4/notify_panel_notification_icon_bg.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-mdpi-v4\\notify_panel_notification_icon_bg.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-v21/notification_action_background.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-v21\\notification_action_background.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xhdpi-v4/ic_call_answer.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_answer.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xhdpi-v4/ic_call_answer_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_answer_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xhdpi-v4/ic_call_answer_video.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_answer_video.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xhdpi-v4/ic_call_answer_video_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_answer_video_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xhdpi-v4/ic_call_decline.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_decline.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xhdpi-v4/ic_call_decline_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\ic_call_decline_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xhdpi-v4/notification_bg_low_normal.9.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_low_normal.9.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xhdpi-v4/notification_bg_low_pressed.9.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_low_pressed.9.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xhdpi-v4/notification_bg_normal.9.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_normal.9.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xhdpi-v4/notification_bg_normal_pressed.9.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notification_bg_normal_pressed.9.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xhdpi-v4/notify_panel_notification_icon_bg.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xhdpi-v4\\notify_panel_notification_icon_bg.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xxhdpi-v4/ic_call_answer.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_answer.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xxhdpi-v4/ic_call_answer_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_answer_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xxhdpi-v4/ic_call_answer_video.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_answer_video.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xxhdpi-v4/ic_call_answer_video_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_answer_video_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xxhdpi-v4/ic_call_decline.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_decline.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xxhdpi-v4/ic_call_decline_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxhdpi-v4\\ic_call_decline_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xxxhdpi-v4/ic_call_answer.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_answer.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xxxhdpi-v4/ic_call_answer_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_answer_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xxxhdpi-v4/ic_call_answer_video.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_answer_video.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xxxhdpi-v4/ic_call_answer_video_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_answer_video_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xxxhdpi-v4/ic_call_decline.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_decline.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable-xxxhdpi-v4/ic_call_decline_low.png=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable-xxxhdpi-v4\\ic_call_decline_low.png
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable/notification_bg.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_bg.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable/notification_bg_low.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_bg_low.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable/notification_icon_background.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_icon_background.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/drawable/notification_tile_bg.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\drawable\\notification_tile_bg.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/layout-v21/notification_action.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_action.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/layout-v21/notification_action_tombstone.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_action_tombstone.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/layout-v21/notification_template_custom_big.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_template_custom_big.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/layout-v21/notification_template_icon_group.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout-v21\\notification_template_icon_group.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/layout/custom_dialog.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\custom_dialog.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/layout/ime_base_split_test_activity.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\ime_base_split_test_activity.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/layout/ime_secondary_split_test_activity.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\ime_secondary_split_test_activity.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/layout/notification_action.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_action.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/layout/notification_action_tombstone.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_action_tombstone.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/layout/notification_template_custom_big.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_template_custom_big.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/layout/notification_template_icon_group.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_template_icon_group.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/layout/notification_template_part_chronometer.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_template_part_chronometer.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13\:/layout/notification_template_part_time.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\layout\\notification_template_part_time.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-fragment-1.7.1-10\:/anim-v21/fragment_fast_out_extra_slow_in.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim-v21\\fragment_fast_out_extra_slow_in.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-fragment-1.7.1-10\:/anim/fragment_fast_out_extra_slow_in.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\anim\\fragment_fast_out_extra_slow_in.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-fragment-1.7.1-10\:/animator/fragment_close_enter.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_close_enter.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-fragment-1.7.1-10\:/animator/fragment_close_exit.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_close_exit.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-fragment-1.7.1-10\:/animator/fragment_fade_enter.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_fade_enter.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-fragment-1.7.1-10\:/animator/fragment_fade_exit.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_fade_exit.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-fragment-1.7.1-10\:/animator/fragment_open_enter.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_open_enter.xml
dev.fluttercommunity.plus.wakelock.wakelock_plus-fragment-1.7.1-10\:/animator/fragment_open_exit.xml=C\:\\Arzawo\\arzawo\\build\\wakelock_plus\\intermediates\\merged_res\\release\\mergeReleaseResources\\animator\\fragment_open_exit.xml
