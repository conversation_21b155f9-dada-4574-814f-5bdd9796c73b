-- إنشاء جدول إعدادات المستخدم
-- قم بتنفيذ هذه الأوامر في Supabase SQL Editor

-- إنشاء جدول user_settings إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    notify_follow BOOLEAN DEFAULT true,
    notify_chat BOOLEAN DEFAULT true,
    notify_app BOOLEAN DEFAULT true,
    post_visibility TEXT DEFAULT 'public',
    comment_permission TEXT DEFAULT 'everyone',
    show_activity BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- <PERSON>ن<PERSON><PERSON><PERSON> RLS (Row Level Security)
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسة الأمان - المستخدم يمكنه رؤية وتعديل إعداداته فقط
CREATE POLICY "Users can view their own settings" ON user_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own settings" ON user_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own settings" ON user_settings
    FOR UPDATE USING (auth.uid() = user_id);

-- إنشاء trigger لتحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_settings_updated_at 
    BEFORE UPDATE ON user_settings 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- التحقق من إنشاء الجدول
SELECT * FROM user_settings LIMIT 1; 