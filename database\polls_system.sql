-- نظام التصويتات "نبض"

-- جدول التصويتات الرئيسي
CREATE TABLE IF NOT EXISTS polls (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    question TEXT NOT NULL,
    type VARCHAR(20) NOT NULL DEFAULT 'public', -- public, private
    category VARCHAR(20) NOT NULL DEFAULT 'general', -- general, sports, community, religion, entertainment, technology, health, education, business, politics
    duration VARCHAR(20) NOT NULL DEFAULT 'unlimited', -- oneHour, sixHours, twelveHours, oneDay, threeDays, oneWeek, unlimited
    allow_comments BOOLEAN NOT NULL DEFAULT true,
    allow_revote BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NULL
);

-- جدول خيارات التصويت
CREATE TABLE IF NOT EXISTS poll_options (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    text TEXT NOT NULL,
    votes INTEGER NOT NULL DEFAULT 0,
    option_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول أصوات المستخدمين
CREATE TABLE IF NOT EXISTS poll_votes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    option_id UUID NOT NULL REFERENCES poll_options(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- منع التصويت المتكرر من نفس المستخدم على نفس التصويت
    UNIQUE(poll_id, user_id)
);

-- جدول تعليقات التصويتات
CREATE TABLE IF NOT EXISTS poll_comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول مشاركات التصويتات
CREATE TABLE IF NOT EXISTS poll_shares (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    share_type VARCHAR(20) NOT NULL DEFAULT 'general', -- general, group, profile
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_polls_user_id ON polls(user_id);
CREATE INDEX IF NOT EXISTS idx_polls_category ON polls(category);
CREATE INDEX IF NOT EXISTS idx_polls_active ON polls(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_polls_created_at ON polls(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_polls_expires_at ON polls(expires_at) WHERE expires_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_poll_options_poll_id ON poll_options(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_options_order ON poll_options(poll_id, option_order);

CREATE INDEX IF NOT EXISTS idx_poll_votes_poll_id ON poll_votes(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_option_id ON poll_votes(option_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_user_id ON poll_votes(user_id);

CREATE INDEX IF NOT EXISTS idx_poll_comments_poll_id ON poll_comments(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_comments_user_id ON poll_comments(user_id);

-- دالة لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إضافة triggers لتحديث updated_at
DROP TRIGGER IF EXISTS update_polls_updated_at ON polls;
CREATE TRIGGER update_polls_updated_at
    BEFORE UPDATE ON polls
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_poll_votes_updated_at ON poll_votes;
CREATE TRIGGER update_poll_votes_updated_at
    BEFORE UPDATE ON poll_votes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_poll_comments_updated_at ON poll_comments;
CREATE TRIGGER update_poll_comments_updated_at
    BEFORE UPDATE ON poll_comments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- دالة لتحديث عدد الأصوات تلقائياً
CREATE OR REPLACE FUNCTION update_poll_option_votes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE poll_options 
        SET votes = votes + 1 
        WHERE id = NEW.option_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE poll_options 
        SET votes = votes - 1 
        WHERE id = OLD.option_id;
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        -- إذا تم تغيير الخيار
        IF OLD.option_id != NEW.option_id THEN
            UPDATE poll_options 
            SET votes = votes - 1 
            WHERE id = OLD.option_id;
            
            UPDATE poll_options 
            SET votes = votes + 1 
            WHERE id = NEW.option_id;
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- إضافة trigger لتحديث عدد الأصوات
DROP TRIGGER IF EXISTS update_option_votes_trigger ON poll_votes;
CREATE TRIGGER update_option_votes_trigger
    AFTER INSERT OR UPDATE OR DELETE ON poll_votes
    FOR EACH ROW
    EXECUTE FUNCTION update_poll_option_votes();

-- دالة لإنهاء التصويتات المنتهية الصلاحية
CREATE OR REPLACE FUNCTION expire_polls()
RETURNS void AS $$
BEGIN
    UPDATE polls 
    SET is_active = false 
    WHERE expires_at IS NOT NULL 
    AND expires_at < NOW() 
    AND is_active = true;
END;
$$ LANGUAGE plpgsql;

-- تفعيل Row Level Security
ALTER TABLE polls ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_options ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_shares ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للتصويتات
CREATE POLICY "الجميع يمكنهم قراءة التصويتات العامة النشطة" ON polls
    FOR SELECT USING (is_active = true AND type = 'public');

CREATE POLICY "المستخدم يمكنه إنشاء تصويتاته" ON polls
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه تحديث تصويتاته" ON polls
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه حذف تصويتاته" ON polls
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان لخيارات التصويت
CREATE POLICY "الجميع يمكنهم قراءة خيارات التصويتات العامة" ON poll_options
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM polls 
            WHERE id = poll_options.poll_id AND is_active = true AND type = 'public'
        )
    );

CREATE POLICY "صاحب التصويت يمكنه إدارة الخيارات" ON poll_options
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM polls 
            WHERE id = poll_options.poll_id AND user_id = auth.uid()
        )
    );

-- سياسات الأمان للأصوات
CREATE POLICY "المستخدم يمكنه قراءة أصواته" ON poll_votes
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه التصويت" ON poll_votes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه تحديث تصويته" ON poll_votes
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه حذف تصويته" ON poll_votes
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان للتعليقات
CREATE POLICY "الجميع يمكنهم قراءة تعليقات التصويتات العامة" ON poll_comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM polls 
            WHERE id = poll_comments.poll_id AND is_active = true AND type = 'public'
        )
        AND is_active = true
    );

CREATE POLICY "المستخدم يمكنه إضافة تعليقات" ON poll_comments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه تحديث تعليقاته" ON poll_comments
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه حذف تعليقاته" ON poll_comments
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان للمشاركات
CREATE POLICY "المستخدم يمكنه إدارة مشاركاته" ON poll_shares
    FOR ALL USING (auth.uid() = user_id);

-- منح الصلاحيات
GRANT ALL ON polls TO authenticated;
GRANT ALL ON poll_options TO authenticated;
GRANT ALL ON poll_votes TO authenticated;
GRANT ALL ON poll_comments TO authenticated;
GRANT ALL ON poll_shares TO authenticated;

GRANT EXECUTE ON FUNCTION expire_polls() TO authenticated;

-- إدراج بيانات تجريبية (اختيارية)
INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote) 
VALUES 
    ('00000000-0000-0000-0000-000000000000', 'ما هو أفضل نادي كرة قدم في العالم؟', 'public', 'sports', 'oneWeek', true, false),
    ('00000000-0000-0000-0000-000000000000', 'أيهما تفضل للعمل؟', 'public', 'business', 'unlimited', true, true)
ON CONFLICT DO NOTHING;

-- رسالة نجاح
SELECT 'تم إنشاء نظام التصويتات "نبض" بنجاح!' as message;
