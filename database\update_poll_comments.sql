-- تحديث جدول تعليقات التصويتات لإضافة الميزات الجديدة

-- إضافة أعمدة جديدة لجدول poll_comments
ALTER TABLE poll_comments ADD COLUMN IF NOT EXISTS parent_id UUID REFERENCES poll_comments(id) ON DELETE CASCADE;
ALTER TABLE poll_comments ADD COLUMN IF NOT EXISTS likes_count INTEGER DEFAULT 0;
ALTER TABLE poll_comments ADD COLUMN IF NOT EXISTS replies_count INTEGER DEFAULT 0;

-- إنشاء جدول إعجابات التعليقات
CREATE TABLE IF NOT EXISTS poll_comment_likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    comment_id UUID NOT NULL REFERENCES poll_comments(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- منع الإعجاب المتكرر
    UNIQUE(comment_id, user_id)
);

-- إنشاء جدول الإبلاغ عن التعليقات
CREATE TABLE IF NOT EXISTS poll_comment_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    comment_id UUID NOT NULL REFERENCES poll_comments(id) ON DELETE CASCADE,
    reporter_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    reason VARCHAR(50) NOT NULL, -- spam, inappropriate, harassment, etc.
    description TEXT,
    status VARCHAR(20) DEFAULT 'pending', -- pending, reviewed, resolved
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- منع الإبلاغ المتكرر من نفس المستخدم
    UNIQUE(comment_id, reporter_id)
);

-- إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_poll_comments_parent_id ON poll_comments(parent_id);
CREATE INDEX IF NOT EXISTS idx_poll_comment_likes_comment_id ON poll_comment_likes(comment_id);
CREATE INDEX IF NOT EXISTS idx_poll_comment_likes_user_id ON poll_comment_likes(user_id);
CREATE INDEX IF NOT EXISTS idx_poll_comment_reports_comment_id ON poll_comment_reports(comment_id);
CREATE INDEX IF NOT EXISTS idx_poll_comment_reports_status ON poll_comment_reports(status);

-- دالة لتحديث عدد الإعجابات
CREATE OR REPLACE FUNCTION update_comment_likes_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE poll_comments 
        SET likes_count = likes_count + 1 
        WHERE id = NEW.comment_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE poll_comments 
        SET likes_count = likes_count - 1 
        WHERE id = OLD.comment_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث عدد الردود
CREATE OR REPLACE FUNCTION update_comment_replies_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' AND NEW.parent_id IS NOT NULL THEN
        UPDATE poll_comments 
        SET replies_count = replies_count + 1 
        WHERE id = NEW.parent_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' AND OLD.parent_id IS NOT NULL THEN
        UPDATE poll_comments 
        SET replies_count = replies_count - 1 
        WHERE id = OLD.parent_id;
        RETURN OLD;
    ELSIF TG_OP = 'UPDATE' THEN
        -- إذا تم تغيير الـ parent_id
        IF OLD.parent_id IS DISTINCT FROM NEW.parent_id THEN
            -- تقليل العدد من الـ parent القديم
            IF OLD.parent_id IS NOT NULL THEN
                UPDATE poll_comments 
                SET replies_count = replies_count - 1 
                WHERE id = OLD.parent_id;
            END IF;
            
            -- زيادة العدد للـ parent الجديد
            IF NEW.parent_id IS NOT NULL THEN
                UPDATE poll_comments 
                SET replies_count = replies_count + 1 
                WHERE id = NEW.parent_id;
            END IF;
        END IF;
        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- إضافة triggers
DROP TRIGGER IF EXISTS update_comment_likes_count_trigger ON poll_comment_likes;
CREATE TRIGGER update_comment_likes_count_trigger
    AFTER INSERT OR DELETE ON poll_comment_likes
    FOR EACH ROW
    EXECUTE FUNCTION update_comment_likes_count();

DROP TRIGGER IF EXISTS update_comment_replies_count_trigger ON poll_comments;
CREATE TRIGGER update_comment_replies_count_trigger
    AFTER INSERT OR UPDATE OR DELETE ON poll_comments
    FOR EACH ROW
    EXECUTE FUNCTION update_comment_replies_count();

-- تفعيل Row Level Security للجداول الجديدة
ALTER TABLE poll_comment_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_comment_reports ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان لإعجابات التعليقات
DROP POLICY IF EXISTS "comment_likes_select_policy" ON poll_comment_likes;
CREATE POLICY "comment_likes_select_policy" ON poll_comment_likes
    FOR SELECT USING (true); -- يمكن للجميع رؤية الإعجابات

DROP POLICY IF EXISTS "comment_likes_insert_policy" ON poll_comment_likes;
CREATE POLICY "comment_likes_insert_policy" ON poll_comment_likes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "comment_likes_delete_policy" ON poll_comment_likes;
CREATE POLICY "comment_likes_delete_policy" ON poll_comment_likes
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان للإبلاغات
DROP POLICY IF EXISTS "comment_reports_select_policy" ON poll_comment_reports;
CREATE POLICY "comment_reports_select_policy" ON poll_comment_reports
    FOR SELECT USING (auth.uid() = reporter_id); -- المبلغ فقط يمكنه رؤية إبلاغاته

DROP POLICY IF EXISTS "comment_reports_insert_policy" ON poll_comment_reports;
CREATE POLICY "comment_reports_insert_policy" ON poll_comment_reports
    FOR INSERT WITH CHECK (auth.uid() = reporter_id);

-- منح الصلاحيات
GRANT ALL ON poll_comment_likes TO authenticated;
GRANT ALL ON poll_comment_reports TO authenticated;
GRANT EXECUTE ON FUNCTION update_comment_likes_count() TO authenticated;
GRANT EXECUTE ON FUNCTION update_comment_replies_count() TO authenticated;

-- تحديث العدادات الحالية
UPDATE poll_comments 
SET likes_count = (
    SELECT COUNT(*) 
    FROM poll_comment_likes 
    WHERE comment_id = poll_comments.id
);

UPDATE poll_comments 
SET replies_count = (
    SELECT COUNT(*) 
    FROM poll_comments AS replies 
    WHERE replies.parent_id = poll_comments.id 
    AND replies.is_active = true
);

-- دالة للحصول على التعليقات مع الردود
CREATE OR REPLACE FUNCTION get_poll_comments_with_replies(poll_uuid UUID)
RETURNS TABLE(
    comment_id UUID,
    content TEXT,
    author_name TEXT,
    author_avatar TEXT,
    is_verified BOOLEAN,
    likes_count INTEGER,
    replies_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    parent_id UUID,
    is_liked BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pc.id as comment_id,
        pc.content,
        p.full_name as author_name,
        p.avatar_url as author_avatar,
        p.is_verified,
        pc.likes_count,
        pc.replies_count,
        pc.created_at,
        pc.parent_id,
        EXISTS(
            SELECT 1 FROM poll_comment_likes pcl 
            WHERE pcl.comment_id = pc.id 
            AND pcl.user_id = auth.uid()
        ) as is_liked
    FROM poll_comments pc
    JOIN profiles p ON pc.user_id = p.id
    WHERE pc.poll_id = poll_uuid 
    AND pc.is_active = true
    ORDER BY 
        CASE WHEN pc.parent_id IS NULL THEN pc.created_at ELSE NULL END DESC,
        pc.parent_id NULLS FIRST,
        pc.created_at ASC;
END;
$$ LANGUAGE plpgsql;

-- منح صلاحية تنفيذ الدالة
GRANT EXECUTE ON FUNCTION get_poll_comments_with_replies(UUID) TO authenticated;

-- رسالة نجاح
SELECT 'تم تحديث نظام تعليقات التصويتات بنجاح!' as message;
