-- إنشاء جدول منشورات المساحات وجميع الجداول المطلوبة - النسخة المحدثة
-- Create space_posts table and all required tables - Updated Version

-- 1. إن<PERSON>اء جدول المساحات إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS public.spaces (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    owner_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    owner_name TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT NOT NULL,
    goal TEXT,
    category TEXT NOT NULL DEFAULT 'other',
    profession TEXT,
    status TEXT NOT NULL DEFAULT 'active',
    privacy TEXT NOT NULL DEFAULT 'public',
    
    -- الصور
    cover_image TEXT,
    profile_image TEXT,
    
    -- معلومات التواصل
    phone_number TEXT,
    email TEXT,
    website TEXT,
    social_links JSONB DEFAULT '{}',
    
    -- الإحصائيات
    followers_count INTEGER DEFAULT 0,
    posts_count INTEGER DEFAULT 0,
    views_count INTEGER DEFAULT 0,
    
    -- التواريخ
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. إنشاء جدول متابعي المساحات
CREATE TABLE IF NOT EXISTS public.space_followers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    space_id UUID NOT NULL REFERENCES public.spaces(id) ON DELETE CASCADE,
    follower_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    followed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- منع المتابعة المكررة
    UNIQUE(space_id, follower_id)
);

-- 3. إنشاء جدول منشورات المساحات
CREATE TABLE IF NOT EXISTS public.space_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    space_id UUID NOT NULL REFERENCES public.spaces(id) ON DELETE CASCADE,
    author_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    author_name TEXT NOT NULL,
    content TEXT NOT NULL,
    media_urls TEXT[] DEFAULT '{}',
    link_url TEXT,
    link_title TEXT,
    link_description TEXT,
    link_image TEXT,
    
    -- الإحصائيات
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,
    views_count INTEGER DEFAULT 0,
    
    -- التواريخ
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. إنشاء جدول تفاعلات منشورات المساحات
CREATE TABLE IF NOT EXISTS public.space_post_reactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES public.space_posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    reaction_type TEXT NOT NULL DEFAULT 'like',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- منع التفاعل المكرر
    UNIQUE(post_id, user_id)
);

-- 5. إنشاء جدول تعليقات منشورات المساحات
CREATE TABLE IF NOT EXISTS public.space_post_comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES public.space_posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES public.space_post_comments(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    
    -- الإحصائيات
    likes_count INTEGER DEFAULT 0,
    replies_count INTEGER DEFAULT 0,
    
    -- التواريخ
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_spaces_owner_id ON public.spaces(owner_id);
CREATE INDEX IF NOT EXISTS idx_spaces_category ON public.spaces(category);
CREATE INDEX IF NOT EXISTS idx_spaces_status ON public.spaces(status);
CREATE INDEX IF NOT EXISTS idx_spaces_privacy ON public.spaces(privacy);
CREATE INDEX IF NOT EXISTS idx_spaces_created_at ON public.spaces(created_at);

CREATE INDEX IF NOT EXISTS idx_space_followers_space_id ON public.space_followers(space_id);
CREATE INDEX IF NOT EXISTS idx_space_followers_follower_id ON public.space_followers(follower_id);
CREATE INDEX IF NOT EXISTS idx_space_followers_followed_at ON public.space_followers(followed_at);

CREATE INDEX IF NOT EXISTS idx_space_posts_space_id ON public.space_posts(space_id);
CREATE INDEX IF NOT EXISTS idx_space_posts_author_id ON public.space_posts(author_id);
CREATE INDEX IF NOT EXISTS idx_space_posts_created_at ON public.space_posts(created_at);
CREATE INDEX IF NOT EXISTS idx_space_posts_media_urls ON public.space_posts USING GIN (media_urls);

CREATE INDEX IF NOT EXISTS idx_space_post_reactions_post_id ON public.space_post_reactions(post_id);
CREATE INDEX IF NOT EXISTS idx_space_post_reactions_user_id ON public.space_post_reactions(user_id);

CREATE INDEX IF NOT EXISTS idx_space_post_comments_post_id ON public.space_post_comments(post_id);
CREATE INDEX IF NOT EXISTS idx_space_post_comments_user_id ON public.space_post_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_space_post_comments_parent_id ON public.space_post_comments(parent_id);

-- 7. إنشاء الدوال المساعدة
CREATE OR REPLACE FUNCTION increment_space_followers(space_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.spaces 
    SET followers_count = followers_count + 1,
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION decrement_space_followers(space_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.spaces 
    SET followers_count = GREATEST(followers_count - 1, 0),
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION increment_space_views(space_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.spaces 
    SET views_count = views_count + 1,
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION increment_space_posts(space_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.spaces 
    SET posts_count = posts_count + 1,
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION decrement_space_posts(space_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.spaces 
    SET posts_count = GREATEST(posts_count - 1, 0),
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql;

-- 8. إنشاء دالة تحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 9. إنشاء المحفزات
CREATE TRIGGER update_spaces_updated_at 
    BEFORE UPDATE ON public.spaces 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_space_posts_updated_at 
    BEFORE UPDATE ON public.space_posts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_space_post_comments_updated_at 
    BEFORE UPDATE ON public.space_post_comments 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 10. إعداد Row Level Security (RLS)
ALTER TABLE public.spaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.space_followers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.space_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.space_post_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.space_post_comments ENABLE ROW LEVEL SECURITY;

-- 11. حذف السياسات الموجودة لتجنب التكرار
DROP POLICY IF EXISTS "spaces_select_policy" ON public.spaces;
DROP POLICY IF EXISTS "spaces_insert_policy" ON public.spaces;
DROP POLICY IF EXISTS "spaces_update_policy" ON public.spaces;
DROP POLICY IF EXISTS "spaces_delete_policy" ON public.spaces;

DROP POLICY IF EXISTS "space_followers_select_policy" ON public.space_followers;
DROP POLICY IF EXISTS "space_followers_insert_policy" ON public.space_followers;
DROP POLICY IF EXISTS "space_followers_delete_policy" ON public.space_followers;

DROP POLICY IF EXISTS "space_posts_select_policy" ON public.space_posts;
DROP POLICY IF EXISTS "space_posts_insert_policy" ON public.space_posts;
DROP POLICY IF EXISTS "space_posts_update_policy" ON public.space_posts;
DROP POLICY IF EXISTS "space_posts_delete_policy" ON public.space_posts;

-- 12. إنشاء سياسات الأمان الجديدة
-- سياسات المساحات
CREATE POLICY "spaces_select_policy" ON public.spaces
    FOR SELECT USING (
        privacy = 'public' OR
        auth.uid() = owner_id OR
        EXISTS (
            SELECT 1 FROM public.space_followers
            WHERE space_id = id AND follower_id = auth.uid()
        )
    );

CREATE POLICY "spaces_insert_policy" ON public.spaces
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "spaces_update_policy" ON public.spaces
    FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "spaces_delete_policy" ON public.spaces
    FOR DELETE USING (auth.uid() = owner_id);

-- سياسات متابعي المساحات
CREATE POLICY "space_followers_select_policy" ON public.space_followers
    FOR SELECT USING (true);

CREATE POLICY "space_followers_insert_policy" ON public.space_followers
    FOR INSERT WITH CHECK (auth.uid() = follower_id);

CREATE POLICY "space_followers_delete_policy" ON public.space_followers
    FOR DELETE USING (auth.uid() = follower_id);

-- سياسات منشورات المساحات
CREATE POLICY "space_posts_select_policy" ON public.space_posts
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.spaces 
            WHERE id = space_id 
            AND (privacy = 'public' OR auth.uid() = owner_id)
        )
    );

CREATE POLICY "space_posts_insert_policy" ON public.space_posts
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.spaces 
            WHERE id = space_id 
            AND auth.uid() = owner_id
        )
    );

CREATE POLICY "space_posts_update_policy" ON public.space_posts
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM public.spaces 
            WHERE id = space_id 
            AND auth.uid() = owner_id
        ) OR auth.uid() = author_id
    );

CREATE POLICY "space_posts_delete_policy" ON public.space_posts
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.spaces 
            WHERE id = space_id 
            AND auth.uid() = owner_id
        ) OR auth.uid() = author_id
    );

-- 13. إدراج بيانات تجريبية للمساحات (إذا لم تكن موجودة)
INSERT INTO public.spaces (
    owner_id,
    owner_name,
    name,
    description,
    goal,
    category,
    profession,
    privacy,
    followers_count,
    posts_count,
    views_count
) VALUES 
(
    '62fb0b2e-8cdd-4226-878f-3eec5131952c',
    'المستخدم الموثق',
    'شركة التقنية المتقدمة',
    'نحن شركة رائدة في مجال تطوير التطبيقات والحلول التقنية المبتكرة. نسعى لتقديم أفضل الخدمات لعملائنا.',
    'تطوير حلول تقنية مبتكرة تساعد الشركات على النمو والتطور',
    'technology',
    'مطور تطبيقات',
    'public',
    150,
    25,
    1500
),
(
    '5c3675a3-ab2b-4248-bdbf-8bf0dc1de485',
    'مستخدم آخر',
    'مطعم الأصالة',
    'مطعم يقدم أشهى الأطباق العربية الأصيلة بطعم لا يُنسى. نستخدم أجود المكونات الطبيعية.',
    'تقديم تجربة طعام استثنائية تعكس التراث العربي الأصيل',
    'food',
    'طاهي محترف',
    'public',
    89,
    12,
    890
)
ON CONFLICT DO NOTHING;

-- 14. رسالة نجاح
SELECT 'تم إنشاء جميع جداول المساحات بنجاح!' as result; 