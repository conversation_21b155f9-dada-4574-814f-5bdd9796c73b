import 'package:flutter/material.dart';
import '../models/user_stats.dart';
import '../utils/number_format.dart';

class VerificationProgressWidget extends StatelessWidget {
  final UserStats stats;
  final VoidCallback? onRequestVerification;

  const VerificationProgressWidget({
    super.key,
    required this.stats,
    this.onRequestVerification,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          Row(
            children: [
              Icon(Icons.verified, color: Colors.blue[600], size: 24),
              const SizedBox(width: 8),
              Text(
                'توثيق الحساب',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'احصل على علامة التوثيق الزرقاء',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          // شرط المتابعين
          _buildRequirementCard(
            context,
            icon: Icons.people,
            title: 'عدد المتابعين',
            current: stats.followersCount,
            required: 50000,
            progress: stats.followersProgress,
            statusMessage: stats.followersStatusMessage,
            isMet: stats.meetsFollowersRequirement,
            color: Colors.blue,
          ),

          const SizedBox(height: 16),

          // شرط المشاهدات
          _buildRequirementCard(
            context,
            icon: Icons.visibility,
            title: 'مجموع المشاهدات',
            current: stats.totalViews,
            required: 100000,
            progress: stats.viewsProgress,
            statusMessage: stats.viewsStatusMessage,
            isMet: stats.meetsViewsRequirement,
            color: Colors.green,
          ),

          const SizedBox(height: 24),

          // زر تقديم الطلب
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: stats.meetsAllRequirements ? onRequestVerification : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: stats.meetsAllRequirements ? Colors.blue[600] : Colors.grey[300],
                foregroundColor: stats.meetsAllRequirements ? Colors.white : Colors.grey[600],
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: stats.meetsAllRequirements ? 2 : 0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    stats.meetsAllRequirements ? Icons.send : Icons.lock,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    stats.meetsAllRequirements 
                        ? 'تقديم طلب التوثيق'
                        : 'تحقق من الشروط أولاً',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),

          if (!stats.meetsAllRequirements) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.orange[600], size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'يجب استيفاء جميع الشروط قبل إمكانية تقديم طلب التوثيق',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.orange[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRequirementCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required int current,
    required int required,
    required double progress,
    required String statusMessage,
    required bool isMet,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isMet ? color.withValues(alpha: 0.1) : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isMet ? color : Colors.grey[300]!,
          width: isMet ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: isMet ? color : Colors.grey[600],
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: isMet ? color : Colors.grey[700],
                  ),
                ),
              ),
              if (isMet)
                Icon(
                  Icons.check_circle,
                  color: color,
                  size: 20,
                ),
            ],
          ),
          const SizedBox(height: 12),
          
          // عرض الأرقام
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${NumberFormatUtil.prettyCount(current)} / ${NumberFormatUtil.prettyCount(required)}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: isMet ? color : Colors.grey[700],
                ),
              ),
              Text(
                '${(progress * 100).toInt()}%',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: isMet ? color : Colors.grey[600],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          
          // شريط التقدم
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(color),
              minHeight: 8,
            ),
          ),
          const SizedBox(height: 8),
          
          // رسالة الحالة
          Text(
            statusMessage,
            style: TextStyle(
              fontSize: 12,
              color: isMet ? color : Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }
} 