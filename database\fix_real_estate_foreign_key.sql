-- إصلاح العلاقة الخارجية لجدول العقارات

-- إضافة العلاقة الخارجية إذا لم تكن موجودة
DO $$
BEGIN
    -- التحقق من وجود العلاقة الخارجية
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'real_estate_properties_user_id_fkey'
        AND table_name = 'real_estate_properties'
    ) THEN
        -- إضافة العلاقة الخارجية
        ALTER TABLE real_estate_properties 
        ADD CONSTRAINT real_estate_properties_user_id_fkey 
        FOREIGN KEY (user_id) REFERENCES profiles(id) ON DELETE CASCADE;
        
        RAISE NOTICE 'تم إضافة العلاقة الخارجية بنجاح';
    ELSE
        RAISE NOTICE 'العلاقة الخارجية موجودة بالفعل';
    END IF;
END $$;

-- حذف السياسات الموجودة وإعادة إنشاؤها
DROP POLICY IF EXISTS "الجميع يمكنهم قراءة العقارات النشطة" ON real_estate_properties;
DROP POLICY IF EXISTS "المستخدم يمكنه إنشاء عقاراته" ON real_estate_properties;
DROP POLICY IF EXISTS "المستخدم يمكنه تحديث عقاراته" ON real_estate_properties;
DROP POLICY IF EXISTS "المستخدم يمكنه حذف عقاراته" ON real_estate_properties;

DROP POLICY IF EXISTS "الجميع يمكنهم قراءة صور العقارات النشطة" ON property_images;
DROP POLICY IF EXISTS "صاحب العقار يمكنه إدارة الصور" ON property_images;

DROP POLICY IF EXISTS "المستخدم يمكنه قراءة مفضلته" ON property_favorites;
DROP POLICY IF EXISTS "المستخدم يمكنه إدارة مفضلته" ON property_favorites;

DROP POLICY IF EXISTS "المستخدم يمكنه الإبلاغ" ON property_reports;

-- إعادة إنشاء السياسات
CREATE POLICY "الجميع يمكنهم قراءة العقارات النشطة" ON real_estate_properties
    FOR SELECT USING (is_active = true);

CREATE POLICY "المستخدم يمكنه إنشاء عقاراته" ON real_estate_properties
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه تحديث عقاراته" ON real_estate_properties
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه حذف عقاراته" ON real_estate_properties
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان لصور العقارات
CREATE POLICY "الجميع يمكنهم قراءة صور العقارات النشطة" ON property_images
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM real_estate_properties 
            WHERE id = property_images.property_id AND is_active = true
        )
    );

CREATE POLICY "صاحب العقار يمكنه إدارة الصور" ON property_images
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM real_estate_properties 
            WHERE id = property_images.property_id AND user_id = auth.uid()
        )
    );

-- سياسات الأمان للمفضلة
CREATE POLICY "المستخدم يمكنه قراءة مفضلته" ON property_favorites
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه إدارة مفضلته" ON property_favorites
    FOR ALL USING (auth.uid() = user_id);

-- سياسات الأمان للإبلاغات
CREATE POLICY "المستخدم يمكنه الإبلاغ" ON property_reports
    FOR INSERT WITH CHECK (auth.uid() = reporter_id);

-- تفعيل Row Level Security
ALTER TABLE real_estate_properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_reports ENABLE ROW LEVEL SECURITY;

-- منح الصلاحيات
GRANT ALL ON real_estate_properties TO authenticated;
GRANT ALL ON property_images TO authenticated;
GRANT ALL ON property_favorites TO authenticated;
GRANT ALL ON property_reports TO authenticated;

-- رسالة نجاح
SELECT 'تم إصلاح جداول العقارات بنجاح!' as message;
