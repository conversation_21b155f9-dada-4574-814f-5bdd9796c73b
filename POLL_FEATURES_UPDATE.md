# 🗳️ تحديث ميزات نظام التصويتات "نبض"

## 🎯 **الميزات الجديدة المضافة:**

### ✅ **1. إعدادات التصويتات:**
- **حذف التصويت** - يمكن للمؤلف حذف تصويته نهائياً
- **تفعيل/إلغاء التصويت** - إيقاف أو تشغيل التصويت
- **إعدادات التعليقات** - السماح أو منع التعليقات
- **إعدادات إعادة التصويت** - السماح بتغيير التصويت
- **إحصائيات مفصلة** - عدد الأصوات والتعليقات والمشاركات

### ✅ **2. إعادة التصويت:**
- **إلغاء التصويت الحالي** - حذف التصويت السابق
- **اختيار خيار جديد** - إمكانية التصويت مرة أخرى
- **تأكيد الإجراء** - حوار تأكيد قبل الإلغاء

### ✅ **3. نظام التعليقات المتقدم:**
- **تعليقات التصويتات** - نفس نظام المنشورات العادية
- **الردود على التعليقات** - إمكانية الرد على أي تعليق
- **الإعجاب بالتعليقات** - نظام الإعجابات
- **حذف التعليقات** - حذف التعليقات الخاصة
- **الإبلاغ عن التعليقات** - إبلاغ عن المحتوى غير المناسب

---

## 📁 **الملفات الجديدة:**

### **🎛️ صفحات جديدة:**
1. **`lib/pages/poll_settings_page.dart`** - صفحة إعدادات التصويت
2. **`lib/pages/poll_comments_page.dart`** - صفحة تعليقات التصويت

### **🔧 خدمات محدثة:**
3. **`lib/services/comment_service.dart`** - خدمة التعليقات للتصويتات
4. **`lib/services/poll_service.dart`** - محدث بالوظائف الجديدة

### **🎨 واجهات محدثة:**
5. **`lib/widgets/poll_card_updated.dart`** - بطاقة التصويت المحدثة

### **🗄️ قاعدة البيانات:**
6. **`database/update_poll_comments.sql`** - تحديث جداول التعليقات

---

## 🚀 **خطوات التطبيق:**

### **الخطوة 1: تحديث قاعدة البيانات**
```sql
\i database/update_poll_comments.sql
```

### **الخطوة 2: استبدال الملفات**
```bash
# استبدال بطاقة التصويت القديمة
mv lib/widgets/poll_card.dart lib/widgets/poll_card_old.dart
mv lib/widgets/poll_card_updated.dart lib/widgets/poll_card.dart
```

### **الخطوة 3: إضافة الاستيرادات**
في ملف `lib/pages/pulse_page.dart` أضف:
```dart
import '../pages/poll_settings_page.dart';
import '../pages/poll_comments_page.dart';
import '../services/comment_service.dart';
```

---

## 🎨 **الواجهات الجديدة:**

### **📊 صفحة إعدادات التصويت:**
- **معلومات التصويت** - عرض تفاصيل التصويت
- **إعدادات التحكم** - تفعيل/إلغاء الميزات
- **إحصائيات مفصلة** - أرقام وبيانات التفاعل
- **إجراءات خطيرة** - حذف التصويت مع تأكيد

### **💬 صفحة التعليقات:**
- **عرض التعليقات** - قائمة التعليقات مع الردود
- **إضافة تعليق** - حقل إدخال مع زر إرسال
- **الرد على التعليقات** - حوار الرد
- **التفاعل** - إعجاب وإبلاغ وحذف

### **🗳️ بطاقة التصويت المحدثة:**
- **قائمة إعدادات** - للمؤلف فقط (⋮)
- **زر التعليقات** - فتح صفحة التعليقات
- **زر إعادة التصويت** - إلغاء التصويت الحالي
- **زر المشاركة** - مشاركة التصويت

---

## 🔧 **الوظائف الجديدة:**

### **في PollService:**
```dart
// إعادة التصويت
Future<void> revote(String pollId)

// تحديث حالة التصويت
Future<void> updatePollStatus(String pollId, bool isActive)

// تحديث إعدادات التصويت
Future<void> updatePollSettings(String pollId, {bool? allowComments, bool? allowRevote})

// حذف التصويت
Future<void> deletePoll(String pollId)

// الحصول على تصويت محدد
Future<Poll?> getPollById(String pollId)
```

### **في CommentService:**
```dart
// الحصول على تعليقات التصويت
Future<List<Comment>> getPollComments(String pollId)

// إضافة تعليق
Future<void> addPollComment({required String pollId, required String content, String? parentId})

// حذف تعليق
Future<void> deleteComment(String commentId)

// الإعجاب بالتعليق
Future<void> toggleCommentLike(String commentId)

// الإبلاغ عن تعليق
Future<void> reportComment(String commentId, String reason)
```

---

## 🗄️ **تحديثات قاعدة البيانات:**

### **جداول جديدة:**
- **`poll_comment_likes`** - إعجابات التعليقات
- **`poll_comment_reports`** - الإبلاغ عن التعليقات

### **أعمدة جديدة في poll_comments:**
- **`parent_id`** - للردود على التعليقات
- **`likes_count`** - عدد الإعجابات
- **`replies_count`** - عدد الردود

### **دوال جديدة:**
- **`update_comment_likes_count()`** - تحديث عدد الإعجابات
- **`update_comment_replies_count()`** - تحديث عدد الردود
- **`get_poll_comments_with_replies()`** - جلب التعليقات مع الردود

---

## 🎯 **كيفية الاستخدام:**

### **للمؤلف:**
1. **النقر على ⋮** في بطاقة التصويت
2. **اختيار "إعدادات التصويت"** لفتح صفحة الإعدادات
3. **تعديل الإعدادات** حسب الحاجة
4. **حذف التصويت** إذا لزم الأمر

### **للمستخدمين:**
1. **النقر على "تعليق"** لفتح صفحة التعليقات
2. **كتابة تعليق** في الحقل السفلي
3. **الرد على التعليقات** بالنقر على زر الرد
4. **الإعجاب** بالتعليقات المفيدة

### **إعادة التصويت:**
1. **النقر على "إعادة تصويت"** (يظهر فقط إذا كان مسموحاً)
2. **تأكيد الإجراء** في الحوار
3. **اختيار خيار جديد** بعد إلغاء التصويت السابق

---

## ✅ **المميزات المضمونة:**

- ✅ **واجهة متسقة** مع باقي التطبيق
- ✅ **أمان كامل** مع Row Level Security
- ✅ **أداء محسن** مع الفهارس والـ triggers
- ✅ **تجربة مستخدم ممتازة** مع الحوارات والتأكيدات
- ✅ **نظام تعليقات متقدم** مثل المنشورات العادية
- ✅ **إدارة كاملة** للتصويتات من قبل المؤلفين

---

## 🎉 **النتيجة النهائية:**

**نظام تصويتات "نبض" متكامل مع جميع الميزات المطلوبة:**
- 🗳️ **إنشاء وإدارة التصويتات**
- ⚙️ **إعدادات شاملة للمؤلفين**
- 🔄 **إعادة التصويت المرنة**
- 💬 **نظام تعليقات متقدم**
- 📊 **إحصائيات مفصلة**
- 🔒 **أمان وحماية كاملة**

**جميع الميزات جاهزة للاستخدام فوراً!**
