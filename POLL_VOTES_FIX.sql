-- إصلا<PERSON> شامل لمشاكل التصويت في قسم النبض
-- Comprehensive fix for poll voting issues

-- 1. إضافة فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_poll_votes_poll_id ON poll_votes(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_user_id ON poll_votes(user_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_option_id ON poll_votes(option_id);
CREATE INDEX IF NOT EXISTS idx_poll_options_poll_id ON poll_options(poll_id);

-- 2. إن<PERSON>اء دالة لتحديث عدد الأصوات لخيار معين
CREATE OR REPLACE FUNCTION update_option_votes(p_option_id UUID)
RETURNS VOID AS $$
DECLARE
    votes_count INTEGER;
BEGIN
    -- حساب عدد الأصوات الفعلي من جدول poll_votes
    SELECT COUNT(*) INTO votes_count
    FROM poll_votes
    WHERE option_id = p_option_id;
    
    -- تحديث عدد الأصوات في جدول poll_options
    UPDATE poll_options
    SET votes = votes_count
    WHERE id = p_option_id;
    
    RAISE NOTICE 'تم تحديث عدد الأصوات للخيار %: %', p_option_id, votes_count;
END;
$$ LANGUAGE plpgsql;

-- 3. إنشاء دالة لتحديث جميع خيارات التصويت
CREATE OR REPLACE FUNCTION update_all_poll_votes(p_poll_id UUID)
RETURNS VOID AS $$
DECLARE
    option_record RECORD;
BEGIN
    -- تحديث عدد الأصوات لجميع خيارات التصويت
    FOR option_record IN 
        SELECT id FROM poll_options WHERE poll_id = p_poll_id
    LOOP
        PERFORM update_option_votes(option_record.id);
    END LOOP;
    
    RAISE NOTICE 'تم تحديث جميع الأصوات للتصويت %', p_poll_id;
END;
$$ LANGUAGE plpgsql;

-- 4. إنشاء trigger محسن لتحديث الأصوات تلقائياً
CREATE OR REPLACE FUNCTION trigger_update_votes()
RETURNS TRIGGER AS $$
BEGIN
    -- عند إضافة تصويت جديد
    IF TG_OP = 'INSERT' THEN
        PERFORM update_option_votes(NEW.option_id);
        RETURN NEW;
    END IF;
    
    -- عند حذف تصويت
    IF TG_OP = 'DELETE' THEN
        PERFORM update_option_votes(OLD.option_id);
        RETURN OLD;
    END IF;
    
    -- عند تحديث تصويت (تغيير الخيار)
    IF TG_OP = 'UPDATE' THEN
        -- إذا تغير الخيار، حدث كلا الخيارين
        IF OLD.option_id != NEW.option_id THEN
            PERFORM update_option_votes(OLD.option_id);
        END IF;
        PERFORM update_option_votes(NEW.option_id);
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 5. إنشاء trigger
DROP TRIGGER IF EXISTS poll_votes_trigger ON poll_votes;
CREATE TRIGGER poll_votes_trigger
    AFTER INSERT OR UPDATE OR DELETE ON poll_votes
    FOR EACH ROW
    EXECUTE FUNCTION trigger_update_votes();

-- 6. إنشاء دالة لإعادة التصويت
CREATE OR REPLACE FUNCTION revote_poll(p_poll_id UUID, p_user_id UUID)
RETURNS VOID AS $$
DECLARE
    current_vote RECORD;
BEGIN
    -- البحث عن التصويت الحالي للمستخدم
    SELECT option_id INTO current_vote
    FROM poll_votes
    WHERE poll_id = p_poll_id AND user_id = p_user_id
    LIMIT 1;
    
    -- إذا وجد تصويت، احذفه
    IF current_vote.option_id IS NOT NULL THEN
        DELETE FROM poll_votes
        WHERE poll_id = p_poll_id AND user_id = p_user_id;
        
        -- تحديث عدد الأصوات للخيار الذي تم حذف التصويت منه
        PERFORM update_option_votes(current_vote.option_id);
        
        RAISE NOTICE 'تم حذف تصويت المستخدم % من الخيار %', p_user_id, current_vote.option_id;
    END IF;
    
    -- تحديث جميع خيارات التصويت للتأكد من الدقة
    PERFORM update_all_poll_votes(p_poll_id);
    
    RAISE NOTICE 'تم إعادة التصويت بنجاح للتصويت %', p_poll_id;
END;
$$ LANGUAGE plpgsql;

-- 7. تحديث الأصوات الموجودة
DO $$
DECLARE
    poll_record RECORD;
BEGIN
    -- تحديث عدد الأصوات لجميع التصويتات
    FOR poll_record IN 
        SELECT DISTINCT poll_id FROM poll_votes
    LOOP
        PERFORM update_all_poll_votes(poll_record.poll_id);
    END LOOP;
    
    RAISE NOTICE 'تم تحديث جميع الأصوات الموجودة';
END $$;

-- 8. إنشاء دالة للحصول على إحصائيات التصويت
CREATE OR REPLACE FUNCTION get_poll_stats(p_poll_id UUID)
RETURNS TABLE(
    total_votes INTEGER,
    options_count INTEGER,
    most_voted_option_id UUID,
    most_voted_count INTEGER
) AS $$
DECLARE
    total_votes_count INTEGER;
    options_count_val INTEGER;
    most_voted_option UUID;
    most_voted_count_val INTEGER;
BEGIN
    -- حساب إجمالي الأصوات
    SELECT COUNT(*) INTO total_votes_count
    FROM poll_votes
    WHERE poll_id = p_poll_id;
    
    -- حساب عدد الخيارات
    SELECT COUNT(*) INTO options_count_val
    FROM poll_options
    WHERE poll_id = p_poll_id;
    
    -- الحصول على الخيار الأكثر تصويتاً
    SELECT option_id, votes INTO most_voted_option, most_voted_count_val
    FROM poll_options
    WHERE poll_id = p_poll_id
    ORDER BY votes DESC
    LIMIT 1;
    
    RETURN QUERY SELECT 
        total_votes_count,
        options_count_val,
        most_voted_option,
        most_voted_count_val;
END;
$$ LANGUAGE plpgsql;

-- 9. التحقق من النتائج
SELECT 
    'Poll System Fixed' as status,
    COUNT(*) as total_polls,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_polls
FROM polls;

-- 10. عرض إحصائيات التصويتات
SELECT 
    p.id as poll_id,
    p.question,
    (SELECT total_votes FROM get_poll_stats(p.id)) as total_votes,
    (SELECT options_count FROM get_poll_stats(p.id)) as options_count,
    (SELECT most_voted_count FROM get_poll_stats(p.id)) as most_voted_count
FROM polls p
WHERE p.is_active = true
ORDER BY p.created_at DESC
LIMIT 10; 