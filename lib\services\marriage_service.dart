import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/marriage_profile.dart';

class MarriageService {
  final SupabaseClient _client = Supabase.instance.client;

  // الحصول على جميع ملفات الزواج
  Future<List<MarriageProfile>> getAllProfiles({
    Gender? gender,
    int? minAge,
    int? maxAge,
    String? city,
    String? country,
    MaritalStatus? maritalStatus,
    MarriageGoal? goal,
  }) async {
    try {
      final tableExists = await _checkMarriageTableExists();
      if (!tableExists) return [];

      var query = _client
          .from('marriage_profiles')
          .select('*')
          .eq('is_active', true);

      // فلترة حسب الجنس
      if (gender != null) {
        query = query.eq('gender', gender.name);
      }

      // فلترة حسب العمر
      if (minAge != null) {
        query = query.gte('age', minAge);
      }
      if (maxAge != null) {
        query = query.lte('age', maxAge);
      }

      // فلترة حسب المدينة
      if (city != null && city.isNotEmpty) {
        query = query.ilike('city', '%$city%');
      }

      // فلترة حسب الدولة
      if (country != null && country.isNotEmpty) {
        query = query.ilike('country', '%$country%');
      }

      // فلترة حسب الحالة الاجتماعية
      if (maritalStatus != null) {
        query = query.eq('marital_status', maritalStatus.name);
      }

      // فلترة حسب الهدف
      if (goal != null) {
        query = query.eq('goal', goal.name);
      }

      final response = await query.order('created_at', ascending: false);
      
      return (response as List).map((json) => MarriageProfile.fromJson(json)).toList();
    } catch (e) {
      return [];
    }
  }

  // التحقق من وجود جدول الزواج
  Future<bool> _checkMarriageTableExists() async {
    try {
      await _client.from('marriage_profiles').select('id').limit(1);
      return true;
    } catch (e) {
      return false;
    }
  }

  // الحصول على ملف زواج محدد
  Future<MarriageProfile?> getProfile(String profileId) async {
    try {
      final response = await _client
          .from('marriage_profiles')
          .select('*')
          .eq('id', profileId)
          .single();

      return MarriageProfile.fromJson(response);
    } catch (e) {
      return null;
    }
  }

  // الحصول على ملف المستخدم الحالي
  Future<MarriageProfile?> getMyProfile() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return null;

      final response = await _client
          .from('marriage_profiles')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();

      return response != null ? MarriageProfile.fromJson(response) : null;
    } catch (e) {
      return null;
    }
  }

  // إنشاء ملف زواج جديد
  Future<String> createProfile(MarriageProfile profile) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // التحقق من عدم وجود ملف مسبق
      final existingProfile = await getMyProfile();
      if (existingProfile != null) {
        throw Exception('لديك ملف زواج بالفعل. يمكنك تعديله بدلاً من إنشاء ملف جديد');
      }

      final profileData = profile.toJson();
      profileData['user_id'] = userId;
      profileData.remove('id');

      final response = await _client
          .from('marriage_profiles')
          .insert(profileData)
          .select()
          .single();

      return response['id'];
    } catch (e) {
      throw Exception('فشل في إنشاء الملف: $e');
    }
  }

  // تحديث ملف الزواج
  Future<void> updateProfile(String profileId, MarriageProfile profile) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final profileData = profile.toJson();
      profileData.remove('id');
      profileData.remove('user_id');
      profileData.remove('created_at');
      profileData['updated_at'] = DateTime.now().toIso8601String();

      await _client
          .from('marriage_profiles')
          .update(profileData)
          .eq('id', profileId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في تحديث الملف: $e');
    }
  }

  // حذف ملف الزواج
  Future<void> deleteProfile(String profileId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('marriage_profiles')
          .delete()
          .eq('id', profileId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في حذف الملف: $e');
    }
  }

  // إرسال طلب تواصل
  Future<void> sendContactRequest({
    required String receiverId,
    String? message,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // التحقق من الحد اليومي
      final canSend = await _client.rpc('check_daily_limit', params: {'p_user_id': userId});
      if (!canSend) {
        throw Exception('لقد تجاوزت الحد اليومي للطلبات (3 طلبات). حاول غداً');
      }

      // التحقق من عدم إرسال طلب مسبق
      final existingRequest = await _client
          .from('contact_requests')
          .select()
          .eq('sender_id', userId)
          .eq('receiver_id', receiverId)
          .maybeSingle();

      if (existingRequest != null) {
        throw Exception('لقد أرسلت طلب تواصل لهذا الشخص مسبقاً');
      }

      await _client.from('contact_requests').insert({
        'sender_id': userId,
        'receiver_id': receiverId,
        'message': message,
      });
    } catch (e) {
      throw Exception('فشل في إرسال طلب التواصل: $e');
    }
  }

  // الرد على طلب تواصل
  Future<void> respondToContactRequest(String requestId, ContactRequestStatus status) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('contact_requests')
          .update({
            'status': status.name,
            'responded_at': DateTime.now().toIso8601String(),
          })
          .eq('id', requestId)
          .eq('receiver_id', userId);
    } catch (e) {
      throw Exception('فشل في الرد على الطلب: $e');
    }
  }

  // الحصول على طلبات التواصل المرسلة
  Future<List<ContactRequest>> getSentRequests() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return [];

      final response = await _client
          .from('contact_requests')
          .select('*')
          .eq('sender_id', userId)
          .order('sent_at', ascending: false);

      return (response as List).map((json) => ContactRequest.fromJson(json)).toList();
    } catch (e) {
      return [];
    }
  }

  // الحصول على طلبات التواصل المستلمة
  Future<List<ContactRequest>> getReceivedRequests() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return [];

      final response = await _client
          .from('contact_requests')
          .select('*')
          .eq('receiver_id', userId)
          .order('sent_at', ascending: false);

      return (response as List).map((json) => ContactRequest.fromJson(json)).toList();
    } catch (e) {
      return [];
    }
  }

  // التحقق من وجود طلب تواصل مقبول
  Future<bool> hasAcceptedRequest(String otherUserId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return false;

      final response = await _client
          .from('contact_requests')
          .select()
          .eq('status', 'accepted')
          .or('sender_id.eq.$userId,receiver_id.eq.$userId')
          .or('sender_id.eq.$otherUserId,receiver_id.eq.$otherUserId')
          .maybeSingle();

      return response != null;
    } catch (e) {
      return false;
    }
  }

  // الحصول على معلومات التواصل للملف
  Future<Map<String, String>?> getContactInfo(String profileId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return null;

      // التحقق من وجود طلب مقبول
      final profile = await getProfile(profileId);
      if (profile == null) return null;

      final hasApproval = await hasAcceptedRequest(profile.userId);
      if (!hasApproval) return null;

      return profile.contactMethods;
    } catch (e) {
      return null;
    }
  }

  // الإبلاغ عن ملف
  Future<void> reportProfile(String profileId, String reason, {String? details}) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client.from('marriage_reports').insert({
        'reporter_id': userId,
        'reported_profile_id': profileId,
        'reason': reason,
        'details': details,
      });
    } catch (e) {
      throw Exception('فشل في الإبلاغ: $e');
    }
  }

  // الحصول على إحصائيات الملف
  Future<Map<String, int>> getProfileStats() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return {};

      final profile = await getMyProfile();
      if (profile == null) return {};

      final sentRequests = await getSentRequests();
      final receivedRequests = await getReceivedRequests();

      return {
        'sent_requests': sentRequests.length,
        'received_requests': receivedRequests.length,
        'pending_requests': receivedRequests.where((r) => r.status == ContactRequestStatus.pending).length,
        'accepted_requests': receivedRequests.where((r) => r.status == ContactRequestStatus.accepted).length,
      };
    } catch (e) {
      return {};
    }
  }

  // البحث في الملفات
  Future<List<MarriageProfile>> searchProfiles(String query) async {
    try {
      final tableExists = await _checkMarriageTableExists();
      if (!tableExists) return [];

      final response = await _client
          .from('marriage_profiles')
          .select('*')
          .eq('is_active', true)
          .or('name.ilike.%$query%,profession.ilike.%$query%,city.ilike.%$query%,country.ilike.%$query%')
          .order('created_at', ascending: false);

      return (response as List).map((json) => MarriageProfile.fromJson(json)).toList();
    } catch (e) {
      return [];
    }
  }

  // حذف طلب تواصل مرسل
  Future<void> deleteContactRequest(String requestId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('contact_requests')
          .delete()
          .eq('id', requestId)
          .eq('sender_id', userId)
          .eq('status', 'pending');
    } catch (e) {
      throw Exception('فشل في حذف طلب التواصل: $e');
    }
  }

  // تفعيل/إلغاء تفعيل الملف
  Future<void> toggleProfileStatus(String profileId, bool isActive) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('marriage_profiles')
          .update({'is_active': isActive})
          .eq('id', profileId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في تحديث حالة الملف: $e');
    }
  }

  // الحصول على عدد الطلبات المرسلة اليوم
  Future<int> getTodayRequestsCount() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return 0;

      final response = await _client
          .from('daily_contact_limits')
          .select('requests_sent')
          .eq('user_id', userId)
          .eq('date', DateTime.now().toIso8601String().split('T')[0])
          .maybeSingle();

      return response?['requests_sent'] ?? 0;
    } catch (e) {
      return 0;
    }
  }

  // التحقق من إمكانية إرسال طلب جديد
  Future<bool> canSendRequest() async {
    try {
      final count = await getTodayRequestsCount();
      return count < 3;
    } catch (e) {
      return false;
    }
  }

  // الحصول على الملفات المحظورة (للمشرفين)
  Future<List<MarriageProfile>> getReportedProfiles() async {
    try {
      // هذه الدالة للمشرفين فقط
      final response = await _client
          .from('marriage_profiles')
          .select('''
            *,
            reports:marriage_reports(count)
          ''')
          .gte('reports.count', 1)
          .order('created_at', ascending: false);

      return (response as List).map((json) => MarriageProfile.fromJson(json)).toList();
    } catch (e) {
      return [];
    }
  }

  // حظر ملف (للمشرفين)
  Future<void> banProfile(String profileId) async {
    try {
      await _client
          .from('marriage_profiles')
          .update({'is_active': false})
          .eq('id', profileId);
    } catch (e) {
      throw Exception('فشل في حظر الملف: $e');
    }
  }
}
