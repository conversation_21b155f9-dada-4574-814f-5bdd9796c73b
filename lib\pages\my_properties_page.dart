import 'package:flutter/material.dart';
import '../models/real_estate_property.dart';
import '../services/real_estate_service.dart';
import '../widgets/property_card.dart';
import 'property_details_page.dart';
import 'edit_property_page.dart';

class MyPropertiesPage extends StatefulWidget {
  const MyPropertiesPage({super.key});

  @override
  State<MyPropertiesPage> createState() => _MyPropertiesPageState();
}

class _MyPropertiesPageState extends State<MyPropertiesPage> {
  final RealEstateService _realEstateService = RealEstateService();
  List<RealEstateProperty> _properties = [];
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _loadMyProperties();
  }

  Future<void> _loadMyProperties() async {
    setState(() => _loading = true);
    try {
      final properties = await _realEstateService.getUserProperties();
      setState(() {
        _properties = properties;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل العقارات: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_properties.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadMyProperties,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _properties.length,
        itemBuilder: (context, index) {
          final property = _properties[index];
          return _buildMyPropertyCard(property);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              shape: BoxShape.circle,
            ),
            child: Icon(Icons.home_outlined, size: 64, color: Colors.blue[600]),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد عقارات',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم تقم بنشر أي عقارات بعد',
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // الانتقال لتبويب إضافة عقار
              DefaultTabController.of(context)?.animateTo(3);
            },
            icon: const Icon(Icons.add),
            label: const Text('أضف عقار جديد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMyPropertyCard(RealEstateProperty property) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          // بطاقة العقار الأساسية
          PropertyCard(
            property: property,
            onTap: () => _openPropertyDetails(property),
          ),
          
          // شريط الإجراءات
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius: const BorderRadius.vertical(bottom: Radius.circular(12)),
            ),
            child: Row(
              children: [
                // حالة العقار
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: property.isActive ? Colors.green[100] : Colors.red[100],
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: property.isActive ? Colors.green : Colors.red,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        property.isActive ? Icons.visibility : Icons.visibility_off,
                        size: 16,
                        color: property.isActive ? Colors.green[700] : Colors.red[700],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        property.isActive ? 'نشط' : 'مخفي',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: property.isActive ? Colors.green[700] : Colors.red[700],
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(width: 12),
                
                // إحصائيات
                Row(
                  children: [
                    Icon(Icons.visibility, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      '${property.viewsCount} مشاهدة',
                      style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                    ),
                  ],
                ),
                
                const Spacer(),
                
                // أزرار الإجراءات
                Row(
                  children: [
                    // تعديل
                    IconButton(
                      onPressed: () => _editProperty(property),
                      icon: const Icon(Icons.edit),
                      tooltip: 'تعديل',
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.blue[100],
                        foregroundColor: Colors.blue[700],
                      ),
                    ),
                    
                    const SizedBox(width: 8),
                    
                    // إخفاء/إظهار
                    IconButton(
                      onPressed: () => _togglePropertyVisibility(property),
                      icon: Icon(property.isActive ? Icons.visibility_off : Icons.visibility),
                      tooltip: property.isActive ? 'إخفاء' : 'إظهار',
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.orange[100],
                        foregroundColor: Colors.orange[700],
                      ),
                    ),
                    
                    const SizedBox(width: 8),
                    
                    // حذف
                    IconButton(
                      onPressed: () => _deleteProperty(property),
                      icon: const Icon(Icons.delete),
                      tooltip: 'حذف',
                      style: IconButton.styleFrom(
                        backgroundColor: Colors.red[100],
                        foregroundColor: Colors.red[700],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _openPropertyDetails(RealEstateProperty property) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => PropertyDetailsPage(property: property),
      ),
    );
  }

  void _editProperty(RealEstateProperty property) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => EditPropertyPage(property: property),
      ),
    ).then((_) => _loadMyProperties());
  }

  Future<void> _togglePropertyVisibility(RealEstateProperty property) async {
    try {
      final updatedProperty = RealEstateProperty(
        id: property.id,
        userId: property.userId,
        title: property.title,
        description: property.description,
        propertyType: property.propertyType,
        purpose: property.purpose,
        category: property.category,
        country: property.country,
        city: property.city,
        district: property.district,
        address: property.address,
        latitude: property.latitude,
        longitude: property.longitude,
        price: property.price,
        currency: property.currency,
        area: property.area,
        bedrooms: property.bedrooms,
        bathrooms: property.bathrooms,
        floors: property.floors,
        parkingSpaces: property.parkingSpaces,
        features: property.features,
        amenities: property.amenities,
        contactPhone: property.contactPhone,
        contactWhatsapp: property.contactWhatsapp,
        allowAppMessages: property.allowAppMessages,
        isActive: !property.isActive, // تبديل الحالة
        isFeatured: property.isFeatured,
        isVerified: property.isVerified,
        viewsCount: property.viewsCount,
        createdAt: property.createdAt,
        updatedAt: DateTime.now(),
        expiresAt: property.expiresAt,
        images: property.images,
        isFavorite: property.isFavorite,
        ownerName: property.ownerName,
        ownerAvatar: property.ownerAvatar,
      );

      await _realEstateService.updateProperty(property.id, updatedProperty);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(property.isActive ? 'تم إخفاء العقار' : 'تم إظهار العقار'),
            backgroundColor: Colors.green,
          ),
        );
        _loadMyProperties();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث العقار: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteProperty(RealEstateProperty property) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف العقار'),
        content: Text('هل أنت متأكد من حذف "${property.title}"؟\n\nلا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      await _realEstateService.deleteProperty(property.id);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف العقار بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        _loadMyProperties();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف العقار: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
