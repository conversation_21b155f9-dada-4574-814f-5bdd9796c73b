-- إضافة نظام المحادثات لقسم الزواج الشرعي

-- جدول المحادثات الخاصة بالزواج
CREATE TABLE IF NOT EXISTS marriage_chats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    contact_request_id UUID NOT NULL REFERENCES contact_requests(id) ON DELETE CASCADE,
    user1_id UUID NOT NULL,
    user2_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(contact_request_id)
);

-- جدول الرسائل
CREATE TABLE IF NOT EXISTS marriage_messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    chat_id UUID NOT NULL REFERENCES marriage_chats(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL,
    content TEXT NOT NULL,
    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'file')),
    file_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_read BOOLEAN DEFAULT FALSE,
    is_deleted BOOLEAN DEFAULT FALSE
);

-- إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_marriage_chats_contact_request_id ON marriage_chats(contact_request_id);
CREATE INDEX IF NOT EXISTS idx_marriage_chats_user1_id ON marriage_chats(user1_id);
CREATE INDEX IF NOT EXISTS idx_marriage_chats_user2_id ON marriage_chats(user2_id);
CREATE INDEX IF NOT EXISTS idx_marriage_chats_updated_at ON marriage_chats(updated_at DESC);

CREATE INDEX IF NOT EXISTS idx_marriage_messages_chat_id ON marriage_messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_marriage_messages_sender_id ON marriage_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_marriage_messages_created_at ON marriage_messages(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_marriage_messages_is_read ON marriage_messages(is_read);

-- دالة لإنشاء محادثة عند قبول طلب التواصل
CREATE OR REPLACE FUNCTION create_marriage_chat_on_acceptance()
RETURNS TRIGGER AS $$
BEGIN
    -- التحقق من أن الحالة تغيرت إلى مقبول
    IF NEW.status = 'accepted' AND OLD.status = 'pending' THEN
        -- إنشاء محادثة جديدة
        INSERT INTO marriage_chats (contact_request_id, user1_id, user2_id)
        VALUES (NEW.id, NEW.sender_id, NEW.receiver_id);
        
        -- إرسال رسالة ترحيبية تلقائية
        INSERT INTO marriage_messages (
            chat_id, 
            sender_id, 
            content, 
            message_type
        )
        SELECT 
            mc.id,
            NEW.receiver_id,
            'تم قبول طلب التواصل! يمكنكم الآن التحدث بحرية. نتمنى لكم التوفيق! 💕',
            'text'
        FROM marriage_chats mc
        WHERE mc.contact_request_id = NEW.id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء التريجر
DROP TRIGGER IF EXISTS trigger_create_chat_on_acceptance ON contact_requests;
CREATE TRIGGER trigger_create_chat_on_acceptance
    AFTER UPDATE ON contact_requests
    FOR EACH ROW
    EXECUTE FUNCTION create_marriage_chat_on_acceptance();

-- دالة لتحديث وقت آخر رسالة في المحادثة
CREATE OR REPLACE FUNCTION update_chat_last_message()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE marriage_chats 
    SET 
        last_message_at = NEW.created_at,
        updated_at = NEW.created_at
    WHERE id = NEW.chat_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إنشاء التريجر لتحديث وقت آخر رسالة
DROP TRIGGER IF EXISTS trigger_update_last_message ON marriage_messages;
CREATE TRIGGER trigger_update_last_message
    AFTER INSERT ON marriage_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_chat_last_message();

-- تفعيل Row Level Security
ALTER TABLE marriage_chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE marriage_messages ENABLE ROW LEVEL SECURITY;

-- حذف السياسات الموجودة
DROP POLICY IF EXISTS "المستخدم يمكنه قراءة محادثاته" ON marriage_chats;
DROP POLICY IF EXISTS "المستخدم يمكنه تحديث محادثاته" ON marriage_chats;

DROP POLICY IF EXISTS "المستخدم يمكنه قراءة رسائل محادثاته" ON marriage_messages;
DROP POLICY IF EXISTS "المستخدم يمكنه إرسال رسائل" ON marriage_messages;
DROP POLICY IF EXISTS "المستخدم يمكنه تحديث رسائله" ON marriage_messages;

-- سياسات الأمان للمحادثات
CREATE POLICY "المستخدم يمكنه قراءة محادثاته" ON marriage_chats
    FOR SELECT USING (auth.uid() = user1_id OR auth.uid() = user2_id);

CREATE POLICY "المستخدم يمكنه تحديث محادثاته" ON marriage_chats
    FOR UPDATE USING (auth.uid() = user1_id OR auth.uid() = user2_id);

-- سياسات الأمان للرسائل
CREATE POLICY "المستخدم يمكنه قراءة رسائل محادثاته" ON marriage_messages
    FOR SELECT USING (
        auth.uid() IN (
            SELECT user1_id FROM marriage_chats WHERE id = marriage_messages.chat_id
            UNION
            SELECT user2_id FROM marriage_chats WHERE id = marriage_messages.chat_id
        )
    );

CREATE POLICY "المستخدم يمكنه إرسال رسائل" ON marriage_messages
    FOR INSERT WITH CHECK (
        auth.uid() = sender_id AND
        auth.uid() IN (
            SELECT user1_id FROM marriage_chats WHERE id = marriage_messages.chat_id
            UNION
            SELECT user2_id FROM marriage_chats WHERE id = marriage_messages.chat_id
        )
    );

CREATE POLICY "المستخدم يمكنه تحديث رسائله" ON marriage_messages
    FOR UPDATE USING (auth.uid() = sender_id);

-- منح الصلاحيات
GRANT ALL ON marriage_chats TO authenticated;
GRANT ALL ON marriage_messages TO authenticated;

GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON FUNCTION create_marriage_chat_on_acceptance() TO authenticated;
GRANT EXECUTE ON FUNCTION update_chat_last_message() TO authenticated;

-- رسالة نجاح
SELECT 'تم إنشاء نظام المحادثات للزواج الشرعي بنجاح!' as message;
