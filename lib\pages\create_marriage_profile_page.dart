import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/marriage_profile.dart';
import '../services/marriage_service.dart';

class CreateMarriageProfilePage extends StatefulWidget {
  final MarriageProfile? profile; // للتعديل

  const CreateMarriageProfilePage({super.key, this.profile});

  @override
  State<CreateMarriageProfilePage> createState() => _CreateMarriageProfilePageState();
}

class _CreateMarriageProfilePageState extends State<CreateMarriageProfilePage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ageController = TextEditingController();
  final _cityController = TextEditingController();
  final _countryController = TextEditingController();
  final _professionController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _desiredPartnerController = TextEditingController();
  final _emailController = TextEditingController();
  final _whatsappController = TextEditingController();

  Gender _selectedGender = Gender.male;
  MaritalStatus _selectedMaritalStatus = MaritalStatus.single;
  MarriageGoal _selectedGoal = MarriageGoal.marriage;
  bool _hideImageUntilApproval = true;
  bool _hideNameUntilApproval = true;
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    if (widget.profile != null) {
      _loadProfileData();
    }
  }

  void _loadProfileData() {
    final profile = widget.profile!;
    _nameController.text = profile.name;
    _ageController.text = profile.age.toString();
    _cityController.text = profile.city;
    _countryController.text = profile.country;
    _professionController.text = profile.profession;
    _descriptionController.text = profile.description;
    _desiredPartnerController.text = profile.desiredPartnerSpecs;
    _emailController.text = profile.contactMethods['email'] ?? '';
    _whatsappController.text = profile.contactMethods['whatsapp'] ?? '';
    _selectedGender = profile.gender;
    _selectedMaritalStatus = profile.maritalStatus;
    _selectedGoal = profile.goal;
    _hideImageUntilApproval = profile.hideImageUntilApproval;
    _hideNameUntilApproval = profile.hideNameUntilApproval;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ageController.dispose();
    _cityController.dispose();
    _countryController.dispose();
    _professionController.dispose();
    _descriptionController.dispose();
    _desiredPartnerController.dispose();
    _emailController.dispose();
    _whatsappController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.profile != null ? 'تعديل الملف' : 'إنشاء ملف زواج'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        actions: [
          TextButton(
            onPressed: _loading ? null : _saveProfile,
            child: _loading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('حفظ'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // رسالة ترحيبية
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.pink[600]!, Colors.pink[800]!],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.favorite, color: Colors.white, size: 24),
                      const SizedBox(width: 8),
                      Text(
                        'بيت الحلال',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'أنشئ ملفك الشخصي للتعارف الشرعي المحترم',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // المعلومات الأساسية
            _buildSectionTitle('المعلومات الأساسية'),
            const SizedBox(height: 16),
            
            // الاسم
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: 'الاسم أو الاسم المستعار *',
                hintText: 'مثال: أحمد أو أم محمد',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.person),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال الاسم';
                }
                if (value.trim().length < 2) {
                  return 'الاسم يجب أن يكون حرفين على الأقل';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // الجنس والعمر
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<Gender>(
                    value: _selectedGender,
                    decoration: const InputDecoration(
                      labelText: 'الجنس *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.wc),
                    ),
                    items: Gender.values.map((gender) => DropdownMenuItem(
                      value: gender,
                      child: Text(gender == Gender.male ? 'ذكر' : 'أنثى'),
                    )).toList(),
                    onChanged: (value) => setState(() => _selectedGender = value!),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _ageController,
                    decoration: const InputDecoration(
                      labelText: 'العمر *',
                      hintText: '25',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.cake),
                    ),
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال العمر';
                      }
                      final age = int.tryParse(value);
                      if (age == null || age < 18 || age > 100) {
                        return 'العمر يجب أن يكون بين 18 و 100';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // المدينة والدولة
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _cityController,
                    decoration: const InputDecoration(
                      labelText: 'المدينة *',
                      hintText: 'الرياض',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.location_city),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال المدينة';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _countryController,
                    decoration: const InputDecoration(
                      labelText: 'الدولة *',
                      hintText: 'السعودية',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.flag),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال الدولة';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // المهنة
            TextFormField(
              controller: _professionController,
              decoration: const InputDecoration(
                labelText: 'المهنة أو مجال العمل *',
                hintText: 'مثال: مهندس، طبيب، ربة منزل',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.work),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال المهنة';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 24),
            
            // الحالة الاجتماعية والهدف
            _buildSectionTitle('الحالة والهدف'),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<MaritalStatus>(
                    value: _selectedMaritalStatus,
                    decoration: const InputDecoration(
                      labelText: 'الحالة الاجتماعية *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.favorite),
                    ),
                    items: MaritalStatus.values.map((status) => DropdownMenuItem(
                      value: status,
                      child: Text(_getMaritalStatusText(status)),
                    )).toList(),
                    onChanged: (value) => setState(() => _selectedMaritalStatus = value!),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<MarriageGoal>(
                    value: _selectedGoal,
                    decoration: const InputDecoration(
                      labelText: 'الهدف *',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.flag),
                    ),
                    items: MarriageGoal.values.map((goal) => DropdownMenuItem(
                      value: goal,
                      child: Text(_getGoalText(goal)),
                    )).toList(),
                    onChanged: (value) => setState(() => _selectedGoal = value!),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // الوصف الشخصي
            _buildSectionTitle('الوصف الشخصي'),
            const SizedBox(height: 16),
            
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف قصير عن النفس *',
                hintText: 'اكتب وصفاً مختصراً عن شخصيتك وما تبحث عنه...',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: 4,
              maxLength: 250,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال وصف شخصي';
                }
                if (value.trim().length < 50) {
                  return 'الوصف يجب أن يكون 50 حرف على الأقل';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // مواصفات الطرف الآخر
            TextFormField(
              controller: _desiredPartnerController,
              decoration: const InputDecoration(
                labelText: 'مواصفات الطرف الآخر المرغوب *',
                hintText: 'اكتب المواصفات التي تبحث عنها في شريك الحياة...',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: 3,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال مواصفات الطرف الآخر';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 24),
            
            // معلومات التواصل
            _buildSectionTitle('معلومات التواصل'),
            const SizedBox(height: 8),
            Text(
              'ستظهر هذه المعلومات فقط بعد قبول طلب التواصل',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _emailController,
                    decoration: const InputDecoration(
                      labelText: 'البريد الإلكتروني',
                      hintText: '<EMAIL>',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.email),
                    ),
                    keyboardType: TextInputType.emailAddress,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _whatsappController,
                    decoration: const InputDecoration(
                      labelText: 'واتساب',
                      hintText: '+966501234567',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.phone),
                    ),
                    keyboardType: TextInputType.phone,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // إعدادات الخصوصية
            _buildSectionTitle('إعدادات الخصوصية'),
            const SizedBox(height: 16),
            
            CheckboxListTile(
              title: const Text('إخفاء الاسم حتى الموافقة'),
              subtitle: const Text('سيظهر "ذكر من المدينة" بدلاً من الاسم'),
              value: _hideNameUntilApproval,
              onChanged: (value) => setState(() => _hideNameUntilApproval = value!),
              controlAffinity: ListTileControlAffinity.leading,
            ),
            
            CheckboxListTile(
              title: const Text('إخفاء الصورة حتى الموافقة'),
              subtitle: const Text('ستظهر أيقونة افتراضية بدلاً من الصورة'),
              value: _hideImageUntilApproval,
              onChanged: (value) => setState(() => _hideImageUntilApproval = value!),
              controlAffinity: ListTileControlAffinity.leading,
            ),
            
            const SizedBox(height: 32),
            
            // معلومات مهمة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.amber[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.amber[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.amber[700]),
                      const SizedBox(width: 8),
                      Text(
                        'معلومات مهمة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Colors.amber[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    '• يمكنك إرسال 3 طلبات تواصل يومياً فقط\n'
                    '• معلومات التواصل تظهر فقط بعد الموافقة\n'
                    '• يمكنك تعديل ملفك في أي وقت\n'
                    '• الهدف هو التعارف الشرعي المحترم',
                    style: TextStyle(
                      color: Colors.amber[700],
                      fontSize: 14,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Colors.black87,
      ),
    );
  }

  String _getMaritalStatusText(MaritalStatus status) {
    switch (status) {
      case MaritalStatus.single:
        return _selectedGender == Gender.male ? 'أعزب' : 'عزباء';
      case MaritalStatus.divorced:
        return _selectedGender == Gender.male ? 'مطلق' : 'مطلقة';
      case MaritalStatus.widowed:
        return _selectedGender == Gender.male ? 'أرمل' : 'أرملة';
    }
  }

  String _getGoalText(MarriageGoal goal) {
    switch (goal) {
      case MarriageGoal.marriage:
        return 'زواج';
      case MarriageGoal.engagement:
        return 'خطبة';
      case MarriageGoal.seriousRelationship:
        return 'تعارف بنية الزواج';
    }
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _loading = true);

    try {
      final contactMethods = <String, String>{};
      if (_emailController.text.trim().isNotEmpty) {
        contactMethods['email'] = _emailController.text.trim();
      }
      if (_whatsappController.text.trim().isNotEmpty) {
        contactMethods['whatsapp'] = _whatsappController.text.trim();
      }

      final profile = MarriageProfile(
        id: widget.profile?.id ?? '',
        userId: Supabase.instance.client.auth.currentUser!.id,
        name: _nameController.text.trim(),
        gender: _selectedGender,
        age: int.parse(_ageController.text.trim()),
        city: _cityController.text.trim(),
        country: _countryController.text.trim(),
        profession: _professionController.text.trim(),
        maritalStatus: _selectedMaritalStatus,
        description: _descriptionController.text.trim(),
        goal: _selectedGoal,
        desiredPartnerSpecs: _desiredPartnerController.text.trim(),
        hideImageUntilApproval: _hideImageUntilApproval,
        hideNameUntilApproval: _hideNameUntilApproval,
        contactMethods: contactMethods,
        createdAt: widget.profile?.createdAt ?? DateTime.now(),
      );

      if (widget.profile != null) {
        await MarriageService().updateProfile(widget.profile!.id, profile);
      } else {
        await MarriageService().createProfile(profile);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.profile != null ? 'تم تحديث الملف بنجاح' : 'تم إنشاء الملف بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'خطأ غير معروف';
        
        if (e.toString().contains('UNIQUE constraint')) {
          errorMessage = 'لديك ملف زواج بالفعل. يمكنك تعديله بدلاً من إنشاء ملف جديد';
        } else if (e.toString().contains('tables')) {
          errorMessage = 'تحتاج إلى إنشاء جداول قاعدة البيانات أولاً';
        } else {
          errorMessage = e.toString().replaceAll('Exception: ', '');
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _loading = false);
      }
    }
  }
}
