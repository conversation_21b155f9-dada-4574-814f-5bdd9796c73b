import 'package:flutter/material.dart';
import '../models/comment.dart';
import '../models/reaction_type.dart';

class CommentReactionButton extends StatefulWidget {
  final Comment comment;
  final Function(Comment, ReactionType) onReaction;

  const CommentReactionButton({
    super.key,
    required this.comment,
    required this.onReaction,
  });

  @override
  State<CommentReactionButton> createState() => _CommentReactionButtonState();
}

class _CommentReactionButtonState extends State<CommentReactionButton>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  // نفس منطق المنشورات - استخدام showDialog بدلاً من overlay
  Future<ReactionType?> _openReactionPickerAt(Offset globalPos) async {
    final items = [
      ReactionType.like,
      ReactionType.dislike,
      ReactionType.support,
      ReactionType.love,
      ReactionType.funny,
      ReactionType.angry,
      ReactionType.sad,
    ];

    final screenSize = MediaQuery.of(context).size;
    const pickerWidth = 280.0;
    const pickerHeight = 60.0;

    double left = globalPos.dx - (pickerWidth / 2);
    double top = globalPos.dy - pickerHeight - 10;

    if (left < 10) left = 10;
    if (left + pickerWidth > screenSize.width - 10) {
      left = screenSize.width - pickerWidth - 10;
    }
    if (top < 50) top = globalPos.dy + 10;

    final selected = await showDialog<ReactionType>(
      context: context,
      barrierColor: Colors.transparent,
      builder: (ctx) {
        return Stack(children: [
          Positioned(
            left: left,
            top: top,
            child: _AnimatedReactionPicker(
              reactions: items,
              onReactionSelected: (reaction) => Navigator.pop(ctx, reaction),
            ),
          ),
        ]);
      },
    );

    return selected;
  }



  @override
  Widget build(BuildContext context) {
    final hasReaction = widget.comment.currentUserReaction != ReactionType.none;
    final totalReactions = widget.comment.reactionCounts.values
        .fold<int>(0, (sum, count) => sum + count);

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          // ضغطة عادية - تفاعل لايك أو إزالة التفاعل الحالي
          if (hasReaction) {
            // إزالة التفاعل الحالي
            widget.onReaction(widget.comment, ReactionType.none);
          } else {
            // إضافة لايك
            widget.onReaction(widget.comment, ReactionType.like);
            _animationController.forward().then((_) {
              _animationController.reverse();
            });
          }
        },
        onLongPressStart: (details) async {
          // ضغطة طويلة - إظهار جميع التفاعلات
          final selected = await _openReactionPickerAt(details.globalPosition);
          if (selected != null) {
            widget.onReaction(widget.comment, selected);
            _animationController.forward().then((_) {
              _animationController.reverse();
            });
          }
        },
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: hasReaction
                ? _getReactionColor(widget.comment.currentUserReaction).withOpacity(0.1)
                : Colors.transparent,
          ),
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // أيقونة التفاعل - عرض التفاعل الفعلي للمستخدم
                    if (hasReaction)
                      Image.asset(
                        widget.comment.currentUserReaction.assetPath,
                        width: 16,
                        height: 16,
                        fit: BoxFit.contain,
                      )
                    else
                      Icon(
                        Icons.thumb_up_outlined,
                        size: 16,
                        color: Colors.grey[600],
                      ),

                    // عدد التفاعلات
                    if (totalReactions > 0) ...[
                      const SizedBox(width: 4),
                      Text(
                        '$totalReactions',
                        style: TextStyle(
                          fontSize: 12,
                          color: hasReaction
                              ? _getReactionColor(widget.comment.currentUserReaction)
                              : Colors.grey[600],
                          fontWeight: hasReaction ? FontWeight.w600 : FontWeight.normal,
                        ),
                      ),
                    ],
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  // دالة لتحديد لون التفاعل
  Color _getReactionColor(ReactionType reaction) {
    switch (reaction) {
      case ReactionType.like:
        return Colors.blue;
      case ReactionType.love:
        return Colors.red;
      case ReactionType.funny:
        return Colors.orange;
      case ReactionType.angry:
        return Colors.red[800]!;
      case ReactionType.sad:
        return Colors.blue[800]!;
      case ReactionType.celebrate:
        return Colors.purple;
      case ReactionType.support:
        return Colors.green;
      case ReactionType.insightful:
        return Colors.amber[700]!;
      default:
        return Colors.grey;
    }
  }
}

// Widget لعرض ملخص التفاعلات (مثل المنشورات)
class CommentReactionsSummary extends StatelessWidget {
  final Comment comment;
  final VoidCallback? onTap;

  const CommentReactionsSummary({
    super.key,
    required this.comment,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final reactionCounts = comment.reactionCounts;
    final totalReactions = reactionCounts.values.fold<int>(0, (sum, count) => sum + count);
    
    if (totalReactions == 0) return const SizedBox.shrink();

    // ترتيب التفاعلات حسب العدد
    final sortedReactions = reactionCounts.entries
        .where((entry) => entry.value > 0)
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    // عرض أكثر 3 تفاعلات
    final topReactions = sortedReactions.take(3).toList();

    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أيقونات التفاعلات المتداخلة
          SizedBox(
            width: topReactions.length == 1 
                ? 16 
                : topReactions.length == 2 
                    ? 28 
                    : 40,
            height: 16,
            child: Stack(
              children: topReactions.asMap().entries.map((entry) {
                final index = entry.key;
                final reactionEntry = entry.value;
                return Positioned(
                  right: index * 12.0,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    child: ClipOval(
                      child: Image.asset(
                        reactionEntry.key.assetPath,
                        width: 16,
                        height: 16,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
          
          const SizedBox(width: 6),
          
          // العدد الإجمالي
          Text(
            '$totalReactions',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

// نسخ _AnimatedReactionPicker من post_card.dart
class _AnimatedReactionPicker extends StatefulWidget {
  final List<ReactionType> reactions;
  final Function(ReactionType) onReactionSelected;

  const _AnimatedReactionPicker({
    required this.reactions,
    required this.onReactionSelected,
  });

  @override
  State<_AnimatedReactionPicker> createState() => _AnimatedReactionPickerState();
}

class _AnimatedReactionPickerState extends State<_AnimatedReactionPicker>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<Offset>> _slideAnimations;
  late List<Animation<double>> _bounceAnimations;
  late List<Animation<double>> _shimmerAnimations;
  late AnimationController _containerController;
  late Animation<double> _containerScaleAnimation;

  @override
  void initState() {
    super.initState();

    // Container animation
    _containerController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _containerScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _containerController,
      curve: Curves.elasticOut,
    ));

    // Individual reaction animations
    _controllers = List.generate(
      widget.reactions.length,
      (index) => AnimationController(
        duration: Duration(milliseconds: 200 + (index * 50)),
        vsync: this,
      ),
    );

    _scaleAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.elasticOut),
      );
    }).toList();

    _slideAnimations = _controllers.asMap().entries.map((entry) {
      return Tween<Offset>(
        begin: Offset(0, 1.0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: entry.value,
        curve: Curves.elasticOut,
      ));
    }).toList();

    _bounceAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 1.0, end: 1.2).animate(
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut),
      );
    }).toList();

    _shimmerAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    // Start animations
    _containerController.forward();
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(Duration(milliseconds: i * 50), () {
        if (mounted) _controllers[i].forward();
      });
    }
  }

  @override
  void dispose() {
    _containerController.dispose();
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _containerScaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _containerScaleAnimation.value,
          child: Material(
            color: Colors.transparent,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: widget.reactions.asMap().entries.map((entry) {
                  final index = entry.key;
                  final reaction = entry.value;
                  return _buildReactionItem(reaction, index);
                }).toList(),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildReactionItem(ReactionType reaction, int index) {
    return AnimatedBuilder(
      animation: Listenable.merge([
        _scaleAnimations[index],
        _slideAnimations[index],
        _bounceAnimations[index],
        _shimmerAnimations[index],
      ]),
      builder: (context, child) {
        return Transform.translate(
          offset: _slideAnimations[index].value * 20,
          child: Transform.scale(
            scale: _scaleAnimations[index].value,
            child: GestureDetector(
              onTap: () => widget.onReactionSelected(reaction),
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 2),
                padding: const EdgeInsets.all(4),
                child: Transform.scale(
                  scale: _bounceAnimations[index].value,
                  child: Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: reaction.color.withOpacity(0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Image.asset(
                      reaction.assetPath,
                      width: 24,
                      height: 24,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
