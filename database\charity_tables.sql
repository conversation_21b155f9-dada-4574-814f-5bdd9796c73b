-- ===================================
-- جداول قسم الصدقات - تطبيق أرزاوو
-- ===================================

-- التحقق من وجود جدول profiles وإنشاؤه إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT,
    avatar_url TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- تفعيل RLS على جدول profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- سياسات profiles
DROP POLICY IF EXISTS "Public profiles are viewable by everyone" ON profiles;
CREATE POLICY "Public profiles are viewable by everyone" ON profiles
    FOR SELECT USING (true);

DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
CREATE POLICY "Users can update own profile" ON profiles
    FOR UPDATE USING (auth.uid() = id);

DROP POLICY IF EXISTS "Users can insert own profile" ON profiles;
CREATE POLICY "Users can insert own profile" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 1. جدول العناصر الخيرية الرئيسي
CREATE TABLE charity_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('donation', 'request', 'urgent')),
    category TEXT NOT NULL CHECK (category IN ('food', 'clothes', 'furniture', 'medicine', 'books', 'electronics', 'other')),
    condition TEXT NOT NULL CHECK (condition IN ('new_item', 'used', 'good', 'urgent')),
    delivery_method TEXT NOT NULL CHECK (delivery_method IN ('hand', 'person', 'charity')),
    city TEXT NOT NULL,
    country TEXT DEFAULT 'السعودية',
    phone_number TEXT,
    images TEXT[] DEFAULT '{}',
    is_urgent BOOLEAN DEFAULT FALSE,
    is_anonymous BOOLEAN DEFAULT FALSE,
    is_completed BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    interest_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. جدول الاهتمامات
CREATE TABLE charity_interests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    item_id UUID REFERENCES charity_items(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(item_id, user_id)
);

-- 3. جدول البلاغات
CREATE TABLE charity_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    item_id UUID REFERENCES charity_items(id) ON DELETE CASCADE,
    reporter_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    reason TEXT NOT NULL,
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===================================
-- الفهارس لتحسين الأداء
-- ===================================

CREATE INDEX idx_charity_items_type ON charity_items(type);
CREATE INDEX idx_charity_items_category ON charity_items(category);
CREATE INDEX idx_charity_items_city ON charity_items(city);
CREATE INDEX idx_charity_items_urgent ON charity_items(is_urgent);
CREATE INDEX idx_charity_items_active ON charity_items(is_active, is_completed);
CREATE INDEX idx_charity_items_created_at ON charity_items(created_at DESC);
CREATE INDEX idx_charity_interests_item_id ON charity_interests(item_id);
CREATE INDEX idx_charity_interests_user_id ON charity_interests(user_id);

-- ===================================
-- سياسات الأمان (Row Level Security)
-- ===================================

-- تفعيل RLS
ALTER TABLE charity_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE charity_interests ENABLE ROW LEVEL SECURITY;
ALTER TABLE charity_reports ENABLE ROW LEVEL SECURITY;

-- سياسات charity_items
-- قراءة: الجميع يمكنهم رؤية العناصر النشطة
CREATE POLICY "Anyone can view active charity items" ON charity_items
    FOR SELECT USING (is_active = true);

-- إدراج: المستخدمون المسجلون فقط
CREATE POLICY "Authenticated users can insert charity items" ON charity_items
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- تحديث: المالك فقط
CREATE POLICY "Users can update own charity items" ON charity_items
    FOR UPDATE USING (auth.uid() = user_id);

-- حذف: المالك فقط
CREATE POLICY "Users can delete own charity items" ON charity_items
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات charity_interests
-- قراءة: المالك والمهتم
CREATE POLICY "Users can view interests for their items" ON charity_interests
    FOR SELECT USING (
        auth.uid() = user_id OR 
        auth.uid() IN (SELECT user_id FROM charity_items WHERE id = item_id)
    );

-- إدراج: المستخدمون المسجلون
CREATE POLICY "Authenticated users can show interest" ON charity_interests
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- حذف: المهتم فقط
CREATE POLICY "Users can remove own interest" ON charity_interests
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات charity_reports
-- إدراج: المستخدمون المسجلون
CREATE POLICY "Authenticated users can report items" ON charity_reports
    FOR INSERT WITH CHECK (auth.uid() = reporter_id);

-- قراءة: المبلغ فقط
CREATE POLICY "Users can view own reports" ON charity_reports
    FOR SELECT USING (auth.uid() = reporter_id);

-- ===================================
-- الدوال المساعدة
-- ===================================

-- دالة تحديث عداد الاهتمامات
CREATE OR REPLACE FUNCTION update_charity_interest_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE charity_items 
        SET interest_count = interest_count + 1 
        WHERE id = NEW.item_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE charity_items 
        SET interest_count = GREATEST(interest_count - 1, 0) 
        WHERE id = OLD.item_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- تطبيق الدالة على جدول الاهتمامات
CREATE TRIGGER charity_interest_count_trigger
    AFTER INSERT OR DELETE ON charity_interests
    FOR EACH ROW EXECUTE FUNCTION update_charity_interest_count();

-- دالة تحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تطبيق دالة التحديث
CREATE TRIGGER update_charity_items_updated_at
    BEFORE UPDATE ON charity_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===================================
-- بيانات تجريبية (اختيارية)
-- ===================================

-- إدراج بعض البيانات التجريبية للاختبار
-- يمكن حذف هذا القسم في الإنتاج

-- INSERT INTO charity_items (
--     user_id, title, description, type, category, condition, 
--     delivery_method, city, phone_number, is_urgent, is_anonymous
-- ) VALUES 
-- (
--     (SELECT id FROM auth.users LIMIT 1),
--     'ملابس شتوية للأطفال',
--     'مجموعة ملابس شتوية جديدة للأطفال من عمر 5-10 سنوات، نظيفة وفي حالة ممتازة',
--     'donation',
--     'clothes',
--     'new_item',
--     'hand',
--     'الرياض',
--     '0501234567',
--     false,
--     false
-- ),
-- (
--     (SELECT id FROM auth.users LIMIT 1),
--     'أحتاج أدوية للضغط',
--     'أحتاج أدوية للضغط بشكل عاجل، الحالة طارئة',
--     'urgent',
--     'medicine',
--     'urgent',
--     'hand',
--     'جدة',
--     '0509876543',
--     true,
--     true
-- );

-- ===================================
-- جداول قسم البحث عن عمل
-- ===================================

-- 4. جدول الباحثين عن عمل
CREATE TABLE IF NOT EXISTS job_seekers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    profile_image TEXT,
    age INTEGER NOT NULL CHECK (age >= 16 AND age <= 70),
    gender TEXT NOT NULL CHECK (gender IN ('ذكر', 'أنثى')),
    marital_status TEXT NOT NULL CHECK (marital_status IN ('single', 'married', 'divorced', 'widowed')),
    current_country TEXT NOT NULL,
    current_city TEXT NOT NULL,
    nationality TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('construction', 'teaching', 'driving', 'barbering', 'programming', 'delivery', 'design', 'carpentry', 'blacksmithing', 'tailoring', 'painting', 'plastering', 'electrical', 'mechanics', 'cleaning', 'cooking', 'healthcare', 'sales', 'accounting', 'security', 'other')),
    skills TEXT[] DEFAULT '{}',
    languages TEXT[] DEFAULT '{}',
    experience_years INTEGER DEFAULT 0 CHECK (experience_years >= 0),
    description TEXT NOT NULL,
    preferred_job_type TEXT NOT NULL CHECK (preferred_job_type IN ('fullTime', 'partTime', 'remote', 'freelance', 'contract')),
    preferred_location TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    email TEXT,
    social_links TEXT,
    cv_url TEXT,
    portfolio_images TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    views_count INTEGER DEFAULT 0,
    likes_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- 5. جدول إعجابات الباحثين عن عمل
CREATE TABLE IF NOT EXISTS job_seeker_likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    seeker_id UUID REFERENCES job_seekers(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(seeker_id, user_id)
);

-- 6. جدول حفظ الباحثين عن عمل
CREATE TABLE IF NOT EXISTS job_seeker_saves (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    seeker_id UUID REFERENCES job_seekers(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(seeker_id, user_id)
);

-- ===================================
-- فهارس قسم البحث عن عمل
-- ===================================

CREATE INDEX IF NOT EXISTS idx_job_seekers_category ON job_seekers(category);
CREATE INDEX IF NOT EXISTS idx_job_seekers_city ON job_seekers(current_city);
CREATE INDEX IF NOT EXISTS idx_job_seekers_country ON job_seekers(current_country);
CREATE INDEX IF NOT EXISTS idx_job_seekers_job_type ON job_seekers(preferred_job_type);
CREATE INDEX IF NOT EXISTS idx_job_seekers_active ON job_seekers(is_active);
CREATE INDEX IF NOT EXISTS idx_job_seekers_created_at ON job_seekers(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_job_seeker_likes_seeker_id ON job_seeker_likes(seeker_id);
CREATE INDEX IF NOT EXISTS idx_job_seeker_saves_user_id ON job_seeker_saves(user_id);

-- ===================================
-- سياسات الأمان لقسم البحث عن عمل
-- ===================================

-- تفعيل RLS
ALTER TABLE job_seekers ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_seeker_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_seeker_saves ENABLE ROW LEVEL SECURITY;

-- سياسات job_seekers
DROP POLICY IF EXISTS "Anyone can view active job seekers" ON job_seekers;
CREATE POLICY "Anyone can view active job seekers" ON job_seekers
    FOR SELECT USING (is_active = true);

DROP POLICY IF EXISTS "Authenticated users can insert job seekers" ON job_seekers;
CREATE POLICY "Authenticated users can insert job seekers" ON job_seekers
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update own job seeker profile" ON job_seekers;
CREATE POLICY "Users can update own job seeker profile" ON job_seekers
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete own job seeker profile" ON job_seekers;
CREATE POLICY "Users can delete own job seeker profile" ON job_seekers
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات job_seeker_likes
DROP POLICY IF EXISTS "Authenticated users can like job seekers" ON job_seeker_likes;
CREATE POLICY "Authenticated users can like job seekers" ON job_seeker_likes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can remove own likes" ON job_seeker_likes;
CREATE POLICY "Users can remove own likes" ON job_seeker_likes
    FOR DELETE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can view likes" ON job_seeker_likes;
CREATE POLICY "Users can view likes" ON job_seeker_likes
    FOR SELECT USING (true);

-- سياسات job_seeker_saves
DROP POLICY IF EXISTS "Authenticated users can save job seekers" ON job_seeker_saves;
CREATE POLICY "Authenticated users can save job seekers" ON job_seeker_saves
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can remove own saves" ON job_seeker_saves;
CREATE POLICY "Users can remove own saves" ON job_seeker_saves
    FOR DELETE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can view own saves" ON job_seeker_saves;
CREATE POLICY "Users can view own saves" ON job_seeker_saves
    FOR SELECT USING (auth.uid() = user_id);

-- ===================================
-- دوال مساعدة لقسم البحث عن عمل
-- ===================================

-- دالة زيادة عدد الإعجابات
CREATE OR REPLACE FUNCTION update_job_seeker_likes_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE job_seekers
        SET likes_count = likes_count + 1
        WHERE id = NEW.seeker_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE job_seekers
        SET likes_count = GREATEST(likes_count - 1, 0)
        WHERE id = OLD.seeker_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- تطبيق دالة الإعجابات
DROP TRIGGER IF EXISTS job_seeker_likes_count_trigger ON job_seeker_likes;
CREATE TRIGGER job_seeker_likes_count_trigger
    AFTER INSERT OR DELETE ON job_seeker_likes
    FOR EACH ROW EXECUTE FUNCTION update_job_seeker_likes_count();

-- دالة زيادة عدد المشاهدات
CREATE OR REPLACE FUNCTION increment_job_seeker_views(seeker_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE job_seekers
    SET views_count = views_count + 1
    WHERE id = seeker_id;
END;
$$ LANGUAGE plpgsql;

-- دالة تحديث updated_at للباحثين عن عمل
DROP TRIGGER IF EXISTS update_job_seekers_updated_at ON job_seekers;
CREATE TRIGGER update_job_seekers_updated_at
    BEFORE UPDATE ON job_seekers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===================================
-- ملاحظات مهمة للتطبيق
-- ===================================

/*
1. تأكد من وجود جدول profiles مع الحقول:
   - full_name
   - avatar_url
   - is_verified

2. تأكد من تفعيل RLS على جدول profiles

3. للحصول على أفضل أداء، استخدم الاستعلامات مع:
   - التصفية حسب is_active = true
   - الترتيب حسب created_at DESC
   - استخدام LIMIT للصفحات

4. لدعم التصفية المتقدمة، يمكن إضافة فهارس مركبة:
   CREATE INDEX idx_charity_items_city_category ON charity_items(city, category);
   CREATE INDEX idx_charity_items_type_urgent ON charity_items(type, is_urgent);
   CREATE INDEX idx_job_seekers_city_category ON job_seekers(current_city, category);

5. لدعم البحث النصي، يمكن إضافة:
   CREATE INDEX idx_charity_items_search ON charity_items
   USING gin(to_tsvector('arabic', title || ' ' || description));

   CREATE INDEX idx_job_seekers_search ON job_seekers
   USING gin(to_tsvector('arabic', full_name || ' ' || description || ' ' || array_to_string(skills, ' ')));
*/
