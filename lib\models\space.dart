import 'package:flutter/material.dart';

// فئات المساحات
enum SpaceCategory {
  business,      // أعمال
  education,     // تعليم
  health,        // صحة
  technology,    // تكنولوجيا
  art,          // فن
  sports,       // رياضة
  food,         // طعام
  travel,       // سفر
  fashion,      // موضة
  entertainment, // ترفيه
  news,         // أخبار
  nonprofit,    // غير ربحي
  personal,     // شخصي
  other,        // أخرى
}

// حالة المساحة
enum SpaceStatus {
  active,    // نشطة
  inactive,  // غير نشطة
  suspended, // معلقة
}

// إعدادات الخصوصية
enum SpacePrivacy {
  public,    // عامة
  private,   // خاصة
  followers, // للمتابعين فقط
}

// نموذج المساحة
class Space {
  final String id;
  final String ownerId;
  final String ownerName;
  final String name;
  final String description;
  final String? goal;
  final String? username; // المعرف الفريد
  final SpaceCategory category;
  final String? profession;
  final SpaceStatus status;
  final SpacePrivacy privacy;
  
  // الصور
  final String? coverImage;
  final String? profileImage;
  
  // معلومات التواصل
  final String? phoneNumber;
  final String? email;
  final String? website;
  final Map<String, String> socialLinks;
  
  // الإحصائيات
  final int followersCount;
  final int postsCount;
  final int viewsCount;
  
  // التواريخ
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // حالة المتابعة للمستخدم الحالي
  final bool isFollowing;
  final bool isOwner;

  const Space({
    required this.id,
    required this.ownerId,
    required this.ownerName,
    required this.name,
    required this.description,
    this.goal,
    this.username,
    required this.category,
    this.profession,
    this.status = SpaceStatus.active,
    this.privacy = SpacePrivacy.public,
    this.coverImage,
    this.profileImage,
    this.phoneNumber,
    this.email,
    this.website,
    this.socialLinks = const {},
    this.followersCount = 0,
    this.postsCount = 0,
    this.viewsCount = 0,
    required this.createdAt,
    required this.updatedAt,
    this.isFollowing = false,
    this.isOwner = false,
  });

  factory Space.fromJson(Map<String, dynamic> json) {
    return Space(
      id: json['id'] ?? '',
      ownerId: json['owner_id'] ?? '',
      ownerName: json['owner_name'] ?? '',
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      goal: json['goal'],
      username: json['username'],
      category: SpaceCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => SpaceCategory.other,
      ),
      profession: json['profession'],
      status: SpaceStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => SpaceStatus.active,
      ),
      privacy: SpacePrivacy.values.firstWhere(
        (e) => e.name == json['privacy'],
        orElse: () => SpacePrivacy.public,
      ),
      coverImage: json['cover_image'] ?? json['cover_url'],
      profileImage: json['profile_image'] ?? json['avatar_url'],
      phoneNumber: json['phone_number'],
      email: json['email'],
      website: json['website'],
      socialLinks: Map<String, String>.from(json['social_links'] ?? {}),
      followersCount: json['followers_count'] ?? 0,
      postsCount: json['posts_count'] ?? 0,
      viewsCount: json['views_count'] ?? 0,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      isFollowing: json['is_following'] ?? false,
      isOwner: json['is_owner'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'owner_id': ownerId,
      'owner_name': ownerName,
      'name': name,
      'description': description,
      'goal': goal,
      'username': username,
      'category': category.name,
      'profession': profession,
      'status': status.name,
      'privacy': privacy.name,
      'cover_image': coverImage,
      'profile_image': profileImage,
      'phone_number': phoneNumber,
      'email': email,
      'website': website,
      'social_links': socialLinks,
      'followers_count': followersCount,
      'posts_count': postsCount,
      'views_count': viewsCount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Space copyWith({
    String? id,
    String? ownerId,
    String? ownerName,
    String? name,
    String? description,
    String? goal,
    String? username,
    SpaceCategory? category,
    String? profession,
    SpaceStatus? status,
    SpacePrivacy? privacy,
    String? coverImage,
    String? profileImage,
    String? phoneNumber,
    String? email,
    String? website,
    Map<String, String>? socialLinks,
    int? followersCount,
    int? postsCount,
    int? viewsCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isFollowing,
    bool? isOwner,
  }) {
    return Space(
      id: id ?? this.id,
      ownerId: ownerId ?? this.ownerId,
      ownerName: ownerName ?? this.ownerName,
      name: name ?? this.name,
      description: description ?? this.description,
      goal: goal ?? this.goal,
      username: username ?? this.username,
      category: category ?? this.category,
      profession: profession ?? this.profession,
      status: status ?? this.status,
      privacy: privacy ?? this.privacy,
      coverImage: coverImage ?? this.coverImage,
      profileImage: profileImage ?? this.profileImage,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      email: email ?? this.email,
      website: website ?? this.website,
      socialLinks: socialLinks ?? this.socialLinks,
      followersCount: followersCount ?? this.followersCount,
      postsCount: postsCount ?? this.postsCount,
      viewsCount: viewsCount ?? this.viewsCount,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isFollowing: isFollowing ?? this.isFollowing,
      isOwner: isOwner ?? this.isOwner,
    );
  }
}

// مساعدات للفئات
class SpaceCategoryHelper {
  static String getCategoryName(SpaceCategory category) {
    switch (category) {
      case SpaceCategory.business:
        return 'أعمال';
      case SpaceCategory.education:
        return 'تعليم';
      case SpaceCategory.health:
        return 'صحة';
      case SpaceCategory.technology:
        return 'تكنولوجيا';
      case SpaceCategory.art:
        return 'فن';
      case SpaceCategory.sports:
        return 'رياضة';
      case SpaceCategory.food:
        return 'طعام';
      case SpaceCategory.travel:
        return 'سفر';
      case SpaceCategory.fashion:
        return 'موضة';
      case SpaceCategory.entertainment:
        return 'ترفيه';
      case SpaceCategory.news:
        return 'أخبار';
      case SpaceCategory.nonprofit:
        return 'غير ربحي';
      case SpaceCategory.personal:
        return 'شخصي';
      case SpaceCategory.other:
        return 'أخرى';
    }
  }

  static IconData getCategoryIcon(SpaceCategory category) {
    switch (category) {
      case SpaceCategory.business:
        return Icons.business;
      case SpaceCategory.education:
        return Icons.school;
      case SpaceCategory.health:
        return Icons.health_and_safety;
      case SpaceCategory.technology:
        return Icons.computer;
      case SpaceCategory.art:
        return Icons.palette;
      case SpaceCategory.sports:
        return Icons.sports;
      case SpaceCategory.food:
        return Icons.restaurant;
      case SpaceCategory.travel:
        return Icons.travel_explore;
      case SpaceCategory.fashion:
        return Icons.checkroom;
      case SpaceCategory.entertainment:
        return Icons.movie;
      case SpaceCategory.news:
        return Icons.newspaper;
      case SpaceCategory.nonprofit:
        return Icons.volunteer_activism;
      case SpaceCategory.personal:
        return Icons.person;
      case SpaceCategory.other:
        return Icons.category;
    }
  }

  static Color getCategoryColor(SpaceCategory category) {
    switch (category) {
      case SpaceCategory.business:
        return Colors.blue;
      case SpaceCategory.education:
        return Colors.green;
      case SpaceCategory.health:
        return Colors.red;
      case SpaceCategory.technology:
        return Colors.purple;
      case SpaceCategory.art:
        return Colors.pink;
      case SpaceCategory.sports:
        return Colors.orange;
      case SpaceCategory.food:
        return Colors.brown;
      case SpaceCategory.travel:
        return Colors.teal;
      case SpaceCategory.fashion:
        return Colors.indigo;
      case SpaceCategory.entertainment:
        return Colors.amber;
      case SpaceCategory.news:
        return Colors.blueGrey;
      case SpaceCategory.nonprofit:
        return Colors.lightGreen;
      case SpaceCategory.personal:
        return Colors.cyan;
      case SpaceCategory.other:
        return Colors.grey;
    }
  }
}
