-- =============================================================
--  Communities Simple Update Script
--  تحديث بسيط لجدول المجتمعات - إضافة الحقول الأساسية فقط
-- =============================================================

-- إضافة الحقول الجديدة لجدول communities
-- -------------------------------------------------------

-- إضافة حقل الصورة الشخصية للمجتمع
ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS avatar_url TEXT;

-- إضافة حقول الحالة (أرشفة وتعطيل)
ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS is_archived BOOLEAN DEFAULT FALSE;

ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS is_disabled BOOLEAN DEFAULT FALSE;

-- إضافة إعدادات المحتوى
ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS allow_member_posts BOOLEAN DEFAULT TRUE;

ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS require_approval BOOLEAN DEFAULT FALSE;

ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS allow_comments BOOLEAN DEFAULT TRUE;

ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS allow_invites BOOLEAN DEFAULT TRUE;

-- إضافة إعدادات الصلاحيات
ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS post_permission TEXT DEFAULT 'members';

ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS join_type TEXT DEFAULT 'open';

-- إنشاء bucket للصور إذا لم يكن موجوداً
-- -------------------------------------------------------

INSERT INTO storage.buckets (id, name, public)
VALUES ('community-images', 'community-images', true)
ON CONFLICT (id) DO NOTHING;

-- إنشاء فهارس أساسية للأداء
-- -------------------------------------------------------

CREATE INDEX IF NOT EXISTS communities_active_idx 
ON public.communities (is_archived, is_disabled, created_at DESC) 
WHERE is_archived = FALSE AND is_disabled = FALSE;

CREATE INDEX IF NOT EXISTS communities_owner_idx 
ON public.communities (owner_id);

-- =============================================================
--  انتهى التحديث البسيط
-- =============================================================

-- للتحقق من نجاح التحديث:
SELECT column_name, data_type, is_nullable, column_default 
FROM information_schema.columns 
WHERE table_name = 'communities' 
AND column_name IN (
  'avatar_url', 'is_archived', 'is_disabled', 
  'allow_member_posts', 'require_approval', 'allow_comments', 
  'allow_invites', 'post_permission', 'join_type'
)
ORDER BY ordinal_position;
