import 'package:flutter/material.dart';
import '../models/community_comment.dart';
import '../supabase_service.dart';
import 'interactive_verified_badge.dart';

class CommunityCommentCard extends StatefulWidget {
  final CommunityComment comment;
  final VoidCallback onChanged;
  const CommunityCommentCard({super.key, required this.comment, required this.onChanged});

  @override
  State<CommunityCommentCard> createState() => _CommunityCommentCardState();
}

class _CommunityCommentCardState extends State<CommunityCommentCard> {
  late CommunityComment _c;
  @override
  void initState() {
    super.initState();
    _c = widget.comment;
  }

  Future<void> _vote(int v) async {
    if (_c.userVote == v) v = 0;
    await SupabaseService().voteCommunityComment(_c.id, v);
    setState(() {
      int up = _c.upVotes;
      int down = _c.downVotes;
      int? prev = _c.userVote;
      if (prev == 1) up--; if (prev == -1) down--;
      if (v == 1) up++; if (v == -1) down++;
      _c = CommunityComment(
        id: _c.id,
        postId: _c.postId,
        userId: _c.userId,
        userName: _c.userName,
        userAvatar: _c.userAvatar,
        content: _c.content,
        createdAt: _c.createdAt,
        upVotes: up,
        downVotes: down,
        userVote: v==0? null: v,
      );
    });
    widget.onChanged();
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: CircleAvatar(backgroundImage: _c.userAvatar.isNotEmpty? NetworkImage(_c.userAvatar):null),
      title: Row(
        children: [
          Text(_c.userName, style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14)),
          if (_c.isVerified) ...[
            const SizedBox(width: 4),
            InteractiveVerifiedBadge(
              size: 12,
              userName: _c.userName,
            ),
          ],
        ],
      ),
      subtitle: Text(_c.content),
      trailing: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          InkWell(onTap: ()=>_vote(1), child: Icon(Icons.arrow_upward, size:20, color: _c.userVote==1?Colors.orange:Colors.grey)),
          Text('${_c.upVotes-_c.downVotes}', style: const TextStyle(fontSize:12)),
          InkWell(onTap: ()=>_vote(-1), child: Icon(Icons.arrow_downward, size:20, color: _c.userVote==-1?Colors.blue:Colors.grey)),
        ],
      ),
    );
  }
} 