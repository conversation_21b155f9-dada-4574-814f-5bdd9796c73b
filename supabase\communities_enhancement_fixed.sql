-- =============================================================
--  Communities Enhancement Script for Supabase (FIXED VERSION)
--  تحسين نظام المجتمعات - إضافة حقول جديدة وميزات متقدمة
-- =============================================================
--  يُنفَّذ هذا الملف مرّة واحدة من تبويب SQL Editor فى لوحة Supabase.
--  سيُضيف:
--    • حقول جديدة لجدول communities
--    • إعدادات متقدمة للمجتمعات
--    • دعم الصور الشخصية وصور الغلاف
--    • نظام الأرشفة والتعطيل
--    • إعدادات الخصوصية والمحتوى
-- =============================================================

-- 1) إضافة الحقول الجديدة لجدول communities
-- -------------------------------------------------------

-- إضافة حقل الصورة الشخصية للمجتمع
ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS avatar_url TEXT;

-- إضافة حقول الحالة (أرشفة وتعطيل)
ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS is_archived BOOLEAN DEFAULT FALSE;

ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS is_disabled BOOLEAN DEFAULT FALSE;

-- إضافة إعدادات المحتوى
ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS allow_member_posts BOOLEAN DEFAULT TRUE;

ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS require_approval BOOLEAN DEFAULT FALSE;

ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS allow_comments BOOLEAN DEFAULT TRUE;

ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS allow_invites BOOLEAN DEFAULT TRUE;

-- إضافة إعدادات الصلاحيات
ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS post_permission TEXT DEFAULT 'members';

ALTER TABLE public.communities 
ADD COLUMN IF NOT EXISTS join_type TEXT DEFAULT 'open';

-- إضافة قيود للتحقق من صحة البيانات
DO $$
BEGIN
  -- إضافة قيد للتحقق من post_permission
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'communities_post_permission_check' 
    AND table_name = 'communities'
  ) THEN
    ALTER TABLE public.communities 
    ADD CONSTRAINT communities_post_permission_check 
    CHECK (post_permission IN ('all', 'members', 'admins'));
  END IF;

  -- إضافة قيد للتحقق من join_type
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'communities_join_type_check' 
    AND table_name = 'communities'
  ) THEN
    ALTER TABLE public.communities 
    ADD CONSTRAINT communities_join_type_check 
    CHECK (join_type IN ('open', 'approval', 'invite_only'));
  END IF;
END $$;

-- 2) إنشاء فهارس للأداء
-- -------------------------------------------------------

-- فهرس للمجتمعات النشطة (غير مؤرشفة وغير معطلة)
CREATE INDEX IF NOT EXISTS communities_active_idx 
ON public.communities (is_archived, is_disabled, created_at DESC) 
WHERE is_archived = FALSE AND is_disabled = FALSE;

-- فهرس للبحث بالفئة
CREATE INDEX IF NOT EXISTS communities_category_idx 
ON public.communities (category) 
WHERE category IS NOT NULL;

-- فهرس للمالك
CREATE INDEX IF NOT EXISTS communities_owner_idx 
ON public.communities (owner_id);

-- 3) إنشاء bucket للصور إذا لم يكن موجوداً
-- -------------------------------------------------------

-- إنشاء bucket لصور المجتمعات
INSERT INTO storage.buckets (id, name, public)
VALUES ('community-images', 'community-images', true)
ON CONFLICT (id) DO NOTHING;

-- 4) إنشاء view للمجتمعات النشطة
-- -------------------------------------------------------

DROP VIEW IF EXISTS public.active_communities;
CREATE VIEW public.active_communities AS
SELECT 
  c.*,
  COALESCE(cm.members_count, 0) as members_count
FROM public.communities c
LEFT JOIN (
  SELECT community_id, COUNT(*) as members_count
  FROM public.community_members
  GROUP BY community_id
) cm ON c.id = cm.community_id
WHERE c.is_archived = FALSE AND c.is_disabled = FALSE
ORDER BY c.created_at DESC;

-- 5) إنشاء دالة للتحقق من صلاحيات المجتمع
-- -------------------------------------------------------

CREATE OR REPLACE FUNCTION public.check_community_permission(
  community_id UUID,
  user_id UUID,
  required_permission TEXT DEFAULT 'member'
) RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  community_record RECORD;
  member_record RECORD;
BEGIN
  -- جلب بيانات المجتمع
  SELECT * INTO community_record 
  FROM public.communities 
  WHERE id = community_id;
  
  IF NOT FOUND THEN
    RETURN FALSE;
  END IF;
  
  -- التحقق من أن المجتمع نشط
  IF community_record.is_disabled OR community_record.is_archived THEN
    -- السماح للمالك فقط بالوصول للمجتمعات المعطلة/المؤرشفة
    RETURN community_record.owner_id::text = user_id::text;
  END IF;
  
  -- التحقق من العضوية
  SELECT * INTO member_record 
  FROM public.community_members 
  WHERE community_id = check_community_permission.community_id 
  AND user_id = check_community_permission.user_id;
  
  -- إذا كان المطلوب مجرد عضوية
  IF required_permission = 'member' THEN
    RETURN FOUND OR community_record.owner_id::text = user_id::text;
  END IF;
  
  -- إذا كان المطلوب صلاحيات إدارية
  IF required_permission = 'admin' THEN
    RETURN (FOUND AND member_record.role = 'admin') 
           OR community_record.owner_id::text = user_id::text;
  END IF;
  
  -- إذا كان المطلوب صلاحيات المالك
  IF required_permission = 'owner' THEN
    RETURN community_record.owner_id::text = user_id::text;
  END IF;
  
  RETURN FALSE;
END;
$$;

-- 6) تحديث سياسات RLS للمجتمعات
-- -------------------------------------------------------

-- تفعيل RLS على جدول المجتمعات
ALTER TABLE public.communities ENABLE ROW LEVEL SECURITY;

-- حذف السياسات القديمة
DROP POLICY IF EXISTS "Public can view active communities" ON public.communities;
DROP POLICY IF EXISTS "Owners can update their communities" ON public.communities;
DROP POLICY IF EXISTS "Owners can delete their communities" ON public.communities;
DROP POLICY IF EXISTS "Users can create communities" ON public.communities;

-- السماح للجميع بقراءة المجتمعات النشطة العامة
CREATE POLICY "Public can view active communities"
ON public.communities FOR SELECT
USING (
  (is_archived = FALSE AND is_disabled = FALSE AND is_private = FALSE)
  OR auth.uid()::text = owner_id
  OR EXISTS (
    SELECT 1 FROM public.community_members 
    WHERE community_id = id AND user_id = auth.uid()
  )
);

-- السماح للمستخدمين المسجلين بإنشاء مجتمعات
CREATE POLICY "Users can create communities"
ON public.communities FOR INSERT
WITH CHECK (auth.uid() IS NOT NULL AND auth.uid()::text = owner_id);

-- السماح للمالكين بتحديث مجتمعاتهم
CREATE POLICY "Owners can update their communities"
ON public.communities FOR UPDATE
USING (auth.uid()::text = owner_id);

-- السماح للمالكين بحذف مجتمعاتهم
CREATE POLICY "Owners can delete their communities"
ON public.communities FOR DELETE
USING (auth.uid()::text = owner_id);

-- 7) إعداد سياسات التخزين للصور
-- -------------------------------------------------------

-- تفعيل RLS على storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- حذف السياسات القديمة للصور
DROP POLICY IF EXISTS "Public can view community images" ON storage.objects;
DROP POLICY IF EXISTS "Community owners can upload images" ON storage.objects;
DROP POLICY IF EXISTS "Community owners can update images" ON storage.objects;
DROP POLICY IF EXISTS "Community owners can delete images" ON storage.objects;

-- السماح للجميع بقراءة صور المجتمعات
CREATE POLICY "Public can view community images"
ON storage.objects FOR SELECT
USING (bucket_id = 'community-images');

-- السماح لمالكي المجتمعات برفع الصور
CREATE POLICY "Community owners can upload images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'community-images' 
  AND auth.uid() IS NOT NULL
  AND EXISTS (
    SELECT 1 FROM public.communities 
    WHERE owner_id = auth.uid()::text 
    AND id::text = split_part(name, '/', 2)
  )
);

-- السماح لمالكي المجتمعات بتحديث الصور
CREATE POLICY "Community owners can update images"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'community-images' 
  AND auth.uid() IS NOT NULL
  AND EXISTS (
    SELECT 1 FROM public.communities 
    WHERE owner_id = auth.uid()::text 
    AND id::text = split_part(name, '/', 2)
  )
);

-- السماح لمالكي المجتمعات بحذف الصور
CREATE POLICY "Community owners can delete images"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'community-images' 
  AND auth.uid() IS NOT NULL
  AND EXISTS (
    SELECT 1 FROM public.communities 
    WHERE owner_id = auth.uid()::text 
    AND id::text = split_part(name, '/', 2)
  )
);

-- =============================================================
--  انتهى السكربت - تم تحسين نظام المجتمعات بنجاح!
-- =============================================================

-- ملاحظات مهمة:
-- 1. تأكد من تشغيل هذا السكربت بصلاحيات المدير
-- 2. قم بعمل نسخة احتياطية من قاعدة البيانات قبل التنفيذ
-- 3. تحقق من أن جميع التطبيقات متوافقة مع التغييرات الجديدة
-- 4. قم بتحديث أذونات RLS حسب احتياجاتك الأمنية

-- للتحقق من نجاح التحديث، يمكنك تشغيل:
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'communities' 
-- ORDER BY ordinal_position;
