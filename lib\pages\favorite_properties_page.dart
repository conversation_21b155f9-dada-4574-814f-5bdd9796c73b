import 'package:flutter/material.dart';
import '../models/real_estate_property.dart';
import '../services/real_estate_service.dart';
import '../widgets/property_card.dart';
import 'property_details_page.dart';

class FavoritePropertiesPage extends StatefulWidget {
  const FavoritePropertiesPage({super.key});

  @override
  State<FavoritePropertiesPage> createState() => _FavoritePropertiesPageState();
}

class _FavoritePropertiesPageState extends State<FavoritePropertiesPage> {
  final RealEstateService _realEstateService = RealEstateService();
  List<RealEstateProperty> _favoriteProperties = [];
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _loadFavoriteProperties();
  }

  Future<void> _loadFavoriteProperties() async {
    setState(() => _loading = true);
    try {
      final properties = await _realEstateService.getFavoriteProperties();
      setState(() {
        _favoriteProperties = properties;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل المفضلة: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_favoriteProperties.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadFavoriteProperties,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _favoriteProperties.length,
        itemBuilder: (context, index) {
          final property = _favoriteProperties[index];
          return PropertyCard(
            property: property,
            onTap: () => _openPropertyDetails(property),
            onFavoriteToggle: () => _toggleFavorite(property.id),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: Colors.red[50],
              shape: BoxShape.circle,
            ),
            child: Icon(Icons.favorite_border, size: 64, color: Colors.red[600]),
          ),
          const SizedBox(height: 24),
          Text(
            'لا توجد عقارات مفضلة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'لم تقم بإضافة أي عقارات للمفضلة بعد',
            style: TextStyle(fontSize: 16, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // الانتقال لتبويب جميع العقارات
              DefaultTabController.of(context)?.animateTo(0);
            },
            icon: const Icon(Icons.search),
            label: const Text('تصفح العقارات'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  void _openPropertyDetails(RealEstateProperty property) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => PropertyDetailsPage(property: property),
      ),
    );
  }

  Future<void> _toggleFavorite(String propertyId) async {
    try {
      await _realEstateService.toggleFavorite(propertyId);
      _loadFavoriteProperties(); // إعادة تحميل لتحديث القائمة
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحديث المفضلة: $e')),
        );
      }
    }
  }
}
