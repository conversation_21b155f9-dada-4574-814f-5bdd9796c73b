import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/charity_item.dart';

class CharityService {
  final SupabaseClient _client = Supabase.instance.client;

  // الحصول على عناصر التبرع
  Future<List<CharityItem>> getDonationItems({
    String? city,
    String? category,
  }) async {
    try {
      final response = await _client
          .from('charity_items')
          .select('*')
          .eq('type', 'donation')
          .eq('is_active', true)
          .eq('is_completed', false)
          .order('created_at', ascending: false);

      return (response as List).map((item) => _mapToCharityItem(item)).toList();
    } catch (e) {
      throw Exception('فشل في جلب التبرعات: $e');
    }
  }

  // الحصول على طلبات المساعدة
  Future<List<CharityItem>> getRequestItems({
    String? city,
    String? category,
  }) async {
    try {
      final response = await _client
          .from('charity_items')
          .select('*')
          .eq('type', 'request')
          .eq('is_active', true)
          .eq('is_completed', false)
          .order('created_at', ascending: false);

      return (response as List).map((item) => _mapToCharityItem(item)).toList();
    } catch (e) {
      throw Exception('فشل في جلب طلبات المساعدة: $e');
    }
  }

  // الحصول على الحالات الطارئة
  Future<List<CharityItem>> getUrgentItems({
    String? city,
    String? category,
  }) async {
    try {
      final response = await _client
          .from('charity_items')
          .select('*')
          .eq('is_urgent', true)
          .eq('is_active', true)
          .eq('is_completed', false)
          .order('created_at', ascending: false);

      return (response as List).map((item) => _mapToCharityItem(item)).toList();
    } catch (e) {
      throw Exception('فشل في جلب الحالات الطارئة: $e');
    }
  }

  // إضافة عنصر صدقة جديد
  Future<void> addCharityItem(CharityItem item) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client.from('charity_items').insert({
        'user_id': userId,
        'title': item.title,
        'description': item.description,
        'type': item.type.name,
        'category': item.category.name,
        'condition': item.condition.name,
        'delivery_method': item.deliveryMethod.name,
        'city': item.city,
        'country': item.country,
        'phone_number': item.phoneNumber,
        'images': item.images,
        'is_urgent': item.isUrgent,
        'is_anonymous': item.isAnonymous,
      });
    } catch (e) {
      throw Exception('فشل في إضافة العنصر: $e');
    }
  }

  // إبداء الاهتمام بعنصر
  Future<void> showInterest(String itemId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // التحقق من عدم وجود اهتمام سابق
      final existing = await _client
          .from('charity_interests')
          .select('id')
          .eq('item_id', itemId)
          .eq('user_id', userId)
          .maybeSingle();

      if (existing != null) {
        throw Exception('لقد أبديت اهتماماً بهذا العنصر من قبل');
      }

      // إضافة الاهتمام
      await _client.from('charity_interests').insert({
        'item_id': itemId,
        'user_id': userId,
      });
    } catch (e) {
      throw Exception('فشل في إبداء الاهتمام: $e');
    }
  }

  // تحديث حالة العنصر إلى مكتمل
  Future<void> markAsCompleted(String itemId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('charity_items')
          .update({
            'is_completed': true,
            'completed_at': DateTime.now().toIso8601String(),
          })
          .eq('id', itemId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في تحديث الحالة: $e');
    }
  }

  // حذف عنصر صدقة
  Future<void> deleteCharityItem(String itemId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('charity_items')
          .delete()
          .eq('id', itemId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في حذف العنصر: $e');
    }
  }

  // الإبلاغ عن عنصر
  Future<void> reportItem(String itemId, String reason) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client.from('charity_reports').insert({
        'item_id': itemId,
        'reporter_id': userId,
        'reason': reason,
      });
    } catch (e) {
      throw Exception('فشل في الإبلاغ: $e');
    }
  }

  // الحصول على إحصائيات الصدقات
  Future<Map<String, int>> getCharityStats() async {
    try {
      // عدد العناصر المكتملة
      final completedResponse = await _client
          .from('charity_items')
          .select('id')
          .eq('is_completed', true);

      // عدد المتبرعين النشطين هذا الأسبوع
      final weekAgo = DateTime.now().subtract(const Duration(days: 7));
      final activeResponse = await _client
          .from('charity_items')
          .select('user_id')
          .gte('created_at', weekAgo.toIso8601String());

      // عدد العناصر هذا الأسبوع
      final thisWeekResponse = await _client
          .from('charity_items')
          .select('id')
          .gte('created_at', weekAgo.toIso8601String());

      final uniqueUsers = (activeResponse as List)
          .map((item) => item['user_id'])
          .toSet()
          .length;

      return {
        'completed': (completedResponse as List).length,
        'active_users': uniqueUsers,
        'this_week': (thisWeekResponse as List).length,
      };
    } catch (e) {
      return {
        'completed': 0,
        'active_users': 0,
        'this_week': 0,
      };
    }
  }

  // الحصول على عناصر المستخدم
  Future<List<CharityItem>> getUserCharityItems() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final response = await _client
          .from('charity_items')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      return (response as List).map((item) => _mapToCharityItem(item)).toList();
    } catch (e) {
      throw Exception('فشل في جلب عناصر المستخدم: $e');
    }
  }

  // الحصول على اهتمامات المستخدم
  Future<List<CharityItem>> getUserInterests() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // الحصول على معرفات العناصر المهتم بها
      final interestsResponse = await _client
          .from('charity_interests')
          .select('item_id')
          .eq('user_id', userId);

      if ((interestsResponse as List).isEmpty) {
        return [];
      }

      final itemIds = (interestsResponse as List)
          .map((item) => item['item_id'])
          .toList();

      // الحصول على تفاصيل العناصر
      final itemsResponse = await _client
          .from('charity_items')
          .select('*')
          .inFilter('id', itemIds)
          .order('created_at', ascending: false);

      return (itemsResponse as List)
          .map((item) => _mapToCharityItem(item))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب اهتمامات المستخدم: $e');
    }
  }

  // إلغاء الاهتمام
  Future<void> removeInterest(String itemId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('charity_interests')
          .delete()
          .eq('item_id', itemId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في إلغاء الاهتمام: $e');
    }
  }

  // تحديث عنصر صدقة
  Future<void> updateCharityItem(
    String itemId, {
    String? title,
    String? description,
    CharityCategory? category,
    CharityCondition? condition,
    DeliveryMethod? deliveryMethod,
    String? city,
    String? phoneNumber,
    bool? isUrgent,
    bool? isAnonymous,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final updateData = <String, dynamic>{};

      if (title != null) updateData['title'] = title;
      if (description != null) updateData['description'] = description;
      if (category != null) updateData['category'] = category.name;
      if (condition != null) updateData['condition'] = condition.name;
      if (deliveryMethod != null) updateData['delivery_method'] = deliveryMethod.name;
      if (city != null) updateData['city'] = city;
      if (phoneNumber != null) updateData['phone_number'] = phoneNumber;
      if (isUrgent != null) updateData['is_urgent'] = isUrgent;
      if (isAnonymous != null) updateData['is_anonymous'] = isAnonymous;

      if (updateData.isNotEmpty) {
        await _client
            .from('charity_items')
            .update(updateData)
            .eq('id', itemId)
            .eq('user_id', userId);
      }
    } catch (e) {
      throw Exception('فشل في تحديث العنصر: $e');
    }
  }

  // حذف جميع بيانات المستخدم
  Future<void> deleteAllUserData() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // حذف جميع الاهتمامات
      await _client
          .from('charity_interests')
          .delete()
          .eq('user_id', userId);

      // حذف جميع العناصر
      await _client
          .from('charity_items')
          .delete()
          .eq('user_id', userId);

      // حذف جميع البلاغات
      await _client
          .from('charity_reports')
          .delete()
          .eq('reporter_id', userId);
    } catch (e) {
      throw Exception('فشل في حذف البيانات: $e');
    }
  }

  // تحويل البيانات إلى CharityItem
  CharityItem _mapToCharityItem(Map<String, dynamic> data) {
    return CharityItem(
      id: data['id'],
      userId: data['user_id'],
      userName: data['is_anonymous'] == true ? 'متبرع كريم' : 'مستخدم',
      userAvatar: '',
      isVerified: false,
      isAnonymous: data['is_anonymous'] ?? false,
      title: data['title'],
      description: data['description'],
      type: CharityType.values.firstWhere(
        (t) => t.name == data['type'],
        orElse: () => CharityType.donation,
      ),
      category: CharityCategory.values.firstWhere(
        (c) => c.name == data['category'],
        orElse: () => CharityCategory.other,
      ),
      condition: CharityCondition.values.firstWhere(
        (c) => c.name == data['condition'],
        orElse: () => CharityCondition.good,
      ),
      deliveryMethod: DeliveryMethod.values.firstWhere(
        (d) => d.name == data['delivery_method'],
        orElse: () => DeliveryMethod.hand,
      ),
      city: data['city'],
      country: data['country'] ?? 'السعودية',
      phoneNumber: data['phone_number'],
      images: List<String>.from(data['images'] ?? []),
      isUrgent: data['is_urgent'] ?? false,
      isCompleted: data['is_completed'] ?? false,
      isActive: data['is_active'] ?? true,
      interestCount: data['interest_count'] ?? 0,
      createdAt: DateTime.parse(data['created_at']),
      completedAt: data['completed_at'] != null
          ? DateTime.parse(data['completed_at'])
          : null,
    );
  }
}
