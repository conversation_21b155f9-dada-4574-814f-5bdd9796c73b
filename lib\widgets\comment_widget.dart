import 'package:flutter/material.dart';
import '../models/comment.dart';
import 'verified_badge.dart';
import 'interactive_verified_badge.dart';

class CommentWidget extends StatelessWidget {
  final Comment comment;
  final Function(Comment)? onReply;
  final Function(String)? onLike;
  final Function(String)? onDelete;
  final Function(String)? onReport;

  const CommentWidget({
    super.key,
    required this.comment,
    this.onReply,
    this.onLike,
    this.onDelete,
    this.onReport,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // معلومات المؤلف
          Row(
            children: [
              CircleAvatar(
                radius: 16,
                backgroundColor: Colors.purple[100],
                backgroundImage: comment.userAvatar.isNotEmpty
                    ? NetworkImage(comment.userAvatar)
                    : null,
                child: comment.userAvatar.isEmpty
                    ? Icon(Icons.person, size: 16, color: Colors.purple[600])
                    : null,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          comment.userName,
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        ),
                        if (comment.isVerified) ...[
                          const SizedBox(width: 4),
                          InteractiveVerifiedBadge(
                            size: 12,
                            userName: comment.userName,
                          ),
                        ],
                      ],
                    ),
                    Text(
                      _formatTime(comment.createdAt),
                      style: TextStyle(
                        fontSize: 10,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              // قائمة الخيارات
              PopupMenuButton<String>(
                icon: Icon(Icons.more_vert, size: 16, color: Colors.grey[600]),
                onSelected: (value) {
                  switch (value) {
                    case 'reply':
                      if (onReply != null) onReply!(comment);
                      break;
                    case 'delete':
                      if (onDelete != null) onDelete!(comment.id);
                      break;
                    case 'report':
                      if (onReport != null) onReport!(comment.id);
                      break;
                  }
                },
                itemBuilder: (context) => [
                  const PopupMenuItem(
                    value: 'reply',
                    child: Row(
                      children: [
                        Icon(Icons.reply, size: 16),
                        SizedBox(width: 8),
                        Text('رد'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(Icons.delete, size: 16, color: Colors.red),
                        SizedBox(width: 8),
                        Text('حذف', style: TextStyle(color: Colors.red)),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'report',
                    child: Row(
                      children: [
                        Icon(Icons.report, size: 16, color: Colors.orange),
                        SizedBox(width: 8),
                        Text('إبلاغ', style: TextStyle(color: Colors.orange)),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          
          const SizedBox(height: 8),
          
          // محتوى التعليق
          Text(
            comment.content,
            style: const TextStyle(fontSize: 14),
          ),
          
          const SizedBox(height: 8),
          
          // أزرار التفاعل
          Row(
            children: [
              // زر الإعجاب
              InkWell(
                onTap: () {
                  if (onLike != null) onLike!(comment.id);
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.favorite_border,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${comment.likesCount}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(width: 16),
              
              // زر الرد
              InkWell(
                onTap: () {
                  if (onReply != null) onReply!(comment);
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.reply, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      'رد',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              
              const Spacer(),
              
              // عدد الردود
              if (comment.replies.isNotEmpty)
                Text(
                  '${comment.replies.length} رد',
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
