-- التحقق من قاعدة البيانات مع رسائل خطأ مفصلة
-- Debug database issue with detailed error messages

-- 1. التحقق من وجود جدول posts
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'posts') THEN
        RAISE NOTICE 'جدول posts موجود';
    ELSE
        RAISE EXCEPTION 'جدول posts غير موجود!';
    END IF;
END $$;

-- 2. التحقق من وجود عمود media_urls
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' AND column_name = 'media_urls'
    ) THEN
        RAISE NOTICE 'عمود media_urls موجود في جدول posts';
    ELSE
        RAISE EXCEPTION 'عمود media_urls غير موجود في جدول posts!';
    END IF;
END $$;

-- 3. التحقق من نوع البيانات
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'posts' AND column_name = 'media_urls';

-- 4. التحقق من وجود bucket media
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'media') THEN
        RAISE NOTICE 'Bucket media موجود';
    ELSE
        RAISE EXCEPTION 'Bucket media غير موجود!';
    END IF;
END $$;

-- 5. التحقق من سياسات التخزين
SELECT 
    policyname, 
    permissive, 
    roles, 
    cmd, 
    qual 
FROM pg_policies 
WHERE tablename = 'objects' AND schemaname = 'storage'
AND policyname LIKE '%media%';

-- 6. التحقق من وجود مستخدمين
SELECT 
    id, 
    email, 
    created_at 
FROM auth.users 
LIMIT 5;

-- 7. إنشاء منشور اختبار مع رسائل خطأ مفصلة
DO $$
DECLARE
    test_user_id UUID;
    test_post_id UUID;
BEGIN
    -- الحصول على أول مستخدم
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    
    IF test_user_id IS NULL THEN
        RAISE EXCEPTION 'لا يوجد مستخدمين في النظام!';
    END IF;
    
    RAISE NOTICE 'معرف المستخدم للاختبار: %', test_user_id;
    
    -- إنشاء منشور اختبار
    INSERT INTO posts (
        user_id,
        content,
        type,
        media_urls,
        created_at
    ) VALUES (
        test_user_id,
        'منشور اختبار للتحقق من المشكلة',
        'image',
        ARRAY['https://example.com/test1.jpg', 'https://example.com/test2.jpg'],
        NOW()
    ) RETURNING id INTO test_post_id;
    
    RAISE NOTICE 'تم إنشاء منشور اختبار بنجاح. معرف المنشور: %', test_post_id;
    
    -- التحقق من المنشور المحدث
    IF EXISTS (
        SELECT 1 FROM posts 
        WHERE id = test_post_id 
        AND media_urls IS NOT NULL 
        AND array_length(media_urls, 1) = 2
    ) THEN
        RAISE NOTICE 'المنشور تم حفظه بشكل صحيح مع media_urls';
    ELSE
        RAISE EXCEPTION 'فشل في حفظ المنشور مع media_urls!';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'خطأ في إنشاء منشور الاختبار: %', SQLERRM;
END $$;

-- 8. عرض المنشورات الأخيرة
SELECT 
    id,
    content,
    type,
    media_url,
    media_urls,
    array_length(media_urls, 1) as media_count,
    created_at,
    user_id
FROM posts 
ORDER BY created_at DESC 
LIMIT 10;

-- 9. التحقق من الأخطاء في السجلات
SELECT 
    'Database check completed successfully' as status,
    (SELECT COUNT(*) FROM posts) as total_posts,
    (SELECT COUNT(*) FROM posts WHERE media_urls IS NOT NULL) as posts_with_media,
    (SELECT COUNT(*) FROM storage.buckets WHERE id = 'media') as media_bucket_exists; 