-- إصلاح مشكلة إحصائيات القصص
-- Fix story statistics issue

-- 1. إنشاء جدول مشاهدات القصص
CREATE TABLE IF NOT EXISTS story_views (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    story_id UUID REFERENCES stories(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(story_id, user_id)
);

-- 2. إنشاء جدول تفاعلات القصص
CREATE TABLE IF NOT EXISTS story_reactions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    story_id UUID REFERENCES stories(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('like', 'love', 'laugh', 'wow', 'sad', 'angry')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(story_id, user_id, type)
);

-- 3. تفعيل RLS
ALTER TABLE story_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE story_reactions ENABLE ROW LEVEL SECURITY;

-- 4. حذف السياسات القديمة إذا كانت موجودة
DROP POLICY IF EXISTS "Users can view their own story views" ON story_views;
DROP POLICY IF EXISTS "Users can insert their own story views" ON story_views;
DROP POLICY IF EXISTS "Story owners can view all views" ON story_views;
DROP POLICY IF EXISTS "Public story views" ON story_views;

DROP POLICY IF EXISTS "Users can view their own story reactions" ON story_reactions;
DROP POLICY IF EXISTS "Users can insert their own story reactions" ON story_reactions;
DROP POLICY IF EXISTS "Users can update their own story reactions" ON story_reactions;
DROP POLICY IF EXISTS "Users can delete their own story reactions" ON story_reactions;
DROP POLICY IF EXISTS "Story owners can view all reactions" ON story_reactions;
DROP POLICY IF EXISTS "Public story reactions" ON story_reactions;

-- 5. إنشاء سياسات جديدة مبسطة
-- سياسات مشاهدات القصص
CREATE POLICY "Public story views" ON story_views
    FOR ALL USING (true);

-- سياسات تفاعلات القصص
CREATE POLICY "Public story reactions" ON story_reactions
    FOR ALL USING (true);

-- 6. إنشاء indexes لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_story_views_story_id ON story_views(story_id);
CREATE INDEX IF NOT EXISTS idx_story_views_user_id ON story_views(user_id);
CREATE INDEX IF NOT EXISTS idx_story_views_created_at ON story_views(created_at);

CREATE INDEX IF NOT EXISTS idx_story_reactions_story_id ON story_reactions(story_id);
CREATE INDEX IF NOT EXISTS idx_story_reactions_user_id ON story_reactions(user_id);
CREATE INDEX IF NOT EXISTS idx_story_reactions_type ON story_reactions(type);
CREATE INDEX IF NOT EXISTS idx_story_reactions_created_at ON story_reactions(created_at);

-- 7. إنشاء دالة لزيادة مشاهدات القصة
CREATE OR REPLACE FUNCTION increment_story_view(p_story_id UUID, p_user_id UUID)
RETURNS VOID AS $$
BEGIN
    INSERT INTO story_views (story_id, user_id)
    VALUES (p_story_id, p_user_id)
    ON CONFLICT (story_id, user_id) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 8. إنشاء دالة لجلب إحصائيات القصة
CREATE OR REPLACE FUNCTION get_story_stats(p_story_id UUID)
RETURNS TABLE(views_count BIGINT, likes_count BIGINT) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM story_views WHERE story_id = p_story_id) as views_count,
        (SELECT COUNT(*) FROM story_reactions WHERE story_id = p_story_id AND type = 'like') as likes_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 9. إنشاء دالة لتبديل تفاعل القصة
CREATE OR REPLACE FUNCTION toggle_story_reaction(p_story_id UUID, p_user_id UUID, p_type TEXT)
RETURNS VOID AS $$
DECLARE
    existing_reaction_id UUID;
BEGIN
    -- البحث عن التفاعل الموجود
    SELECT id INTO existing_reaction_id
    FROM story_reactions
    WHERE story_id = p_story_id AND user_id = p_user_id AND type = p_type;
    
    IF existing_reaction_id IS NULL THEN
        -- إضافة تفاعل جديد
        INSERT INTO story_reactions (story_id, user_id, type)
        VALUES (p_story_id, p_user_id, p_type);
    ELSE
        -- إزالة التفاعل الموجود
        DELETE FROM story_reactions WHERE id = existing_reaction_id;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 10. التحقق من إنشاء الجداول
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name IN ('story_views', 'story_reactions')
ORDER BY table_name, column_name;

-- 11. إنشاء بيانات اختبار
DO $$
DECLARE
    test_story_id UUID;
    test_user_id UUID;
BEGIN
    -- الحصول على أول قصة ومستخدم للاختبار
    SELECT id INTO test_story_id FROM stories LIMIT 1;
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    
    IF test_story_id IS NOT NULL AND test_user_id IS NOT NULL THEN
        -- إضافة مشاهدة اختبار
        PERFORM increment_story_view(test_story_id, test_user_id);
        
        -- إضافة تفاعل اختبار
        PERFORM toggle_story_reaction(test_story_id, test_user_id, 'like');
        
        RAISE NOTICE 'تم إنشاء بيانات اختبار للقصة: %', test_story_id;
    END IF;
END $$;

-- 12. عرض الإحصائيات الحالية
SELECT 
    'Story Statistics' as info,
    (SELECT COUNT(*) FROM story_views) as total_views,
    (SELECT COUNT(*) FROM story_reactions WHERE type = 'like') as total_likes,
    (SELECT COUNT(*) FROM stories) as total_stories; 