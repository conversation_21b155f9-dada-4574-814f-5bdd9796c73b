import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
// import 'package:flutter_linkify/flutter_linkify.dart';
// import 'package:url_launcher/url_launcher.dart';
import '../models/comment.dart';
import '../models/reaction_type.dart';
import '../supabase_service.dart';

import '../widgets/user_level_badge.dart';

// مرجع للكاش لاستخدامه فى إعادة عرض الاسم حال كان مفقوداً

import 'comment_reaction_button.dart';
import 'package:readmore/readmore.dart';

class HierarchicalCommentWidget extends StatefulWidget {
  final Comment comment;
  final Function(Comment) onReply;
  final Function(Comment, ReactionType)? onReaction;
  final Function(String)? onCommentDeleted; // callback للحذف الفوري
  final Function(String, String)? onCommentEdited; // callback للتعديل الفوري
  final int maxDepth;
  final bool showConnectorLines;
  final bool isLast;

  const HierarchicalCommentWidget({
    super.key,
    required this.comment,
    required this.onReply,
    this.onReaction,
    this.onCommentDeleted,
    this.onCommentEdited,
    this.maxDepth = 10,
    this.showConnectorLines = true,
    this.isLast = false,
  });

  @override
  State<HierarchicalCommentWidget> createState() => _HierarchicalCommentWidgetState();
}

class _HierarchicalCommentWidgetState extends State<HierarchicalCommentWidget> {
  bool _showAllReplies = false; // في البداية نعرض رد واحد فقط

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // التعليق الأساسي
        _buildCommentItem(widget.comment),
        
        // الردود مع التسلسل الهرمي
        if (widget.comment.replies.isNotEmpty)
          _buildRepliesSection(),
      ],
    );
  }

  Widget _buildCommentItem(Comment comment) {
    final isReply = comment.depth > 0;

    // جلب الاسم والصورة من الكاش إذا ما زالت القيمة "مستخدم"
    String displayName = comment.userName;
    String displayAvatar = comment.userAvatar;
    if (displayName == 'مستخدم' || displayName.isEmpty) {
      final cached = SupabaseService().profilesCache.value[comment.userId];
      if (cached != null) {
        displayName = cached['name'] ?? cached['username'] ?? displayName;
        displayAvatar = cached['avatar_url'] ?? displayAvatar;
      }
    }

    return Container(
      margin: EdgeInsets.only(
        right: isReply ? 50.0 : 0, // إزاحة أكبر للردود لتجنب التداخل
        bottom: 8,
        top: 4,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // سهم للردود لتوضيح العلاقة
          if (isReply && widget.showConnectorLines)
            Container(
              margin: const EdgeInsets.only(left: 8, top: 8),
              child: Icon(
                Icons.reply,
                size: 16,
                color: Colors.blue[600],
              ),
            ),

          // صورة المستخدم
          CircleAvatar(
            radius: isReply ? 14 : 18,
            backgroundImage: displayAvatar.isNotEmpty
                ? NetworkImage(displayAvatar)
                : null,
            backgroundColor: Colors.grey[300],
            child: displayAvatar.isEmpty
                ? Icon(
                    Icons.person,
                    size: isReply ? 16 : 20,
                    color: Colors.grey[600],
                  )
                : null,
          ),

          const SizedBox(width: 8),
          
          // محتوى التعليق
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // فقاعة التعليق
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // اسم المستخدم مع رمز التوثيق ومستوى المستخدم
                      QuickUserLevel(
                        userId: comment.userId,
                        name: displayName,
                        isVerified: false,
                        textStyle: TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: isReply ? 13 : 14,
                          color: Colors.blue[800],
                        ),
                        badgeSize: isReply ? 12 : 14,
                      ),
                      
                      // إشارة للمستخدم المُرد عليه
                      if (comment.replyToUserName != null)
                        Text(
                          'رداً على ${comment.replyToUserName}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      
                      const SizedBox(height: 4),
                      
                      // محتوى التعليق (سطر واحد + عرض المزيد)
                      ReadMoreText(
                        comment.content,
                        trimLines: 1,
                        trimMode: TrimMode.Line,
                        trimCollapsedText: ' عرض المزيد',
                        trimExpandedText: ' عرض أقل',
                        moreStyle: const TextStyle(color: Colors.blue),
                        lessStyle: const TextStyle(color: Colors.blue),
                        style: TextStyle(fontSize: isReply ? 13 : 14),
                      ),
                      
                      // الميديا إن وجدت
                      if (comment.mediaUrl != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: _buildMediaContent(comment),
                        ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 4),
                
                // أزرار الإجراءات
                _buildActionButtons(comment),
              ],
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildMediaContent(Comment comment) {
    if (comment.mediaUrl == null) return const SizedBox.shrink();
    
    switch (comment.type) {
      case CommentType.image:
        return GestureDetector(
          onTap: () => _showFullImage(comment.mediaUrl!),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: Image.network(
              comment.mediaUrl!,
              height: 150,
              width: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stack) => Container(
                height: 150,
                color: Colors.grey[300],
                child: const Icon(Icons.broken_image),
              ),
            ),
          ),
        );
      
      case CommentType.video:
        return Container(
          height: 150,
          decoration: BoxDecoration(
            color: Colors.black,
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Center(
            child: Icon(Icons.play_circle_fill, color: Colors.white, size: 50),
          ),
        );
      
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildActionButtons(Comment comment) {
    return Padding(
      padding: const EdgeInsets.only(right: 12),
      child: Row(
        children: [
          // زمن النشر
          Text(
            _formatTime(comment.createdAt),
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),

          const SizedBox(width: 16),

          // زر التفاعلات (نفس منطق المنشورات)
          if (widget.onReaction != null)
            CommentReactionButton(
              comment: comment,
              onReaction: widget.onReaction!,
            ),

          const SizedBox(width: 16),

          // زر الرد
          GestureDetector(
            onTap: () => widget.onReply(comment),
            child: Text(
              'رد',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),

          const SizedBox(width: 16),

          // زر القائمة (ثلاث نقاط)
          GestureDetector(
            onTap: () => _showCommentMenu(comment),
            child: Icon(
              Icons.more_horiz,
              size: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRepliesSection() {
    return Column(
      children: [
        // زر إظهار/إخفاء الردود - يظهر فقط إذا كان هناك أكثر من رد واحد
        if (widget.comment.replies.length > 1)
          Padding(
            padding: const EdgeInsets.only(right: 46, top: 8, bottom: 4),
            child: GestureDetector(
              onTap: () => setState(() => _showAllReplies = !_showAllReplies),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _showAllReplies ? Colors.blue[50] : Colors.grey[100],
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: _showAllReplies ? Colors.blue[300]! : Colors.grey[300]!,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _showAllReplies ? Icons.expand_less : Icons.expand_more,
                      size: 16,
                      color: _showAllReplies ? Colors.blue[700] : Colors.blue[600],
                    ),
                    const SizedBox(width: 6),
                    Text(
                      _showAllReplies
                          ? 'إخفاء الردود (${widget.comment.replies.length})'
                          : 'عرض جميع الردود (${widget.comment.replies.length})',
                      style: TextStyle(
                        fontSize: 12,
                        color: _showAllReplies ? Colors.blue[700] : Colors.blue[600],
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

        // الردود في صف عمودي واحد
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: _getRepliesToShow().map((reply) {
            return Container(
              margin: const EdgeInsets.only(top: 8),
              decoration: BoxDecoration(
                border: Border(
                  right: BorderSide(
                    color: Colors.blue[200]!,
                    width: 2,
                  ),
                ),
              ),
              padding: const EdgeInsets.only(right: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // مؤشر الرد
                  if (widget.showConnectorLines)
                    Container(
                      margin: const EdgeInsets.only(bottom: 4),
                      child: Row(
                        children: [
                          Icon(
                            Icons.reply,
                            size: 12,
                            color: Colors.blue[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'رد على ${widget.comment.userName}',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.grey[600],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ],
                      ),
                    ),

                  // التعليق نفسه
                  HierarchicalCommentWidget(
                    comment: reply.copyWith(depth: 1), // عمق ثابت لجميع الردود
                    onReply: widget.onReply,
                    onReaction: widget.onReaction,
                    onCommentDeleted: widget.onCommentDeleted,
                    onCommentEdited: widget.onCommentEdited,
                    maxDepth: widget.maxDepth,
                    showConnectorLines: false, // لا نريد أسهم إضافية
                    isLast: false,
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }



  // دالة تحديد الردود المراد عرضها
  List<Comment> _getRepliesToShow() {
    if (widget.comment.replies.isEmpty) return [];

    // إذا كان هناك رد واحد فقط، اعرضه دائماً
    if (widget.comment.replies.length == 1) {
      return widget.comment.replies;
    }

    // إذا كان المستخدم يريد عرض جميع الردود
    if (_showAllReplies) {
      return widget.comment.replies;
    }

    // خلاف ذلك، اعرض الرد الأول فقط
    return [widget.comment.replies.first];
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} د';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} س';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} ي';
    } else {
      return '${dateTime.day}/${dateTime.month}';
    }
  }

  // زر لايك بسيط للتعليقات
  Widget _buildReactionButton(Comment comment) {
    final hasLike = comment.currentUserReaction == ReactionType.like;
    final likesCount = comment.reactionCounts[ReactionType.like] ?? 0;

    return GestureDetector(
      onTap: () {
        if (hasLike) {
          // إزالة اللايك
          widget.onReaction!(comment, ReactionType.none);
        } else {
          // إضافة لايك
          widget.onReaction!(comment, ReactionType.like);
        }
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // أيقونة اللايك
          Icon(
            hasLike ? Icons.thumb_up : Icons.thumb_up_outlined,
            size: 14,
            color: hasLike ? Colors.blue[600] : Colors.grey[600],
          ),

          // عدد اللايكات
          if (likesCount > 0) ...[
            const SizedBox(width: 3),
            Text(
              '$likesCount',
              style: TextStyle(
                fontSize: 11,
                color: hasLike ? Colors.blue[600] : Colors.grey[600],
                fontWeight: hasLike ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // عرض الصورة بالحجم الكامل
  void _showFullImage(String imageUrl) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Stack(
          children: [
            // الصورة
            Center(
              child: InteractiveViewer(
                child: Image.network(
                  imageUrl,
                  fit: BoxFit.contain,
                  errorBuilder: (context, error, stack) => Container(
                    color: Colors.grey[300],
                    child: const Icon(Icons.broken_image, size: 50),
                  ),
                ),
              ),
            ),

            // زر الإغلاق
            Positioned(
              top: 40,
              right: 20,
              child: GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.close,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // عرض قائمة خيارات التعليق
  void _showCommentMenu(Comment comment) {
    final currentUserId = SupabaseService().getCurrentUserId();
    final isMyComment = comment.userId == currentUserId;

    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // عنوان
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            if (isMyComment) ...[
              // خيارات المالك
              if (comment.type == CommentType.text)
                _buildMenuOption(
                  icon: Icons.edit,
                  title: 'تعديل التعليق',
                  onTap: () {
                    Navigator.pop(context);
                    _editComment(comment);
                  },
                ),

              _buildMenuOption(
                icon: Icons.delete,
                title: 'حذف التعليق',
                color: Colors.red,
                onTap: () {
                  Navigator.pop(context);
                  _deleteComment(comment);
                },
              ),
            ] else ...[
              // خيارات للآخرين
              _buildMenuOption(
                icon: Icons.report,
                title: 'الإبلاغ عن التعليق',
                onTap: () {
                  Navigator.pop(context);
                  _reportComment(comment);
                },
              ),

              _buildMenuOption(
                icon: Icons.block,
                title: 'حظر ${comment.userName}',
                color: Colors.red,
                onTap: () {
                  Navigator.pop(context);
                  _blockUser(comment);
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildMenuOption({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? color,
  }) {
    return ListTile(
      leading: Icon(icon, color: color ?? Colors.grey[700]),
      title: Text(
        title,
        style: TextStyle(
          color: color ?? Colors.grey[800],
          fontWeight: FontWeight.w500,
        ),
      ),
      onTap: onTap,
    );
  }

  // تعديل التعليق
  void _editComment(Comment comment) {
    final controller = TextEditingController(text: comment.content);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل التعليق'),
        content: TextField(
          controller: controller,
          maxLines: 3,
          decoration: const InputDecoration(
            hintText: 'اكتب تعليقك...',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);

              // تعديل فوري في الواجهة
              if (widget.onCommentEdited != null) {
                widget.onCommentEdited!(comment.id, controller.text);
              }

              // تعديل في قاعدة البيانات في الخلفية
              _performEditComment(comment.id, controller.text);
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  // دوال التطبيق
  Future<void> _performEditComment(String commentId, String newContent) async {
    final success = await SupabaseService().editComment(commentId, newContent);
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? 'تم تعديل التعليق' : 'فشل في تعديل التعليق'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );
    }
  }

  Future<void> _deleteComment(Comment comment) async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف التعليق'),
        content: const Text('هل أنت متأكد من حذف هذا التعليق؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);

              // حذف فوري من الواجهة
              if (widget.onCommentDeleted != null) {
                widget.onCommentDeleted!(comment.id);
              }

              // حذف من قاعدة البيانات في الخلفية
              final success = await SupabaseService().deleteComment(comment.id);

              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(success ? 'تم حذف التعليق' : 'فشل في حذف التعليق'),
                    backgroundColor: success ? Colors.green : Colors.red,
                  ),
                );
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  Future<void> _reportComment(Comment comment) async {
    String selectedReason = 'spam';
    final controller = TextEditingController();

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإبلاغ عن التعليق'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DropdownButtonFormField<String>(
              value: selectedReason,
              decoration: const InputDecoration(
                labelText: 'سبب الإبلاغ',
                border: OutlineInputBorder(),
              ),
              items: const [
                DropdownMenuItem(value: 'spam', child: Text('رسائل مزعجة')),
                DropdownMenuItem(value: 'harassment', child: Text('تحرش')),
                DropdownMenuItem(value: 'inappropriate', child: Text('محتوى غير مناسب')),
                DropdownMenuItem(value: 'fake', child: Text('معلومات مزيفة')),
                DropdownMenuItem(value: 'violence', child: Text('عنف')),
                DropdownMenuItem(value: 'other', child: Text('أخرى')),
              ],
              onChanged: (value) => selectedReason = value!,
            ),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              maxLines: 3,
              decoration: const InputDecoration(
                hintText: 'تفاصيل إضافية (اختياري)',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('إبلاغ'),
          ),
        ],
      ),
    );

    if (result == true) {
      final success = await SupabaseService().reportComment(
        comment.id,
        selectedReason,
        description: controller.text.trim().isEmpty ? null : controller.text.trim(),
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? 'تم الإبلاغ عن التعليق' : 'فشل في الإبلاغ'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _blockUser(Comment comment) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حظر المستخدم'),
        content: Text('هل أنت متأكد من حظر ${comment.userName}؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حظر', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (result == true) {
      final success = await SupabaseService().blockCommentUser(comment.userId);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? 'تم حظر المستخدم' : 'فشل في حظر المستخدم'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }
}




