import 'package:flutter/material.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:share_plus/share_plus.dart';
import '../models/podcast.dart';
import '../services/podcast_service.dart';
import '../widgets/cached_image.dart';
import '../widgets/verified_badge.dart';
import '../pages/podcast_details_page.dart';
import '../pages/profile_page.dart';

class PodcastCard extends StatefulWidget {
  final Podcast podcast;
  final VoidCallback? onRefresh;

  const PodcastCard({
    super.key,
    required this.podcast,
    this.onRefresh,
  });

  @override
  State<PodcastCard> createState() => _PodcastCardState();
}

class _PodcastCardState extends State<PodcastCard> {
  final PodcastService _podcastService = PodcastService();
  final AudioPlayer _audioPlayer = AudioPlayer();
  
  bool _isPlaying = false;
  bool _isLoading = false;
  Duration _currentPosition = Duration.zero;
  Duration _totalDuration = Duration.zero;
  bool _isLiked = false;
  bool _isSaved = false;
  int _likesCount = 0;

  @override
  void initState() {
    super.initState();
    _isLiked = widget.podcast.isLiked;
    _isSaved = widget.podcast.isSaved;
    _likesCount = widget.podcast.likesCount;
    
    _audioPlayer.onDurationChanged.listen((duration) {
      setState(() => _totalDuration = duration);
    });
    
    _audioPlayer.onPositionChanged.listen((position) {
      setState(() => _currentPosition = position);
    });
    
    _audioPlayer.onPlayerStateChanged.listen((state) {
      setState(() => _isPlaying = state == PlayerState.playing);
    });
  }

  @override
  void dispose() {
    _audioPlayer.dispose();
    super.dispose();
  }

  Future<void> _togglePlayPause() async {
    try {
      setState(() => _isLoading = true);
      
      if (_isPlaying) {
        await _audioPlayer.pause();
      } else {
        // تسجيل تشغيل جديد
        await _podcastService.recordPlay(widget.podcast.id);
        
        // تشغيل الصوت
        await _audioPlayer.play(UrlSource(widget.podcast.audioUrl));
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في تشغيل الصوت: $e')),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _toggleLike() async {
    try {
      await _podcastService.toggleLike(widget.podcast.id);
      setState(() {
        _isLiked = !_isLiked;
        _likesCount += _isLiked ? 1 : -1;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في الإعجاب: $e')),
      );
    }
  }

  Future<void> _toggleSave() async {
    try {
      await _podcastService.toggleSave(widget.podcast.id);
      setState(() => _isSaved = !_isSaved);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_isSaved ? 'تم حفظ البودكاست' : 'تم إلغاء حفظ البودكاست'),
          duration: const Duration(seconds: 1),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في الحفظ: $e')),
      );
    }
  }

  void _sharePodcast() {
    Share.share(
      'استمع إلى هذا البودكاست الرائع: "${widget.podcast.title}" على تطبيق أرزاوو',
      subject: widget.podcast.title,
    );
  }

  void _openPodcastDetails() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PodcastDetailsPage(podcast: widget.podcast),
      ),
    );
  }

  void _openUserProfile() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ProfilePage(
          userId: widget.podcast.userId,
          username: widget.podcast.userName,
        ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: _openPodcastDetails,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات المستخدم
              Row(
                children: [
                  GestureDetector(
                    onTap: _openUserProfile,
                    child: CircleAvatar(
                      radius: 20,
                      backgroundImage: widget.podcast.userAvatar.isNotEmpty
                          ? NetworkImage(widget.podcast.userAvatar)
                          : null,
                      child: widget.podcast.userAvatar.isEmpty
                          ? const Icon(Icons.person)
                          : null,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            GestureDetector(
                              onTap: _openUserProfile,
                              child: Text(
                                widget.podcast.userName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                            if (widget.podcast.isVerified) ...[
                              const SizedBox(width: 4),
                              const VerifiedBadge(size: 16),
                            ],
                          ],
                        ),
                        Text(
                          _formatTimeAgo(widget.podcast.createdAt),
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // فئة البودكاست
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: widget.podcast.category.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          widget.podcast.category.icon,
                          size: 14,
                          color: widget.podcast.category.color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          widget.podcast.category.displayName,
                          style: TextStyle(
                            color: widget.podcast.category.color,
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // محتوى البودكاست
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // صورة الغلاف أو أيقونة افتراضية
                  Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: Colors.grey[200],
                    ),
                    child: widget.podcast.coverImageUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: CachedImage(
                              imageUrl: widget.podcast.coverImageUrl!,
                              fit: BoxFit.cover,
                            ),
                          )
                        : Icon(
                            Icons.podcasts,
                            size: 40,
                            color: Colors.grey[400],
                          ),
                  ),
                  
                  const SizedBox(width: 12),
                  
                  // تفاصيل البودكاست
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.podcast.title,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        
                        if (widget.podcast.description != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            widget.podcast.description!,
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                        
                        const SizedBox(height: 8),
                        
                        // معلومات إضافية
                        Row(
                          children: [
                            Icon(Icons.access_time, size: 14, color: Colors.grey[600]),
                            const SizedBox(width: 4),
                            Text(
                              widget.podcast.formattedDuration,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Icon(Icons.play_circle_outline, size: 14, color: Colors.grey[600]),
                            const SizedBox(width: 4),
                            Text(
                              widget.podcast.formattedPlaysCount,
                              style: TextStyle(
                                color: Colors.grey[600],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // زر التشغيل
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.deepPurple,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.deepPurple.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: IconButton(
                      onPressed: _togglePlayPause,
                      icon: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : Icon(
                              _isPlaying ? Icons.pause : Icons.play_arrow,
                              color: Colors.white,
                              size: 24,
                            ),
                    ),
                  ),
                ],
              ),
              
              // شريط التقدم (يظهر فقط أثناء التشغيل)
              if (_isPlaying || _currentPosition.inSeconds > 0) ...[
                const SizedBox(height: 12),
                Column(
                  children: [
                    LinearProgressIndicator(
                      value: _totalDuration.inSeconds > 0
                          ? _currentPosition.inSeconds / _totalDuration.inSeconds
                          : 0,
                      backgroundColor: Colors.grey[300],
                      valueColor: const AlwaysStoppedAnimation<Color>(Colors.deepPurple),
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          _formatDuration(_currentPosition),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        Text(
                          _formatDuration(_totalDuration),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
              
              const SizedBox(height: 12),
              
              // أزرار التفاعل
              Row(
                children: [
                  // إعجاب
                  InkWell(
                    onTap: _toggleLike,
                    borderRadius: BorderRadius.circular(20),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _isLiked ? Icons.favorite : Icons.favorite_border,
                            size: 20,
                            color: _isLiked ? Colors.red : Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _likesCount.toString(),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  // تعليقات
                  InkWell(
                    onTap: _openPodcastDetails,
                    borderRadius: BorderRadius.circular(20),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.comment_outlined, size: 20, color: Colors.grey[600]),
                          const SizedBox(width: 4),
                          Text(
                            widget.podcast.commentsCount.toString(),
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  
                  const Spacer(),
                  
                  // حفظ
                  InkWell(
                    onTap: _toggleSave,
                    borderRadius: BorderRadius.circular(20),
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: Icon(
                        _isSaved ? Icons.bookmark : Icons.bookmark_border,
                        size: 20,
                        color: _isSaved ? Colors.deepPurple : Colors.grey[600],
                      ),
                    ),
                  ),
                  
                  // مشاركة
                  InkWell(
                    onTap: _sharePodcast,
                    borderRadius: BorderRadius.circular(20),
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: Icon(Icons.share, size: 20, color: Colors.grey[600]),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
