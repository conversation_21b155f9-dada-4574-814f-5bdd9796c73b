import 'package:flutter/material.dart';
import '../supabase_service.dart';
import 'archived_chats_page.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class ChatSettingsPage extends StatefulWidget {
  const ChatSettingsPage({super.key});

  @override
  State<ChatSettingsPage> createState() => _ChatSettingsPageState();
}

class _ChatSettingsPageState extends State<ChatSettingsPage> {
  bool showActivity = true;
  String messagesPrivacy = 'everyone';

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    final s = await SupabaseService().fetchSettings();
    setState(() {
      showActivity = s['show_activity'] ?? true;
    });

    // جلب الخصوصية من ملف profile مباشرة
    final uid = Supabase.instance.client.auth.currentUser!.id;
    final profile = SupabaseService().profilesCache.value[uid];
    messagesPrivacy = profile?['messages_privacy'] ?? 'everyone';
  }

  Future<void> _save() async {
    await SupabaseService().updateSettings({'show_activity': showActivity});
    await SupabaseService().setMessagesPrivacy(messagesPrivacy);
    if (context.mounted) ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم الحفظ')));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إعدادات الدردشة')),
      body: ListView(
        children: [
          const ListTile(title: Text('الخصوصية', style: TextStyle(fontWeight: FontWeight.bold))),
          ListTile(
            title: const Text('من يمكنه بدء محادثة معي؟'),
            subtitle: Text(messagesPrivacy == 'everyone' ? 'الجميع' : 'المتابِعون فقط'),
            trailing: DropdownButton<String>(
              value: messagesPrivacy,
              items: const [
                DropdownMenuItem(value: 'everyone', child: Text('الجميع')),
                DropdownMenuItem(value: 'followers', child: Text('المتابِعون فقط')),
              ],
              onChanged: (v) => setState(() => messagesPrivacy = v!),
            ),
          ),
          SwitchListTile(
            title: const Text('إظهار حالتي النشطة'),
            value: showActivity,
            onChanged: (v) => setState(() => showActivity = v),
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.archive, color: Colors.blueGrey),
            title: const Text('الدردشات المؤرشفة'),
            trailing: const Icon(Icons.chevron_left),
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (_) => const ArchivedChatsPage())),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: ElevatedButton(onPressed: _save, child: const Text('حفظ')),
          ),
        ],
      ),
    );
  }
} 