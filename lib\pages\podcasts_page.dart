import 'package:flutter/material.dart';
import '../models/podcast.dart';
import '../services/podcast_service.dart';
import '../widgets/podcast_card.dart';
import '../widgets/podcast_skeleton.dart';
import 'create_podcast_page.dart';
import 'my_podcasts_page.dart';
import 'saved_podcasts_page.dart';

class PodcastsPage extends StatefulWidget {
  const PodcastsPage({super.key});

  @override
  State<PodcastsPage> createState() => _PodcastsPageState();
}

class _PodcastsPageState extends State<PodcastsPage> with TickerProviderStateMixin {
  final PodcastService _podcastService = PodcastService();
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  
  late TabController _tabController;
  
  List<Podcast> _podcasts = [];
  bool _loading = true;
  bool _loadingMore = false;
  bool _hasMore = true;
  String? _error;
  PodcastCategory? _selectedCategory;
  String _searchQuery = '';
  
  final int _limit = 20;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _scrollController.addListener(_onScroll);
    _loadPodcasts();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 300) {
      _loadMorePodcasts();
    }
  }

  Future<void> _loadPodcasts() async {
    setState(() {
      _loading = true;
      _error = null;
    });

    try {
      final podcasts = await _podcastService.fetchPodcasts(
        limit: _limit,
        offset: 0,
        category: _selectedCategory,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
      );

      setState(() {
        _podcasts = podcasts;
        _hasMore = podcasts.length == _limit;
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'فشل في تحميل البودكاستات: $e';
        _loading = false;
      });
    }
  }

  Future<void> _loadMorePodcasts() async {
    if (_loadingMore || !_hasMore) return;

    setState(() => _loadingMore = true);

    try {
      final morePodcasts = await _podcastService.fetchPodcasts(
        limit: _limit,
        offset: _podcasts.length,
        category: _selectedCategory,
        searchQuery: _searchQuery.isEmpty ? null : _searchQuery,
      );

      setState(() {
        _podcasts.addAll(morePodcasts);
        _hasMore = morePodcasts.length == _limit;
        _loadingMore = false;
      });
    } catch (e) {
      setState(() => _loadingMore = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في تحميل المزيد: $e')),
        );
      }
    }
  }

  Future<void> _onRefresh() async {
    await _loadPodcasts();
  }

  void _onCategoryChanged(PodcastCategory? category) {
    setState(() {
      _selectedCategory = category;
    });
    _loadPodcasts();
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
    });
    _loadPodcasts();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('البودكاست الصوتي'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 1,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.deepPurple,
          unselectedLabelColor: Colors.grey,
          indicatorColor: Colors.deepPurple,
          tabs: const [
            Tab(text: 'الكل', icon: Icon(Icons.podcasts)),
            Tab(text: 'صوتياتي', icon: Icon(Icons.person)),
            Tab(text: 'المحفوظة', icon: Icon(Icons.bookmark)),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => const CreatePodcastPage()),
              ).then((_) => _loadPodcasts());
            },
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllPodcastsTab(),
          const MyPodcastsPage(),
          const SavedPodcastsPage(),
        ],
      ),
    );
  }

  Widget _buildAllPodcastsTab() {
    return Column(
      children: [
        // شريط البحث والفلترة
        Container(
          padding: const EdgeInsets.all(16),
          color: Colors.grey[50],
          child: Column(
            children: [
              // شريط البحث
              TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'ابحث في البودكاستات...',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _searchController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () {
                            _searchController.clear();
                            _onSearchChanged('');
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(25),
                    borderSide: BorderSide.none,
                  ),
                  filled: true,
                  fillColor: Colors.white,
                ),
                onChanged: _onSearchChanged,
              ),
              
              const SizedBox(height: 12),
              
              // فلتر الفئات
              SizedBox(
                height: 40,
                child: ListView(
                  scrollDirection: Axis.horizontal,
                  children: [
                    _buildCategoryChip(null, 'الكل'),
                    ...PodcastCategory.values.map(
                      (category) => _buildCategoryChip(category, category.displayName),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        // قائمة البودكاستات
        Expanded(
          child: _buildPodcastsList(),
        ),
      ],
    );
  }

  Widget _buildCategoryChip(PodcastCategory? category, String label) {
    final isSelected = _selectedCategory == category;
    
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (_) => _onCategoryChanged(category),
        backgroundColor: Colors.white,
        selectedColor: Colors.deepPurple.withOpacity(0.2),
        checkmarkColor: Colors.deepPurple,
        labelStyle: TextStyle(
          color: isSelected ? Colors.deepPurple : Colors.grey[700],
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }

  Widget _buildPodcastsList() {
    if (_loading) {
      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: 6,
        itemBuilder: (context, index) => const PodcastSkeleton(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadPodcasts,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    if (_podcasts.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.podcasts, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد بودكاستات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'كن أول من ينشر بودكاست صوتي',
              style: TextStyle(color: Colors.grey[500]),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const CreatePodcastPage()),
                ).then((_) => _loadPodcasts());
              },
              icon: const Icon(Icons.add),
              label: const Text('إنشاء بودكاست'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurple,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _onRefresh,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: _podcasts.length + (_loadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _podcasts.length) {
            return const Padding(
              padding: EdgeInsets.symmetric(vertical: 16),
              child: Center(child: CircularProgressIndicator()),
            );
          }

          return PodcastCard(
            podcast: _podcasts[index],
            onRefresh: _loadPodcasts,
          );
        },
      ),
    );
  }
}
