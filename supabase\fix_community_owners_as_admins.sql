-- =============================================================
--  إصلاح أدوار مالكي المجتمعات
--  Fix Community Owners as Admins
-- =============================================================

-- هذا السكريپت يضمن أن جميع مالكي المجتمعات يظهرون كمديرين

-- 1) فحص المجتمعات الموجودة
-- -------------------------------------------------------

SELECT 
  '🔍 COMMUNITIES CHECK' as check_type,
  COUNT(*)::text || ' communities found' as status,
  'Total communities in database' as details
FROM communities;

-- 2) فحص المالكين الذين ليسوا أعضاء
-- -------------------------------------------------------

SELECT 
  '🔍 MISSING OWNERS' as check_type,
  COUNT(*)::text || ' owners not in members table' as status,
  'Owners who need to be added as admins' as details
FROM communities c
WHERE NOT EXISTS (
  SELECT 1 FROM community_members cm 
  WHERE cm.community_id = c.id 
  AND cm.user_id = c.owner_id
);

-- 3) إضافة المالكين المفقودين كمديرين
-- -------------------------------------------------------

INSERT INTO community_members (community_id, user_id, role, joined_at)
SELECT 
  c.id as community_id,
  c.owner_id as user_id,
  'admin' as role,
  c.created_at as joined_at
FROM communities c
WHERE NOT EXISTS (
  SELECT 1 FROM community_members cm 
  WHERE cm.community_id = c.id 
  AND cm.user_id = c.owner_id
)
ON CONFLICT (community_id, user_id) DO UPDATE SET
  role = 'admin';

-- 4) تحديث المالكين الموجودين ليكونوا مديرين
-- -------------------------------------------------------

UPDATE community_members 
SET role = 'admin'
WHERE (community_id, user_id) IN (
  SELECT c.id, c.owner_id 
  FROM communities c
  WHERE c.owner_id IS NOT NULL
)
AND role != 'admin';

-- 5) فحص النتائج
-- -------------------------------------------------------

SELECT 
  '🔍 OWNERS AS ADMINS' as check_type,
  COUNT(*)::text || ' owners are now admins' as status,
  'Community owners with admin role' as details
FROM communities c
JOIN community_members cm ON cm.community_id = c.id AND cm.user_id = c.owner_id
WHERE cm.role = 'admin';

-- 6) فحص المالكين الذين لا يزالون ليسوا مديرين
-- -------------------------------------------------------

SELECT 
  '🔍 REMAINING ISSUES' as check_type,
  CASE 
    WHEN COUNT(*) = 0 
    THEN '✅ SUCCESS: All owners are admins'
    ELSE '⚠️ WARNING: ' || COUNT(*)::text || ' owners still not admins'
  END as status,
  'Owners who still need fixing' as details
FROM communities c
LEFT JOIN community_members cm ON cm.community_id = c.id AND cm.user_id = c.owner_id
WHERE cm.role IS NULL OR cm.role != 'admin';

-- 7) إحصائيات تفصيلية
-- -------------------------------------------------------

SELECT 
  '📊 DETAILED STATS' as info_type,
  'Total: ' || 
  (SELECT COUNT(*) FROM communities)::text || 
  ' communities, ' ||
  (SELECT COUNT(*) FROM community_members WHERE role = 'admin')::text ||
  ' admins, ' ||
  (SELECT COUNT(*) FROM community_members WHERE role = 'member')::text ||
  ' members' as details;

-- 8) عرض المجتمعات مع أدوار المالكين
-- -------------------------------------------------------

SELECT 
  c.id as community_id,
  c.name as community_name,
  c.owner_id,
  COALESCE(cm.role, 'NOT_MEMBER') as owner_role,
  CASE 
    WHEN cm.role = 'admin' THEN '✅ CORRECT'
    WHEN cm.role = 'member' THEN '⚠️ SHOULD_BE_ADMIN'
    WHEN cm.role IS NULL THEN '❌ NOT_MEMBER'
    ELSE '❓ UNKNOWN'
  END as status
FROM communities c
LEFT JOIN community_members cm ON cm.community_id = c.id AND cm.user_id = c.owner_id
ORDER BY c.created_at DESC
LIMIT 10;

-- 9) النتيجة النهائية
-- -------------------------------------------------------

SELECT 
  '🎉 FINAL RESULT' as check_type,
  CASE 
    WHEN NOT EXISTS (
      SELECT 1 FROM communities c
      LEFT JOIN community_members cm ON cm.community_id = c.id AND cm.user_id = c.owner_id
      WHERE cm.role IS NULL OR cm.role != 'admin'
    )
    THEN '✅ SUCCESS: All community owners are now admins!'
    ELSE '⚠️ PARTIAL: Some owners may still need manual fixing'
  END as status,
  'Community owners admin role fix completed' as details;

-- =============================================================
--  تعليمات ما بعد التشغيل
-- =============================================================

/*

بعد تشغيل هذا السكريپت:

1. جميع مالكي المجتمعات سيكونون أعضاء في جدول community_members
2. جميع المالكين سيكون لديهم role = 'admin'
3. في التطبيق، المالكون سيظهرون كـ "مالك المجتمع" مع chip ذهبي
4. المديرون العاديون سيظهرون كـ "مدير" مع chip أزرق
5. الأعضاء العاديون سيظهرون كـ "عضو" مع زر إزالة

إذا رأيت "✅ SUCCESS: All community owners are now admins!"
فهذا يعني أن جميع المالكين تم إصلاحهم بنجاح.

*/

-- =============================================================
--  انتهى إصلاح أدوار مالكي المجتمعات
-- =============================================================
