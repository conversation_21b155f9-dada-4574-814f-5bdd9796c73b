import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../supabase_service.dart';
import '../main.dart';

class SuggestUsersPage extends StatefulWidget {
  const SuggestUsersPage({super.key});

  @override
  State<SuggestUsersPage> createState() => _SuggestUsersPageState();
}

class _SuggestUsersPageState extends State<SuggestUsersPage> {
  List<Map<String, dynamic>> _users = [];
  final Set<String> _selected = {};
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    final list = await SupabaseService().fetchUsers();
    setState(() {
      _users = list.take(100).toList();
      _loading = false;
    });
  }

  Future<void> _markDoneAndGoHome() async {
    // mark done
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('suggestions_done', true);
    if (!mounted) return;
    Navigator.pushAndRemoveUntil(
      context,
      MaterialPageRoute(builder: (_) => const HomePage()),
      (route) => false,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('اقتراحات متابعة')),
      body: _loading
          ? const Center(child: CircularProgressIndicator())
          : ListView.builder(
              itemCount: _users.length,
              itemBuilder: (context, index) {
                final u = _users[index];
                final id = u['id'] as String;
                final followed = _selected.contains(id) || (u['is_following'] as bool);
                return ListTile(
                  leading: CircleAvatar(backgroundImage: (u['avatar_url'] as String).isNotEmpty ? NetworkImage(u['avatar_url']) : null),
                  title: Text(u['name'] as String),
                  trailing: ElevatedButton(
                    onPressed: () async {
                      final now = await SupabaseService().toggleFollow(id);
                      setState(() {
                        if (now) {
                          _selected.add(id);
                        } else {
                          _selected.remove(id);
                        }
                      });
                    },
                    child: Text(followed ? 'تم' : 'متابعة'),
                  ),
                );
              },
            ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _markDoneAndGoHome,
                  child: const Text('تخطي'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: _markDoneAndGoHome,
                  child: const Text('متابعة إلى التطبيق'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 