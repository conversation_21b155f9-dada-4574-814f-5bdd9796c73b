-- إضافة عمود media_urls للصور والفيديوهات المتعددة
-- Add media_urls column for multiple images and videos

-- التحقق من وجود العمود وإضافته إذا لم يكن موجوداً
DO $$ 
BEGIN 
    -- إضافة عمود media_urls إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' AND column_name = 'media_urls'
    ) THEN
        ALTER TABLE posts ADD COLUMN media_urls TEXT[];
        RAISE NOTICE 'تم إضافة عمود media_urls بنجاح';
    ELSE
        RAISE NOTICE 'عمود media_urls موجود بالفعل';
    END IF;
END $$;

-- التحقق من أن العمود تم إضافته بنجاح
SELECT column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'posts' AND column_name = 'media_urls';

-- تحديث المنشورات الموجودة لتحويل media_url إلى media_urls إذا كان موجوداً
UPDATE posts 
SET media_urls = ARRAY[media_url] 
WHERE media_url IS NOT NULL AND media_urls IS NULL;

-- إضافة تعليق على العمود
COMMENT ON COLUMN posts.media_urls IS 'مصفوفة من روابط الوسائط المتعددة (صور/فيديوهات)';

-- عرض بعض المنشورات للتحقق
SELECT id, media_url, media_urls FROM posts WHERE media_urls IS NOT NULL LIMIT 5; 