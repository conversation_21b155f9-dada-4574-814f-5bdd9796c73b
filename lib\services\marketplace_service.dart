import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/marketplace_product.dart';
import '../models/product_category.dart';

class MarketplaceService {
  final SupabaseClient _client = Supabase.instance.client;

  // جلب جميع الفئات
  Future<List<ProductCategory>> getCategories() async {
    try {
      final response = await _client
          .from('product_categories')
          .select('*')
          .eq('is_active', true)
          .order('sort_order');

      return response.map((item) => ProductCategory.fromMap(item)).toList();
    } catch (e) {
      throw Exception('خطأ في جلب الفئات: $e');
    }
  }

  // جلب الفئات الرئيسية مع الفرعية
  Future<List<ProductCategory>> getMainCategoriesWithSubs() async {
    try {
      // جلب الفئات الرئيسية
      final mainResponse = await _client
          .from('product_categories')
          .select('*')
          .isFilter('parent_id', null)
          .eq('is_active', true)
          .order('sort_order');

      // جلب الفئات الفرعية
      final subResponse = await _client
          .from('product_categories')
          .select('*')
          .not('parent_id', 'is', null)
          .eq('is_active', true)
          .order('sort_order');

      final mainCategories = mainResponse.map((item) => ProductCategory.fromMap(item)).toList();
      final subCategories = subResponse.map((item) => ProductCategory.fromMap(item)).toList();

      // ربط الفئات الفرعية بالرئيسية
      for (var mainCategory in mainCategories) {
        final subs = subCategories.where((sub) => sub.parentId == mainCategory.id).toList();
        mainCategories[mainCategories.indexOf(mainCategory)] = 
            mainCategory.copyWith(subcategories: subs);
      }

      return mainCategories;
    } catch (e) {
      throw Exception('خطأ في جلب الفئات: $e');
    }
  }

  // البحث في المنتجات
  Future<List<MarketplaceProduct>> searchProducts({
    String searchQuery = '',
    String? categoryId,
    String? city,
    double? minPrice,
    double? maxPrice,
    String? condition,
    String sortBy = 'created_at',
    String sortOrder = 'DESC',
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final response = await _client.rpc('search_marketplace_products', params: {
        'search_query': searchQuery,
        'category_filter': categoryId,
        'city_filter': city,
        'min_price': minPrice,
        'max_price': maxPrice,
        'condition_filter': condition,
        'sort_by': sortBy,
        'sort_order': sortOrder,
        'limit_count': limit,
        'offset_count': offset,
      });

      return (response as List).map((item) => MarketplaceProduct.fromMap(item)).toList();
    } catch (e) {
      throw Exception('خطأ في البحث: $e');
    }
  }

  // جلب المنتجات مع معلومات إضافية
  Future<List<MarketplaceProduct>> getProductsWithDetails({
    String? categoryId,
    String? city,
    String? userId,
    int limit = 20,
    int offset = 0,
  }) async {
    try {
      final response = await _client
          .from('marketplace_products')
          .select('''
            *,
            users!marketplace_products_user_id_fkey(name, avatar_url),
            product_categories!marketplace_products_category_id_fkey(name_ar, icon)
          ''')
          .eq('status', 'active')
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return response.map((item) {
        final user = item['users'];
        final category = item['product_categories'];
        
        return MarketplaceProduct.fromMap({
          ...item,
          'user_name': user?['name'],
          'user_avatar': user?['avatar_url'],
          'category_name': category?['name_ar'],
          'category_icon': category?['icon'],
        });
      }).toList();
    } catch (e) {
      throw Exception('خطأ في جلب المنتجات: $e');
    }
  }

  // جلب منتج واحد بالتفاصيل
  Future<MarketplaceProduct?> getProductById(String productId) async {
    try {
      final response = await _client
          .from('marketplace_products')
          .select('''
            *,
            users!marketplace_products_user_id_fkey(name, avatar_url),
            product_categories!marketplace_products_category_id_fkey(name_ar, icon)
          ''')
          .eq('id', productId)
          .single();

      final user = response['users'];
      final category = response['product_categories'];

      // تحديث عداد المشاهدات
      await _client.rpc('increment_product_views', params: {'product_uuid': productId});

      return MarketplaceProduct.fromMap({
        ...response,
        'user_name': user?['name'],
        'user_avatar': user?['avatar_url'],
        'category_name': category?['name_ar'],
        'category_icon': category?['icon'],
      });
    } catch (e) {
      throw Exception('خطأ في جلب المنتج: $e');
    }
  }

  // إنشاء منتج جديد
  Future<String> createProduct(MarketplaceProduct product) async {
    try {
      final response = await _client
          .from('marketplace_products')
          .insert(product.toMap())
          .select('id')
          .single();

      return response['id'];
    } catch (e) {
      throw Exception('خطأ في إنشاء المنتج: $e');
    }
  }

  // تحديث منتج
  Future<void> updateProduct(String productId, Map<String, dynamic> updates) async {
    try {
      await _client
          .from('marketplace_products')
          .update(updates)
          .eq('id', productId);
    } catch (e) {
      throw Exception('خطأ في تحديث المنتج: $e');
    }
  }

  // حذف منتج
  Future<void> deleteProduct(String productId) async {
    try {
      await _client
          .from('marketplace_products')
          .delete()
          .eq('id', productId);
    } catch (e) {
      throw Exception('خطأ في حذف المنتج: $e');
    }
  }

  // إضافة/إزالة من المفضلة
  Future<bool> toggleFavorite(String productId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول');

      // التحقق من وجود المفضلة
      final existing = await _client
          .from('product_favorites')
          .select('id')
          .eq('user_id', userId)
          .eq('product_id', productId)
          .maybeSingle();

      if (existing != null) {
        // إزالة من المفضلة
        await _client
            .from('product_favorites')
            .delete()
            .eq('user_id', userId)
            .eq('product_id', productId);
        return false;
      } else {
        // إضافة للمفضلة
        await _client
            .from('product_favorites')
            .insert({
              'user_id': userId,
              'product_id': productId,
            });
        return true;
      }
    } catch (e) {
      throw Exception('خطأ في تحديث المفضلة: $e');
    }
  }

  // جلب المفضلة للمستخدم
  Future<List<MarketplaceProduct>> getUserFavorites() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول');

      final response = await _client
          .from('product_favorites')
          .select('''
            marketplace_products(
              *,
              users!marketplace_products_user_id_fkey(name, avatar_url),
              product_categories!marketplace_products_category_id_fkey(name_ar, icon)
            )
          ''')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      return response.map((item) {
        final product = item['marketplace_products'];
        final user = product['users'];
        final category = product['product_categories'];
        
        return MarketplaceProduct.fromMap({
          ...product,
          'user_name': user?['name'],
          'user_avatar': user?['avatar_url'],
          'category_name': category?['name_ar'],
          'category_icon': category?['icon'],
          'is_favorited': true,
        });
      }).toList();
    } catch (e) {
      throw Exception('خطأ في جلب المفضلة: $e');
    }
  }

  // جلب منتجات المستخدم
  Future<List<MarketplaceProduct>> getUserProducts(String userId) async {
    try {
      final response = await _client
          .from('marketplace_products')
          .select('''
            *,
            product_categories!marketplace_products_category_id_fkey(name_ar, icon)
          ''')
          .eq('user_id', userId)
          .order('created_at', ascending: false);

      return response.map((item) {
        final category = item['product_categories'];
        
        return MarketplaceProduct.fromMap({
          ...item,
          'category_name': category?['name_ar'],
          'category_icon': category?['icon'],
        });
      }).toList();
    } catch (e) {
      throw Exception('خطأ في جلب منتجات المستخدم: $e');
    }
  }

  // جلب إحصائيات البائع
  Future<Map<String, dynamic>> getSellerStats(String sellerId) async {
    try {
      final response = await _client.rpc('get_seller_stats', params: {
        'seller_user_id': sellerId,
      });

      return Map<String, dynamic>.from(response);
    } catch (e) {
      throw Exception('خطأ في جلب إحصائيات البائع: $e');
    }
  }

  // جلب المدن المتاحة
  Future<List<String>> getAvailableCities() async {
    try {
      final response = await _client
          .from('marketplace_products')
          .select('city')
          .eq('status', 'active');

      final cities = response
          .map((item) => item['city'] as String)
          .where((city) => city.isNotEmpty)
          .toSet()
          .toList();

      cities.sort();
      return cities;
    } catch (e) {
      throw Exception('خطأ في جلب المدن: $e');
    }
  }

  // الإبلاغ عن منتج
  Future<void> reportProduct(String productId, String reason, String? description) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول');

      await _client.from('product_reports').insert({
        'product_id': productId,
        'reporter_id': userId,
        'reason': reason,
        'description': description,
      });
    } catch (e) {
      throw Exception('خطأ في الإبلاغ: $e');
    }
  }
}
