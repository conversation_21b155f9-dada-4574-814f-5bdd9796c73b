import 'package:flutter/material.dart';
import '../models/poll.dart';
import '../services/poll_service.dart';
import '../widgets/poll_card.dart';
import 'create_poll_page.dart';

class PollsPage extends StatefulWidget {
  const PollsPage({super.key});

  @override
  State<PollsPage> createState() => _PollsPageState();
}

class _PollsPageState extends State<PollsPage> with TickerProviderStateMixin {
  late TabController _tabController;
  final PollService _pollService = PollService();
  
  List<Poll> _recentPolls = [];
  List<Poll> _trendingPolls = [];
  List<Poll> _filteredPolls = [];
  
  bool _loadingRecent = true;
  bool _loadingTrending = true;
  bool _loadingFiltered = false;
  
  PollCategory? _selectedCategory;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadRecentPolls();
    _loadTrendingPolls();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadRecentPolls() async {
    setState(() => _loadingRecent = true);
    try {
      final polls = await _pollService.getPolls(
        sortBy: 'created_at',
        ascending: false,
        limit: 20,
      );
      setState(() {
        _recentPolls = polls;
        _loadingRecent = false;
      });
    } catch (e) {
      setState(() => _loadingRecent = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل التصويتات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadTrendingPolls() async {
    setState(() => _loadingTrending = true);
    try {
      final polls = await _pollService.getTrendingPolls(limit: 15);
      setState(() {
        _trendingPolls = polls;
        _loadingTrending = false;
      });
    } catch (e) {
      setState(() => _loadingTrending = false);
    }
  }

  Future<void> _loadFilteredPolls() async {
    if (_selectedCategory == null) return;
    
    setState(() => _loadingFiltered = true);
    try {
      final polls = await _pollService.getPolls(
        category: _selectedCategory,
        sortBy: 'created_at',
        ascending: false,
        limit: 20,
      );
      setState(() {
        _filteredPolls = polls;
        _loadingFiltered = false;
      });
    } catch (e) {
      setState(() => _loadingFiltered = false);
    }
  }

  Future<void> _searchPolls(String query) async {
    if (query.trim().isEmpty) {
      setState(() => _filteredPolls = []);
      return;
    }

    setState(() => _loadingFiltered = true);
    try {
      final polls = await _pollService.searchPolls(query.trim());
      setState(() {
        _filteredPolls = polls;
        _loadingFiltered = false;
      });
    } catch (e) {
      setState(() => _loadingFiltered = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('نبض'),
        backgroundColor: Colors.purple[600],
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'الأحدث', icon: Icon(Icons.access_time)),
            Tab(text: 'الأكثر تفاعلاً', icon: Icon(Icons.trending_up)),
            Tab(text: 'استكشاف', icon: Icon(Icons.explore)),
          ],
        ),
        actions: [
          IconButton(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => const CreatePollPage(),
                ),
              ).then((_) {
                _loadRecentPolls();
                _loadTrendingPolls();
              });
            },
            icon: const Icon(Icons.add),
            tooltip: 'إنشاء تصويت',
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // تبويب الأحدث
          _buildRecentTab(),
          
          // تبويب الأكثر تفاعلاً
          _buildTrendingTab(),
          
          // تبويب الاستكشاف
          _buildExploreTab(),
        ],
      ),
    );
  }

  Widget _buildRecentTab() {
    return RefreshIndicator(
      onRefresh: _loadRecentPolls,
      child: _loadingRecent
          ? const Center(child: CircularProgressIndicator())
          : _recentPolls.isEmpty
              ? _buildEmptyState('لا توجد تصويتات حديثة')
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _recentPolls.length,
                  itemBuilder: (context, index) {
                    return PollCard(
                      poll: _recentPolls[index],
                      onVote: (pollId, optionId) => _handleVote(pollId, optionId),
                      onRefresh: _loadRecentPolls,
                    );
                  },
                ),
    );
  }

  Widget _buildTrendingTab() {
    return RefreshIndicator(
      onRefresh: _loadTrendingPolls,
      child: _loadingTrending
          ? const Center(child: CircularProgressIndicator())
          : _trendingPolls.isEmpty
              ? _buildEmptyState('لا توجد تصويتات رائجة')
              : ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: _trendingPolls.length,
                  itemBuilder: (context, index) {
                    return PollCard(
                      poll: _trendingPolls[index],
                      onVote: (pollId, optionId) => _handleVote(pollId, optionId),
                      onRefresh: _loadTrendingPolls,
                    );
                  },
                ),
    );
  }

  Widget _buildExploreTab() {
    return Column(
      children: [
        // شريط البحث
        Container(
          padding: const EdgeInsets.all(16),
          child: TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'ابحث في التصويتات...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              filled: true,
              fillColor: Colors.grey[100],
            ),
            onSubmitted: _searchPolls,
          ),
        ),
        
        // فلتر الفئات
        Container(
          height: 50,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: PollCategory.values.length,
            itemBuilder: (context, index) {
              final category = PollCategory.values[index];
              final isSelected = _selectedCategory == category;
              
              return Padding(
                padding: const EdgeInsets.only(right: 8),
                child: FilterChip(
                  label: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(category.icon, size: 16),
                      const SizedBox(width: 4),
                      Text(category.arabicName),
                    ],
                  ),
                  selected: isSelected,
                  onSelected: (selected) {
                    setState(() {
                      _selectedCategory = selected ? category : null;
                      _filteredPolls = [];
                    });
                    if (selected) {
                      _loadFilteredPolls();
                    }
                  },
                  selectedColor: category.color.withValues(alpha: 0.3),
                  checkmarkColor: category.color,
                ),
              );
            },
          ),
        ),
        
        const SizedBox(height: 16),
        
        // النتائج
        Expanded(
          child: _loadingFiltered
              ? const Center(child: CircularProgressIndicator())
              : _filteredPolls.isEmpty
                  ? _buildEmptyState(
                      _selectedCategory != null
                          ? 'لا توجد تصويتات في فئة ${_selectedCategory!.arabicName}'
                          : 'ابحث أو اختر فئة لاستكشاف التصويتات'
                    )
                  : ListView.builder(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: _filteredPolls.length,
                      itemBuilder: (context, index) {
                        return PollCard(
                          poll: _filteredPolls[index],
                          onVote: (pollId, optionId) => _handleVote(pollId, optionId),
                          onRefresh: () {
                            if (_selectedCategory != null) {
                              _loadFilteredPolls();
                            } else if (_searchController.text.isNotEmpty) {
                              _searchPolls(_searchController.text);
                            }
                          },
                        );
                      },
                    ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.poll,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (_) => const CreatePollPage(),
                ),
              ).then((_) {
                _loadRecentPolls();
                _loadTrendingPolls();
              });
            },
            icon: const Icon(Icons.add),
            label: const Text('إنشاء تصويت جديد'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.purple[600],
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleVote(String pollId, String optionId) async {
    try {
      await _pollService.vote(pollId, optionId);
      
      // تحديث التصويت في جميع القوائم
      _loadRecentPolls();
      _loadTrendingPolls();
      if (_selectedCategory != null) {
        _loadFilteredPolls();
      } else if (_searchController.text.isNotEmpty) {
        _searchPolls(_searchController.text);
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم التصويت بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التصويت: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
