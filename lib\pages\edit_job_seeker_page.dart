import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../models/job_seeker.dart';
import '../services/job_seekers_service.dart';

class EditJobSeekerPage extends StatefulWidget {
  final JobSeeker jobSeeker;

  const EditJobSeekerPage({super.key, required this.jobSeeker});

  @override
  State<EditJobSeekerPage> createState() => _EditJobSeekerPageState();
}

class _EditJobSeekerPageState extends State<EditJobSeekerPage> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _ageController;
  late TextEditingController _nationalityController;
  late TextEditingController _currentCountryController;
  late TextEditingController _currentCityController;
  late TextEditingController _descriptionController;
  late TextEditingController _preferredLocationController;
  late TextEditingController _phoneController;
  late TextEditingController _emailController;
  late TextEditingController _socialLinksController;
  
  late String _selectedGender;
  late MaritalStatus _selectedMaritalStatus;
  late JobCategory _selectedCategory;
  late JobType _selectedJobType;
  late int _experienceYears;
  
  late List<String> _skills;
  late List<String> _languages;
  final TextEditingController _skillController = TextEditingController();
  final TextEditingController _languageController = TextEditingController();
  
  XFile? _profileImage;
  List<XFile> _portfolioImages = [];
  final ImagePicker _picker = ImagePicker();
  
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    _nameController = TextEditingController(text: widget.jobSeeker.fullName);
    _ageController = TextEditingController(text: widget.jobSeeker.age.toString());
    _nationalityController = TextEditingController(text: widget.jobSeeker.nationality);
    _currentCountryController = TextEditingController(text: widget.jobSeeker.currentCountry);
    _currentCityController = TextEditingController(text: widget.jobSeeker.currentCity);
    _descriptionController = TextEditingController(text: widget.jobSeeker.description);
    _preferredLocationController = TextEditingController(text: widget.jobSeeker.preferredLocation);
    _phoneController = TextEditingController(text: widget.jobSeeker.phoneNumber);
    _emailController = TextEditingController(text: widget.jobSeeker.email ?? '');
    _socialLinksController = TextEditingController(text: widget.jobSeeker.socialLinks ?? '');
    
    _selectedGender = widget.jobSeeker.gender;
    _selectedMaritalStatus = widget.jobSeeker.maritalStatus;
    _selectedCategory = widget.jobSeeker.category;
    _selectedJobType = widget.jobSeeker.preferredJobType;
    _experienceYears = widget.jobSeeker.experienceYears;
    
    _skills = List.from(widget.jobSeeker.skills);
    _languages = List.from(widget.jobSeeker.languages);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ageController.dispose();
    _nationalityController.dispose();
    _currentCountryController.dispose();
    _currentCityController.dispose();
    _descriptionController.dispose();
    _preferredLocationController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    _socialLinksController.dispose();
    _skillController.dispose();
    _languageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'تعديل الملف المهني',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.indigo[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: _deleteProfile,
            tooltip: 'حذف الملف',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // تحذير التعديل
            Card(
              color: Colors.blue[50],
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue[600]),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'تعديل الملف المهني',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.blue[800],
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'يمكنك تعديل جميع معلومات ملفك المهني. التغييرات ستظهر فوراً للمستخدمين الآخرين.',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.blue[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // المعلومات الشخصية
            _buildPersonalInfoSection(),
            
            const SizedBox(height: 16),
            
            // المعلومات المهنية
            _buildProfessionalInfoSection(),
            
            const SizedBox(height: 16),
            
            // المهارات واللغات
            _buildSkillsSection(),
            
            const SizedBox(height: 16),
            
            // معلومات التواصل
            _buildContactSection(),
            
            const SizedBox(height: 16),
            
            // الصور
            _buildImagesSection(),
            
            const SizedBox(height: 24),
            
            // أزرار الحفظ والإلغاء
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: BorderSide(color: Colors.grey[400]!),
                    ),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _loading ? null : _saveChanges,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.indigo[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: _loading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              color: Colors.white,
                              strokeWidth: 2,
                            ),
                          )
                        : const Text(
                            'حفظ التغييرات',
                            style: TextStyle(fontWeight: FontWeight.bold),
                          ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // زر حذف الملف
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: _deleteProfile,
                icon: const Icon(Icons.delete_forever, color: Colors.red),
                label: const Text(
                  'حذف الملف المهني نهائياً',
                  style: TextStyle(color: Colors.red),
                ),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: const BorderSide(color: Colors.red),
                ),
              ),
            ),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('المعلومات الشخصية', Icons.person),
            
            const SizedBox(height: 16),
            
            // الاسم الكامل
            _buildTextField(
              controller: _nameController,
              label: 'الاسم الكامل',
              icon: Icons.badge,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال الاسم الكامل';
                }
                if (value.trim().length < 3) {
                  return 'الاسم قصير جداً';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // العمر والجنس
            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: _ageController,
                    label: 'العمر',
                    icon: Icons.cake,
                    keyboardType: TextInputType.number,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال العمر';
                      }
                      final age = int.tryParse(value);
                      if (age == null || age < 16 || age > 70) {
                        return 'العمر يجب أن يكون بين 16-70';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedGender,
                    decoration: InputDecoration(
                      labelText: 'الجنس',
                      prefixIcon: Icon(Icons.person_outline, color: Colors.indigo[600]),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                    items: ['ذكر', 'أنثى'].map((gender) {
                      return DropdownMenuItem(
                        value: gender,
                        child: Text(gender),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedGender = value!);
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // الحالة الاجتماعية
            DropdownButtonFormField<MaritalStatus>(
              value: _selectedMaritalStatus,
              decoration: InputDecoration(
                labelText: 'الحالة الاجتماعية',
                prefixIcon: Icon(Icons.family_restroom, color: Colors.indigo[600]),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              items: MaritalStatus.values.map((status) {
                return DropdownMenuItem(
                  value: status,
                  child: Text(status.arabicName),
                );
              }).toList(),
              onChanged: (value) {
                setState(() => _selectedMaritalStatus = value!);
              },
            ),
            
            const SizedBox(height: 16),
            
            // الجنسية
            _buildTextField(
              controller: _nationalityController,
              label: 'الجنسية',
              icon: Icons.flag,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال الجنسية';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // البلد والمدينة الحالية
            Row(
              children: [
                Expanded(
                  child: _buildTextField(
                    controller: _currentCountryController,
                    label: 'البلد الحالي',
                    icon: Icons.public,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال البلد';
                      }
                      return null;
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildTextField(
                    controller: _currentCityController,
                    label: 'المدينة الحالية',
                    icon: Icons.location_city,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال المدينة';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfessionalInfoSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('المعلومات المهنية', Icons.work),
            
            const SizedBox(height: 16),
            
            // فئة العمل
            DropdownButtonFormField<JobCategory>(
              value: _selectedCategory,
              decoration: InputDecoration(
                labelText: 'فئة العمل',
                prefixIcon: Icon(_selectedCategory.icon, color: Colors.indigo[600]),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Colors.white,
              ),
              items: JobCategory.values.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Row(
                    children: [
                      Icon(category.icon, size: 20, color: category.color),
                      const SizedBox(width: 8),
                      Text(category.arabicName),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() => _selectedCategory = value!);
              },
            ),
            
            const SizedBox(height: 16),
            
            // سنوات الخبرة ونوع العمل المطلوب
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<int>(
                    value: _experienceYears,
                    decoration: InputDecoration(
                      labelText: 'سنوات الخبرة',
                      prefixIcon: Icon(Icons.work_history, color: Colors.indigo[600]),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                    items: List.generate(21, (index) => index).map((years) {
                      return DropdownMenuItem(
                        value: years,
                        child: Text(years == 0 ? 'بدون خبرة' : '$years سنة'),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _experienceYears = value!);
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: DropdownButtonFormField<JobType>(
                    value: _selectedJobType,
                    decoration: InputDecoration(
                      labelText: 'نوع العمل المطلوب',
                      prefixIcon: Icon(_selectedJobType.icon, color: Colors.indigo[600]),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                    items: JobType.values.map((type) {
                      return DropdownMenuItem(
                        value: type,
                        child: Text(type.arabicName),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() => _selectedJobType = value!);
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // الوصف
            _buildTextField(
              controller: _descriptionController,
              label: 'نبذة عن خبراتك ومهاراتك',
              icon: Icons.description,
              maxLines: 4,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال نبذة عن خبراتك';
                }
                if (value.trim().length < 20) {
                  return 'النبذة قصيرة جداً (20 حرف على الأقل)';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // المكان المفضل للعمل
            _buildTextField(
              controller: _preferredLocationController,
              label: 'المكان المفضل للعمل',
              icon: Icons.location_on,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال المكان المفضل للعمل';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkillsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('المهارات واللغات', Icons.star),
            
            const SizedBox(height: 16),
            
            // إضافة مهارة
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _skillController,
                    decoration: InputDecoration(
                      labelText: 'أضف مهارة',
                      prefixIcon: Icon(Icons.add_circle, color: Colors.indigo[600]),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _addSkill,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo[600],
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('إضافة'),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // عرض المهارات
            if (_skills.isNotEmpty)
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _skills.map((skill) {
                  return Chip(
                    label: Text(skill),
                    deleteIcon: const Icon(Icons.close, size: 18),
                    onDeleted: () => _removeSkill(skill),
                    backgroundColor: Colors.blue[50],
                    deleteIconColor: Colors.blue[700],
                  );
                }).toList(),
              ),
            
            const SizedBox(height: 16),
            
            // إضافة لغة
            Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _languageController,
                    decoration: InputDecoration(
                      labelText: 'أضف لغة',
                      prefixIcon: Icon(Icons.language, color: Colors.indigo[600]),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      filled: true,
                      fillColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: _addLanguage,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo[600],
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('إضافة'),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // عرض اللغات
            if (_languages.isNotEmpty)
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _languages.map((language) {
                  return Chip(
                    label: Text(language),
                    deleteIcon: const Icon(Icons.close, size: 18),
                    onDeleted: () => _removeLanguage(language),
                    backgroundColor: Colors.purple[50],
                    deleteIconColor: Colors.purple[700],
                  );
                }).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('معلومات التواصل', Icons.contact_phone),
            
            const SizedBox(height: 16),
            
            // رقم الهاتف
            _buildTextField(
              controller: _phoneController,
              label: 'رقم الهاتف',
              icon: Icons.phone,
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال رقم الهاتف';
                }
                if (!RegExp(r'^05[0-9]{8}$').hasMatch(value.trim())) {
                  return 'رقم الهاتف غير صحيح (يجب أن يبدأ بـ 05)';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // البريد الإلكتروني (اختياري)
            _buildTextField(
              controller: _emailController,
              label: 'البريد الإلكتروني (اختياري)',
              icon: Icons.email,
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value != null && value.trim().isNotEmpty) {
                  if (!RegExp(r'^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$').hasMatch(value.trim())) {
                    return 'صيغة البريد الإلكتروني غير صحيحة';
                  }
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // روابط التواصل الاجتماعي (اختياري)
            _buildTextField(
              controller: _socialLinksController,
              label: 'روابط التواصل الاجتماعي (اختياري)',
              icon: Icons.link,
              maxLines: 2,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImagesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildSectionTitle('الصور', Icons.photo_camera),
            
            const SizedBox(height: 16),
            
            // الصورة الشخصية الحالية
            if (widget.jobSeeker.profileImage != null) ...[
              const Text('الصورة الشخصية الحالية:'),
              const SizedBox(height: 8),
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  widget.jobSeeker.profileImage!,
                  height: 100,
                  width: 100,
                  fit: BoxFit.cover,
                ),
              ),
              const SizedBox(height: 12),
            ],
            
            // تغيير الصورة الشخصية
            Row(
              children: [
                const Text('تغيير الصورة الشخصية:'),
                const SizedBox(width: 12),
                ElevatedButton.icon(
                  onPressed: _pickProfileImage,
                  icon: const Icon(Icons.camera_alt),
                  label: Text(_profileImage == null ? 'اختر صورة' : 'تغيير الصورة'),
                ),
              ],
            ),
            
            if (_profileImage != null) ...[
              const SizedBox(height: 12),
              ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  _profileImage!.path,
                  height: 100,
                  width: 100,
                  fit: BoxFit.cover,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.indigo[600]),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        prefixIcon: Icon(icon, color: Colors.indigo[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.indigo[600]!),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
    );
  }

  void _addSkill() {
    final skill = _skillController.text.trim();
    if (skill.isNotEmpty && !_skills.contains(skill)) {
      setState(() {
        _skills.add(skill);
        _skillController.clear();
      });
    }
  }

  void _removeSkill(String skill) {
    setState(() {
      _skills.remove(skill);
    });
  }

  void _addLanguage() {
    final language = _languageController.text.trim();
    if (language.isNotEmpty && !_languages.contains(language)) {
      setState(() {
        _languages.add(language);
        _languageController.clear();
      });
    }
  }

  void _removeLanguage(String language) {
    setState(() {
      _languages.remove(language);
    });
  }

  Future<void> _pickProfileImage() async {
    final image = await _picker.pickImage(source: ImageSource.gallery);
    if (image != null) {
      setState(() {
        _profileImage = image;
      });
    }
  }

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;

    if (_skills.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة مهارة واحدة على الأقل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    if (_languages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى إضافة لغة واحدة على الأقل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() => _loading = true);

    try {
      // تحديث البيانات
      await JobSeekersService().updateJobSeeker(widget.jobSeeker.id, {
        'full_name': _nameController.text.trim(),
        'age': int.parse(_ageController.text),
        'gender': _selectedGender,
        'marital_status': _selectedMaritalStatus.name,
        'nationality': _nationalityController.text.trim(),
        'current_country': _currentCountryController.text.trim(),
        'current_city': _currentCityController.text.trim(),
        'category': _selectedCategory.name,
        'experience_years': _experienceYears,
        'description': _descriptionController.text.trim(),
        'preferred_job_type': _selectedJobType.name,
        'preferred_location': _preferredLocationController.text.trim(),
        'phone_number': _phoneController.text.trim(),
        'email': _emailController.text.trim().isEmpty ? null : _emailController.text.trim(),
        'social_links': _socialLinksController.text.trim().isEmpty ? null : _socialLinksController.text.trim(),
        'skills': _skills,
        'languages': _languages,
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ التغييرات بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ التغييرات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }

  void _deleteProfile() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تحذير!'),
        content: const Text(
          'هل أنت متأكد من حذف ملفك المهني نهائياً؟\n\n'
          'سيتم حذف:\n'
          '• جميع معلومات ملفك المهني\n'
          '• جميع الإعجابات والمشاهدات\n'
          '• جميع التفاعلات المرتبطة بملفك\n\n'
          'هذا الإجراء لا يمكن التراجع عنه!',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              setState(() => _loading = true);
              try {
                await JobSeekersService().deleteJobSeeker(widget.jobSeeker.id);
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف الملف المهني بنجاح'),
                      backgroundColor: Colors.green,
                    ),
                  );
                  Navigator.pop(context);
                  Navigator.pop(context);
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في الحذف: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              } finally {
                setState(() => _loading = false);
              }
            },
            child: const Text('حذف نهائياً', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
