import 'package:flutter/material.dart';
import '../models/post.dart';
import '../models/reaction_type.dart';
import '../supabase_service.dart';
import '../utils/number_format.dart';
import 'feed_media.dart';
import 'comments_sheet.dart';
import 'share_post_sheet.dart';
import 'link_preview.dart';
import 'reaction_details_sheet.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../widgets/user_level_badge.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:readmore/readmore.dart';
import 'dart:async';
import 'profile_avatar.dart';
import '../pages/profile_page.dart';
import 'package:flutter/services.dart'; // NEW: للحافظة
import 'package:share_plus/share_plus.dart';
import 'follow_button.dart';
import 'post_hide_options_sheet.dart';
import 'hidden_post_card.dart';
import '../widgets/interactive_verified_badge.dart';

class PostCard extends StatefulWidget {
  final Post post;
  final VoidCallback? onRefresh;
  final bool isInProfile; // لتحديد ما إذا كانت البطاقة في الملف الشخصي
  const PostCard({
    super.key,
    required this.post,
    this.onRefresh,
    this.isInProfile = false, // افتراضياً false
  });

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  late Post _post;
  bool _processing = false;
  DateTime? _lastViewSent; // آخر مرة أُرسِل فيها احتساب مشاهدة


  void _openUserProfile() {
    Navigator.push(
      context,
      PageRouteBuilder(
        pageBuilder: (_, __, ___) => ProfilePage(userId: _post.userId, username: _post.userName),
        transitionsBuilder: (_, animation, __, child) {
          return FadeTransition(opacity: animation, child: child);
        },
      ),
    );
  }

  bool _isUserVerified(String userId) {
    // أولاً، جرب استخدام isVerified من المنشور مباشرة
    if (_post.isVerified) {
      print('Post isVerified: true for user: $userId');
      return true;
    }
    
    // إذا لم يكن متاحاً، استخدم الكاش
    final cache = SupabaseService().profilesCache.value;
    final profile = cache[userId];
    final isVerified = profile?['is_verified'] ?? false;
    
    // Debug print لمعرفة حالة التحقق
    print('User ID: $userId, Is Verified: $isVerified, Profile: $profile');
    
    return isVerified;
  }

  // دالة جديدة لفحص حالة التحقق مباشرة من قاعدة البيانات
  Future<bool> _checkUserVerificationStatus(String userId) async {
    try {
      final profile = await SupabaseService().fetchProfile(userId);
      return profile?['is_verified'] ?? false;
    } catch (e) {
      print('Error checking verification status: $e');
      return false;
    }
  }

  @override
  void initState() {
    super.initState();
    _post = widget.post;
    
    // تحديث كاش الملف الشخصي إذا كان المستخدم موثقاً
    if (_post.isVerified) {
      SupabaseService().refreshUserProfile(_post.userId);
    }
  }

  Future<void> _react(ReactionType type) async {
    if (_processing) return;

    setState(() {
      Map<ReactionType, int> counts = Map.of(_post.reactionCounts);

      final bool wasPositive = _isPositive(_post.currentUserReaction);
      final bool willBePositive = _isPositive(type);

      if (_post.currentUserReaction == type) {
        // إزالة التفاعل (إلغاء)
        if (counts.containsKey(type)) {
          if (counts[type]! <= 1) {
            counts.remove(type);
          } else {
            counts[type] = counts[type]! - 1;
          }
        }
        _post = _post.copyWith(
          likesCount: wasPositive ? _post.likesCount - 1 : _post.likesCount,
          currentUserReaction: ReactionType.none,
          reactionCounts: counts,
        );
      } else {
        // إزالة تأثير التفاعل السابق إن وجد
        if (_post.currentUserReaction != ReactionType.none) {
          final old = _post.currentUserReaction;
          if (counts.containsKey(old)) {
            if (counts[old]! <= 1) {
              counts.remove(old);
            } else {
              counts[old] = counts[old]! - 1;
            }
          }
          if (wasPositive) {
            // كان سابقًا تفاعل إيجابى، نقص العدّاد
            _post = _post.copyWith(likesCount: _post.likesCount - 1);
          }
        }

        // أضف التفاعل الجديد
        counts[type] = (counts[type] ?? 0) + 1;

        _post = _post.copyWith(
          likesCount: (_post.currentUserReaction == ReactionType.none && willBePositive)
              ? _post.likesCount + 1
              : _post.likesCount + ((willBePositive ? 1 : 0) - (wasPositive ? 1 : 0)),
          currentUserReaction: type,
          reactionCounts: counts,
        );
      }
    });

    // Call backend without blocking UI
    _processing = true;
    SupabaseService()
        .toggleReaction(postId: _post.id, reaction: type)
        .whenComplete(() => setState(() => _processing = false));
  }

  Future<void> _openReactionPicker() async {
    // النقر العادي يعمل فقط كـ like عادي
    _react(ReactionType.like);
  }

  Future<ReactionType?> _openReactionPickerAt(Offset globalPos) async {
    final items = [
      ReactionType.like,
      ReactionType.dislike,
      ReactionType.support,
      ReactionType.love,
      ReactionType.funny,
      ReactionType.angry,
      ReactionType.sad,
    ];

    const double iconWidth = 38; // حجم أصغر (32 حجم الإيموجى + حشوة صغيرة)
    final double barWidth = items.length * iconWidth + 16; // إضافة padding للحاوية
    const double barHeight = 44; // ارتفاع أصغر

    final media = MediaQuery.of(context);
    // تحريك الحاوية لليسار ضعفين
    double left = globalPos.dx - barWidth / 2 - (barWidth / 2);
    left = left.clamp(8.0, media.size.width - barWidth - 8.0);

    // جعل التفاعلات تظهر فوق الفاصل العلوي للأزرار (أعلى أكثر)
    double top = globalPos.dy - barHeight - 60; // أعلى بكثير من الإصبع لتظهر فوق الفاصل
    if (top < media.padding.top + 20) {
      top = media.padding.top + 20;
    }

    final selected = await showDialog<ReactionType>(
      context: context,
      barrierColor: Colors.transparent,
      builder: (ctx) {
        return Stack(children: [
          Positioned(
            left: left,
            top: top,
            child: _AnimatedReactionPicker(
              reactions: items,
              onReactionSelected: (reaction) => Navigator.pop(ctx, reaction),
            ),
          ),
        ]);
      },
    );

    return selected;
  }

  void _openComments() async {
    // استخدام نفس نظام التعليقات العادي للجميع (منشورات عادية ومنشورات مجتمع)
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) => CommentsSheet(
        postId: _post.id,
        postType: 'post',
        onCommentAdded: () async {
          // تحديث عدد التعليقات فوراً في الواجهة
          setState(() => _post = _post.copyWith(commentsCount: _post.commentsCount + 1));

          // إعادة تحميل عدد التعليقات الصحيح من قاعدة البيانات
          _refreshCommentsCount();

          // تحديث الصفحة الرئيسية
          widget.onRefresh?.call();
        },
      ),
    );
  }

  /// إعادة تحميل عدد التعليقات الصحيح من قاعدة البيانات
  Future<void> _refreshCommentsCount() async {
    try {
      final actualCount = await SupabaseService().getPostCommentsCount(_post.id);
      if (mounted && actualCount != _post.commentsCount) {
        setState(() {
          _post = _post.copyWith(commentsCount: actualCount);
        });
      }
    } catch (e) {
      // في حالة الخطأ، نحتفظ بالعدد الحالي
    }
  }



  void _openShareInternal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) => SharePostSheet(post: _post, onShared: widget.onRefresh),
    );
  }

  void _shareToChat() async {
    final followers = await SupabaseService().fetchFollowingUsers();
    if (!mounted) return;
    showModalBottomSheet(
      context: context,
      builder: (_) => SafeArea(
        child: ListView.builder(
          itemCount: followers.length,
          itemBuilder: (ctx, i) {
            final f = followers[i];
            return ListTile(
              leading: ProfileAvatar(userId: f['id']),
              title: Text(f['name'] ?? 'مستخدم'),
              onTap: () async {
                Navigator.pop(ctx);
                final chatId = await SupabaseService().getOrCreateChat(f['id']);
                await SupabaseService().sendMessage(
                  chatId: chatId,
                  content: 'https://arzawo.com/posts/${_post.id}',
                );
                if (mounted) ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تمت مشاركة المنشور فى الدردشة')));
              },
            );
          },
        ),
      ),
    );
  }

  void _handleMenu(String value) async {
    switch (value) {
      case 'edit':
        final controller = TextEditingController(text: _post.content);
        final newText = await showDialog<String>(
          context: context,
          builder: (ctx) => AlertDialog(
            title: const Text('تعديل المنشور'),
            content: TextField(controller: controller, maxLines: 5),
            actions: [
              TextButton(onPressed: () => Navigator.pop(ctx), child: const Text('إلغاء')),
              TextButton(onPressed: () => Navigator.pop(ctx, controller.text.trim()), child: const Text('حفظ')),
            ],
          ),
        );
        if (newText != null && newText.isNotEmpty && newText != _post.content) {
          await SupabaseService().updatePostContent(_post.id, newText);
          widget.onRefresh?.call();
        }
        break;
      case 'delete':
        final ok = await showDialog<bool>(
          context: context,
          builder: (ctx) => AlertDialog(
            title: const Text('حذف المنشور؟'),
            content: const Text('لا يمكن التراجع بعد الحذف.'),
            actions: [
              TextButton(onPressed: () => Navigator.pop(ctx, false), child: const Text('إلغاء')),
              TextButton(onPressed: () => Navigator.pop(ctx, true), child: const Text('حذف')),
            ],
          ),
        );
        if (ok == true) {
          await SupabaseService().deletePost(_post.id);
          widget.onRefresh?.call();
        }
        break;
      case 'report':
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم إرسال البلاغ')));
        break;
      case 'block':
        await SupabaseService().blockUser(_post.userId);
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم حظر المستخدم')));
        break;
      case 'toggle_save':
        final wasSaved = _post.isSaved;
        await SupabaseService().toggleSavePost(_post.id);
        setState(() => _post = _post.copyWith(isSaved: !_post.isSaved));
        if (widget.onRefresh != null) widget.onRefresh!();
        
        // رسائل أكثر دقة حسب نوع المنشور
        String msg;
        if (wasSaved) {
          // كان محفوظاً وأصبح غير محفوظ
          switch (_post.type) {
            case PostType.video:
              msg = 'تم إزالة الفيديو من المحفوظات';
              break;
            case PostType.image:
              msg = 'تم إزالة الصورة من المحفوظات';
              break;
            default:
              msg = 'تم إزالة المنشور من المحفوظات';
          }
        } else {
          // لم يكن محفوظاً وأصبح محفوظاً
          switch (_post.type) {
            case PostType.video:
              msg = 'تم حفظ الفيديو في المحفوظات';
              break;
            case PostType.image:
              msg = 'تم حفظ الصورة في المحفوظات';
              break;
            default:
              msg = 'تم حفظ المنشور في المحفوظات';
          }
        }
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(msg)));
        break;
      case 'privacy':
        final choice = await showDialog<String>(
          context: context,
          builder: (ctx) => SimpleDialog(
            title: const Text('من يمكنه رؤية هذا المنشور؟'),
            children: [
              SimpleDialogOption(onPressed: () => Navigator.pop(ctx, 'public'), child: const Text('الجميع')),
              SimpleDialogOption(onPressed: () => Navigator.pop(ctx, 'followers'), child: const Text('المتابِعون فقط')),
            ],
          ),
        );
        if (choice != null) {
          await SupabaseService().updatePostPrivacy(_post.id, choice);
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم تحديث الخصوصية')));
        }
        break;
      case 'comment_perm':
        final choice2 = await showDialog<String>(
          context: context,
          builder: (ctx) => SimpleDialog(
            title: const Text('من يمكنه التعليق؟'),
            children: [
              SimpleDialogOption(onPressed: () => Navigator.pop(ctx, 'everyone'), child: const Text('الجميع')),
              SimpleDialogOption(onPressed: () => Navigator.pop(ctx, 'followers'), child: const Text('المتابِعون فقط')),
            ],
          ),
        );
        if (choice2 != null) {
          await SupabaseService().updatePostCommentPermission(_post.id, choice2);
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم تحديث إعداد التعليقات')));
        }
        break;
      case 'watch_later':
        await SupabaseService().toggleWatchLater(_post.id);
        setState(() {
          _post = _post.copyWith(isSaved: !_post.isSaved);
        });
        break;
      case 'copy_link':
        final link = 'https://arzawo.com/posts/${_post.id}';
        await Clipboard.setData(ClipboardData(text: link));

        try {
          final bool firstTime = await SupabaseService().copyPost(_post.id);
          if (firstTime) {
            if (mounted) {
              setState(() {
                _post = _post.copyWith(copiesCount: _post.copiesCount + 1);
              });
            }
          }
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: const Text('تم نسخ الرابط إلى الحافظة'),
                action: SnackBarAction(
                  label: 'مشاركة',
                  textColor: Colors.white,
                  onPressed: () => Share.share(link),
                ),
              ),
            );
          }
        } catch (e) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('تعذّر تسجيل النسخ: $e')),
            );
          }
        }
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // إذا كان المنشور مخفياً، عرض بطاقة المنشور المخفي
    if (_post.isHidden) {
      return HiddenPostCard(
        post: _post,
        onUndo: _undoHidePost,
      );
    }

    return VisibilityDetector(
      key: ValueKey('post_${_post.id}'),
      onVisibilityChanged: (info) {
        const double minFraction = 0.5;   // النسبة المطلوبة لاحتساب مشاهدة
        const double resetFraction = 0.2; // النسبة التى نعتبر عندها أنّ المستخدم غادر
        const cooldown = Duration(seconds: 3); // أقل مدة بين عدّتين متتاليتين

        final now = DateTime.now();

        // عندما يكون المنشور ظاهرًا بما يكفى
        if (info.visibleFraction >= minFraction) {
          if (_lastViewSent == null || now.difference(_lastViewSent!) >= cooldown) {
            _lastViewSent = now;
            SupabaseService().incrementPostViews(_post.id);
            setState(() => _post = _post.copyWith(viewsCount: _post.viewsCount + 1));
          }
        } else if (info.visibleFraction < resetFraction) {
          // إعادة السماح بعد مغادرة المستخدم منطقة الرؤية
          _lastViewSent = null;
        }
      },
      child: Container(
        width: double.infinity, // عرض كامل الشاشة
        color: Colors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // هيدر المنشور مع padding داخلي فقط
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
              Row(
                children: [
                  GestureDetector(
                    onTap: _openUserProfile,
                    child: Hero( // NEW: انتقال سلس للملف الشخصي
                      tag: 'avatar_${_post.userId}',
                      child: CircleAvatar(
                        radius: 20,
                        backgroundColor: Colors.grey.shade300,
                        backgroundImage: (_post.userAvatar.isNotEmpty)
                            ? NetworkImage(_post.userAvatar)
                            : null,
                        child: _post.userAvatar.isEmpty
                            ? const Icon(Icons.person, size: 20)
                            : null,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: GestureDetector(
                      onTap: _openUserProfile,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: FutureBuilder<Map<String, dynamic>?>(
                                  future: SupabaseService().fetchProfile(_post.userId),
                                  builder: (context, snapshot) {
                                    final isUserVerified = snapshot.hasData && (snapshot.data?['is_verified'] ?? false);
                                    final finalIsVerified = _isUserVerified(_post.userId) || isUserVerified;
                                    
                                    print('PostCard FutureBuilder - Name: ${_post.userName}, Is Verified: $finalIsVerified, User ID: ${_post.userId}');
                                    
                                    return QuickUserLevel(
                                      userId: _post.userId,
                                      name: _post.userName,
                                      isVerified: finalIsVerified,
                                      textStyle: const TextStyle(fontWeight: FontWeight.bold),
                                      badgeSize: 16,
                                    );
                                  },
                                ),
                              ),
                              const SizedBox(width: 8),
                              FollowButton(
                                userId: _post.userId,
                                userName: _post.userName,
                              ),
                            ],
                          ),
                          Text(_formatTime(_post.createdAt), style: theme.textTheme.bodySmall),
                        ],
                      ),
                    ),
                  ),
                  PopupMenuButton<String>(
                    onSelected: _handleMenu,
                    itemBuilder: (_) {
                      final isMe = Supabase.instance.client.auth.currentUser?.id == _post.userId;
                      List<PopupMenuEntry<String>> items = [];
                      if (isMe) {
                        if (_post.type == PostType.text) {
                          items.add(_popupItem(
                            value: 'edit',
                            icon: Icons.edit,
                            label: 'تعديل المحتوى',
                            subtitle: 'تعديل نص المنشور الذى كتبته',
                          ));
                        }
                        items.add(_popupItem(
                          value: 'privacy',
                          icon: Icons.lock_outline,
                          label: 'تعديل الخصوصية',
                          subtitle: 'تحديد من يمكنه رؤية المنشور',
                        ));
                        items.add(_popupItem(
                          value: 'comment_perm',
                          icon: Icons.mode_comment_outlined,
                          label: 'من يمكنه التعليق؟',
                          subtitle: 'اختر من يستطيع إضافة تعليقات',
                        ));
                        items.add(_popupItem(
                          value: 'delete',
                          icon: Icons.delete_outline,
                          label: 'حذف',
                          subtitle: 'إزالة المنشور نهائياً',
                        ));
                      } else {
                        items.add(_popupItem(
                          value: 'report',
                          icon: Icons.flag_outlined,
                          label: 'الإبلاغ عن المنشور',
                          subtitle: 'سيتم مراجعته من الإدارة',
                        ));
                        items.add(_popupItem(
                          value: 'block',
                          icon: Icons.block,
                          label: 'حظر المستخدم',
                          subtitle: 'لن ترى محتوى هذا المستخدم بعد الآن',
                        ));
                      }

                      // رسائل أكثر دقة حسب نوع المنشور
                      String saveLabel;
                      String saveSubtitle;
                      
                      if (_post.isSaved) {
                        // المنشور محفوظ حالياً
                        switch (_post.type) {
                          case PostType.video:
                            saveLabel = 'إزالة الفيديو من المحفوظات';
                            saveSubtitle = 'سيتم إزالة الفيديو من قائمة المحفوظات';
                            break;
                          case PostType.image:
                            saveLabel = 'إزالة الصورة من المحفوظات';
                            saveSubtitle = 'سيتم إزالة الصورة من قائمة المحفوظات';
                            break;
                          default:
                            saveLabel = 'إزالة المنشور من المحفوظات';
                            saveSubtitle = 'سيتم إزالة المنشور من قائمة المحفوظات';
                        }
                      } else {
                        // المنشور غير محفوظ حالياً
                        switch (_post.type) {
                          case PostType.video:
                            saveLabel = 'حفظ الفيديو';
                            saveSubtitle = 'سيتم حفظ الفيديو في قسم المحفوظات';
                            break;
                          case PostType.image:
                            saveLabel = 'حفظ الصورة';
                            saveSubtitle = 'سيتم حفظ الصورة في قسم المحفوظات';
                            break;
                          default:
                            saveLabel = 'حفظ المنشور';
                            saveSubtitle = 'سيتم حفظ المنشور في قسم المحفوظات';
                        }
                      }
                      items.add(_popupItem(
                        value: 'toggle_save',
                        icon: _post.isSaved ? Icons.bookmark_remove_outlined : Icons.bookmark_add_outlined,
                        label: saveLabel,
                        subtitle: saveSubtitle,
                      ));
                      if (_post.type == PostType.video) {
                        items.add(_popupItem(
                          value: 'watch_later',
                          icon: Icons.watch_later_outlined,
                          label: _post.isSaved ? 'إزالة من المشاهدة لاحقًا' : 'حفظ للمشاهدة لاحقًا',
                          subtitle: 'ستجده في "للمشاهدة لاحقًا" داخل الإعدادات',
                        ));
                      }
                      return items;
                    },
                  ),
                  // زر الإغلاق (لا يظهر لصاحب المنشور أو في الملف الشخصي)
                  if (!_post.isCurrentUserPost && !widget.isInProfile)
                    IconButton(
                      onPressed: () => _showHideOptions(),
                      icon: const Icon(Icons.close, size: 20),
                      tooltip: 'إخفاء المنشور',
                      style: IconButton.styleFrom(
                        foregroundColor: Colors.grey[600],
                        padding: const EdgeInsets.all(4),
                        minimumSize: const Size(32, 32),
                      ),
                    ),
                ],
              ),
                ],
              ),
            ),

            // المحتوى والوسائط بدون padding جانبي
            if (_post.bgColor != null && _post.type == PostType.text)
              Builder(builder: (context) {
                final bgColor = Color(int.parse(_post.bgColor!.substring(1, 7), radix: 16) + 0xFF000000);
                final textColor = ThemeData.estimateBrightnessForColor(bgColor) == Brightness.light
                    ? Colors.black
                    : Colors.white;
                return Container(
                  width: double.infinity, // عرض كامل الشاشة
                  padding: const EdgeInsets.symmetric(vertical: 60, horizontal: 20), // padding أكبر
                  decoration: BoxDecoration(
                    color: bgColor,
                    // إزالة borderRadius لجعلها بعرض كامل
                  ),
                    child: ReadMoreText(
                      _post.content,
                      trimLines: 1,
                      trimMode: TrimMode.Line,
                      trimCollapsedText: ' عرض المزيد',
                      trimExpandedText: ' عرض أقل',
                      moreStyle: TextStyle(
                        color: textColor.withOpacity(0.85),
                        fontWeight: FontWeight.bold,
                        decoration: TextDecoration.underline,
                      ),
                      lessStyle: TextStyle(
                        color: textColor.withOpacity(0.85),
                        fontWeight: FontWeight.bold,
                        decoration: TextDecoration.underline,
                      ),
                      style: TextStyle(fontSize: 24, color: textColor, fontWeight: FontWeight.bold),
                      textAlign: TextAlign.center,
                    ),
                  );
                })
            else if (_post.content.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: ReadMoreText(
                  _post.content,
                  trimLines: 1,
                  trimMode: TrimMode.Line,
                  trimCollapsedText: ' عرض المزيد',
                  trimExpandedText: ' عرض أقل',
                  moreStyle: const TextStyle(color: Colors.blue),
                  lessStyle: const TextStyle(color: Colors.blue),
                ),
              ),

            // الوسائط بعرض كامل الشاشة
            if (_post.type == PostType.shared && _post.originalPost != null) ...[
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: _buildSharedPreview(_post.originalPost!),
              ),
            ] else if (_post.type == PostType.shared && _post.originalPost == null) ...[
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: _buildSharedMissing(),
              ),
            ] else if (_post.type == PostType.image && (_post.mediaUrl != null || _post.mediaUrls != null)) ...[
              const SizedBox(height: 8),
              // Debug print
              Builder(
                builder: (context) {
                  print('DEBUG: Post ${_post.id} has mediaUrl: ${_post.mediaUrl}');
                  print('DEBUG: Post ${_post.id} has mediaUrls: ${_post.mediaUrls}');
                  return const SizedBox.shrink();
                },
              ),
              // الصور بعرض كامل الشاشة - أولوية للصور المتعددة
              SizedBox(
                width: double.infinity,
                child: _post.mediaUrls != null && _post.mediaUrls!.isNotEmpty
                    ? FeedMultiImage(urls: _post.mediaUrls!)
                    : (_post.mediaUrl != null ? FeedImage(url: _post.mediaUrl!) : const SizedBox.shrink()),
              ),
            ] else if (_post.type == PostType.video && (_post.mediaUrl != null || _post.mediaUrls != null)) ...[
              const SizedBox(height: 8),
              // الفيديوهات بعرض كامل الشاشة - أولوية للفيديوهات المتعددة
              SizedBox(
                width: double.infinity,
                child: _post.mediaUrls != null && _post.mediaUrls!.isNotEmpty
                    ? FeedMultiVideo(urls: _post.mediaUrls!)
                    : (_post.mediaUrl != null ? FeedVideoPlayer(url: _post.mediaUrl!) : const SizedBox.shrink()),
              ),
            ] else if ((_post.type == PostType.audio || _post.type == PostType.voice) && _post.mediaUrl != null) ...[
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: FeedAudioPlayer(url: _post.mediaUrl!, isVoice: _post.type == PostType.voice),
              ),
            ] else if (_post.type == PostType.link && _post.linkUrl != null) ...[
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                child: LinkPreview(url: _post.linkUrl!, meta: _post.linkMeta),
              ),
            ],

            // الإحصائيات والأزرار مع padding داخلي
            Padding(
              padding: const EdgeInsets.all(12),
              child: Column(
                children: [
                  // سطر واحد يحتوي على الأيقونات المتداخلة والإحصائيات
                  _buildStatsRow(),
                  const SizedBox(height: 8),
                  // أزرار التفاعل
                  Row(
                    children: [
                    _fbAction(
                      icon: _reactionIconSmall(),
                      label: _reactionLabel(),
                      count: _post.likesCount,
                      onTap: () {
                        if (_post.currentUserReaction == ReactionType.none || _post.currentUserReaction == ReactionType.like) {
                          _react(ReactionType.like);
                        } else {
                          _openReactionPicker();
                        }
                      },
                      onLongPressStart: (details) async {
                        final selected = await _openReactionPickerAt(details.globalPosition);
                        if (selected != null) {
                          _react(selected);
                        }
                      },
                    ),
                    _fbAction(
                      icon: const Icon(Icons.mode_comment, size: 18),
                      label: 'تعليق',
                      count: _post.commentsCount,
                      onTap: _openComments,
                    ),
                    _fbAction(
                      icon: const Icon(Icons.ios_share, size: 18),
                      label: 'مشاركة',
                      count: _post.sharesCount,
                      onTap: _openShareInternal,
                    ),
                    _fbAction(
                      icon: const Icon(Icons.copy, size: 18),
                      label: 'نسخ',
                      count: _post.copiesCount,
                      onTap: _copyLink,
                    ),
                    _fbAction(
                      icon: const Icon(Icons.visibility, size: 18),
                      label: 'مشاهدة',
                      count: _post.viewsCount,
                      onTap: null,
                    ),
                    ],
                  ),
                ],
              ),
            ),

            // فاصل بين المنشورات
            Container(
              height: 8,
              color: Colors.grey[100],
            ),
          ],
        ),
      ),
    );
  }

  Widget _fbAction({
    required Widget icon,
    required String label,
    required int count,
    VoidCallback? onTap,
    GestureLongPressStartCallback? onLongPressStart,
  }) {
    final color = Colors.grey.shade600;
    final content = Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        icon,
        const SizedBox(width: 4),
        Text(label, style: const TextStyle(fontSize: 12, color: Colors.black87)),
      ],
    );

    return Expanded(
      child: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: onTap,
        onLongPressStart: onLongPressStart,
        child: content,
      ),
    );
  }

  Widget _reactionIconSmall() {
    if (_post.currentUserReaction == ReactionType.none) {
      return const Icon(Icons.thumb_up_alt_outlined, size: 18);
    }
    return Image.asset(
      _post.currentUserReaction.assetPath,
      width: 18,
      height: 18,
      fit: BoxFit.contain,
    );
  }

  String _reactionLabel() {
    if (_post.currentUserReaction == ReactionType.none || _post.currentUserReaction == ReactionType.like) {
      return 'أعجبني';
    }
    // Arabic labels mapping
    switch (_post.currentUserReaction) {
      case ReactionType.dislike:
        return 'لا يعجبني';
      case ReactionType.celebrate:
        return 'مبروك';
      case ReactionType.support:
        return 'أدعمك';
      case ReactionType.love:
        return 'أحببته';
      case ReactionType.funny:
        return 'أضحكني';
      case ReactionType.angry:
        return 'أغضبني';
      case ReactionType.sad:
        return 'أحزنني';
      default:
        return 'أعجبني';
    }
  }

  // ------------------ معاينة منشور مُعاد ------------------ //
  Widget _buildSharedPreview(Post orig) {
    return Card(
      margin: EdgeInsets.zero,
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.red[300],
                  backgroundImage: orig.userAvatar.isNotEmpty ? NetworkImage(orig.userAvatar) : null,
                  child: orig.userAvatar.isEmpty ? Text(orig.userName.isNotEmpty ? orig.userName[0] : 'م') : null,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: QuickUserLevel(
                              userId: orig.userId,
                              name: orig.userName,
                              isVerified: false,
                              textStyle: const TextStyle(fontWeight: FontWeight.bold),
                              badgeSize: 14,
                            ),
                          ),
                          const SizedBox(width: 8),
                          FollowButton(
                            userId: orig.userId,
                            userName: orig.userName,
                            height: 20,
                            fontSize: 9,
                            compact: true,
                          ),
                        ],
                      ),
                      Text(_formatTime(orig.createdAt), style: const TextStyle(fontSize: 12, color: Colors.grey)),
                    ],
                  ),
                ),
              ],
            ),
            if (orig.content.isNotEmpty) ...[
              const SizedBox(height: 8),
              Text(orig.content),
            ],
            if (orig.type == PostType.image && orig.mediaUrl != null) ...[
              const SizedBox(height: 8),
              FeedImage(url: orig.mediaUrl!),
            ] else if (orig.type == PostType.video && orig.mediaUrl != null) ...[
              const SizedBox(height: 8),
              FeedVideoPlayer(url: orig.mediaUrl!),
            ] else if ((orig.type == PostType.audio || orig.type == PostType.voice) && orig.mediaUrl != null) ...[
              const SizedBox(height: 8),
              FeedAudioPlayer(url: orig.mediaUrl!, isVoice: orig.type == PostType.voice),
            ] else if (orig.type == PostType.link && orig.linkUrl != null) ...[
              const SizedBox(height: 8),
              LinkPreview(url: orig.linkUrl!, meta: orig.linkMeta),
            ],
          ],
        ),
      ),
    );
  }

  // ------------------ معاينة مفقودة ------------------ //
  Widget _buildSharedMissing() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: const [
          Icon(Icons.error_outline, color: Colors.grey),
          SizedBox(width: 8),
          Expanded(child: Text('تعذّر العثور على المنشور الأصلي أو لم يعد متاحًا', style: TextStyle(color: Colors.grey))),
        ],
      ),
    );
  }

  String _formatTime(DateTime dt) {
    final diff = DateTime.now().difference(dt);
    if (diff.inSeconds < 60) return 'الآن';
    if (diff.inMinutes < 60) return 'قبل ${diff.inMinutes} دقيقة';
    if (diff.inHours < 24) return 'قبل ${diff.inHours} ساعة';
    if (diff.inDays < 7) return 'قبل ${diff.inDays} يوم';
    return '${dt.day}/${dt.month}/${dt.year}';
  }

  PopupMenuItem<String> _popupItem({required String value, required IconData icon, required String label, required String subtitle}) {
    return PopupMenuItem(
      value: value,
      child: Row(
        children: [
          Icon(icon, color: Colors.black87),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(label, style: const TextStyle(fontWeight: FontWeight.bold, color: Colors.black87)),
                const SizedBox(height: 2),
                Text(subtitle, style: const TextStyle(fontSize: 11, color: Colors.grey)),
              ],
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildStatsRow() {
    // بناء الأيقونات المتداخلة
    final entries = _post.reactionCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final iconWidgets = <Widget>[];
    // عرض 3 تفاعلات كحد أقصى مع تداخل مثل فيسبوك
    for (int i = 0; i < entries.length && i < 3; i++) {
      final rt = entries[i].key;
      iconWidgets.add(
        Positioned(
          right: i * 12.0, // تداخل حقيقي: كل أيقونة تتداخل مع السابقة بـ 12 بكسل
          child: Container(
            width: 18, // حجم أصغر
            height: 18,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Colors.white,
                width: 1.5, // حدود بيضاء لفصل الأيقونات
              ),
            ),
            child: ClipOval(
              child: Image.asset(
                rt.assetPath,
                width: 18,
                height: 18,
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),
      );
    }

    final totalRaw = _post.reactionCounts.values.fold<int>(0, (a, b) => a + b);
    final total = NumberFormatUtil.prettyCount(totalRaw);

    // بناء الإحصائيات
    final stats = <String>[];

    if (_post.commentsCount > 0) {
      stats.add('${NumberFormatUtil.prettyCount(_post.commentsCount)} تعليق');
    }

    if (_post.sharesCount > 0) {
      stats.add('${NumberFormatUtil.prettyCount(_post.sharesCount)} مشاركة');
    }

    if (_post.copiesCount > 0) {
      stats.add('${NumberFormatUtil.prettyCount(_post.copiesCount)} نسخ');
    }

    if (_post.viewsCount > 0) {
      stats.add('${NumberFormatUtil.prettyCount(_post.viewsCount)} مشاهدة');
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // إحصائيات التفاعلات (الأيقونات المتداخلة + العدد) مثل فيسبوك
        if (_post.reactionCounts.isNotEmpty)
          GestureDetector(
            onTap: () => _showReactionDetails(),
            child: Row(
              children: [
                // Stack للأيقونات المتداخلة
                SizedBox(
                  width: iconWidgets.length == 1
                      ? 18
                      : iconWidgets.length == 2
                          ? 30 // 18 + 12 تداخل
                          : 42, // 18 + 12 + 12 تداخل
                  height: 18,
                  child: Stack(
                    children: iconWidgets,
                  ),
                ),
                const SizedBox(width: 6),
                Text(
                  '$total',
                  style: const TextStyle(fontSize: 13,fontWeight: FontWeight.w500,color: Colors.black87),
                ),
              ],
            ),
          )
        else
          const SizedBox.shrink(),

        // إحصائيات الأزرار الأخرى (التعليقات، المشاركات، المشاهدات) في اليمين
        if (stats.isNotEmpty)
          Text(
            stats.join(' • '),
            style: const TextStyle(fontSize: 12, color: Colors.black87),
          )
        else
          const SizedBox.shrink(),
      ],
    );
  }

  void _showReactionDetails() async {
    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(
          child: CircularProgressIndicator(),
        ),
      );

      // جلب تفاصيل المتفاعلين من الخادم
      final reactionDetails = await SupabaseService().getPostReactionDetails(_post.id);

      if (!mounted) return;

      // إغلاق مؤشر التحميل
      Navigator.of(context).pop();

      // عرض النافذة المنبثقة
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder: (context) => ReactionDetailsSheet(
          reactionDetails: reactionDetails,
          reactionCounts: _post.reactionCounts,
        ),
      );
    } catch (e) {
      if (!mounted) return;

      // إغلاق مؤشر التحميل في حالة الخطأ
      Navigator.of(context).pop();

      // عرض رسالة خطأ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في جلب تفاصيل التفاعلات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  bool _isPositive(ReactionType t) => [
        ReactionType.like,
        ReactionType.celebrate,
        ReactionType.support,
        ReactionType.love,
        ReactionType.insightful,
        ReactionType.funny,
      ].contains(t);

  void _copyLink() async {
    final link = 'https://arzawo.com/posts/${_post.id}';
    await Clipboard.setData(ClipboardData(text: link));

    try {
      final bool firstTime = await SupabaseService().copyPost(_post.id);
      if (firstTime) {
        if (mounted) {
          setState(() {
            _post = _post.copyWith(copiesCount: _post.copiesCount + 1);
          });
        }
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('تم نسخ الرابط إلى الحافظة'),
            action: SnackBarAction(
              label: 'مشاركة',
              textColor: Colors.white,
              onPressed: () => Share.share(link),
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('تعذّر تسجيل النسخ: $e')),
        );
      }
    }
  }

  // دالة إظهار خيارات الإخفاء
  void _showHideOptions() {
    showPostHideOptions(
      context: context,
      post: _post,
      onHide: (reason) => _hidePost(reason),
      onReport: () => _reportPost(),
    );
  }

  // دالة إخفاء المنشور
  void _hidePost(HideReason reason) async {
    await SupabaseService().hidePost(_post.id, reason.toString().split('.').last);
    setState(() {
      _post = _post.copyWith(
        isHidden: true,
        hideReason: reason.toString().split('.').last,
        hiddenAt: DateTime.now(),
      );
    });

    // إظهار رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('تم إخفاء المنشور'),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: 'تراجع',
          textColor: Colors.white,
          onPressed: () => _undoHidePost(),
        ),
      ),
    );
  }

  // دالة التراجع عن الإخفاء
  void _undoHidePost() async {
    await SupabaseService().unhidePost(_post.id);
    setState(() {
      _post = _post.copyWith(
        isHidden: false,
        hideReason: null,
        hiddenAt: null,
      );
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إظهار المنشور مرة أخرى'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  // دالة الإبلاغ عن المنشور
  void _reportPost() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإبلاغ عن المنشور'),
        content: const Text('هل تريد الإبلاغ عن هذا المنشور كمحتوى مخالف؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement report functionality
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إرسال البلاغ، شكراً لك'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('إبلاغ', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}

class _AnimatedReactionPicker extends StatefulWidget {
  final List<ReactionType> reactions;
  final Function(ReactionType) onReactionSelected;

  const _AnimatedReactionPicker({
    required this.reactions,
    required this.onReactionSelected,
  });

  @override
  State<_AnimatedReactionPicker> createState() => _AnimatedReactionPickerState();
}

class _AnimatedReactionPickerState extends State<_AnimatedReactionPicker>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<Offset>> _slideAnimations;
  late List<Animation<double>> _bounceAnimations;
  late List<Animation<double>> _shimmerAnimations;
  late AnimationController _containerController;
  late Animation<double> _containerScaleAnimation;

  @override
  void initState() {
    super.initState();

    // كونترولر للحاوية الرئيسية
    _containerController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );

    _containerScaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _containerController, curve: Curves.easeOutBack),
    );

    // كونترولرز للتفاعلات الفردية - أسرع بكثير
    _controllers = List.generate(
      widget.reactions.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 120), // أسرع جداً مثل Facebook
        vsync: this,
      ),
    );

    // أنيميشن التكبير مع تأثير مرتد قوي
    _scaleAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.elasticOut),
      );
    }).toList();

    // أنيميشن الانزلاق من الأسفل
    _slideAnimations = _controllers.map((controller) {
      return Tween<Offset>(begin: const Offset(0, 1.0), end: Offset.zero).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeOutCubic),
      );
    }).toList();

    // أنيميشن الارتداد المستمر
    _bounceAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 1.0, end: 1.1).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    // أنيميشن اللمعان
    _shimmerAnimations = _controllers.map((controller) {
      return Tween<double>(begin: 0.0, end: 1.0).animate(
        CurvedAnimation(parent: controller, curve: Curves.easeInOut),
      );
    }).toList();

    // بدء الأنيميشن فوراً وبسرعة
    _startFastSequentialAnimation();
  }

  void _startFastSequentialAnimation() async {
    // بدء أنيميشن الحاوية أولاً
    _containerController.forward();

    // بدء التفاعلات بسرعة فائقة مثل Facebook
    for (int i = 0; i < _controllers.length; i++) {
      _controllers[i].forward();
      // تأخير قصير جداً بين الأيقونات لتأثير متتالي سريع
      await Future.delayed(const Duration(milliseconds: 25));
    }
  }



  @override
  void dispose() {
    _containerController.dispose();
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: widget.reactions.asMap().entries.map((entry) {
            final index = entry.key;
            final reaction = entry.value;

            return AnimatedBuilder(
              animation: _controllers[index],
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimations[index].value,
                  child: SlideTransition(
                    position: _slideAnimations[index],
                    child: _buildShimmeringReaction(reaction, index),
                  ),
                );
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildShimmeringReaction(ReactionType reaction, int index) {
    return GestureDetector(
      onTap: () => widget.onReactionSelected(reaction),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: 2),
        child: Stack(
          children: [
            // الأيقونة الأساسية مع تأثير لامع
            Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  colors: [
                    Colors.white.withOpacity(0.9),
                    Colors.white.withOpacity(0.6),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Image.asset(
                reaction.assetPath,
                width: 28,
                height: 28,
                fit: BoxFit.contain,
              ),
            ),
            // تأثير اللمعان المتحرك
            AnimatedBuilder(
              animation: _shimmerAnimations[index],
              builder: (context, child) {
                return Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(20),
                      gradient: LinearGradient(
                        colors: [
                          Colors.white.withOpacity(0.0),
                          Colors.white.withOpacity(0.7 * _shimmerAnimations[index].value),
                          Colors.white.withOpacity(0.0),
                        ],
                        stops: const [0.0, 0.5, 1.0],
                        begin: Alignment(-1.0 + 2.0 * _shimmerAnimations[index].value, -1.0),
                        end: Alignment(1.0 + 2.0 * _shimmerAnimations[index].value, 1.0),
                      ),
                    ),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}