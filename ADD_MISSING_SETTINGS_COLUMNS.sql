-- إضافة الأعمدة المفقودة في جدول user_settings
-- قم بتنفيذ هذه الأوامر في Supabase SQL Editor

-- إضافة عمود comment_permission إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'comment_permission') THEN
        ALTER TABLE user_settings ADD COLUMN comment_permission TEXT DEFAULT 'everyone';
    END IF;
END $$;

-- إضافة عمود post_visibility إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'post_visibility') THEN
        ALTER TABLE user_settings ADD COLUMN post_visibility TEXT DEFAULT 'public';
    END IF;
END $$;

-- إضافة عمود notify_follow إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'notify_follow') THEN
        ALTER TABLE user_settings ADD COLUMN notify_follow BOOLEAN DEFAULT true;
    END IF;
END $$;

-- إضافة عمود notify_chat إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'notify_chat') THEN
        ALTER TABLE user_settings ADD COLUMN notify_chat BOOLEAN DEFAULT true;
    END IF;
END $$;

-- إضافة عمود notify_app إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'notify_app') THEN
        ALTER TABLE user_settings ADD COLUMN notify_app BOOLEAN DEFAULT true;
    END IF;
END $$;

-- إضافة عمود show_activity إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'show_activity') THEN
        ALTER TABLE user_settings ADD COLUMN show_activity BOOLEAN DEFAULT true;
    END IF;
END $$;

-- التحقق من جميع الأعمدة المطلوبة
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'user_settings' 
ORDER BY column_name; 