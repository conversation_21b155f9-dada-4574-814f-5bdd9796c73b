import 'package:flutter/material.dart';
import 'package:file_picker/file_picker.dart';
import 'package:record/record.dart';
import 'package:audioplayers/audioplayers.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../models/podcast.dart';
import '../services/podcast_service.dart';

class CreatePodcastPage extends StatefulWidget {
  const CreatePodcastPage({super.key});

  @override
  State<CreatePodcastPage> createState() => _CreatePodcastPageState();
}

class _CreatePodcastPageState extends State<CreatePodcastPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _tagsController = TextEditingController();
  
  final PodcastService _podcastService = PodcastService();
  final AudioRecorder _recorder = AudioRecorder();
  final AudioPlayer _player = AudioPlayer();
  final ImagePicker _imagePicker = ImagePicker();
  
  PodcastCategory _selectedCategory = PodcastCategory.other;
  File? _audioFile;
  File? _coverImage;
  bool _isRecording = false;
  bool _isPlaying = false;
  bool _allowDownload = true;
  bool _allowComments = true;
  bool _isUploading = false;
  Duration _recordingDuration = Duration.zero;
  Duration _audioDuration = Duration.zero;
  String? _recordingPath;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _tagsController.dispose();
    _recorder.dispose();
    _player.dispose();
    super.dispose();
  }

  Future<void> _startRecording() async {
    try {
      if (await _recorder.hasPermission()) {
        final path = '${Directory.systemTemp.path}/recording_${DateTime.now().millisecondsSinceEpoch}.m4a';
        await _recorder.start(const RecordConfig(), path: path);
        
        setState(() {
          _isRecording = true;
          _recordingPath = path;
          _recordingDuration = Duration.zero;
        });
        
        // تحديث مدة التسجيل
        _updateRecordingDuration();
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل في بدء التسجيل: $e')),
      );
    }
  }

  Future<void> _stopRecording() async {
    try {
      final path = await _recorder.stop();
      if (path != null) {
        setState(() {
          _isRecording = false;
          _audioFile = File(path);
          _audioDuration = _recordingDuration;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل في إيقاف التسجيل: $e')),
      );
    }
  }

  void _updateRecordingDuration() {
    if (_isRecording) {
      Future.delayed(const Duration(seconds: 1), () {
        if (_isRecording) {
          setState(() {
            _recordingDuration = Duration(seconds: _recordingDuration.inSeconds + 1);
          });
          _updateRecordingDuration();
        }
      });
    }
  }

  Future<void> _pickAudioFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.audio,
        allowedExtensions: ['mp3', 'wav', 'ogg', 'm4a'],
      );

      if (result != null && result.files.single.path != null) {
        final file = File(result.files.single.path!);
        
        // تشغيل الملف لمعرفة مدته
        await _player.setSourceDeviceFile(file.path);
        final duration = await _player.getDuration();
        
        setState(() {
          _audioFile = file;
          _audioDuration = duration ?? Duration.zero;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل في اختيار الملف: $e')),
      );
    }
  }

  Future<void> _pickCoverImage() async {
    try {
      final image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 80,
      );

      if (image != null) {
        setState(() {
          _coverImage = File(image.path);
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل في اختيار الصورة: $e')),
      );
    }
  }

  Future<void> _playPauseAudio() async {
    try {
      if (_isPlaying) {
        await _player.pause();
        setState(() => _isPlaying = false);
      } else {
        if (_audioFile != null) {
          await _player.play(DeviceFileSource(_audioFile!.path));
          setState(() => _isPlaying = true);
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل في تشغيل الصوت: $e')),
      );
    }
  }

  Future<void> _createPodcast() async {
    if (!_formKey.currentState!.validate() || _audioFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى ملء جميع الحقول المطلوبة')),
      );
      return;
    }

    setState(() => _isUploading = true);

    try {
      await _podcastService.createPodcast(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty 
            ? null 
            : _descriptionController.text.trim(),
        audioFile: _audioFile!,
        coverImage: _coverImage,
        category: _selectedCategory,
        duration: _audioDuration,
        tags: _tagsController.text.trim().isEmpty 
            ? null 
            : _tagsController.text.trim(),
        allowDownload: _allowDownload,
        allowComments: _allowComments,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إنشاء البودكاست بنجاح!')),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل في إنشاء البودكاست: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isUploading = false);
      }
    }
  }

  String _formatDuration(Duration duration) {
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إنشاء بودكاست'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 1,
        actions: [
          if (_isUploading)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            TextButton(
              onPressed: _createPodcast,
              child: const Text(
                'نشر',
                style: TextStyle(
                  color: Colors.deepPurple,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // قسم التسجيل/رفع الملف
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'الملف الصوتي',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    
                    if (_audioFile == null) ...[
                      // أزرار التسجيل ورفع الملف
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: _isRecording ? _stopRecording : _startRecording,
                              icon: Icon(_isRecording ? Icons.stop : Icons.mic),
                              label: Text(_isRecording ? 'إيقاف التسجيل' : 'تسجيل صوتي'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: _isRecording ? Colors.red : Colors.deepPurple,
                                foregroundColor: Colors.white,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: OutlinedButton.icon(
                              onPressed: _pickAudioFile,
                              icon: const Icon(Icons.upload_file),
                              label: const Text('رفع ملف'),
                            ),
                          ),
                        ],
                      ),
                      
                      if (_isRecording) ...[
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.fiber_manual_record, color: Colors.red, size: 16),
                            const SizedBox(width: 8),
                            Text(
                              'جاري التسجيل... ${_formatDuration(_recordingDuration)}',
                              style: const TextStyle(
                                color: Colors.red,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ] else ...[
                      // معاينة الملف الصوتي
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            IconButton(
                              onPressed: _playPauseAudio,
                              icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow),
                              style: IconButton.styleFrom(
                                backgroundColor: Colors.deepPurple,
                                foregroundColor: Colors.white,
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'الملف الصوتي جاهز',
                                    style: TextStyle(fontWeight: FontWeight.bold),
                                  ),
                                  Text(
                                    'المدة: ${_formatDuration(_audioDuration)}',
                                    style: TextStyle(color: Colors.grey[600]),
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              onPressed: () {
                                setState(() {
                                  _audioFile = null;
                                  _audioDuration = Duration.zero;
                                });
                              },
                              icon: const Icon(Icons.delete),
                              style: IconButton.styleFrom(
                                foregroundColor: Colors.red,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // العنوان
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'عنوان البودكاست *',
                hintText: 'أدخل عنواناً جذاباً للبودكاست',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'العنوان مطلوب';
                }
                return null;
              },
              maxLength: 100,
            ),
            
            const SizedBox(height: 16),
            
            // الوصف
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'الوصف (اختياري)',
                hintText: 'اكتب وصفاً مختصراً عن محتوى البودكاست',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              maxLength: 500,
            ),
            
            const SizedBox(height: 16),
            
            // الفئة
            DropdownButtonFormField<PodcastCategory>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'الفئة',
                border: OutlineInputBorder(),
              ),
              items: PodcastCategory.values.map((category) {
                return DropdownMenuItem(
                  value: category,
                  child: Row(
                    children: [
                      Icon(category.icon, color: category.color, size: 20),
                      const SizedBox(width: 8),
                      Text(category.displayName),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() => _selectedCategory = value);
                }
              },
            ),
            
            const SizedBox(height: 16),
            
            // الكلمات المفتاحية
            TextFormField(
              controller: _tagsController,
              decoration: const InputDecoration(
                labelText: 'الكلمات المفتاحية (اختياري)',
                hintText: 'مثال: تطوير، تحفيز، قصة',
                border: OutlineInputBorder(),
              ),
              maxLength: 200,
            ),
            
            const SizedBox(height: 16),
            
            // صورة الغلاف
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'صورة الغلاف (اختياري)',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    
                    if (_coverImage != null) ...[
                      Container(
                        height: 120,
                        width: 120,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          image: DecorationImage(
                            image: FileImage(_coverImage!),
                            fit: BoxFit.cover,
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                    ],
                    
                    OutlinedButton.icon(
                      onPressed: _pickCoverImage,
                      icon: const Icon(Icons.image),
                      label: Text(_coverImage != null ? 'تغيير الصورة' : 'اختيار صورة'),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // الإعدادات
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'إعدادات البودكاست',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    
                    SwitchListTile(
                      title: const Text('السماح بالتحميل'),
                      subtitle: const Text('يمكن للمستخدمين تحميل البودكاست'),
                      value: _allowDownload,
                      onChanged: (value) => setState(() => _allowDownload = value),
                      activeColor: Colors.deepPurple,
                    ),
                    
                    SwitchListTile(
                      title: const Text('السماح بالتعليقات'),
                      subtitle: const Text('يمكن للمستخدمين التعليق على البودكاست'),
                      value: _allowComments,
                      onChanged: (value) => setState(() => _allowComments = value),
                      activeColor: Colors.deepPurple,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
