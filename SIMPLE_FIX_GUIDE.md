# دليل الحل البسيط
# Simple Fix Guide

## المشكلة
بعد التعديلات الأخيرة، توقفت وظيفة رفع الوسائط عن العمل.

## الحل البسيط

### الخطوة 1: تنفيذ SQL البسيط
1. اذهب إلى Supabase Dashboard
2. افتح SQL Editor
3. انسخ والصق محتوى ملف `SIMPLE_MEDIA_BUCKET.sql`
4. اضغط Run

### الخطوة 2: التحقق من النتائج
بعد تنفيذ SQL، يجب أن ترى:
```
id   | name  | public | file_size_limit
-----|-------|--------|----------------
media| media | true   | 52428800
```

وفي النهاية:
```
result
--------------------------------
تم إنشاء bucket media بنجاح!
```

### الخطوة 3: اختبار التطبيق
1. افتح التطبيق الجديد
2. جرب إنشاء منشور مع صور متعددة
3. يجب أن تعمل الآن

## ما تم إصلاحه

### 1. إعادة الكود إلى الحالة الأصلية
- أزلت التحقق المعقد من buckets
- أعدت الكود إلى الحالة البسيطة الأصلية
- حافظت على رسائل debug الأساسية

### 2. إنشاء bucket media فقط
- bucket واحد بسيط للوسائط
- سياسات أمان أساسية
- دعم جميع أنواع الصور والفيديوهات

## رسائل DEBUG المتوقعة

عندما تعمل بشكل صحيح، يجب أن ترى:
```
DEBUG: uploadMedia called with path: post_1234567890_0.jpg, size: 123456 bytes
DEBUG: File uploaded successfully. URL: https://...
```

## إذا استمرت المشكلة

### 1. تحقق من رسائل الخطأ
انظر إلى رسائل الخطأ في console لمعرفة المشكلة بالضبط.

### 2. تحقق من Supabase Dashboard
- اذهب إلى Storage
- تأكد من وجود bucket باسم `media`
- اذهب إلى Authentication > Policies
- تأكد من وجود سياسات `media_*_policy`

### 3. إذا لم توجد أي buckets
نفذ هذا SQL بسيط جداً:
```sql
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES ('media', 'media', true, 52428800, ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/mp4'])
ON CONFLICT (id) DO NOTHING;
```

## التحقق النهائي

بعد تنفيذ جميع الخطوات، جرب:

1. **إنشاء منشور نصي** - يجب أن يعمل
2. **إنشاء منشور بصورة واحدة** - يجب أن يعمل
3. **إنشاء منشور بصور متعددة** - يجب أن يعمل

إذا عملت جميع هذه الاختبارات، فالمشكلة محلولة!

## الدعم

إذا استمرت المشكلة:
1. انسخ رسائل الخطأ من التطبيق
2. انسخ نتائج SQL من Supabase
3. أرسل جميع المعلومات للمساعدة في التشخيص

**الآن نفذ `SIMPLE_MEDIA_BUCKET.sql` واختبر التطبيق! 🚀** 