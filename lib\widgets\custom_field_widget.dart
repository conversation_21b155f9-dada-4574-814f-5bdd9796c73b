import 'package:flutter/material.dart';
import '../models/product_category.dart';

class CustomFieldWidget extends StatefulWidget {
  final CustomField field;
  final dynamic value;
  final Function(dynamic) onChanged;

  const CustomFieldWidget({
    super.key,
    required this.field,
    this.value,
    required this.onChanged,
  });

  @override
  State<CustomFieldWidget> createState() => _CustomFieldWidgetState();
}

class _CustomFieldWidgetState extends State<CustomFieldWidget> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(
      text: widget.value?.toString() ?? widget.field.defaultValue ?? '',
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    switch (widget.field.type) {
      case CustomFieldType.text:
        return _buildTextField();
      case CustomFieldType.textarea:
        return _buildTextAreaField();
      case CustomFieldType.dropdown:
        return _buildDropdownField();
      case CustomFieldType.boolean:
        return _buildBooleanField();
      case CustomFieldType.number:
        return _buildNumberField();
      case CustomFieldType.date:
        return _buildDateField();
    }
  }

  Widget _buildTextField() {
    return TextFormField(
      controller: _controller,
      decoration: InputDecoration(
        labelText: widget.field.label + (widget.field.required ? ' *' : ''),
        hintText: widget.field.placeholder,
        border: const OutlineInputBorder(),
      ),
      onChanged: (value) => widget.onChanged(value.isEmpty ? null : value),
      validator: widget.field.required
          ? (value) {
              if (value == null || value.trim().isEmpty) {
                return '${widget.field.label} مطلوب';
              }
              return null;
            }
          : null,
    );
  }

  Widget _buildTextAreaField() {
    return TextFormField(
      controller: _controller,
      maxLines: 3,
      decoration: InputDecoration(
        labelText: widget.field.label + (widget.field.required ? ' *' : ''),
        hintText: widget.field.placeholder,
        border: const OutlineInputBorder(),
        alignLabelWithHint: true,
      ),
      onChanged: (value) => widget.onChanged(value.isEmpty ? null : value),
      validator: widget.field.required
          ? (value) {
              if (value == null || value.trim().isEmpty) {
                return '${widget.field.label} مطلوب';
              }
              return null;
            }
          : null,
    );
  }

  Widget _buildDropdownField() {
    return DropdownButtonFormField<String>(
      value: widget.value?.toString(),
      decoration: InputDecoration(
        labelText: widget.field.label + (widget.field.required ? ' *' : ''),
        border: const OutlineInputBorder(),
      ),
      items: [
        if (!widget.field.required)
          const DropdownMenuItem<String>(
            value: null,
            child: Text('اختر...'),
          ),
        ...widget.field.options!.map((option) {
          return DropdownMenuItem<String>(
            value: option,
            child: Text(option),
          );
        }),
      ],
      onChanged: (value) => widget.onChanged(value),
      validator: widget.field.required
          ? (value) {
              if (value == null || value.isEmpty) {
                return '${widget.field.label} مطلوب';
              }
              return null;
            }
          : null,
    );
  }

  Widget _buildBooleanField() {
    return FormField<bool>(
      initialValue: widget.value ?? false,
      builder: (field) {
        return CheckboxListTile(
          title: Text(widget.field.label),
          value: field.value ?? false,
          onChanged: (value) {
            field.didChange(value);
            widget.onChanged(value);
          },
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
        );
      },
    );
  }

  Widget _buildNumberField() {
    return TextFormField(
      controller: _controller,
      keyboardType: TextInputType.number,
      decoration: InputDecoration(
        labelText: widget.field.label + (widget.field.required ? ' *' : ''),
        hintText: widget.field.placeholder,
        border: const OutlineInputBorder(),
      ),
      onChanged: (value) {
        final number = double.tryParse(value);
        widget.onChanged(number);
      },
      validator: widget.field.required
          ? (value) {
              if (value == null || value.trim().isEmpty) {
                return '${widget.field.label} مطلوب';
              }
              if (double.tryParse(value) == null) {
                return 'يجب أن يكون رقماً صحيحاً';
              }
              return null;
            }
          : (value) {
              if (value != null && value.isNotEmpty && double.tryParse(value) == null) {
                return 'يجب أن يكون رقماً صحيحاً';
              }
              return null;
            },
    );
  }

  Widget _buildDateField() {
    return FormField<DateTime>(
      initialValue: widget.value is String 
          ? DateTime.tryParse(widget.value) 
          : widget.value,
      builder: (field) {
        return InkWell(
          onTap: () async {
            final date = await showDatePicker(
              context: context,
              initialDate: field.value ?? DateTime.now(),
              firstDate: DateTime(1900),
              lastDate: DateTime(2100),
            );
            if (date != null) {
              field.didChange(date);
              widget.onChanged(date.toIso8601String());
            }
          },
          child: InputDecorator(
            decoration: InputDecoration(
              labelText: widget.field.label + (widget.field.required ? ' *' : ''),
              border: const OutlineInputBorder(),
              suffixIcon: const Icon(Icons.calendar_today),
            ),
            child: Text(
              field.value != null
                  ? _formatDate(field.value!)
                  : widget.field.placeholder ?? 'اختر التاريخ',
              style: TextStyle(
                color: field.value != null ? null : Colors.grey[600],
              ),
            ),
          ),
        );
      },
      validator: widget.field.required
          ? (value) {
              if (value == null) {
                return '${widget.field.label} مطلوب';
              }
              return null;
            }
          : null,
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

// ويدجت لعرض الحقول المخصصة في وضع القراءة فقط
class CustomFieldDisplay extends StatelessWidget {
  final String label;
  final dynamic value;
  final CustomFieldType type;

  const CustomFieldDisplay({
    super.key,
    required this.label,
    required this.value,
    required this.type,
  });

  @override
  Widget build(BuildContext context) {
    if (value == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              _formatValue(),
              style: const TextStyle(
                fontWeight: FontWeight.w400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatValue() {
    switch (type) {
      case CustomFieldType.boolean:
        return value == true ? 'نعم' : 'لا';
      case CustomFieldType.date:
        if (value is String) {
          final date = DateTime.tryParse(value);
          if (date != null) {
            return '${date.day}/${date.month}/${date.year}';
          }
        }
        return value.toString();
      default:
        return value.toString();
    }
  }
}

// ويدجت لعرض جميع الحقول المخصصة
class CustomFieldsDisplay extends StatelessWidget {
  final Map<String, dynamic> customFields;
  final String categoryNameEn;

  const CustomFieldsDisplay({
    super.key,
    required this.customFields,
    required this.categoryNameEn,
  });

  @override
  Widget build(BuildContext context) {
    if (customFields.isEmpty) return const SizedBox.shrink();

    final categoryFields = CategoryCustomFields.getCategoryFields(categoryNameEn);
    final basicFields = categoryFields['basic'] ?? [];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل إضافية',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...basicFields.where((field) => customFields.containsKey(field.key)).map(
              (field) => CustomFieldDisplay(
                label: field.label,
                value: customFields[field.key],
                type: field.type,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// ويدجت للبحث في الحقول المخصصة
class CustomFieldsFilter extends StatefulWidget {
  final String categoryNameEn;
  final Map<String, dynamic> initialValues;
  final Function(Map<String, dynamic>) onChanged;

  const CustomFieldsFilter({
    super.key,
    required this.categoryNameEn,
    this.initialValues = const {},
    required this.onChanged,
  });

  @override
  State<CustomFieldsFilter> createState() => _CustomFieldsFilterState();
}

class _CustomFieldsFilterState extends State<CustomFieldsFilter> {
  late Map<String, dynamic> _values;

  @override
  void initState() {
    super.initState();
    _values = Map.from(widget.initialValues);
  }

  @override
  Widget build(BuildContext context) {
    final categoryFields = CategoryCustomFields.getCategoryFields(widget.categoryNameEn);
    final basicFields = categoryFields['basic'] ?? [];

    if (basicFields.isEmpty) return const SizedBox.shrink();

    return ExpansionTile(
      title: const Text('فلاتر إضافية'),
      children: basicFields.map((field) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: CustomFieldWidget(
            field: CustomField(
              key: field.key,
              label: field.label,
              type: field.type,
              options: field.options,
              required: false, // الفلاتر ليست مطلوبة
            ),
            value: _values[field.key],
            onChanged: (value) {
              setState(() {
                if (value == null) {
                  _values.remove(field.key);
                } else {
                  _values[field.key] = value;
                }
              });
              widget.onChanged(_values);
            },
          ),
        );
      }).toList(),
    );
  }
}
