-- إعد<PERSON> جدول المستخدمين وقسم الوظائف

-- التحقق من وجود جدول المستخدمين وإضافة الأعمدة المفقودة
DO $$
BEGIN
    -- إضا<PERSON>ة عمود is_private إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'profiles' AND column_name = 'is_private') THEN
        ALTER TABLE profiles ADD COLUMN is_private BOOLEAN DEFAULT FALSE;
    END IF;

    -- إضافة عمود is_verified إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'profiles' AND column_name = 'is_verified') THEN
        ALTER TABLE profiles ADD COLUMN is_verified BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول المساحات إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS spaces (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    category TEXT,
    owner_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
    avatar_url TEXT,
    cover_url TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    followers_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول الوظائف
CREATE TABLE IF NOT EXISTS jobs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    company_name TEXT NOT NULL,
    job_title TEXT NOT NULL,
    job_type TEXT NOT NULL CHECK (job_type IN ('fullTime', 'partTime', 'freelance', 'remote', 'contract', 'internship')),
    salary TEXT,
    location TEXT NOT NULL,
    is_remote BOOLEAN DEFAULT FALSE,
    description TEXT NOT NULL,
    required_skills TEXT[] DEFAULT '{}',
    category TEXT NOT NULL CHECK (category IN ('technology', 'marketing', 'education', 'construction', 'restaurant', 'healthcare', 'finance', 'design', 'sales', 'customerService', 'other')),
    application_link TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    publisher_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    space_id UUID REFERENCES spaces(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    applications_count INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT FALSE
);

-- إنشاء جدول طلبات التقديم
CREATE TABLE IF NOT EXISTS job_applications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    applicant_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    resume_url TEXT,
    cover_letter TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'interviewed')),
    UNIQUE(job_id, applicant_id)
);

-- إنشاء جدول الوظائف المحفوظة
CREATE TABLE IF NOT EXISTS saved_jobs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, job_id)
);

-- إنشاء جدول الإبلاغ عن الوظائف
CREATE TABLE IF NOT EXISTS job_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    reporter_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    reason TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_profiles_username ON profiles(username);
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);

CREATE INDEX IF NOT EXISTS idx_jobs_publisher_id ON jobs(publisher_id);
CREATE INDEX IF NOT EXISTS idx_jobs_category ON jobs(category);
CREATE INDEX IF NOT EXISTS idx_jobs_job_type ON jobs(job_type);
CREATE INDEX IF NOT EXISTS idx_jobs_location ON jobs(location);
CREATE INDEX IF NOT EXISTS idx_jobs_is_active ON jobs(is_active);
CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON jobs(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_job_applications_job_id ON job_applications(job_id);
CREATE INDEX IF NOT EXISTS idx_job_applications_applicant_id ON job_applications(applicant_id);

CREATE INDEX IF NOT EXISTS idx_saved_jobs_user_id ON saved_jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_saved_jobs_job_id ON saved_jobs(job_id);

-- تفعيل Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE spaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE saved_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_reports ENABLE ROW LEVEL SECURITY;

-- حذف السياسات الموجودة إذا كانت موجودة
DROP POLICY IF EXISTS "الجميع يمكنهم قراءة الملفات الشخصية العامة" ON profiles;
DROP POLICY IF EXISTS "المستخدمون يمكنهم تحديث ملفاتهم الشخصية" ON profiles;
DROP POLICY IF EXISTS "المستخدمون يمكنهم إدراج ملفاتهم الشخصية" ON profiles;

DROP POLICY IF EXISTS "الجميع يمكنهم قراءة المساحات العامة" ON spaces;
DROP POLICY IF EXISTS "أصحاب المساحات يمكنهم تحديثها" ON spaces;
DROP POLICY IF EXISTS "المستخدمون يمكنهم إنشاء مساحات" ON spaces;

DROP POLICY IF EXISTS "الجميع يمكنهم قراءة الوظائف النشطة" ON jobs;
DROP POLICY IF EXISTS "المستخدمون المسجلون يمكنهم إنشاء وظائف" ON jobs;
DROP POLICY IF EXISTS "الناشر يمكنه تحديث وظائفه" ON jobs;
DROP POLICY IF EXISTS "الناشر يمكنه حذف وظائفه" ON jobs;

-- سياسات الأمان للملفات الشخصية
CREATE POLICY "الجميع يمكنهم قراءة الملفات الشخصية العامة" ON profiles
    FOR SELECT USING (
        CASE
            WHEN EXISTS (SELECT 1 FROM information_schema.columns
                        WHERE table_name = 'profiles' AND column_name = 'is_private')
            THEN (NOT is_private OR auth.uid() = id)
            ELSE true
        END
    );

CREATE POLICY "المستخدمون يمكنهم تحديث ملفاتهم الشخصية" ON profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "المستخدمون يمكنهم إدراج ملفاتهم الشخصية" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- سياسات الأمان للمساحات
CREATE POLICY "الجميع يمكنهم قراءة المساحات العامة" ON spaces
    FOR SELECT USING (true);

CREATE POLICY "أصحاب المساحات يمكنهم تحديثها" ON spaces
    FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "المستخدمون يمكنهم إنشاء مساحات" ON spaces
    FOR INSERT WITH CHECK (auth.uid() = owner_id);

-- سياسات الأمان للوظائف
CREATE POLICY "الجميع يمكنهم قراءة الوظائف النشطة" ON jobs
    FOR SELECT USING (is_active = true);

CREATE POLICY "المستخدمون المسجلون يمكنهم إنشاء وظائف" ON jobs
    FOR INSERT WITH CHECK (auth.uid() = publisher_id);

CREATE POLICY "الناشر يمكنه تحديث وظائفه" ON jobs
    FOR UPDATE USING (auth.uid() = publisher_id);

CREATE POLICY "الناشر يمكنه حذف وظائفه" ON jobs
    FOR DELETE USING (auth.uid() = publisher_id);

-- سياسات الأمان لطلبات التقديم
CREATE POLICY "المتقدم يمكنه قراءة طلباته" ON job_applications
    FOR SELECT USING (auth.uid() = applicant_id);

CREATE POLICY "صاحب الوظيفة يمكنه قراءة طلبات وظائفه" ON job_applications
    FOR SELECT USING (
        auth.uid() IN (
            SELECT publisher_id FROM jobs WHERE id = job_applications.job_id
        )
    );

CREATE POLICY "المستخدمون المسجلون يمكنهم التقديم" ON job_applications
    FOR INSERT WITH CHECK (auth.uid() = applicant_id);

CREATE POLICY "المتقدم يمكنه تحديث طلبه" ON job_applications
    FOR UPDATE USING (auth.uid() = applicant_id);

CREATE POLICY "صاحب الوظيفة يمكنه تحديث حالة الطلبات" ON job_applications
    FOR UPDATE USING (
        auth.uid() IN (
            SELECT publisher_id FROM jobs WHERE id = job_applications.job_id
        )
    );

CREATE POLICY "المتقدم يمكنه حذف طلبه" ON job_applications
    FOR DELETE USING (auth.uid() = applicant_id);

-- سياسات الأمان للوظائف المحفوظة
CREATE POLICY "المستخدم يمكنه قراءة وظائفه المحفوظة" ON saved_jobs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه حفظ الوظائف" ON saved_jobs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه إلغاء حفظ الوظائف" ON saved_jobs
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان للإبلاغات
CREATE POLICY "المستخدم يمكنه قراءة إبلاغاته" ON job_reports
    FOR SELECT USING (auth.uid() = reporter_id);

CREATE POLICY "المستخدمون المسجلون يمكنهم الإبلاغ" ON job_reports
    FOR INSERT WITH CHECK (auth.uid() = reporter_id);

-- دالة لزيادة عدد المتقدمين
CREATE OR REPLACE FUNCTION increment_applications_count(job_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE jobs 
    SET applications_count = applications_count + 1 
    WHERE id = job_id;
END;
$$ LANGUAGE plpgsql;

-- دالة لتقليل عدد المتقدمين
CREATE OR REPLACE FUNCTION decrement_applications_count(job_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE jobs 
    SET applications_count = GREATEST(applications_count - 1, 0) 
    WHERE id = job_id;
END;
$$ LANGUAGE plpgsql;

-- تريجر لزيادة عدد المتقدمين عند إضافة طلب جديد
CREATE OR REPLACE FUNCTION trigger_increment_applications()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM increment_applications_count(NEW.job_id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_job_application_insert ON job_applications;
CREATE TRIGGER trigger_job_application_insert
    AFTER INSERT ON job_applications
    FOR EACH ROW
    EXECUTE FUNCTION trigger_increment_applications();

-- تريجر لتقليل عدد المتقدمين عند حذف طلب
CREATE OR REPLACE FUNCTION trigger_decrement_applications()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM decrement_applications_count(OLD.job_id);
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_job_application_delete ON job_applications;
CREATE TRIGGER trigger_job_application_delete
    AFTER DELETE ON job_applications
    FOR EACH ROW
    EXECUTE FUNCTION trigger_decrement_applications();

-- دالة لإنشاء ملف شخصي تلقائياً عند التسجيل
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, username, full_name, email)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', 'user_' || substr(NEW.id::text, 1, 8)),
        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
        NEW.email
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- تريجر لإنشاء ملف شخصي عند التسجيل
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- منح الصلاحيات
GRANT ALL ON profiles TO authenticated;
GRANT ALL ON spaces TO authenticated;
GRANT ALL ON jobs TO authenticated;
GRANT ALL ON job_applications TO authenticated;
GRANT ALL ON saved_jobs TO authenticated;
GRANT ALL ON job_reports TO authenticated;

GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON FUNCTION increment_applications_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION decrement_applications_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION handle_new_user() TO authenticated;
