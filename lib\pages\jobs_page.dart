import 'package:flutter/material.dart';
import '../models/job.dart';
import '../services/jobs_service.dart';
import '../widgets/job_card.dart';
import 'add_job_page.dart';
import 'job_details_page.dart';

class JobsPage extends StatefulWidget {
  const JobsPage({super.key});

  @override
  State<JobsPage> createState() => _JobsPageState();
}

class _JobsPageState extends State<JobsPage> with TickerProviderStateMixin {
  late TabController _tabController;
  List<Job> _jobs = [];
  List<Job> _savedJobs = [];
  List<Job> _myJobs = [];
  bool _loading = true;
  String _selectedCategory = 'all';
  String _selectedJobType = 'all';
  String _selectedLocation = 'all';
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadJobs();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadJobs() async {
    setState(() => _loading = true);
    try {
      final jobs = await JobsService().getAllJobs();
      final savedJobs = await JobsService().getSavedJobs();
      final myJobs = await JobsService().getMyJobs();
      
      setState(() {
        _jobs = jobs;
        _savedJobs = savedJobs;
        _myJobs = myJobs;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تحتاج إلى إنشاء جداول قاعدة البيانات أولاً'),
            backgroundColor: Colors.orange,
            action: SnackBarAction(
              label: 'معرفة المزيد',
              textColor: Colors.white,
              onPressed: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('إعداد قاعدة البيانات'),
                    content: const Text(
                      'يبدو أن جداول الوظائف غير موجودة في قاعدة البيانات.\n\n'
                      'يرجى تشغيل ملف create_jobs_tables.sql في قاعدة البيانات أولاً.'
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: const Text('حسناً'),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      }
    }
  }

  List<Job> get _filteredJobs {
    List<Job> filtered = _jobs;

    // فلترة حسب البحث
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((job) =>
        job.title.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        job.companyName.toLowerCase().contains(_searchQuery.toLowerCase()) ||
        job.jobTitle.toLowerCase().contains(_searchQuery.toLowerCase())
      ).toList();
    }

    // فلترة حسب الفئة
    if (_selectedCategory != 'all') {
      filtered = filtered.where((job) => job.category.name == _selectedCategory).toList();
    }

    // فلترة حسب نوع العمل
    if (_selectedJobType != 'all') {
      filtered = filtered.where((job) => job.jobType.name == _selectedJobType).toList();
    }

    // فلترة حسب الموقع
    if (_selectedLocation != 'all') {
      if (_selectedLocation == 'remote') {
        filtered = filtered.where((job) => job.isRemote).toList();
      } else {
        filtered = filtered.where((job) => 
          job.location.toLowerCase().contains(_selectedLocation.toLowerCase())
        ).toList();
      }
    }

    return filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('الوظائف'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.blue,
          unselectedLabelColor: Colors.grey[600],
          indicatorColor: Colors.blue,
          tabs: const [
            Tab(text: 'جميع الوظائف'),
            Tab(text: 'المحفوظات'),
            Tab(text: 'وظائفي'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: _showSearchDialog,
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          // رسالة ترحيبية
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue[600]!, Colors.blue[800]!],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.work, color: Colors.white, size: 24),
                    const SizedBox(width: 8),
                    Text(
                      'مرحباً بك في قسم الوظائف',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'ابحث عن الوظيفة المناسبة أو انشر وظيفة جديدة',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          // التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllJobsTab(),
                _buildSavedJobsTab(),
                _buildMyJobsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          final result = await Navigator.push(
            context,
            MaterialPageRoute(builder: (_) => const AddJobPage()),
          );
          if (result == true) {
            _loadJobs();
          }
        },
        backgroundColor: Colors.blue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildAllJobsTab() {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    final filteredJobs = _filteredJobs;

    if (filteredJobs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.work_outline, size: 64, color: Colors.blue[600]),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد وظائف متاحة حالياً',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'كن أول من ينشر وظيفة في المجتمع!',
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const AddJobPage()),
                );
                if (result == true) {
                  _loadJobs();
                }
              },
              icon: const Icon(Icons.add),
              label: const Text('نشر وظيفة جديدة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadJobs,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredJobs.length,
        itemBuilder: (context, index) {
          final job = filteredJobs[index];
          return JobCard(
            job: job,
            onTap: () => _openJobDetails(job),
            onSave: () => _toggleSaveJob(job),
            isSaved: _savedJobs.any((j) => j.id == job.id),
          );
        },
      ),
    );
  }

  Widget _buildSavedJobsTab() {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_savedJobs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.bookmark_border, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لا توجد وظائف محفوظة',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'احفظ الوظائف المهمة لمراجعتها لاحقاً',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadJobs,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _savedJobs.length,
        itemBuilder: (context, index) {
          final job = _savedJobs[index];
          return JobCard(
            job: job,
            onTap: () => _openJobDetails(job),
            onSave: () => _toggleSaveJob(job),
            isSaved: true,
          );
        },
      ),
    );
  }

  Widget _buildMyJobsTab() {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_myJobs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.work_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لم تنشر أي وظائف بعد',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'انشر وظيفة جديدة للبدء',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () async {
                final result = await Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => const AddJobPage()),
                );
                if (result == true) {
                  _loadJobs();
                }
              },
              icon: const Icon(Icons.add),
              label: const Text('نشر وظيفة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadJobs,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _myJobs.length,
        itemBuilder: (context, index) {
          final job = _myJobs[index];
          return JobCard(
            job: job,
            onTap: () => _openJobDetails(job),
            isOwner: true,
            onEdit: () => _editJob(job),
            onDelete: () => _deleteJob(job),
          );
        },
      ),
    );
  }

  void _openJobDetails(Job job) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => JobDetailsPage(job: job),
      ),
    );
  }

  Future<void> _toggleSaveJob(Job job) async {
    try {
      final isSaved = _savedJobs.any((j) => j.id == job.id);
      if (isSaved) {
        await JobsService().unsaveJob(job.id);
        setState(() {
          _savedJobs.removeWhere((j) => j.id == job.id);
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إلغاء حفظ الوظيفة')),
          );
        }
      } else {
        await JobsService().saveJob(job.id);
        setState(() {
          _savedJobs.add(job);
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حفظ الوظيفة')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: $e')),
        );
      }
    }
  }

  void _editJob(Job job) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => AddJobPage(job: job),
      ),
    );
    if (result == true) {
      _loadJobs();
    }
  }

  Future<void> _deleteJob(Job job) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الوظيفة'),
        content: const Text('هل أنت متأكد من حذف هذه الوظيفة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await JobsService().deleteJob(job.id);
        setState(() {
          _myJobs.removeWhere((j) => j.id == job.id);
          _jobs.removeWhere((j) => j.id == job.id);
        });
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حذف الوظيفة')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في حذف الوظيفة: $e')),
          );
        }
      }
    }
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('البحث في الوظائف'),
        content: TextField(
          onChanged: (value) => setState(() => _searchQuery = value),
          decoration: const InputDecoration(
            hintText: 'ابحث عن وظيفة...',
            prefixIcon: Icon(Icons.search),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() => _searchQuery = '');
              Navigator.pop(context);
            },
            child: const Text('مسح'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('بحث'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('فلترة الوظائف'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // فلترة الفئة
            DropdownButtonFormField<String>(
              value: _selectedCategory,
              decoration: const InputDecoration(labelText: 'الفئة'),
              items: [
                const DropdownMenuItem(value: 'all', child: Text('جميع الفئات')),
                ...JobCategory.values.map((category) => DropdownMenuItem(
                  value: category.name,
                  child: Text(_getCategoryText(category)),
                )),
              ],
              onChanged: (value) => setState(() => _selectedCategory = value!),
            ),
            const SizedBox(height: 16),
            // فلترة نوع العمل
            DropdownButtonFormField<String>(
              value: _selectedJobType,
              decoration: const InputDecoration(labelText: 'نوع العمل'),
              items: [
                const DropdownMenuItem(value: 'all', child: Text('جميع الأنواع')),
                ...JobType.values.map((type) => DropdownMenuItem(
                  value: type.name,
                  child: Text(_getJobTypeText(type)),
                )),
              ],
              onChanged: (value) => setState(() => _selectedJobType = value!),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              setState(() {
                _selectedCategory = 'all';
                _selectedJobType = 'all';
                _selectedLocation = 'all';
              });
              Navigator.pop(context);
            },
            child: const Text('مسح الفلاتر'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('تطبيق'),
          ),
        ],
      ),
    );
  }

  String _getCategoryText(JobCategory category) {
    switch (category) {
      case JobCategory.technology: return 'تكنولوجيا';
      case JobCategory.marketing: return 'تسويق';
      case JobCategory.education: return 'تعليم';
      case JobCategory.construction: return 'بناء';
      case JobCategory.restaurant: return 'مطاعم';
      case JobCategory.healthcare: return 'صحة';
      case JobCategory.finance: return 'مالية';
      case JobCategory.design: return 'تصميم';
      case JobCategory.sales: return 'مبيعات';
      case JobCategory.customerService: return 'خدمة عملاء';
      case JobCategory.other: return 'أخرى';
    }
  }

  String _getJobTypeText(JobType type) {
    switch (type) {
      case JobType.fullTime: return 'دوام كامل';
      case JobType.partTime: return 'دوام جزئي';
      case JobType.freelance: return 'عمل حر';
      case JobType.remote: return 'عن بعد';
      case JobType.contract: return 'عقد مؤقت';
      case JobType.internship: return 'تدريب';
    }
  }
}
