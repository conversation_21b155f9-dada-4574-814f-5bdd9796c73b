-- إنشاء جدول user_settings كاملاً مع جميع الأعمدة المطلوبة
-- قم بتنفيذ هذه الأوامر في Supabase SQL Editor

-- إنشاء جدول user_settings إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    notify_follow BOOLEAN DEFAULT true,
    notify_chat BOOLEAN DEFAULT true,
    notify_app BOOLEAN DEFAULT true,
    post_visibility TEXT DEFAULT 'public',
    comment_permission TEXT DEFAULT 'everyone',
    show_activity BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- إضافة الأعمدة المفقودة إذا كان الجدول موجوداً
DO $$ 
BEGIN
    -- إضا<PERSON>ة عمود notify_follow إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'notify_follow') THEN
        ALTER TABLE user_settings ADD COLUMN notify_follow BOOLEAN DEFAULT true;
    END IF;
    
    -- إضافة عمود notify_chat إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'notify_chat') THEN
        ALTER TABLE user_settings ADD COLUMN notify_chat BOOLEAN DEFAULT true;
    END IF;
    
    -- إضافة عمود notify_app إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'notify_app') THEN
        ALTER TABLE user_settings ADD COLUMN notify_app BOOLEAN DEFAULT true;
    END IF;
    
    -- إضافة عمود post_visibility إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'post_visibility') THEN
        ALTER TABLE user_settings ADD COLUMN post_visibility TEXT DEFAULT 'public';
    END IF;
    
    -- إضافة عمود comment_permission إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'comment_permission') THEN
        ALTER TABLE user_settings ADD COLUMN comment_permission TEXT DEFAULT 'everyone';
    END IF;
    
    -- إضافة عمود show_activity إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'show_activity') THEN
        ALTER TABLE user_settings ADD COLUMN show_activity BOOLEAN DEFAULT true;
    END IF;
    
    -- إضافة عمود updated_at إذا لم يكن موجوداً
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_settings' AND column_name = 'updated_at') THEN
        ALTER TABLE user_settings ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
END $$;

-- إنشاء RLS (Row Level Security)
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;

-- إنشاء سياسة الأمان - المستخدم يمكنه رؤية وتعديل إعداداته فقط
CREATE POLICY "Users can view their own settings" ON user_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own settings" ON user_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own settings" ON user_settings
    FOR UPDATE USING (auth.uid() = user_id);

-- إنشاء trigger لتحديث updated_at
CREATE OR REPLACE FUNCTION update_user_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- إنشاء trigger إذا لم يكن موجوداً
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_user_settings_updated_at') THEN
        CREATE TRIGGER update_user_settings_updated_at 
            BEFORE UPDATE ON user_settings 
            FOR EACH ROW 
            EXECUTE FUNCTION update_user_settings_updated_at();
    END IF;
END $$;

-- إنشاء indexes لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_settings_updated_at ON user_settings(updated_at);

-- التحقق من إنشاء الجدول والأعمدة
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'user_settings' 
ORDER BY column_name; 