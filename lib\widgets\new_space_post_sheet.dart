import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'dart:typed_data';
import '../services/space_posts_service.dart';
import '../supabase_service.dart';

class NewSpacePostSheet extends StatefulWidget {
  final String spaceId;
  final String spaceName;
  final VoidCallback? onPostCreated;

  const NewSpacePostSheet({
    super.key,
    required this.spaceId,
    required this.spaceName,
    this.onPostCreated,
  });

  @override
  State<NewSpacePostSheet> createState() => _NewSpacePostSheetState();
}

class _NewSpacePostSheetState extends State<NewSpacePostSheet> {
  final _contentController = TextEditingController();
  final _linkController = TextEditingController();
  final _spacePostsService = SpacePostsService();
  final _imagePicker = ImagePicker();
  
  bool _isPosting = false;
  bool _showLinkField = false;
  List<XFile> _selectedImages = [];

  @override
  void dispose() {
    _contentController.dispose();
    _linkController.dispose();
    super.dispose();
  }

  Future<void> _pickImages() async {
    try {
      final images = await _imagePicker.pickMultiImage();
      if (images.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(images);
          // حد أقصى 4 صور
          if (_selectedImages.length > 4) {
            _selectedImages = _selectedImages.take(4).toList();
          }
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في اختيار الصور: $e')),
      );
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  Future<void> _createPost() async {
    if (_contentController.text.trim().isEmpty && _selectedImages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('يرجى كتابة محتوى أو إضافة صورة')),
      );
      return;
    }

    setState(() {
      _isPosting = true;
    });

    try {
      // رفع الصور إذا كانت موجودة
      List<String> mediaUrls = [];
      if (_selectedImages.isNotEmpty) {
        final supabaseService = SupabaseService();
        for (int i = 0; i < _selectedImages.length; i++) {
          final image = _selectedImages[i];
          final bytes = await image.readAsBytes();
          final fileName = 'space_posts/${widget.spaceId}/${DateTime.now().millisecondsSinceEpoch}_$i.jpg';
          final url = await supabaseService.uploadSpaceImage(bytes, fileName);
          mediaUrls.add(url);
        }
      }

      await _spacePostsService.createSpacePost(
        spaceId: widget.spaceId,
        content: _contentController.text.trim(),
        mediaUrls: mediaUrls,
        linkUrl: _linkController.text.trim().isEmpty ? null : _linkController.text.trim(),
      );

      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نشر المنشور بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
        
        if (widget.onPostCreated != null) {
          widget.onPostCreated!();
        }
      }
    } catch (e) {
      setState(() {
        _isPosting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في نشر المنشور: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildImagePreview() {
    if (_selectedImages.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الصور المختارة (${_selectedImages.length}/4)',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1,
            ),
            itemCount: _selectedImages.length,
            itemBuilder: (context, index) {
              return Stack(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image: FileImage(File(_selectedImages[index].path)),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 4,
                    right: 4,
                    child: GestureDetector(
                      onTap: () => _removeImage(index),
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.close,
                          size: 16,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // المقبض
          Container(
            margin: const EdgeInsets.only(top: 8),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // الهيدر
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'منشور جديد',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'في ${widget.spaceName}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                ElevatedButton(
                  onPressed: _isPosting ? null : _createPost,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue[600],
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  child: _isPosting
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('نشر'),
                ),
              ],
            ),
          ),

          const Divider(height: 1),

          // المحتوى
          Flexible(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // حقل النص
                  TextField(
                    controller: _contentController,
                    decoration: const InputDecoration(
                      hintText: 'ما الذي تريد مشاركته؟',
                      border: InputBorder.none,
                      hintStyle: TextStyle(fontSize: 18),
                    ),
                    style: const TextStyle(fontSize: 18),
                    maxLines: null,
                    minLines: 3,
                    autofocus: true,
                  ),

                  // معاينة الصور
                  _buildImagePreview(),

                  // حقل الرابط (إذا كان مفعلاً)
                  if (_showLinkField) ...[
                    const SizedBox(height: 16),
                    TextField(
                      controller: _linkController,
                      decoration: InputDecoration(
                        hintText: 'أدخل رابط...',
                        prefixIcon: const Icon(Icons.link),
                        suffixIcon: IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () {
                            setState(() {
                              _showLinkField = false;
                              _linkController.clear();
                            });
                          },
                        ),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      keyboardType: TextInputType.url,
                    ),
                  ],

                  const SizedBox(height: 16),

                  // أزرار الخيارات
                  Row(
                    children: [
                      // زر إضافة رابط
                      if (!_showLinkField)
                        IconButton(
                          onPressed: () {
                            setState(() {
                              _showLinkField = true;
                            });
                          },
                          icon: Icon(Icons.link, color: Colors.blue[600]),
                          tooltip: 'إضافة رابط',
                        ),

                      // زر إضافة صور
                      IconButton(
                        onPressed: _selectedImages.length < 4 ? _pickImages : null,
                        icon: Icon(
                          Icons.image,
                          color: _selectedImages.length < 4 ? Colors.blue[600] : Colors.grey[400],
                        ),
                        tooltip: 'إضافة صور (حتى 4)',
                      ),

                      // زر إضافة فيديو (مؤقتاً معطل)
                      IconButton(
                        onPressed: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('إضافة الفيديوهات قيد التطوير')),
                          );
                        },
                        icon: Icon(Icons.videocam, color: Colors.grey[400]),
                        tooltip: 'إضافة فيديو',
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          // مساحة آمنة في الأسفل
          SizedBox(height: MediaQuery.of(context).viewInsets.bottom),
        ],
      ),
    );
  }
}
