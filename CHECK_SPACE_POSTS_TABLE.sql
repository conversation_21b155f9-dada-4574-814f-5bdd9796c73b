-- التحقق من وجود جدول space_posts
SELECT EXISTS (
    SELECT FROM information_schema.tables 
    WHERE table_schema = 'public' 
    AND table_name = 'space_posts'
);

-- عرض هيكل جدول space_posts إذا كان موجوداً
SELECT column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_name = 'space_posts' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- عرض عدد المنشورات في كل مساحة
SELECT 
    s.name as space_name,
    s.id as space_id,
    COUNT(sp.id) as posts_count
FROM spaces s
LEFT JOIN space_posts sp ON s.id = sp.space_id
GROUP BY s.id, s.name
ORDER BY posts_count DESC;

-- عرض آخر 10 منشورات في المساحات
SELECT 
    sp.id,
    sp.content,
    sp.space_id,
    s.name as space_name,
    sp.created_at
FROM space_posts sp
JOIN spaces s ON sp.space_id = s.id
ORDER BY sp.created_at DESC
LIMIT 10; 