-- ===================================
-- إعداد قسم المذكرة
-- قاعدة بيانات شاملة للمذكرات والمهام والتذكيرات
-- ===================================

-- 1. جدول المذكرات الرئيسي
CREATE TABLE notes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    content TEXT NOT NULL,
    html_content TEXT,
    type TEXT NOT NULL CHECK (type IN ('note', 'task', 'reminder', 'journal')),
    category TEXT NOT NULL CHECK (category IN ('work', 'personal', 'goals', 'daily', 'study', 'health', 'finance', 'travel', 'other')),
    task_status TEXT CHECK (task_status IN ('pending', 'inProgress', 'completed', 'cancelled')),
    tags TEXT[] DEFAULT '{}',
    is_pinned BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    is_archived BOOLEAN DEFAULT FALSE,
    reminder_date TIMESTAMP WITH TIME ZONE,
    due_date TIMESTAMP WITH TIME ZONE,
    word_count INTEGER DEFAULT 0,
    character_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. جدول مشاركة المذكرات
CREATE TABLE note_shares (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    note_id UUID REFERENCES notes(id) ON DELETE CASCADE,
    shared_by UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    shared_with UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    permission TEXT NOT NULL CHECK (permission IN ('read', 'edit')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. جدول تعليقات المذكرات (للمذكرات العامة)
CREATE TABLE note_comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    note_id UUID REFERENCES notes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 4. جدول إعجابات المذكرات (للمذكرات العامة)
CREATE TABLE note_likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    note_id UUID REFERENCES notes(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(note_id, user_id)
);

-- 5. جدول إعدادات المذكرات للمستخدم
CREATE TABLE note_user_settings (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE UNIQUE,
    auto_save_enabled BOOLEAN DEFAULT TRUE,
    auto_save_interval INTEGER DEFAULT 30, -- بالثواني
    default_category TEXT DEFAULT 'personal',
    enable_notifications BOOLEAN DEFAULT TRUE,
    enable_reminders BOOLEAN DEFAULT TRUE,
    default_reminder_time INTEGER DEFAULT 60, -- بالدقائق قبل الموعد
    theme_preference TEXT DEFAULT 'light' CHECK (theme_preference IN ('light', 'dark', 'auto')),
    font_size INTEGER DEFAULT 14,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 6. تفعيل Row Level Security
ALTER TABLE notes ENABLE ROW LEVEL SECURITY;
ALTER TABLE note_shares ENABLE ROW LEVEL SECURITY;
ALTER TABLE note_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE note_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE note_user_settings ENABLE ROW LEVEL SECURITY;

-- 7. سياسات الأمان للمذكرات
-- المستخدمون يمكنهم رؤية مذكراتهم الخاصة والمذكرات العامة
CREATE POLICY "Users can view own notes and public notes" ON notes
    FOR SELECT USING (
        auth.uid() = user_id OR 
        (is_public = true AND is_archived = false)
    );

-- المستخدمون يمكنهم إنشاء مذكرات خاصة بهم
CREATE POLICY "Users can insert own notes" ON notes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- المستخدمون يمكنهم تحديث مذكراتهم الخاصة
CREATE POLICY "Users can update own notes" ON notes
    FOR UPDATE USING (auth.uid() = user_id);

-- المستخدمون يمكنهم حذف مذكراتهم الخاصة
CREATE POLICY "Users can delete own notes" ON notes
    FOR DELETE USING (auth.uid() = user_id);

-- 8. سياسات مشاركة المذكرات
CREATE POLICY "Users can view shares involving them" ON note_shares
    FOR SELECT USING (auth.uid() = shared_by OR auth.uid() = shared_with);

CREATE POLICY "Users can create shares for their notes" ON note_shares
    FOR INSERT WITH CHECK (
        auth.uid() = shared_by AND 
        EXISTS (SELECT 1 FROM notes WHERE id = note_id AND user_id = auth.uid())
    );

CREATE POLICY "Users can delete their shares" ON note_shares
    FOR DELETE USING (auth.uid() = shared_by);

-- 9. سياسات تعليقات المذكرات
CREATE POLICY "Users can view comments on public notes" ON note_comments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM notes 
            WHERE id = note_id AND (is_public = true OR user_id = auth.uid())
        )
    );

CREATE POLICY "Users can add comments on public notes" ON note_comments
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (SELECT 1 FROM notes WHERE id = note_id AND is_public = true)
    );

CREATE POLICY "Users can update own comments" ON note_comments
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own comments" ON note_comments
    FOR DELETE USING (auth.uid() = user_id);

-- 10. سياسات إعجابات المذكرات
CREATE POLICY "Users can view likes on public notes" ON note_likes
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM notes 
            WHERE id = note_id AND (is_public = true OR user_id = auth.uid())
        )
    );

CREATE POLICY "Users can like public notes" ON note_likes
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (SELECT 1 FROM notes WHERE id = note_id AND is_public = true)
    );

CREATE POLICY "Users can remove own likes" ON note_likes
    FOR DELETE USING (auth.uid() = user_id);

-- 11. سياسات إعدادات المستخدم
CREATE POLICY "Users can view own settings" ON note_user_settings
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own settings" ON note_user_settings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own settings" ON note_user_settings
    FOR UPDATE USING (auth.uid() = user_id);

-- 12. إنشاء الفهارس
CREATE INDEX idx_notes_user_id ON notes(user_id);
CREATE INDEX idx_notes_type ON notes(type);
CREATE INDEX idx_notes_category ON notes(category);
CREATE INDEX idx_notes_is_public ON notes(is_public);
CREATE INDEX idx_notes_is_archived ON notes(is_archived);
CREATE INDEX idx_notes_is_pinned ON notes(is_pinned);
CREATE INDEX idx_notes_created_at ON notes(created_at);
CREATE INDEX idx_notes_updated_at ON notes(updated_at);
CREATE INDEX idx_notes_reminder_date ON notes(reminder_date);
CREATE INDEX idx_notes_due_date ON notes(due_date);
CREATE INDEX idx_notes_tags ON notes USING GIN(tags);

CREATE INDEX idx_note_shares_note_id ON note_shares(note_id);
CREATE INDEX idx_note_shares_shared_with ON note_shares(shared_with);
CREATE INDEX idx_note_comments_note_id ON note_comments(note_id);
CREATE INDEX idx_note_likes_note_id ON note_likes(note_id);

-- 13. دالة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 14. محفزات تحديث updated_at
CREATE TRIGGER update_notes_updated_at
    BEFORE UPDATE ON notes
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_note_comments_updated_at
    BEFORE UPDATE ON note_comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_note_user_settings_updated_at
    BEFORE UPDATE ON note_user_settings
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 15. دالة البحث في المذكرات
CREATE OR REPLACE FUNCTION search_notes(
    search_term TEXT,
    user_id_param UUID DEFAULT NULL,
    note_type TEXT DEFAULT NULL,
    note_category TEXT DEFAULT NULL,
    limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    content TEXT,
    type TEXT,
    category TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        n.id,
        n.title,
        n.content,
        n.type,
        n.category,
        n.created_at,
        n.updated_at
    FROM notes n
    WHERE 
        (user_id_param IS NULL OR n.user_id = user_id_param) AND
        (note_type IS NULL OR n.type = note_type) AND
        (note_category IS NULL OR n.category = note_category) AND
        n.is_archived = false AND
        (
            LOWER(n.title) LIKE LOWER('%' || search_term || '%') OR
            LOWER(n.content) LIKE LOWER('%' || search_term || '%') OR
            search_term = ANY(n.tags)
        )
    ORDER BY n.updated_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 16. دالة إحصائيات المذكرات
CREATE OR REPLACE FUNCTION get_notes_statistics(user_id_param UUID)
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_notes', (
            SELECT COUNT(*) FROM notes 
            WHERE user_id = user_id_param AND is_archived = false
        ),
        'total_tasks', (
            SELECT COUNT(*) FROM notes 
            WHERE user_id = user_id_param AND type = 'task' AND is_archived = false
        ),
        'completed_tasks', (
            SELECT COUNT(*) FROM notes 
            WHERE user_id = user_id_param AND type = 'task' 
            AND task_status = 'completed' AND is_archived = false
        ),
        'pending_tasks', (
            SELECT COUNT(*) FROM notes 
            WHERE user_id = user_id_param AND type = 'task' 
            AND task_status IN ('pending', 'inProgress') AND is_archived = false
        ),
        'pinned_notes', (
            SELECT COUNT(*) FROM notes 
            WHERE user_id = user_id_param AND is_pinned = true AND is_archived = false
        ),
        'archived_notes', (
            SELECT COUNT(*) FROM notes 
            WHERE user_id = user_id_param AND is_archived = true
        ),
        'public_notes', (
            SELECT COUNT(*) FROM notes 
            WHERE user_id = user_id_param AND is_public = true AND is_archived = false
        ),
        'upcoming_reminders', (
            SELECT COUNT(*) FROM notes 
            WHERE user_id = user_id_param AND reminder_date > NOW() 
            AND reminder_date <= NOW() + INTERVAL '7 days' AND is_archived = false
        ),
        'overdue_tasks', (
            SELECT COUNT(*) FROM notes 
            WHERE user_id = user_id_param AND type = 'task' 
            AND due_date < NOW() AND task_status != 'completed' AND is_archived = false
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 17. دالة حذف جميع مذكرات المستخدم
CREATE OR REPLACE FUNCTION delete_all_user_notes()
RETURNS TEXT AS $$
DECLARE
    current_user_id UUID;
    deleted_count INTEGER;
BEGIN
    current_user_id := auth.uid();
    
    IF current_user_id IS NULL THEN
        RETURN 'خطأ: يجب تسجيل الدخول';
    END IF;
    
    -- حذف جميع المذكرات
    DELETE FROM notes WHERE user_id = current_user_id;
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- حذف الإعدادات
    DELETE FROM note_user_settings WHERE user_id = current_user_id;
    
    RETURN format('تم حذف %s مذكرة وجميع البيانات المرتبطة', deleted_count);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 18. منح الصلاحيات
GRANT EXECUTE ON FUNCTION search_notes(TEXT, UUID, TEXT, TEXT, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION get_notes_statistics(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION delete_all_user_notes() TO authenticated;

-- 19. تم إنشاء قسم المذكرة بنجاح
SELECT 
    'تم إنشاء قسم المذكرة بنجاح!' as message,
    'يمكن للمستخدمين الآن إنشاء مذكراتهم ومهامهم وتذكيراتهم.' as note;
