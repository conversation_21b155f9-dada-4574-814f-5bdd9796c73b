# إصلاح مدة عرض القصص - مثل فيسبوك
# Story Duration Fix - Like Facebook

## المشكلة:
القصص كانت تختفي بسرعة كبيرة (4-5 ثواني) بدلاً من المدة المناسبة مثل فيسبوك.

## الحل المطبق:

### ✅ **تحديث مدة العرض:**

#### **قبل التحديث:**
```dart
static const int _textStoryDuration = 4; // 4 ثواني للنصوص
static const int _imageStoryDuration = 5; // 5 ثواني للصور
static const int _videoStoryDuration = 60; // دقيقة واحدة للفيديو
```

#### **بعد التحديث:**
```dart
static const int _textStoryDuration = 15; // 15 ثانية للنصوص
static const int _imageStoryDuration = 12; // 12 ثانية للصور
static const int _videoStoryDuration = 20; // 20 ثانية للفيديو (أو مدة الفيديو الفعلية إذا كانت أقل)
```

### ✅ **تحسين معالج الفيديو:**

#### **إضافة Callback لتحديث المدة:**
```dart
class _VideoPlayer extends StatefulWidget {
  final String url;
  final int maxDuration;
  final Function(int)? onDurationChanged; // callback جديد
  const _VideoPlayer({
    required this.url, 
    this.maxDuration = 20,
    this.onDurationChanged,
  });
}
```

#### **استخدام مدة الفيديو الفعلية:**
```dart
// التحقق من مدة الفيديو
final duration = _controller.value.duration;
final maxDuration = Duration(seconds: widget.maxDuration);

// استخدام مدة الفيديو الفعلية أو المدة القصوى
final actualDuration = duration < maxDuration ? duration : maxDuration;

// إخطار الوالد بمدة الفيديو الفعلية
if (widget.onDurationChanged != null) {
  widget.onDurationChanged!(actualDuration.inSeconds);
}
```

#### **تحديث شريط التقدم:**
```dart
case StoryType.video:
  content = _VideoPlayer(
    url: story.mediaUrl ?? '',
    maxDuration: _videoStoryDuration,
    onDurationChanged: (int actualDuration) {
      // تحديث مدة الفيديو الفعلية
      if (mounted) {
        setState(() {
          // إعادة تهيئة شريط التقدم بالمدة الجديدة
          _progressController.duration = Duration(seconds: actualDuration);
          _progressController.reset();
          _progressController.forward();
        });
      }
    },
  );
  break;
```

## التحسينات المطبقة:

### ✅ **مدة عرض محسنة:**
- **النصوص**: 15 ثانية (بدلاً من 4)
- **الصور**: 12 ثانية (بدلاً من 5)
- **الفيديو**: 20 ثانية أو مدة الفيديو الفعلية (أيهما أقل)

### ✅ **معالجة ذكية للفيديو:**
- استخدام مدة الفيديو الفعلية إذا كانت أقل من 20 ثانية
- إعادة تهيئة شريط التقدم تلقائياً
- عرض مدة الفيديو في console للتتبع

### ✅ **تجربة مستخدم محسنة:**
- وقت كافي لقراءة النصوص
- وقت كافي لمشاهدة الصور
- مدة مناسبة للفيديو القصير

## النتائج المتوقعة:

### 🎯 **مدة عرض مناسبة:**
- **النصوص**: 15 ثانية كافية للقراءة
- **الصور**: 12 ثانية كافية للمشاهدة
- **الفيديو**: 20 ثانية أو مدة الفيديو الفعلية

### 🎯 **تجربة مثل فيسبوك:**
- مدة عرض مريحة
- شريط تقدم دقيق
- انتقال سلس بين القصص

### 🎯 **معالجة ذكية:**
- استخدام مدة الفيديو الفعلية
- تجنب قطع الفيديو
- عرض مدة الفيديو في console

## اختبار الإصلاح:

### 1. **افتح التطبيق**
### 2. **اذهب إلى القصص**
### 3. **اختبر أنواع مختلفة:**
- **نص**: يجب أن يبقى 15 ثانية
- **صورة**: يجب أن تبقى 12 ثانية
- **فيديو**: يجب أن يبقى 20 ثانية أو مدة الفيديو الفعلية

### 4. **تحقق من Console:**
ابحث عن:
- `🎬 مدة الفيديو: X ثانية، المدة المستخدمة: Y ثانية`

**الآن القصص ستبقى لمدة مناسبة مثل فيسبوك!** 