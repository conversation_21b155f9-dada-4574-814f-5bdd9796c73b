import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import '../models/real_estate_property.dart';
import '../services/real_estate_service.dart';

class PropertySettingsPage extends StatefulWidget {
  final RealEstateProperty property;

  const PropertySettingsPage({
    super.key,
    required this.property,
  });

  @override
  State<PropertySettingsPage> createState() => _PropertySettingsPageState();
}

class _PropertySettingsPageState extends State<PropertySettingsPage> {
  final RealEstateService _realEstateService = RealEstateService();
  late RealEstateProperty _property;
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    _property = widget.property;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات العقار'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // معلومات العقار
          _buildPropertyInfo(),
          
          const SizedBox(height: 24),
          
          // إعدادات الخصوصية
          _buildPrivacySettings(),
          
          const SizedBox(height: 24),
          
          // إعدادات العرض
          _buildDisplaySettings(),
          
          const SizedBox(height: 24),
          
          // إجراءات العقار
          _buildPropertyActions(),
          
          const SizedBox(height: 24),
          
          // إجراءات خطيرة
          _buildDangerousActions(),
        ],
      ),
    );
  }

  Widget _buildPropertyInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Text(
                  'معلومات العقار',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              _property.title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.visibility, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  '${_property.viewsCount} مشاهدة',
                  style: TextStyle(color: Colors.grey[600]),
                ),
                const SizedBox(width: 16),
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  'منذ ${DateTime.now().difference(_property.createdAt).inDays} يوم',
                  style: TextStyle(color: Colors.grey[600]),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _property.isActive ? Colors.green[100] : Colors.red[100],
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: _property.isActive ? Colors.green : Colors.red,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    _property.isActive ? Icons.visibility : Icons.visibility_off,
                    size: 16,
                    color: _property.isActive ? Colors.green[700] : Colors.red[700],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _property.isActive ? 'نشط' : 'مخفي',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: _property.isActive ? Colors.green[700] : Colors.red[700],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrivacySettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.privacy_tip_outlined, color: Colors.orange[600]),
                const SizedBox(width: 8),
                Text(
                  'إعدادات الخصوصية',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('السماح بالرسائل داخل التطبيق'),
              subtitle: const Text('يمكن للمهتمين مراسلتك مباشرة'),
              value: _property.allowAppMessages,
              onChanged: _loading ? null : (value) => _updateAllowMessages(value),
              activeColor: Colors.blue[600],
            ),
            const Divider(),
            ListTile(
              leading: Icon(Icons.phone, color: Colors.green[600]),
              title: const Text('رقم الهاتف'),
              subtitle: Text(_property.contactPhone ?? 'غير محدد'),
              trailing: const Icon(Icons.edit),
              onTap: () => _editContactInfo('phone'),
            ),
            ListTile(
              leading: Icon(Icons.chat, color: Colors.green[700]),
              title: const Text('رقم الواتساب'),
              subtitle: Text(_property.contactWhatsapp ?? 'غير محدد'),
              trailing: const Icon(Icons.edit),
              onTap: () => _editContactInfo('whatsapp'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDisplaySettings() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.display_settings, color: Colors.purple[600]),
                const SizedBox(width: 8),
                Text(
                  'إعدادات العرض',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.purple[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('إظهار العقار'),
              subtitle: Text(_property.isActive 
                  ? 'العقار مرئي للجميع' 
                  : 'العقار مخفي عن الجميع'),
              value: _property.isActive,
              onChanged: _loading ? null : (value) => _toggleVisibility(value),
              activeColor: Colors.green[600],
            ),
            if (_property.isActive) ...[
              const Divider(),
              ListTile(
                leading: Icon(Icons.star, color: Colors.amber[600]),
                title: const Text('ترقية إلى مميز'),
                subtitle: const Text('اجعل عقارك يظهر في المقدمة'),
                trailing: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.amber[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'قريباً',
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.amber[700],
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                onTap: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('ميزة الترقية ستكون متاحة قريباً')),
                  );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyActions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Text(
                  'إجراءات العقار',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: Icon(Icons.edit, color: Colors.blue[600]),
              title: const Text('تعديل العقار'),
              subtitle: const Text('تحديث معلومات وتفاصيل العقار'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _editProperty(),
            ),
            ListTile(
              leading: Icon(Icons.share, color: Colors.green[600]),
              title: const Text('مشاركة العقار'),
              subtitle: const Text('شارك العقار مع الأصدقاء'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _shareProperty(),
            ),
            ListTile(
              leading: Icon(Icons.analytics, color: Colors.purple[600]),
              title: const Text('إحصائيات العقار'),
              subtitle: const Text('عرض تفاصيل المشاهدات والاهتمام'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () => _showStatistics(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDangerousActions() {
    return Card(
      color: Colors.red[50],
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.warning, color: Colors.red[600]),
                const SizedBox(width: 8),
                Text(
                  'إجراءات خطيرة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.red[600],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: Icon(Icons.delete_forever, color: Colors.red[600]),
              title: Text(
                'حذف العقار نهائياً',
                style: TextStyle(color: Colors.red[700], fontWeight: FontWeight.w600),
              ),
              subtitle: const Text('لا يمكن التراجع عن هذا الإجراء'),
              onTap: () => _deleteProperty(),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _updateAllowMessages(bool value) async {
    setState(() => _loading = true);
    try {
      final updatedProperty = _createUpdatedProperty(allowAppMessages: value);
      await _realEstateService.updateProperty(_property.id, updatedProperty);
      
      setState(() {
        _property = updatedProperty;
        _loading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث إعدادات الرسائل'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التحديث: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _toggleVisibility(bool value) async {
    setState(() => _loading = true);
    try {
      final updatedProperty = _createUpdatedProperty(isActive: value);
      await _realEstateService.updateProperty(_property.id, updatedProperty);
      
      setState(() {
        _property = updatedProperty;
        _loading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(value ? 'تم إظهار العقار' : 'تم إخفاء العقار'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في التحديث: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  RealEstateProperty _createUpdatedProperty({
    bool? allowAppMessages,
    bool? isActive,
    String? contactPhone,
    String? contactWhatsapp,
  }) {
    return RealEstateProperty(
      id: _property.id,
      userId: _property.userId,
      title: _property.title,
      description: _property.description,
      propertyType: _property.propertyType,
      purpose: _property.purpose,
      category: _property.category,
      country: _property.country,
      city: _property.city,
      district: _property.district,
      address: _property.address,
      latitude: _property.latitude,
      longitude: _property.longitude,
      price: _property.price,
      currency: _property.currency,
      area: _property.area,
      bedrooms: _property.bedrooms,
      bathrooms: _property.bathrooms,
      floors: _property.floors,
      parkingSpaces: _property.parkingSpaces,
      features: _property.features,
      amenities: _property.amenities,
      contactPhone: contactPhone ?? _property.contactPhone,
      contactWhatsapp: contactWhatsapp ?? _property.contactWhatsapp,
      allowAppMessages: allowAppMessages ?? _property.allowAppMessages,
      isActive: isActive ?? _property.isActive,
      isFeatured: _property.isFeatured,
      isVerified: _property.isVerified,
      viewsCount: _property.viewsCount,
      createdAt: _property.createdAt,
      updatedAt: DateTime.now(),
      expiresAt: _property.expiresAt,
      images: _property.images,
      isFavorite: _property.isFavorite,
      ownerName: _property.ownerName,
      ownerAvatar: _property.ownerAvatar,
    );
  }

  void _editContactInfo(String type) {
    final controller = TextEditingController(
      text: type == 'phone' ? _property.contactPhone ?? '' : _property.contactWhatsapp ?? '',
    );
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('تعديل ${type == 'phone' ? 'رقم الهاتف' : 'رقم الواتساب'}'),
        content: TextField(
          controller: controller,
          decoration: InputDecoration(
            labelText: type == 'phone' ? 'رقم الهاتف' : 'رقم الواتساب',
            hintText: '+966xxxxxxxxx',
          ),
          keyboardType: TextInputType.phone,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              final newValue = controller.text.trim().isNotEmpty ? controller.text.trim() : null;
              
              try {
                final updatedProperty = type == 'phone'
                    ? _createUpdatedProperty(contactPhone: newValue)
                    : _createUpdatedProperty(contactWhatsapp: newValue);
                
                await _realEstateService.updateProperty(_property.id, updatedProperty);
                setState(() => _property = updatedProperty);
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم تحديث معلومات التواصل'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في التحديث: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  void _editProperty() {
    Navigator.pushNamed(context, '/edit_property', arguments: _property);
  }

  void _shareProperty() {
    final shareText = '''
🏠 ${_property.title}

${_property.purpose.arabicName} - ${_property.propertyType.arabicName}
💰 ${_property.getFormattedPrice()}
📍 ${_property.getFullAddress()}

${_property.description}

تطبيق أرزاوو - منصة العقارات الموثوقة
    ''';
    
    Share.share(shareText, subject: _property.title);
  }

  void _showStatistics() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إحصائيات العقار'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStatItem(Icons.visibility, 'المشاهدات', '${_property.viewsCount}'),
            _buildStatItem(Icons.calendar_today, 'أيام النشر', '${DateTime.now().difference(_property.createdAt).inDays}'),
            _buildStatItem(Icons.favorite, 'الإعجابات', 'قريباً'),
            _buildStatItem(Icons.message, 'الرسائل', 'قريباً'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: Colors.blue[600]),
          const SizedBox(width: 12),
          Expanded(child: Text(label)),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteProperty() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف العقار'),
        content: Text(
          'هل أنت متأكد من حذف "${_property.title}"؟\n\n'
          'سيتم حذف العقار نهائياً ولا يمكن التراجع عن هذا الإجراء.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف نهائياً', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() => _loading = true);
    try {
      await _realEstateService.deleteProperty(_property.id);
      
      if (mounted) {
        Navigator.pop(context, true); // العودة مع إشارة الحذف
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف العقار بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف العقار: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
