# دليل التحقق الشامل من مشكلة التخزين
# Complete Debug Guide for Storage Issue

## المشكلة
رسالة الخطأ: `Bucket media not found`

## الحل خطوة بخطوة

### الخطوة 1: تنفيذ SQL التفصيلي
1. اذهب إلى Supabase Dashboard
2. افتح SQL Editor
3. انسخ والصق محتوى ملف `DETAILED_STORAGE_CHECK.sql`
4. اضغط Run
5. تحقق من النتائج

### الخطوة 2: التحقق من النتائج
بعد تنفيذ SQL، يجب أن ترى:

#### في الجزء الأول (buckets الموجودة):
```
id      | name   | public | file_size_limit | allowed_mime_types
--------|--------|--------|-----------------|-------------------
media   | media  | true   | 52428800       | {image/jpeg,image/jpg,...}
```

#### في الجزء الثاني (السياسات):
```
policyname           | permissive | cmd
---------------------|------------|--------
media_delete_policy  | true       | DELETE
media_insert_policy  | true       | INSERT
media_select_policy  | true       | SELECT
media_update_policy  | true       | UPDATE
```

#### في الجزء الأخير (ملخص الحالة):
```
check_type      | media_bucket_exists | media_policies_count | users_count
----------------|---------------------|---------------------|------------
Storage Status  | 1                  | 4                   | [عدد المستخدمين]
```

### الخطوة 3: اختبار التطبيق
1. افتح التطبيق الجديد
2. جرب إنشاء منشور مع صور متعددة
3. راقب رسائل DEBUG في console

### الخطوة 4: قراءة رسائل DEBUG
عندما تعمل بشكل صحيح، يجب أن ترى:
```
DEBUG: uploadMedia called with path: post_1234567890_0.jpg, size: 123456 bytes
DEBUG: Checking if media bucket exists...
DEBUG: Available buckets: [media, avatars, ...]
DEBUG: Media bucket found: media
DEBUG: Media bucket public: true
DEBUG: Media bucket file size limit: 52428800
DEBUG: Starting file upload to media bucket...
DEBUG: File upload completed successfully
DEBUG: File uploaded successfully. URL: https://...
```

## إذا استمرت المشكلة

### التحقق من Supabase Dashboard:
1. اذهب إلى Storage في Supabase
2. تأكد من وجود bucket باسم `media`
3. اذهب إلى Authentication > Policies
4. تأكد من وجود سياسات `media_*_policy`

### التحقق من التطبيق:
1. افتح Flutter Inspector أو console
2. ابحث عن رسائل ERROR
3. انسخ رسائل الخطأ بالكامل

### المشاكل المحتملة وحلولها:

#### 1. مشكلة في السياسات:
```sql
-- إعادة إنشاء السياسات
DROP POLICY IF EXISTS "media_select_policy" ON storage.objects;
CREATE POLICY "media_select_policy" ON storage.objects FOR SELECT USING (bucket_id = 'media');
```

#### 2. مشكلة في Bucket:
```sql
-- إعادة إنشاء bucket
DELETE FROM storage.buckets WHERE id = 'media';
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES ('media', 'media', true, 52428800, ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/avi', 'video/mov', 'video/quicktime']);
```

#### 3. مشكلة في RLS:
```sql
-- تفعيل RLS
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
```

## رسائل الخطأ الشائعة وحلولها:

### `Bucket media not found`
**الحل**: نفذ `DETAILED_STORAGE_CHECK.sql`

### `Permission denied`
**الحل**: تحقق من سياسات RLS

### `File too large`
**الحل**: تحقق من `file_size_limit` في bucket

### `Invalid file type`
**الحل**: تحقق من `allowed_mime_types` في bucket

## التحقق النهائي

بعد تنفيذ جميع الخطوات، جرب:

1. **إنشاء منشور نصي** (بدون صور) - يجب أن يعمل
2. **إنشاء منشور بصورة واحدة** - يجب أن يعمل
3. **إنشاء منشور بصور متعددة** - يجب أن يعمل

إذا عملت جميع هذه الاختبارات، فالمشكلة محلولة!

## الدعم

إذا استمرت المشكلة:
1. انسخ جميع رسائل DEBUG و ERROR
2. انسخ نتائج SQL
3. أرسل جميع المعلومات للمساعدة في التشخيص 