import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/job.dart';
import '../services/jobs_service.dart';
import 'job_application_page.dart';

class JobDetailsPage extends StatefulWidget {
  final Job job;

  const JobDetailsPage({super.key, required this.job});

  @override
  State<JobDetailsPage> createState() => _JobDetailsPageState();
}

class _JobDetailsPageState extends State<JobDetailsPage> {
  bool _isSaved = false;
  bool _hasApplied = false;
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _checkJobStatus();
  }

  Future<void> _checkJobStatus() async {
    try {
      final hasApplied = await JobsService().hasAppliedToJob(widget.job.id);
      // يمكن إضافة فحص الحفظ هنا أيضاً
      setState(() {
        _hasApplied = hasApplied;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الوظيفة'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        actions: [
          IconButton(
            onPressed: _toggleSaveJob,
            icon: Icon(
              _isSaved ? Icons.bookmark : Icons.bookmark_border,
              color: _isSaved ? Colors.blue : null,
            ),
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'share',
                child: Row(
                  children: [
                    Icon(Icons.share, size: 20),
                    SizedBox(width: 8),
                    Text('مشاركة'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'report',
                child: Row(
                  children: [
                    Icon(Icons.report, size: 20, color: Colors.red),
                    SizedBox(width: 8),
                    Text('إبلاغ', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _loading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildJobHeader(),
                  const SizedBox(height: 24),
                  _buildJobInfo(),
                  const SizedBox(height: 24),
                  _buildJobDescription(),
                  const SizedBox(height: 24),
                  _buildRequiredSkills(),
                  const SizedBox(height: 24),
                  _buildCompanyInfo(),
                  const SizedBox(height: 100), // مساحة للزر السفلي
                ],
              ),
            ),
      bottomNavigationBar: _buildBottomBar(),
    );
  }

  Widget _buildJobHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue[600]!,
            Colors.blue[800]!,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.job.title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            widget.job.jobTitle,
            style: const TextStyle(
              fontSize: 18,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: Colors.white,
                backgroundImage: widget.job.publisherAvatar != null
                    ? NetworkImage(widget.job.publisherAvatar!)
                    : null,
                child: widget.job.publisherAvatar == null
                    ? Icon(Icons.business, color: Colors.blue[600])
                    : null,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.job.companyName,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                            ),
                          ),
                        ),
                        if (widget.job.isVerified)
                          const Icon(
                            Icons.verified,
                            color: Colors.white,
                            size: 18,
                          ),
                      ],
                    ),
                    Text(
                      'نشر بواسطة ${widget.job.publisherName}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildJobInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'معلومات الوظيفة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            children: [
              _buildInfoRow(
                icon: Icons.work_outline,
                label: 'نوع العمل',
                value: widget.job.jobTypeText,
                color: _getJobTypeColor(widget.job.jobType),
              ),
              const Divider(),
              _buildInfoRow(
                icon: Icons.category_outlined,
                label: 'الفئة',
                value: widget.job.categoryText,
                color: Colors.purple,
              ),
              const Divider(),
              _buildInfoRow(
                icon: widget.job.isRemote ? Icons.home_work : Icons.location_on_outlined,
                label: 'الموقع',
                value: widget.job.location,
                color: widget.job.isRemote ? Colors.green : Colors.orange,
              ),
              if (widget.job.salary != null) ...[
                const Divider(),
                _buildInfoRow(
                  icon: Icons.attach_money,
                  label: 'الراتب',
                  value: widget.job.salary!,
                  color: Colors.teal,
                ),
              ],
              const Divider(),
              _buildInfoRow(
                icon: Icons.access_time,
                label: 'تاريخ النشر',
                value: _formatTimeAgo(widget.job.createdAt),
                color: Colors.grey,
              ),
              if (widget.job.expiresAt != null) ...[
                const Divider(),
                _buildInfoRow(
                  icon: Icons.event_busy,
                  label: 'تاريخ الانتهاء',
                  value: _formatDate(widget.job.expiresAt!),
                  color: widget.job.isExpired ? Colors.red : Colors.orange,
                ),
              ],
              if (widget.job.applicationsCount > 0) ...[
                const Divider(),
                _buildInfoRow(
                  icon: Icons.people_outline,
                  label: 'عدد المتقدمين',
                  value: '${widget.job.applicationsCount} متقدم',
                  color: Colors.blue,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildJobDescription() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'وصف الوظيفة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Text(
            widget.job.description,
            style: const TextStyle(
              fontSize: 16,
              height: 1.6,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRequiredSkills() {
    if (widget.job.requiredSkills.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المهارات المطلوبة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: widget.job.requiredSkills.map((skill) => Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.blue[50],
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.blue[200]!),
            ),
            child: Text(
              skill,
              style: TextStyle(
                color: Colors.blue[700],
                fontWeight: FontWeight.w500,
              ),
            ),
          )).toList(),
        ),
      ],
    );
  }

  Widget _buildCompanyInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'معلومات الشركة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Row(
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: Colors.grey[200],
                backgroundImage: widget.job.publisherAvatar != null
                    ? NetworkImage(widget.job.publisherAvatar!)
                    : null,
                child: widget.job.publisherAvatar == null
                    ? Icon(Icons.business, color: Colors.grey[600], size: 30)
                    : null,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            widget.job.companyName,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        if (widget.job.isVerified)
                          Icon(Icons.verified, color: Colors.blue, size: 20),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'نشر بواسطة ${widget.job.publisherName}',
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    if (widget.job.spaceName != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        'مساحة: ${widget.job.spaceName}',
                        style: TextStyle(
                          color: Colors.blue[600],
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBottomBar() {
    if (widget.job.isExpired) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Text(
            'انتهت صلاحية هذه الوظيفة',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: Colors.red[700],
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        children: [
          if (widget.job.applicationLink != null)
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _openApplicationLink,
                icon: const Icon(Icons.open_in_new),
                label: const Text('رابط التقديم'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          if (widget.job.applicationLink != null) const SizedBox(width: 12),
          Expanded(
            flex: widget.job.applicationLink != null ? 1 : 2,
            child: ElevatedButton.icon(
              onPressed: _hasApplied ? null : _applyToJob,
              icon: Icon(_hasApplied ? Icons.check : Icons.send),
              label: Text(_hasApplied ? 'تم التقديم' : 'تقديم الآن'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _hasApplied ? Colors.green : Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _toggleSaveJob() async {
    try {
      if (_isSaved) {
        await JobsService().unsaveJob(widget.job.id);
        setState(() => _isSaved = false);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إلغاء حفظ الوظيفة')),
          );
        }
      } else {
        await JobsService().saveJob(widget.job.id);
        setState(() => _isSaved = true);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حفظ الوظيفة')),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ: $e')),
        );
      }
    }
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'share':
        // TODO: إضافة مشاركة الوظيفة
        break;
      case 'report':
        _showReportDialog();
        break;
    }
  }

  void _showReportDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إبلاغ عن الوظيفة'),
        content: const Text('هل تريد الإبلاغ عن هذه الوظيفة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await JobsService().reportJob(widget.job.id, 'محتوى مشبوه');
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم الإبلاغ عن الوظيفة')),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('خطأ: $e')),
                  );
                }
              }
            },
            child: const Text('إبلاغ'),
          ),
        ],
      ),
    );
  }

  Future<void> _openApplicationLink() async {
    if (widget.job.applicationLink != null) {
      final uri = Uri.parse(widget.job.applicationLink!);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
      }
    }
  }

  void _applyToJob() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => JobApplicationPage(job: widget.job),
      ),
    ).then((applied) {
      if (applied == true) {
        setState(() => _hasApplied = true);
      }
    });
  }

  Color _getJobTypeColor(JobType jobType) {
    switch (jobType) {
      case JobType.fullTime: return Colors.blue;
      case JobType.partTime: return Colors.orange;
      case JobType.freelance: return Colors.purple;
      case JobType.remote: return Colors.green;
      case JobType.contract: return Colors.teal;
      case JobType.internship: return Colors.indigo;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
