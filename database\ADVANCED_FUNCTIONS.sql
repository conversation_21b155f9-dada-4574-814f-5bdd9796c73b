-- ===================================
-- دوال متقدمة لقسم البحث عن عمل
-- تشغل بعد SIMPLE_SETUP.sql
-- ===================================

-- 1. دالة حذف جميع بيانات المستخدم
CREATE OR REPLACE FUNCTION delete_user_job_seeker_data(target_user_id UUID)
RETURNS TEXT AS $$
DECLARE
    deleted_likes INTEGER;
    deleted_saves INTEGER;
    deleted_profiles INTEGER;
    deleted_settings INTEGER;
BEGIN
    -- حذف الإعجابات
    DELETE FROM job_seeker_likes WHERE user_id = target_user_id;
    GET DIAGNOSTICS deleted_likes = ROW_COUNT;
    
    -- حذف الحفظ
    DELETE FROM job_seeker_saves WHERE user_id = target_user_id;
    GET DIAGNOSTICS deleted_saves = ROW_COUNT;
    
    -- حذف الإعدادات
    DELETE FROM job_seeker_user_settings WHERE user_id = target_user_id;
    GET DIAGNOSTICS deleted_settings = ROW_COUNT;
    
    -- حذف الملف المهني
    DELETE FROM job_seekers WHERE user_id = target_user_id;
    GET DIAGNOSTICS deleted_profiles = ROW_COUNT;
    
    RETURN format('تم حذف: %s ملف مهني، %s إعجاب، %s حفظ، %s إعدادات', 
                  deleted_profiles, deleted_likes, deleted_saves, deleted_settings);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. دالة مسح البيانات المحفوظة فقط
CREATE OR REPLACE FUNCTION clear_user_saved_data(target_user_id UUID)
RETURNS TEXT AS $$
DECLARE
    deleted_likes INTEGER;
    deleted_saves INTEGER;
BEGIN
    -- حذف الإعجابات
    DELETE FROM job_seeker_likes WHERE user_id = target_user_id;
    GET DIAGNOSTICS deleted_likes = ROW_COUNT;
    
    -- حذف الحفظ
    DELETE FROM job_seeker_saves WHERE user_id = target_user_id;
    GET DIAGNOSTICS deleted_saves = ROW_COUNT;
    
    RETURN format('تم مسح: %s إعجاب، %s حفظ', deleted_likes, deleted_saves);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. دالة تصدير بيانات المستخدم
CREATE OR REPLACE FUNCTION export_user_job_data(target_user_id UUID)
RETURNS JSON AS $$
DECLARE
    user_profile JSON;
    user_likes JSON;
    user_saves JSON;
    user_settings JSON;
    result JSON;
BEGIN
    -- الملف المهني
    SELECT row_to_json(js) INTO user_profile
    FROM job_seekers js
    WHERE js.user_id = target_user_id;
    
    -- الإعجابات
    SELECT json_agg(
        json_build_object(
            'seeker_id', jsl.seeker_id,
            'created_at', jsl.created_at,
            'seeker_name', js.full_name
        )
    ) INTO user_likes
    FROM job_seeker_likes jsl
    LEFT JOIN job_seekers js ON jsl.seeker_id = js.id
    WHERE jsl.user_id = target_user_id;
    
    -- الحفظ
    SELECT json_agg(
        json_build_object(
            'seeker_id', jss.seeker_id,
            'created_at', jss.created_at,
            'seeker_name', js.full_name
        )
    ) INTO user_saves
    FROM job_seeker_saves jss
    LEFT JOIN job_seekers js ON jss.seeker_id = js.id
    WHERE jss.user_id = target_user_id;
    
    -- الإعدادات
    SELECT row_to_json(settings) INTO user_settings
    FROM job_seeker_user_settings settings
    WHERE settings.user_id = target_user_id;
    
    -- تجميع النتائج
    result := json_build_object(
        'export_date', NOW(),
        'user_id', target_user_id,
        'profile', user_profile,
        'likes', COALESCE(user_likes, '[]'::json),
        'saves', COALESCE(user_saves, '[]'::json),
        'settings', user_settings
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. دالة إحصائيات المستخدم
CREATE OR REPLACE FUNCTION get_user_job_stats(target_user_id UUID)
RETURNS JSON AS $$
DECLARE
    profile_stats JSON;
    interaction_stats JSON;
    result JSON;
BEGIN
    -- إحصائيات الملف
    SELECT json_build_object(
        'has_profile', COUNT(*) > 0,
        'profile_views', COALESCE(MAX(views_count), 0),
        'profile_likes', COALESCE(MAX(likes_count), 0),
        'profile_created', MAX(created_at),
        'profile_updated', MAX(updated_at),
        'is_active', COALESCE(MAX(is_active::int), 0) = 1
    ) INTO profile_stats
    FROM job_seekers
    WHERE user_id = target_user_id;
    
    -- إحصائيات التفاعل
    SELECT json_build_object(
        'given_likes', (
            SELECT COUNT(*) FROM job_seeker_likes 
            WHERE user_id = target_user_id
        ),
        'saved_profiles', (
            SELECT COUNT(*) FROM job_seeker_saves 
            WHERE user_id = target_user_id
        ),
        'has_settings', (
            SELECT COUNT(*) > 0 FROM job_seeker_user_settings 
            WHERE user_id = target_user_id
        )
    ) INTO interaction_stats;
    
    result := json_build_object(
        'profile', profile_stats,
        'interactions', interaction_stats,
        'generated_at', NOW()
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 5. دالة البحث المتقدم
CREATE OR REPLACE FUNCTION search_job_seekers_advanced(
    search_term TEXT DEFAULT NULL,
    filter_city TEXT DEFAULT NULL,
    filter_category TEXT DEFAULT NULL,
    filter_job_type TEXT DEFAULT NULL,
    filter_country TEXT DEFAULT NULL,
    min_experience INTEGER DEFAULT NULL,
    max_experience INTEGER DEFAULT NULL,
    limit_count INTEGER DEFAULT 20
)
RETURNS TABLE (
    id UUID,
    full_name TEXT,
    category TEXT,
    current_city TEXT,
    current_country TEXT,
    experience_years INTEGER,
    preferred_job_type TEXT,
    description TEXT,
    skills TEXT[],
    languages TEXT[],
    phone_number TEXT,
    views_count INTEGER,
    likes_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        js.id,
        js.full_name,
        js.category,
        js.current_city,
        js.current_country,
        js.experience_years,
        js.preferred_job_type,
        js.description,
        js.skills,
        js.languages,
        js.phone_number,
        js.views_count,
        js.likes_count,
        js.created_at
    FROM job_seekers js
    WHERE js.is_active = true
    AND (search_term IS NULL OR (
        LOWER(js.full_name) LIKE LOWER('%' || search_term || '%') OR
        LOWER(js.description) LIKE LOWER('%' || search_term || '%') OR
        EXISTS (
            SELECT 1 FROM unnest(js.skills) skill 
            WHERE LOWER(skill) LIKE LOWER('%' || search_term || '%')
        )
    ))
    AND (filter_city IS NULL OR js.current_city = filter_city)
    AND (filter_category IS NULL OR js.category = filter_category)
    AND (filter_job_type IS NULL OR js.preferred_job_type = filter_job_type)
    AND (filter_country IS NULL OR js.current_country = filter_country)
    AND (min_experience IS NULL OR js.experience_years >= min_experience)
    AND (max_experience IS NULL OR js.experience_years <= max_experience)
    ORDER BY js.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- 6. دالة الإحصائيات العامة
CREATE OR REPLACE FUNCTION get_job_seekers_general_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total_seekers', (
            SELECT COUNT(*) FROM job_seekers WHERE is_active = true
        ),
        'new_this_week', (
            SELECT COUNT(*) FROM job_seekers 
            WHERE is_active = true 
            AND created_at >= NOW() - INTERVAL '7 days'
        ),
        'new_this_month', (
            SELECT COUNT(*) FROM job_seekers 
            WHERE is_active = true 
            AND created_at >= NOW() - INTERVAL '30 days'
        ),
        'total_categories', (
            SELECT COUNT(DISTINCT category) FROM job_seekers WHERE is_active = true
        ),
        'total_cities', (
            SELECT COUNT(DISTINCT current_city) FROM job_seekers WHERE is_active = true
        ),
        'total_likes', (
            SELECT COUNT(*) FROM job_seeker_likes
        ),
        'total_saves', (
            SELECT COUNT(*) FROM job_seeker_saves
        ),
        'avg_experience', (
            SELECT ROUND(AVG(experience_years), 1) FROM job_seekers WHERE is_active = true
        ),
        'top_categories', (
            SELECT json_agg(
                json_build_object('category', category, 'count', count)
                ORDER BY count DESC
            )
            FROM (
                SELECT category, COUNT(*) as count
                FROM job_seekers 
                WHERE is_active = true
                GROUP BY category
                ORDER BY count DESC
                LIMIT 5
            ) top_cats
        ),
        'top_cities', (
            SELECT json_agg(
                json_build_object('city', current_city, 'count', count)
                ORDER BY count DESC
            )
            FROM (
                SELECT current_city, COUNT(*) as count
                FROM job_seekers 
                WHERE is_active = true
                GROUP BY current_city
                ORDER BY count DESC
                LIMIT 5
            ) top_cities
        )
    ) INTO result;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 7. دالة تنظيف البيانات القديمة
CREATE OR REPLACE FUNCTION cleanup_old_job_data()
RETURNS TEXT AS $$
DECLARE
    deleted_inactive INTEGER;
    result TEXT;
BEGIN
    -- حذف الملفات غير النشطة لأكثر من 6 أشهر
    DELETE FROM job_seekers 
    WHERE is_active = false 
    AND updated_at < NOW() - INTERVAL '6 months';
    
    GET DIAGNOSTICS deleted_inactive = ROW_COUNT;
    
    result := format('تم حذف %s ملف غير نشط قديم', deleted_inactive);
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 8. دالة إعادة حساب الإحصائيات
CREATE OR REPLACE FUNCTION recalculate_job_seekers_stats()
RETURNS TEXT AS $$
DECLARE
    updated_likes INTEGER;
    updated_views INTEGER;
BEGIN
    -- إعادة حساب عدد الإعجابات
    UPDATE job_seekers 
    SET likes_count = (
        SELECT COUNT(*) 
        FROM job_seeker_likes 
        WHERE seeker_id = job_seekers.id
    );
    
    GET DIAGNOSTICS updated_likes = ROW_COUNT;
    
    -- إعادة تعيين المشاهدات للملفات الجديدة
    UPDATE job_seekers 
    SET views_count = 0 
    WHERE views_count IS NULL;
    
    GET DIAGNOSTICS updated_views = ROW_COUNT;
    
    RETURN format('تم تحديث %s ملف للإعجابات و %s ملف للمشاهدات', 
                  updated_likes, updated_views);
END;
$$ LANGUAGE plpgsql;

-- 9. دالة التحقق من صحة البيانات
CREATE OR REPLACE FUNCTION validate_job_seeker_data()
RETURNS TABLE (
    seeker_id UUID,
    full_name TEXT,
    issues TEXT[]
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        js.id,
        js.full_name,
        ARRAY_REMOVE(ARRAY[
            CASE WHEN LENGTH(TRIM(js.full_name)) < 3 THEN 'اسم قصير جداً' END,
            CASE WHEN js.age < 16 OR js.age > 70 THEN 'عمر غير صحيح' END,
            CASE WHEN array_length(js.skills, 1) IS NULL THEN 'لا توجد مهارات' END,
            CASE WHEN array_length(js.languages, 1) IS NULL THEN 'لا توجد لغات' END,
            CASE WHEN LENGTH(TRIM(js.description)) < 10 THEN 'وصف قصير جداً' END,
            CASE WHEN js.phone_number !~ '^05[0-9]{8}$' THEN 'رقم هاتف غير صحيح' END
        ], NULL) as issues
    FROM job_seekers js
    WHERE is_active = true;
END;
$$ LANGUAGE plpgsql;

-- 10. منح الصلاحيات للمستخدمين المصرح لهم
GRANT EXECUTE ON FUNCTION delete_user_job_seeker_data(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION clear_user_saved_data(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION export_user_job_data(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_job_stats(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION search_job_seekers_advanced(TEXT, TEXT, TEXT, TEXT, TEXT, INTEGER, INTEGER, INTEGER) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_job_seekers_general_stats() TO anon, authenticated;
