-- =============================================================
--  تشخيص شامل لحالة Storage
--  Complete Storage Diagnosis
-- =============================================================

-- هذا الاستعلام سيخبرك بالضبط ما هي المشكلة

-- 1) فحص bucket
-- -------------------------------------------------------

SELECT 
  '🔍 BUCKET DIAGNOSIS' as section,
  CASE 
    WHEN NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images')
    THEN '❌ Bucket community-images غير موجود'
    WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images' AND public = false)
    THEN '❌ Bucket موجود لكنه ليس عام (public = false)'
    WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images' AND public = true)
    THEN '✅ Bucket موجود وعام'
    ELSE '❓ حالة غير معروفة'
  END as status,
  COALESCE(
    (SELECT CONCAT('Size limit: ', file_size_limit/1024/1024, 'MB') 
     FROM storage.buckets WHERE id = 'community-images'), 
    'N/A'
  ) as details;

-- 2) فحص RLS (Row Level Security)
-- -------------------------------------------------------

SELECT 
  '🔍 RLS DIAGNOSIS' as section,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'storage' 
      AND c.relname = 'objects'
      AND c.relrowsecurity = true
    )
    THEN '❌ RLS مفعل - قد يمنع رفع الصور'
    WHEN EXISTS (
      SELECT 1 FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'storage' 
      AND c.relname = 'objects'
      AND c.relrowsecurity = false
    )
    THEN '✅ RLS معطل - رفع الصور يجب أن يعمل'
    ELSE '❓ لا يمكن تحديد حالة RLS'
  END as status,
  'Row Level Security controls access to storage.objects' as details;

-- 3) فحص السياسات
-- -------------------------------------------------------

SELECT 
  '🔍 POLICIES DIAGNOSIS' as section,
  CASE 
    WHEN NOT EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE tablename = 'objects' 
      AND schemaname = 'storage'
    )
    THEN '✅ لا توجد سياسات - إذا كان RLS معطل فهذا جيد'
    WHEN EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE tablename = 'objects' 
      AND schemaname = 'storage'
      AND policyname LIKE '%community%'
    )
    THEN '✅ توجد سياسات للمجتمعات'
    ELSE '❌ توجد سياسات أخرى قد تتداخل'
  END as status,
  COALESCE(
    (SELECT STRING_AGG(policyname, ', ') 
     FROM pg_policies 
     WHERE tablename = 'objects' AND schemaname = 'storage'), 
    'No policies'
  ) as details;

-- 4) فحص الصلاحيات
-- -------------------------------------------------------

SELECT 
  '🔍 PERMISSIONS DIAGNOSIS' as section,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.table_privileges 
      WHERE table_schema = 'storage' 
      AND table_name = 'objects'
      AND grantee = 'authenticated'
      AND privilege_type = 'INSERT'
    )
    THEN '✅ المستخدمين المسجلين لديهم صلاحية INSERT'
    ELSE '❌ لا توجد صلاحية INSERT للمستخدمين المسجلين'
  END as status,
  COALESCE(
    (SELECT STRING_AGG(DISTINCT privilege_type, ', ') 
     FROM information_schema.table_privileges 
     WHERE table_schema = 'storage' 
     AND table_name = 'objects'
     AND grantee = 'authenticated'), 
    'No permissions'
  ) as details;

-- 5) فحص المستخدم الحالي
-- -------------------------------------------------------

SELECT 
  '🔍 USER DIAGNOSIS' as section,
  CASE 
    WHEN auth.uid() IS NULL
    THEN '❌ لا يوجد مستخدم مسجل دخول'
    ELSE '✅ مستخدم مسجل دخول'
  END as status,
  COALESCE(auth.uid()::text, 'No user') as details;

-- 6) اختبار إنشاء bucket (إذا لم يكن موجود)
-- -------------------------------------------------------

DO $$
BEGIN
  IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images') THEN
    INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
    VALUES ('community-images', 'community-images', true, 104857600, ARRAY['image/*']);
    
    RAISE NOTICE '✅ تم إنشاء bucket جديد';
  ELSE
    RAISE NOTICE '✅ Bucket موجود بالفعل';
  END IF;
END $$;

-- 7) التوصيات النهائية
-- -------------------------------------------------------

SELECT 
  '💡 RECOMMENDATIONS' as section,
  CASE 
    WHEN NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images')
    THEN '🔧 شغل: INSERT INTO storage.buckets (id, name, public) VALUES (''community-images'', ''community-images'', true);'
    WHEN EXISTS (
      SELECT 1 FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'storage' 
      AND c.relname = 'objects'
      AND c.relrowsecurity = true
    )
    THEN '🔧 شغل: ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;'
    ELSE '✅ كل شيء يبدو جيد - المشكلة قد تكون في كود التطبيق'
  END as status,
  'Follow the recommendation above' as details;

-- 8) معلومات إضافية مفيدة
-- -------------------------------------------------------

SELECT 
  '📊 ADDITIONAL INFO' as section,
  'Bucket count' as status,
  (SELECT COUNT(*)::text FROM storage.buckets) as details

UNION ALL

SELECT 
  '📊 ADDITIONAL INFO' as section,
  'Objects count in community-images' as status,
  COALESCE(
    (SELECT COUNT(*)::text FROM storage.objects WHERE bucket_id = 'community-images'), 
    '0'
  ) as details

UNION ALL

SELECT 
  '📊 ADDITIONAL INFO' as section,
  'Database version' as status,
  version() as details;

-- =============================================================
--  تعليمات قراءة النتائج
-- =============================================================

/*

كيفية قراءة النتائج:

✅ = جيد، لا مشكلة
❌ = مشكلة تحتاج إصلاح  
❓ = غير واضح، قد تحتاج تحقق إضافي

إذا رأيت ❌ في أي قسم، اتبع التوصية في قسم RECOMMENDATIONS

إذا كانت جميع النتائج ✅ ولا تزال المشكلة موجودة، 
فالمشكلة في كود التطبيق وليس في Supabase.

*/
