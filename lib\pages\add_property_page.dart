import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../models/real_estate_property.dart';
import '../services/real_estate_service.dart';

class AddPropertyPage extends StatefulWidget {
  const AddPropertyPage({super.key});

  @override
  State<AddPropertyPage> createState() => _AddPropertyPageState();
}

class _AddPropertyPageState extends State<AddPropertyPage> {
  final _formKey = GlobalKey<FormState>();
  final RealEstateService _realEstateService = RealEstateService();
  
  // Controllers
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _areaController = TextEditingController();
  final _countryController = TextEditingController();
  final _cityController = TextEditingController();
  final _districtController = TextEditingController();
  final _addressController = TextEditingController();
  final _phoneController = TextEditingController();
  final _whatsappController = TextEditingController();
  
  // Form values
  PropertyType _selectedType = PropertyType.apartment;
  PropertyPurpose _selectedPurpose = PropertyPurpose.sale;
  PropertyCategory _selectedCategory = PropertyCategory.residential;
  Currency _selectedCurrency = Currency.usd;
  
  int _bedrooms = 1;
  int _bathrooms = 1;
  int _floors = 1;
  int _parkingSpaces = 0;
  
  List<String> _selectedFeatures = [];
  List<String> _selectedAmenities = [];
  List<XFile> _selectedImages = [];
  
  bool _allowAppMessages = true;
  bool _loading = false;

  // قوائم الميزات والخدمات
  final List<String> _availableFeatures = [
    'مصعد', 'حديقة', 'مسبح', 'أمن وحراسة', 'موقف سيارات',
    'شرفة', 'تكييف مركزي', 'تدفئة مركزية', 'إنترنت', 'كاميرات مراقبة'
  ];
  
  final List<String> _availableAmenities = [
    'مدرسة قريبة', 'مستشفى قريب', 'مواصلات عامة', 'مراكز تسوق',
    'مطاعم ومقاهي', 'حدائق عامة', 'مسجد قريب', 'صيدلية', 'بنك', 'محطة وقود'
  ];

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _areaController.dispose();
    _countryController.dispose();
    _cityController.dispose();
    _districtController.dispose();
    _addressController.dispose();
    _phoneController.dispose();
    _whatsappController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // عنوان الصفحة
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.blue[600]!, Colors.blue[800]!],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Icon(Icons.add_home, size: 48, color: Colors.white),
                  const SizedBox(height: 12),
                  Text(
                    'إضافة عقار جديد',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'أضف تفاصيل عقارك لعرضه للمهتمين',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white70,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 24),
            
            // معلومات أساسية
            _buildSection(
              'المعلومات الأساسية',
              Icons.info_outline,
              [
                _buildTextField(
                  controller: _titleController,
                  label: 'عنوان الإعلان',
                  hint: 'مثال: شقة للبيع في وسط المدينة',
                  validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
                ),
                
                const SizedBox(height: 16),
                
                _buildTextField(
                  controller: _descriptionController,
                  label: 'وصف العقار',
                  hint: 'اكتب وصفاً مفصلاً عن العقار...',
                  maxLines: 4,
                  validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // نوع العقار والغرض
            _buildSection(
              'نوع العقار والغرض',
              Icons.home_work,
              [
                _buildDropdown<PropertyType>(
                  label: 'نوع العقار',
                  value: _selectedType,
                  items: PropertyType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Row(
                        children: [
                          Text(type.icon),
                          const SizedBox(width: 8),
                          Text(type.arabicName),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) => setState(() => _selectedType = value!),
                ),
                
                const SizedBox(height: 16),
                
                _buildDropdown<PropertyPurpose>(
                  label: 'الغرض',
                  value: _selectedPurpose,
                  items: PropertyPurpose.values.map((purpose) {
                    return DropdownMenuItem(
                      value: purpose,
                      child: Row(
                        children: [
                          Text(purpose.icon),
                          const SizedBox(width: 8),
                          Text(purpose.arabicName),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) => setState(() => _selectedPurpose = value!),
                ),
                
                const SizedBox(height: 16),
                
                _buildDropdown<PropertyCategory>(
                  label: 'الفئة',
                  value: _selectedCategory,
                  items: PropertyCategory.values.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(category.arabicName),
                    );
                  }).toList(),
                  onChanged: (value) => setState(() => _selectedCategory = value!),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // السعر والمساحة
            _buildSection(
              'السعر والمساحة',
              Icons.attach_money,
              [
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: _buildTextField(
                        controller: _priceController,
                        label: 'السعر',
                        hint: '0',
                        keyboardType: TextInputType.number,
                        validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildDropdown<Currency>(
                        label: 'العملة',
                        value: _selectedCurrency,
                        items: Currency.values.map((currency) {
                          return DropdownMenuItem(
                            value: currency,
                            child: Text(currency.symbol),
                          );
                        }).toList(),
                        onChanged: (value) => setState(() => _selectedCurrency = value!),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                _buildTextField(
                  controller: _areaController,
                  label: 'المساحة (متر مربع)',
                  hint: '0',
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // تفاصيل العقار
            if (_selectedCategory == PropertyCategory.residential) ...[
              _buildSection(
                'تفاصيل العقار',
                Icons.bed,
                [
                  Row(
                    children: [
                      Expanded(
                        child: _buildNumberField(
                          label: 'عدد الغرف',
                          value: _bedrooms,
                          onChanged: (value) => setState(() => _bedrooms = value),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildNumberField(
                          label: 'عدد الحمامات',
                          value: _bathrooms,
                          onChanged: (value) => setState(() => _bathrooms = value),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: _buildNumberField(
                          label: 'عدد الطوابق',
                          value: _floors,
                          onChanged: (value) => setState(() => _floors = value),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildNumberField(
                          label: 'مواقف السيارات',
                          value: _parkingSpaces,
                          onChanged: (value) => setState(() => _parkingSpaces = value),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
            ],
            
            // الموقع
            _buildSection(
              'الموقع',
              Icons.location_on,
              [
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _countryController,
                        label: 'البلد',
                        hint: 'مثال: السعودية',
                        validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildTextField(
                        controller: _cityController,
                        label: 'المدينة',
                        hint: 'مثال: الرياض',
                        validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _districtController,
                        label: 'الحي',
                        hint: 'مثال: العليا',
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildTextField(
                        controller: _addressController,
                        label: 'العنوان التفصيلي',
                        hint: 'اختياري',
                      ),
                    ),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // الميزات
            _buildSection(
              'الميزات',
              Icons.star,
              [
                _buildMultiSelectChips(
                  'ميزات العقار',
                  _availableFeatures,
                  _selectedFeatures,
                  (features) => setState(() => _selectedFeatures = features),
                ),
                
                const SizedBox(height: 16),
                
                _buildMultiSelectChips(
                  'الخدمات القريبة',
                  _availableAmenities,
                  _selectedAmenities,
                  (amenities) => setState(() => _selectedAmenities = amenities),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // معلومات التواصل
            _buildSection(
              'معلومات التواصل',
              Icons.contact_phone,
              [
                _buildTextField(
                  controller: _phoneController,
                  label: 'رقم الهاتف',
                  hint: '+966xxxxxxxxx',
                  keyboardType: TextInputType.phone,
                ),
                
                const SizedBox(height: 16),
                
                _buildTextField(
                  controller: _whatsappController,
                  label: 'رقم الواتساب',
                  hint: '+966xxxxxxxxx',
                  keyboardType: TextInputType.phone,
                ),
                
                const SizedBox(height: 16),
                
                SwitchListTile(
                  title: const Text('السماح بالرسائل داخل التطبيق'),
                  subtitle: const Text('يمكن للمهتمين مراسلتك مباشرة'),
                  value: _allowAppMessages,
                  onChanged: (value) => setState(() => _allowAppMessages = value),
                  activeColor: Colors.blue[600],
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // الصور
            _buildSection(
              'صور العقار',
              Icons.photo_camera,
              [
                _buildImagePicker(),
              ],
            ),
            
            const SizedBox(height: 32),
            
            // زر الحفظ
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _loading ? null : _saveProperty,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: _loading
                    ? const CircularProgressIndicator(color: Colors.white)
                    : const Text(
                        'نشر العقار',
                        style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                      ),
              ),
            ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: Colors.blue[600]),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.blue[600]!),
        ),
      ),
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
    );
  }

  Widget _buildDropdown<T>({
    required String label,
    required T value,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.blue[600]!),
        ),
      ),
      items: items,
      onChanged: onChanged,
    );
  }

  Widget _buildNumberField({
    required String label,
    required int value,
    required void Function(int) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            IconButton(
              onPressed: value > 0 ? () => onChanged(value - 1) : null,
              icon: const Icon(Icons.remove),
              style: IconButton.styleFrom(
                backgroundColor: Colors.grey[200],
                foregroundColor: Colors.grey[700],
              ),
            ),
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  value.toString(),
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                ),
              ),
            ),
            IconButton(
              onPressed: () => onChanged(value + 1),
              icon: const Icon(Icons.add),
              style: IconButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMultiSelectChips(
    String title,
    List<String> options,
    List<String> selected,
    void Function(List<String>) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: options.map((option) {
            final isSelected = selected.contains(option);
            return FilterChip(
              label: Text(option),
              selected: isSelected,
              onSelected: (isSelected) {
                final newSelected = List<String>.from(selected);
                if (isSelected) {
                  newSelected.add(option);
                } else {
                  newSelected.remove(option);
                }
                onChanged(newSelected);
              },
              selectedColor: Colors.blue[100],
              checkmarkColor: Colors.blue[600],
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildImagePicker() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _pickImages,
                icon: const Icon(Icons.add_photo_alternate),
                label: const Text('إضافة صور'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.blue[600],
                  side: BorderSide(color: Colors.blue[600]!),
                ),
              ),
            ),
          ],
        ),
        
        if (_selectedImages.isNotEmpty) ...[
          const SizedBox(height: 16),
          Text(
            'الصور المحددة (${_selectedImages.length})',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          const SizedBox(height: 8),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _selectedImages.length,
              itemBuilder: (context, index) {
                return Container(
                  margin: const EdgeInsets.only(right: 8),
                  child: Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          File(_selectedImages[index].path),
                          width: 100,
                          height: 100,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 100,
                              height: 100,
                              color: Colors.grey[300],
                              child: const Icon(Icons.error),
                            );
                          },
                        ),
                      ),
                      Positioned(
                        top: 4,
                        right: 4,
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              _selectedImages.removeAt(index);
                            });
                          },
                          child: Container(
                            padding: const EdgeInsets.all(4),
                            decoration: const BoxDecoration(
                              color: Colors.red,
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.close,
                              size: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  Future<void> _pickImages() async {
    final ImagePicker picker = ImagePicker();
    final List<XFile> images = await picker.pickMultiImage();
    
    if (images.isNotEmpty) {
      setState(() {
        _selectedImages.addAll(images);
      });
    }
  }

  Future<void> _saveProperty() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _loading = true);

    try {
      final property = RealEstateProperty(
        id: '',
        userId: '',
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        propertyType: _selectedType,
        purpose: _selectedPurpose,
        category: _selectedCategory,
        country: _countryController.text.trim(),
        city: _cityController.text.trim(),
        district: _districtController.text.trim().isNotEmpty ? _districtController.text.trim() : null,
        address: _addressController.text.trim().isNotEmpty ? _addressController.text.trim() : null,
        price: double.parse(_priceController.text.trim()),
        currency: _selectedCurrency,
        area: _areaController.text.trim().isNotEmpty ? double.parse(_areaController.text.trim()) : null,
        bedrooms: _bedrooms,
        bathrooms: _bathrooms,
        floors: _floors,
        parkingSpaces: _parkingSpaces,
        features: _selectedFeatures,
        amenities: _selectedAmenities,
        contactPhone: _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : null,
        contactWhatsapp: _whatsappController.text.trim().isNotEmpty ? _whatsappController.text.trim() : null,
        allowAppMessages: _allowAppMessages,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        expiresAt: DateTime.now().add(const Duration(days: 90)),
      );

      final propertyId = await _realEstateService.createProperty(property);

      // رفع الصور إذا كانت موجودة
      if (_selectedImages.isNotEmpty) {
        final imagePaths = _selectedImages.map((image) => image.path).toList();
        await _realEstateService.uploadPropertyImages(propertyId, imagePaths);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم نشر العقار بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
        
        // مسح النموذج
        _formKey.currentState!.reset();
        _clearForm();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في نشر العقار: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }

  void _clearForm() {
    _titleController.clear();
    _descriptionController.clear();
    _priceController.clear();
    _areaController.clear();
    _countryController.clear();
    _cityController.clear();
    _districtController.clear();
    _addressController.clear();
    _phoneController.clear();
    _whatsappController.clear();
    
    setState(() {
      _selectedType = PropertyType.apartment;
      _selectedPurpose = PropertyPurpose.sale;
      _selectedCategory = PropertyCategory.residential;
      _selectedCurrency = Currency.usd;
      _bedrooms = 1;
      _bathrooms = 1;
      _floors = 1;
      _parkingSpaces = 0;
      _selectedFeatures.clear();
      _selectedAmenities.clear();
      _selectedImages.clear();
      _allowAppMessages = true;
    });
  }
}
