import 'package:flutter/material.dart';
import 'dart:async';
import '../models/note.dart';
import '../services/notes_service.dart';

class CreateNotePage extends StatefulWidget {
  final Note? editNote;

  const CreateNotePage({super.key, this.editNote});

  @override
  State<CreateNotePage> createState() => _CreateNotePageState();
}

class _CreateNotePageState extends State<CreateNotePage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _tagController = TextEditingController();
  
  final NotesService _notesService = NotesService();
  
  NoteType _selectedType = NoteType.note;
  NoteCategory _selectedCategory = NoteCategory.personal;
  TaskStatus? _selectedTaskStatus;
  List<String> _tags = [];
  bool _isPinned = false;
  bool _isPublic = false;
  DateTime? _reminderDate;
  DateTime? _dueDate;
  
  bool _loading = false;
  bool _autoSaving = false;
  Timer? _autoSaveTimer;
  
  int _wordCount = 0;
  int _characterCount = 0;

  @override
  void initState() {
    super.initState();
    if (widget.editNote != null) {
      _loadExistingNote();
    }
    _contentController.addListener(_updateCounts);
    _contentController.addListener(_scheduleAutoSave);
  }

  @override
  void dispose() {
    _autoSaveTimer?.cancel();
    _titleController.dispose();
    _contentController.dispose();
    _tagController.dispose();
    super.dispose();
  }

  void _loadExistingNote() {
    final note = widget.editNote!;
    _titleController.text = note.title;
    _contentController.text = note.content;
    _selectedType = note.type;
    _selectedCategory = note.category;
    _selectedTaskStatus = note.taskStatus;
    _tags = List.from(note.tags);
    _isPinned = note.isPinned;
    _isPublic = note.isPublic;
    _reminderDate = note.reminderDate;
    _dueDate = note.dueDate;
    _updateCounts();
  }

  void _updateCounts() {
    final text = _contentController.text;
    setState(() {
      _characterCount = text.length;
      _wordCount = text.trim().isEmpty ? 0 : text.trim().split(RegExp(r'\s+')).length;
    });
  }

  void _scheduleAutoSave() {
    if (widget.editNote == null) return; // لا نحفظ تلقائياً للمذكرات الجديدة
    
    _autoSaveTimer?.cancel();
    _autoSaveTimer = Timer(const Duration(seconds: 3), () {
      _performAutoSave();
    });
  }

  void _performAutoSave() async {
    if (widget.editNote == null || _titleController.text.trim().isEmpty) return;
    
    setState(() => _autoSaving = true);
    try {
      await _notesService.autoSave(
        widget.editNote!.id,
        _contentController.text,
      );
    } catch (e) {
      // تجاهل أخطاء الحفظ التلقائي
    } finally {
      if (mounted) {
        setState(() => _autoSaving = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          widget.editNote != null ? 'تعديل المذكرة' : 'مذكرة جديدة',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.deepPurple[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (_autoSaving)
            const Padding(
              padding: EdgeInsets.all(16),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 2,
                ),
              ),
            ),
          IconButton(
            icon: Icon(_isPinned ? Icons.push_pin : Icons.push_pin_outlined),
            onPressed: () => setState(() => _isPinned = !_isPinned),
            tooltip: _isPinned ? 'إلغاء التثبيت' : 'تثبيت',
          ),
          IconButton(
            icon: Icon(_isPublic ? Icons.public : Icons.lock),
            onPressed: () => setState(() => _isPublic = !_isPublic),
            tooltip: _isPublic ? 'جعل خاص' : 'جعل عام',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // شريط المعلومات
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.white,
              child: Column(
                children: [
                  // العنوان
                  TextFormField(
                    controller: _titleController,
                    decoration: InputDecoration(
                      hintText: 'عنوان المذكرة...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey[100],
                      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    ),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'يرجى إدخال عنوان المذكرة';
                      }
                      return null;
                    },
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // النوع والفئة
                  Row(
                    children: [
                      Expanded(
                        child: DropdownButtonFormField<NoteType>(
                          value: _selectedType,
                          decoration: InputDecoration(
                            labelText: 'النوع',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          items: NoteType.values.map((type) {
                            return DropdownMenuItem(
                              value: type,
                              child: Row(
                                children: [
                                  Icon(type.icon, size: 16, color: type.color),
                                  const SizedBox(width: 8),
                                  Text(type.arabicName),
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() {
                              _selectedType = value!;
                              if (_selectedType == NoteType.task && _selectedTaskStatus == null) {
                                _selectedTaskStatus = TaskStatus.pending;
                              }
                            });
                          },
                        ),
                      ),
                      
                      const SizedBox(width: 12),
                      
                      Expanded(
                        child: DropdownButtonFormField<NoteCategory>(
                          value: _selectedCategory,
                          decoration: InputDecoration(
                            labelText: 'الفئة',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                          items: NoteCategory.values.map((category) {
                            return DropdownMenuItem(
                              value: category,
                              child: Row(
                                children: [
                                  Icon(category.icon, size: 16, color: category.color),
                                  const SizedBox(width: 8),
                                  Text(category.arabicName),
                                ],
                              ),
                            );
                          }).toList(),
                          onChanged: (value) {
                            setState(() => _selectedCategory = value!);
                          },
                        ),
                      ),
                    ],
                  ),
                  
                  // حالة المهمة (إذا كان النوع مهمة)
                  if (_selectedType == NoteType.task) ...[
                    const SizedBox(height: 12),
                    DropdownButtonFormField<TaskStatus>(
                      value: _selectedTaskStatus,
                      decoration: InputDecoration(
                        labelText: 'حالة المهمة',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      ),
                      items: TaskStatus.values.map((status) {
                        return DropdownMenuItem(
                          value: status,
                          child: Row(
                            children: [
                              Icon(status.icon, size: 16, color: status.color),
                              const SizedBox(width: 8),
                              Text(status.arabicName),
                            ],
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() => _selectedTaskStatus = value);
                      },
                    ),
                  ],
                ],
              ),
            ),
            
            // المحتوى
            Expanded(
              child: Container(
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.1),
                      spreadRadius: 1,
                      blurRadius: 4,
                    ),
                  ],
                ),
                child: TextFormField(
                  controller: _contentController,
                  decoration: const InputDecoration(
                    hintText: 'اكتب محتوى مذكرتك هنا...',
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.all(16),
                  ),
                  maxLines: null,
                  expands: true,
                  textAlignVertical: TextAlignVertical.top,
                  style: const TextStyle(
                    fontSize: 16,
                    height: 1.5,
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'يرجى إدخال محتوى المذكرة';
                    }
                    return null;
                  },
                ),
              ),
            ),
            
            // شريط الأدوات السفلي
            Container(
              padding: const EdgeInsets.all(16),
              color: Colors.white,
              child: Column(
                children: [
                  // إحصائيات النص
                  Row(
                    children: [
                      Text(
                        '$_wordCount كلمة • $_characterCount حرف',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                      const Spacer(),
                      if (_autoSaving)
                        Row(
                          children: [
                            SizedBox(
                              width: 12,
                              height: 12,
                              child: CircularProgressIndicator(
                                color: Colors.green[600],
                                strokeWidth: 2,
                              ),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'حفظ تلقائي...',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.green[600],
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // الوسوم
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _tagController,
                          decoration: InputDecoration(
                            hintText: 'أضف وسم...',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            isDense: true,
                          ),
                          onSubmitted: _addTag,
                        ),
                      ),
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () => _addTag(_tagController.text),
                        icon: const Icon(Icons.add),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.deepPurple[600],
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ],
                  ),
                  
                  // عرض الوسوم
                  if (_tags.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: _tags.map((tag) {
                        return Chip(
                          label: Text('#$tag'),
                          deleteIcon: const Icon(Icons.close, size: 16),
                          onDeleted: () => _removeTag(tag),
                          backgroundColor: Colors.blue[50],
                          deleteIconColor: Colors.blue[700],
                        );
                      }).toList(),
                    ),
                  ],
                  
                  const SizedBox(height: 12),
                  
                  // أزرار الحفظ والإلغاء
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: () => Navigator.pop(context),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: const Text('إلغاء'),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        flex: 2,
                        child: ElevatedButton(
                          onPressed: _loading ? null : _saveNote,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.deepPurple[600],
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: _loading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : Text(
                                  widget.editNote != null ? 'حفظ التغييرات' : 'إنشاء المذكرة',
                                  style: const TextStyle(fontWeight: FontWeight.bold),
                                ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _addTag(String tag) {
    final trimmedTag = tag.trim();
    if (trimmedTag.isNotEmpty && !_tags.contains(trimmedTag)) {
      setState(() {
        _tags.add(trimmedTag);
        _tagController.clear();
      });
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  void _saveNote() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _loading = true);

    try {
      if (widget.editNote != null) {
        // تحديث مذكرة موجودة
        await _notesService.updateNote(
          widget.editNote!.id,
          title: _titleController.text.trim(),
          content: _contentController.text.trim(),
          type: _selectedType,
          category: _selectedCategory,
          taskStatus: _selectedTaskStatus,
          tags: _tags,
          isPinned: _isPinned,
          isPublic: _isPublic,
          reminderDate: _reminderDate,
          dueDate: _dueDate,
        );
      } else {
        // إنشاء مذكرة جديدة
        await _notesService.createNote(
          title: _titleController.text.trim(),
          content: _contentController.text.trim(),
          type: _selectedType,
          category: _selectedCategory,
          taskStatus: _selectedTaskStatus,
          tags: _tags,
          isPinned: _isPinned,
          isPublic: _isPublic,
          reminderDate: _reminderDate,
          dueDate: _dueDate,
        );
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.editNote != null ? 'تم حفظ التغييرات بنجاح!' : 'تم إنشاء المذكرة بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في الحفظ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }
}
