-- =============================================================
--  إصلاح أعمدة جدول منشورات المجتمع
--  Fix Community Posts Table Columns
-- =============================================================

-- هذا السكريپت يضيف الأعمدة المفقودة لجدول community_posts

-- 1) فحص الأعمدة الموجودة حالياً
-- -------------------------------------------------------

SELECT 
  '🔍 CURRENT COLUMNS' as check_type,
  string_agg(column_name, ', ' ORDER BY ordinal_position) as columns
FROM information_schema.columns 
WHERE table_name = 'community_posts' 
AND table_schema = 'public';

-- 2) إضافة الأعمدة المفقودة
-- -------------------------------------------------------

-- إضافة عمود التصويتات الإيجابية
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'community_posts' 
    AND column_name = 'up_votes'
  ) THEN
    ALTER TABLE community_posts ADD COLUMN up_votes INTEGER DEFAULT 0;
    RAISE NOTICE '✅ Added up_votes column';
  ELSE
    RAISE NOTICE '⚠️ up_votes column already exists';
  END IF;
END $$;

-- إضافة عمود التصويتات السلبية
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'community_posts' 
    AND column_name = 'down_votes'
  ) THEN
    ALTER TABLE community_posts ADD COLUMN down_votes INTEGER DEFAULT 0;
    RAISE NOTICE '✅ Added down_votes column';
  ELSE
    RAISE NOTICE '⚠️ down_votes column already exists';
  END IF;
END $$;

-- إضافة عمود عدد التعليقات
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns 
    WHERE table_name = 'community_posts' 
    AND column_name = 'comments_count'
  ) THEN
    ALTER TABLE community_posts ADD COLUMN comments_count INTEGER DEFAULT 0;
    RAISE NOTICE '✅ Added comments_count column';
  ELSE
    RAISE NOTICE '⚠️ comments_count column already exists';
  END IF;
END $$;

-- 3) تحديث القيم الافتراضية للمنشورات الموجودة
-- -------------------------------------------------------

UPDATE community_posts 
SET 
  up_votes = COALESCE(up_votes, 0),
  down_votes = COALESCE(down_votes, 0),
  comments_count = COALESCE(comments_count, 0)
WHERE up_votes IS NULL OR down_votes IS NULL OR comments_count IS NULL;

-- 4) إضافة قيود للتأكد من صحة البيانات
-- -------------------------------------------------------

-- التأكد من أن التصويتات لا تكون سالبة
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints 
    WHERE constraint_name = 'community_posts_up_votes_check'
  ) THEN
    ALTER TABLE community_posts 
    ADD CONSTRAINT community_posts_up_votes_check 
    CHECK (up_votes >= 0);
    RAISE NOTICE '✅ Added up_votes check constraint';
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints 
    WHERE constraint_name = 'community_posts_down_votes_check'
  ) THEN
    ALTER TABLE community_posts 
    ADD CONSTRAINT community_posts_down_votes_check 
    CHECK (down_votes >= 0);
    RAISE NOTICE '✅ Added down_votes check constraint';
  END IF;
END $$;

DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.check_constraints 
    WHERE constraint_name = 'community_posts_comments_count_check'
  ) THEN
    ALTER TABLE community_posts 
    ADD CONSTRAINT community_posts_comments_count_check 
    CHECK (comments_count >= 0);
    RAISE NOTICE '✅ Added comments_count check constraint';
  END IF;
END $$;

-- 5) فحص النتائج النهائية
-- -------------------------------------------------------

SELECT 
  '🔍 UPDATED COLUMNS' as check_type,
  string_agg(column_name, ', ' ORDER BY ordinal_position) as columns
FROM information_schema.columns 
WHERE table_name = 'community_posts' 
AND table_schema = 'public';

-- فحص الأعمدة المطلوبة
SELECT 
  '🔍 REQUIRED COLUMNS CHECK' as check_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'community_posts' AND column_name = 'up_votes')
    AND EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'community_posts' AND column_name = 'down_votes')
    AND EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'community_posts' AND column_name = 'comments_count')
    THEN '✅ SUCCESS: All required columns exist'
    ELSE '❌ FAILED: Some columns are missing'
  END as status;

-- 6) اختبار استعلام المنشورات
-- -------------------------------------------------------

SELECT 
  '🧪 QUERY TEST' as check_type,
  'Testing community posts query...' as status;

-- اختبار الاستعلام الذي كان يفشل
SELECT 
  id, community_id, user_id, content, media_url, media_type, 
  created_at, up_votes, down_votes, comments_count
FROM community_posts 
LIMIT 1;

-- 7) النتيجة النهائية
-- -------------------------------------------------------

SELECT 
  '🎉 FINAL RESULT' as check_type,
  '✅ COMMUNITY POSTS COLUMNS FIXED!' as status,
  'Posts can now be fetched without column errors' as details;

-- =============================================================
--  تعليمات ما بعد التشغيل
-- =============================================================

/*

بعد تشغيل هذا السكريپت:

1. جدول community_posts سيحتوي على جميع الأعمدة المطلوبة:
   - up_votes (عدد التصويتات الإيجابية)
   - down_votes (عدد التصويتات السلبية)  
   - comments_count (عدد التعليقات)

2. جميع المنشورات الموجودة ستحصل على قيم افتراضية (0)

3. قيود للتأكد من أن القيم لا تكون سالبة

4. التطبيق سيتمكن من جلب المنشورات بدون أخطاء

إذا رأيت "✅ SUCCESS: All required columns exist"
فهذا يعني أن جميع الأعمدة تم إضافتها بنجاح.

*/

-- =============================================================
--  انتهى إصلاح أعمدة منشورات المجتمع
-- =============================================================
