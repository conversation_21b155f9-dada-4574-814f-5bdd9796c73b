# إصلاح مشكلة إنشاء المنتجات في السوق
# Marketplace Product Creation Fix

## المشكلة:
المنتج يتم إنشاؤه بنجاح ويظهر "تم نشر المنتج" لكنه لا يظهر في التطبيق.

## السبب:
كان الكود يستخدم جدولين مختلفين:
1. **`products`** - يستخدم في `SupabaseService.createProduct()`
2. **`marketplace_products`** - يستخدم في `MarketplaceService` لعرض المنتجات

## الحل المطبق:

### ✅ **تحديث دالة `createProduct()` في `SupabaseService`:**

#### **قبل التحديث:**
```dart
.from('products')
.insert({
  'name': name,
  'negotiable': negotiable,
  'category': category,
  // ... حقول أخرى
})
```

#### **بعد التحديث:**
```dart
.from('marketplace_products')
.insert({
  'title': name, // تغيير من name إلى title
  'is_negotiable': negotiable, // تغيير من negotiable إلى is_negotiable
  'category_id': _getCategoryId(category), // تحويل الفئة إلى ID
  'status': 'active', // إضافة حالة المنتج
  'created_at': DateTime.now().toIso8601String(),
  'updated_at': DateTime.now().toIso8601String(),
  'expires_at': DateTime.now().add(const Duration(days: 30)).toIso8601String(),
})
```

### ✅ **تحديث طريقة حفظ الصور:**

#### **قبل التحديث:**
```dart
// حفظ في جدول منفصل
await _client.from('product_images').insert({
  'product_id': productId,
  'url': url,
  'idx': i,
});
```

#### **بعد التحديث:**
```dart
// حفظ في حقل images مباشرة
final List<String> imageUrls = [];
for (int i = 0; i < imagesBytes.length; i++) {
  final url = await uploadMedia(bytes, path);
  imageUrls.add(url);
}

await _client
    .from('marketplace_products')
    .update({
      'images': imageUrls,
    })
    .eq('id', productId);
```

### ✅ **إضافة دالة تحويل الفئات:**

```dart
String _getCategoryId(String categoryName) {
  final categoryMap = {
    'إلكترونيات': 'electronics',
    'ملابس': 'clothing',
    'سيارات': 'vehicles',
    'عقارات': 'real_estate',
    'أثاث': 'furniture',
    'أجهزة منزلية': 'home_appliances',
    'كتب': 'books',
    'رياضة': 'sports',
    'موبايلات': 'mobile_phones',
    'كمبيوترات': 'computers',
    'مكياج': 'cosmetics',
    'عطور': 'perfumes',
    'مجوهرات': 'jewelry',
    'خدمات': 'services',
    'حيوانات أليفة': 'pets',
    'ألعاب فيديو': 'video_games',
    'موسيقى': 'music',
    'فن': 'art',
    'تذاكر': 'tickets',
    'دراجات': 'bicycles',
    'معدات صناعية': 'industrial_equipment',
    'آلات تصوير': 'cameras',
    'ساعات': 'watches',
    'أطعمة': 'food',
    'مستلزمات طبية': 'medical_supplies',
    'أثاث مكتبي': 'office_furniture',
    'قرطاسية': 'stationery',
    'مستلزمات أطفال': 'baby_supplies',
    'مستلزمات زراعية': 'agricultural_supplies',
    'معدات البناء': 'construction_equipment',
    'أجهزة ألعاب': 'gaming_consoles',
    'إكسسوارات سيارات': 'car_accessories',
    'خيم ورحلات': 'camping',
    'معدات بحرية': 'marine_equipment',
    'أدوات موسيقية': 'musical_instruments',
    'برامج': 'software',
    'ألعاب أطفال': 'children_toys',
    'معدات كهربائية': 'electrical_equipment',
    'دراجات نارية': 'motorcycles',
    'مزارع': 'farms',
    'تصميم وخدمات': 'design_services',
    'استشارات': 'consulting',
    'شحن وتوصيل': 'shipping_delivery',
    'تطوير مواقع': 'web_development',
    'تسويق': 'marketing',
    'تعليم ودورات': 'education_courses',
    'وظائف': 'jobs',
    'مناسبات': 'events',
    'أخرى': 'other',
  };

  return categoryMap[categoryName] ?? 'other';
}
```

## التحسينات المطبقة:

### ✅ **توحيد الجداول:**
- استخدام `marketplace_products` في جميع العمليات
- إزالة التضارب بين الجداول

### ✅ **تحديث أسماء الحقول:**
- `name` → `title`
- `negotiable` → `is_negotiable`
- `category` → `category_id`

### ✅ **إضافة الحقول المطلوبة:**
- `status: 'active'` - حالة المنتج
- `updated_at` - تاريخ التحديث
- `expires_at` - تاريخ انتهاء الصلاحية (30 يوم)

### ✅ **تحسين حفظ الصور:**
- حفظ URLs في حقل `images` مباشرة
- إزالة الحاجة لجدول منفصل للصور

## النتائج المتوقعة:

### 🎯 **ظهور المنتجات المنشورة:**
- المنتجات الجديدة ستظهر في قسم السوق
- ستظهر مع الصور والتفاصيل كاملة
- ستظهر أسماء البائعين الحقيقية

### 🎯 **عمل جميع الميزات:**
- البحث في المنتجات
- تصفية حسب الفئة
- المفضلة والإبلاغ
- عرض تفاصيل المنتج

## اختبار الإصلاح:

### 1. **افتح التطبيق**
### 2. **اذهب إلى قسم السوق**
### 3. **اضغط على "إضافة منتج"**
### 4. **أدخل بيانات المنتج**
### 5. **اضغط "نشر المنتج"**
### 6. **تحقق من ظهور المنتج في القائمة**

**الآن يجب أن تظهر المنتجات المنشورة في قسم السوق!** 