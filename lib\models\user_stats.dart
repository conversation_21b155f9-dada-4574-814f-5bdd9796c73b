class UserStats {
  final int followersCount;
  final int totalViews;
  final bool isVerified;
  final DateTime? verificationRequestedAt;
  final String? verificationStatus; // 'pending', 'approved', 'rejected'

  const UserStats({
    required this.followersCount,
    required this.totalViews,
    this.isVerified = false,
    this.verificationRequestedAt,
    this.verificationStatus,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      followersCount: json['followers_count'] ?? 0,
      totalViews: json['total_views'] ?? 0,
      isVerified: json['is_verified'] ?? false,
      verificationRequestedAt: json['verification_requested_at'] != null 
          ? DateTime.parse(json['verification_requested_at']) 
          : null,
      verificationStatus: json['verification_status'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'followers_count': followersCount,
      'total_views': totalViews,
      'is_verified': isVerified,
      'verification_requested_at': verificationRequestedAt?.toIso8601String(),
      'verification_status': verificationStatus,
    };
  }

  // التحقق من استيفاء شروط التوثيق
  bool get meetsFollowersRequirement => followersCount >= 50000;
  bool get meetsViewsRequirement => totalViews >= 100000;
  bool get meetsAllRequirements => meetsFollowersRequirement && meetsViewsRequirement;

  // حساب النسب المئوية
  double get followersProgress => (followersCount / 50000).clamp(0.0, 1.0);
  double get viewsProgress => (totalViews / 100000).clamp(0.0, 1.0);

  // رسائل الحالة
  String get followersStatusMessage {
    if (followersProgress >= 0.9) return 'أنت قريب من الوصول!';
    if (followersProgress >= 0.7) return 'واصل النمو لزيادة متابعيك';
    return 'واصل النشر لزيادة متابعيك';
  }

  String get viewsStatusMessage {
    if (viewsProgress >= 0.9) return 'أنت قريب من الوصول!';
    if (viewsProgress >= 0.7) return 'واصل النشر لزيادة مشاهداتك';
    return 'واصل النشر لزيادة مشاهداتك';
  }
} 