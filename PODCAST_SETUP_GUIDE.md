# دليل إعداد قسم البودكاست الصوتي 🎙️

## نظرة عامة

تم إضافة قسم البودكاست الصوتي الجديد إلى تطبيق أرزاوو بنجاح! هذا القسم يتيح للمستخدمين:

- 🎤 تسجيل البودكاستات مباشرة من التطبيق
- 📁 رفع ملفات صوتية جاهزة
- 🎨 إضافة صور غلاف مخصصة
- 📂 تصنيف البودكاستات حسب الفئات
- 💬 التعليق والتفاعل مع البودكاستات
- 💾 حفظ البودكاستات المفضلة
- 🔄 مشاركة البودكاستات

## الملفات المضافة

### النماذج (Models)
- `lib/models/podcast.dart` - نموذج البودكاست مع جميع الخصائص المطلوبة

### الخدمات (Services)
- `lib/services/podcast_service.dart` - خدمة إدارة البودكاستات

### الصفحات (Pages)
- `lib/pages/podcasts_page.dart` - الصفحة الرئيسية للبودكاستات
- `lib/pages/create_podcast_page.dart` - صفحة إنشاء بودكاست جديد
- `lib/pages/my_podcasts_page.dart` - صفحة بودكاستات المستخدم
- `lib/pages/saved_podcasts_page.dart` - صفحة البودكاستات المحفوظة
- `lib/pages/podcast_details_page.dart` - صفحة تفاصيل البودكاست

### الواجهات (Widgets)
- `lib/widgets/podcast_card.dart` - بطاقة عرض البودكاست
- `lib/widgets/podcast_skeleton.dart` - هيكل التحميل للبودكاست

### قاعدة البيانات
- `database/podcasts_setup.sql` - سكريبت إعداد جداول قاعدة البيانات

## خطوات الإعداد

### 1. إعداد قاعدة البيانات

قم بتشغيل السكريبت التالي في Supabase SQL Editor:

```sql
-- تشغيل ملف database/podcasts_setup.sql
```

هذا السكريبت سيقوم بإنشاء:
- جدول `podcasts` - البودكاستات الأساسية
- جدول `podcast_likes` - إعجابات البودكاستات
- جدول `podcast_saves` - البودكاستات المحفوظة
- جدول `podcast_plays` - سجل تشغيل البودكاستات
- جدول `podcast_comments` - تعليقات البودكاستات
- جدول `podcast_comment_likes` - إعجابات التعليقات
- الفهارس والمحفزات اللازمة
- سياسات الأمان (RLS)
- bucket التخزين للملفات الصوتية

### 2. إعداد التخزين

تأكد من إنشاء bucket جديد في Supabase Storage:
- اسم البucket: `podcasts`
- النوع: Public
- المجلدات الفرعية: `audio/` و `covers/`

### 3. الأذونات المطلوبة

تأكد من إضافة الأذونات التالية في `android/app/src/main/AndroidManifest.xml`:

```xml
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
<uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
```

وفي `ios/Runner/Info.plist`:

```xml
<key>NSMicrophoneUsageDescription</key>
<string>يحتاج التطبيق إلى الوصول للميكروفون لتسجيل البودكاستات</string>
<key>NSPhotoLibraryUsageDescription</key>
<string>يحتاج التطبيق إلى الوصول للمعرض لاختيار صور الغلاف</string>
```

## الميزات المتاحة

### 🎙️ تسجيل البودكاست
- تسجيل مباشر من التطبيق
- عرض مدة التسجيل في الوقت الفعلي
- إمكانية إيقاف وإعادة تشغيل التسجيل

### 📁 رفع الملفات
- دعم صيغ: MP3, WAV, OGG, M4A
- اكتشاف تلقائي لمدة الملف الصوتي
- معاينة الملف قبل النشر

### 🎨 التخصيص
- إضافة صورة غلاف اختيارية
- اختيار من 11 فئة مختلفة
- إضافة كلمات مفتاحية
- إعدادات الخصوصية (السماح بالتحميل/التعليقات)

### 🔍 التصفح والبحث
- تصفح حسب الفئات
- البحث في العناوين والأوصاف
- فلترة متقدمة
- ترتيب حسب الأحدث

### 🎵 مشغل الصوت
- مشغل صوتي متقدم مع شريط التقدم
- إمكانية التحكم في الموضع
- تشغيل في الخلفية
- عرض معلومات التشغيل

### 💬 التفاعل الاجتماعي
- إعجاب وحفظ البودكاستات
- نظام تعليقات متكامل
- مشاركة البودكاستات
- متابعة إحصائيات التشغيل

## الفئات المتاحة

1. **ديني** 🕌 - محتوى ديني وروحاني
2. **تطوير ذات** 🧠 - تطوير شخصي ومهني
3. **قصة** 📚 - قصص وحكايات
4. **أطفال** 👶 - محتوى للأطفال
5. **مواهب** ⭐ - عرض المواهب
6. **تعليمي** 🎓 - محتوى تعليمي
7. **أخبار** 📰 - أخبار ومستجدات
8. **ترفيه** 🎭 - محتوى ترفيهي
9. **صحة** 🏥 - نصائح صحية
10. **تقنية** 💻 - محتوى تقني
11. **أخرى** ➕ - فئات أخرى

## الاستخدام

### الوصول للقسم
يمكن الوصول لقسم البودكاست من خلال:
1. النقر على زر "+" في شريط التطبيق العلوي
2. اختيار "بودكاست صوتي" من القائمة المنسدلة

### إنشاء بودكاست جديد
1. اختر "إنشاء بودكاست" من الصفحة الرئيسية
2. سجل صوتياً أو ارفع ملف جاهز
3. أضف العنوان والوصف
4. اختر الفئة المناسبة
5. أضف صورة غلاف (اختياري)
6. اضبط الإعدادات
7. انقر "نشر"

### تصفح البودكاستات
- **الكل**: جميع البودكاستات
- **صوتياتي**: البودكاستات التي نشرتها
- **المحفوظة**: البودكاستات المحفوظة

## الأمان والخصوصية

- جميع البودكاستات محمية بسياسات RLS
- المستخدمون يمكنهم فقط تعديل/حذف بودكاستاتهم
- إمكانية التحكم في السماح بالتحميل والتعليقات
- تشفير جميع الملفات المرفوعة

## الأداء والتحسين

- تحميل تدريجي للبودكاستات (Pagination)
- تخزين مؤقت للبيانات
- ضغط الصور تلقائياً
- فهرسة قاعدة البيانات للبحث السريع

## المتطلبات التقنية

- Flutter SDK 3.7.2+
- Supabase Account
- أذونات الميكروفون والتخزين
- مساحة تخزين كافية للملفات الصوتية

## الدعم والصيانة

للحصول على الدعم أو الإبلاغ عن مشاكل:
1. تحقق من logs التطبيق
2. راجع إعدادات Supabase
3. تأكد من صحة الأذونات
4. اتصل بفريق التطوير

---

**تم إنشاء هذا القسم بواسطة Augment Agent** 🤖
**تاريخ الإنشاء**: 2025-07-29
