import 'package:flutter/material.dart';
import '../models/marriage_profile.dart';
import '../services/marriage_service.dart';

class MarriageSettingsPage extends StatefulWidget {
  final MarriageProfile profile;

  const MarriageSettingsPage({super.key, required this.profile});

  @override
  State<MarriageSettingsPage> createState() => _MarriageSettingsPageState();
}

class _MarriageSettingsPageState extends State<MarriageSettingsPage> {
  late bool _isActive;
  late bool _hideImageUntilApproval;
  late bool _hideNameUntilApproval;
  bool _loading = false;
  Map<String, int> _stats = {};

  @override
  void initState() {
    super.initState();
    _isActive = widget.profile.isActive;
    _hideImageUntilApproval = widget.profile.hideImageUntilApproval;
    _hideNameUntilApproval = widget.profile.hideNameUntilApproval;
    _loadStats();
  }

  Future<void> _loadStats() async {
    try {
      final stats = await MarriageService().getProfileStats();
      setState(() => _stats = stats);
    } catch (e) {
      // تجاهل الخطأ
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعدادات الملف'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // إحصائيات الملف
          _buildStatsSection(),
          
          const SizedBox(height: 24),
          
          // إعدادات الخصوصية
          _buildPrivacySection(),
          
          const SizedBox(height: 24),
          
          // إعدادات الملف
          _buildProfileSection(),
          
          const SizedBox(height: 24),
          
          // معلومات مهمة
          _buildInfoSection(),
          
          const SizedBox(height: 24),
          
          // أزرار الإجراءات
          _buildActionsSection(),
        ],
      ),
    );
  }

  Widget _buildStatsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.pink[600]!, Colors.pink[800]!],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: Colors.white, size: 24),
              const SizedBox(width: 8),
              Text(
                'إحصائيات الملف',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'طلبات مرسلة',
                  '${_stats['sent_requests'] ?? 0}',
                  Icons.send,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'طلبات مستلمة',
                  '${_stats['received_requests'] ?? 0}',
                  Icons.inbox,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'طلبات معلقة',
                  '${_stats['pending_requests'] ?? 0}',
                  Icons.schedule,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  'طلبات مقبولة',
                  '${_stats['accepted_requests'] ?? 0}',
                  Icons.check_circle,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 4),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.white, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPrivacySection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 10,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.privacy_tip, color: Colors.blue[600], size: 24),
              const SizedBox(width: 8),
              Text(
                'إعدادات الخصوصية',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          SwitchListTile(
            title: const Text('إخفاء الاسم حتى الموافقة'),
            subtitle: const Text('سيظهر "ذكر من المدينة" بدلاً من الاسم الحقيقي'),
            value: _hideNameUntilApproval,
            onChanged: (value) {
              setState(() => _hideNameUntilApproval = value);
              _updatePrivacySettings();
            },
            activeColor: Colors.pink[600],
          ),
          
          const Divider(),
          
          SwitchListTile(
            title: const Text('إخفاء الصورة حتى الموافقة'),
            subtitle: const Text('ستظهر أيقونة افتراضية بدلاً من الصورة الشخصية'),
            value: _hideImageUntilApproval,
            onChanged: (value) {
              setState(() => _hideImageUntilApproval = value);
              _updatePrivacySettings();
            },
            activeColor: Colors.pink[600],
          ),
        ],
      ),
    );
  }

  Widget _buildProfileSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 10,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.settings, color: Colors.green[600], size: 24),
              const SizedBox(width: 8),
              Text(
                'إعدادات الملف',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          
          SwitchListTile(
            title: const Text('تفعيل الملف'),
            subtitle: Text(_isActive 
                ? 'ملفك مرئي للآخرين ويمكنهم إرسال طلبات تواصل'
                : 'ملفك مخفي ولن يراه أحد'),
            value: _isActive,
            onChanged: (value) {
              setState(() => _isActive = value);
              _updateProfileStatus();
            },
            activeColor: Colors.green[600],
          ),
          
          const SizedBox(height: 16),
          
          // معلومات الحد اليومي
          FutureBuilder<int>(
            future: MarriageService().getTodayRequestsCount(),
            builder: (context, snapshot) {
              final count = snapshot.data ?? 0;
              return Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.blue[600]),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'استخدمت $count من 3 طلبات تواصل اليوم',
                        style: TextStyle(
                          color: Colors.blue[700],
                          fontSize: 14,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.amber[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.amber[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb_outline, color: Colors.amber[700]),
              const SizedBox(width: 8),
              Text(
                'نصائح مهمة',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: Colors.amber[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '• إعدادات الخصوصية تحميك من المتطفلين\n'
            '• يمكنك إلغاء تفعيل ملفك مؤقتاً إذا كنت مشغولاً\n'
            '• الحد اليومي يمنع الإزعاج ويضمن الجدية\n'
            '• تحديث ملفك بانتظام يزيد من فرص التواصل',
            style: TextStyle(
              color: Colors.amber[700],
              fontSize: 14,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionsSection() {
    return Column(
      children: [
        // زر حذف الملف
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _deleteProfile,
            icon: const Icon(Icons.delete_forever),
            label: const Text('حذف الملف نهائياً'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.red[700],
              side: BorderSide(color: Colors.red[300]!),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        
        const SizedBox(height: 12),
        
        // معلومات الحذف
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.red[50],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.red[200]!),
          ),
          child: Row(
            children: [
              Icon(Icons.warning_outlined, color: Colors.red[600], size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'حذف الملف سيؤدي إلى فقدان جميع البيانات والطلبات نهائياً',
                  style: TextStyle(
                    color: Colors.red[700],
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _updatePrivacySettings() async {
    if (_loading) return;
    
    setState(() => _loading = true);
    
    try {
      final updatedProfile = widget.profile.copyWith(
        hideImageUntilApproval: _hideImageUntilApproval,
        hideNameUntilApproval: _hideNameUntilApproval,
      );
      
      await MarriageService().updateProfile(widget.profile.id, updatedProfile);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث إعدادات الخصوصية'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحديث الإعدادات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _loading = false);
      }
    }
  }

  Future<void> _updateProfileStatus() async {
    if (_loading) return;
    
    setState(() => _loading = true);
    
    try {
      await MarriageService().toggleProfileStatus(widget.profile.id, _isActive);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isActive ? 'تم تفعيل الملف' : 'تم إلغاء تفعيل الملف'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isActive = !_isActive); // إرجاع الحالة السابقة
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في تحديث حالة الملف: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _loading = false);
      }
    }
  }

  Future<void> _deleteProfile() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف الملف نهائياً'),
        content: const Text(
          'هل أنت متأكد من حذف ملفك الشخصي نهائياً؟\n\n'
          'سيتم حذف:\n'
          '• جميع بياناتك الشخصية\n'
          '• جميع طلبات التواصل\n'
          '• جميع الإحصائيات\n\n'
          'لا يمكن التراجع عن هذا الإجراء!'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف نهائياً', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() => _loading = true);

    try {
      await MarriageService().deleteProfile(widget.profile.id);
      
      if (mounted) {
        Navigator.pop(context, true); // العودة مع إشارة الحذف
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف الملف نهائياً'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في حذف الملف: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _loading = false);
      }
    }
  }
}
