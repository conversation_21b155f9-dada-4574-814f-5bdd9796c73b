import 'package:flutter/material.dart';
import 'models/post.dart';
import 'widgets/comments_sheet.dart';
import 'pages/profile_page.dart';
import 'pages/chat_page.dart';
import 'pages/notifications_page.dart';
import 'pages/users_page.dart';
import 'pages/chats_page.dart';
import 'pages/my_spaces_page.dart';
import 'pages/jobs_page.dart';
import 'pages/marriage_page.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'supabase_config.dart';
import 'supabase_service.dart';
import 'widgets/new_post_sheet.dart';
import 'widgets/feed_media.dart';
import 'widgets/share_post_sheet.dart';
import 'widgets/facebook_menu_overlay.dart';
import 'pages/app_settings_page.dart';
import 'dart:async';
import 'models/app_notification.dart';
import 'pages/products_page.dart';
import 'pages/real_estate_page.dart';
import 'package:app_links/app_links.dart';
import 'package:url_launcher/url_launcher.dart';
import 'widgets/connectivity_banner.dart';
import 'widgets/post_card.dart';
import 'pages/videos_page.dart';
import 'pages/community_page.dart';
import 'pages/polls_page.dart';
import 'pages/products_page.dart';
import 'pages/notes_page.dart';
import 'pages/job_seekers_page.dart';
import 'widgets/stories_bar.dart';
import 'widgets/post_skeleton.dart';
import 'widgets/follow_suggestions_card.dart';
import 'app_theme.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'pages/suggest_users_page.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

// متغير مراقِب لنمط الثيم
final ValueNotifier<ThemeMode> themeModeNotifier = ValueNotifier(ThemeMode.light);
final ValueNotifier<Locale> localeNotifier = ValueNotifier(const Locale('ar', '')); // NEW: يتحكم فى لغة التطبيق

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Hive.initFlutter();
  await Hive.openBox('cache');
  // تحميل تفضيل الثيم قبل تشغيل التطبيق
  final prefs = await SharedPreferences.getInstance();
  final saved = prefs.getString('theme_mode');
  if (saved == 'dark') {
    themeModeNotifier.value = ThemeMode.dark;
  } else if (saved == 'system') {
    themeModeNotifier.value = ThemeMode.system;
  }
  final savedLang = prefs.getString('locale'); // NEW: تحميل اللغة المحفوظة
  if (savedLang == 'en') {
    localeNotifier.value = const Locale('en', '');
  } else {
    localeNotifier.value = const Locale('ar', '');
  }
  await Supabase.initialize(
    url: SupabaseConfig.url,
    anonKey: SupabaseConfig.anonKey,
  );
  // استماع حي لملفات المستخدمين
  SupabaseService().initProfilesListener();
  runApp(const MyApp());
}

// احصل على عميل Supabase بسهولة
final supabase = Supabase.instance.client;

// FIRST_EDIT: introduce primary brand color
const Color kPrimaryColor = Color(0xFFC62828); // Dark red tone

// Logo asset path
const String kLogoAsset = 'assets/preload_icon.png';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<ThemeMode>(
      valueListenable: themeModeNotifier,
      builder: (context, mode, _) {
        return ValueListenableBuilder<Locale>( // NEW: إعادة البناء عند تغيير اللغة
          valueListenable: localeNotifier,
          builder: (context, loc, __) {
            return Directionality(
              textDirection: TextDirection.rtl,
              child: ConnectivityBanner(
                child: MaterialApp(
                  debugShowCheckedModeBanner: false,
                  title: 'أرزاوو',
                  theme: AppTheme.lightTheme,
                  darkTheme: AppTheme.darkTheme,
                  themeMode: mode,
                  locale: loc, // NEW: اللغة المختارة
                  supportedLocales: const [Locale('ar', ''), Locale('en', '')],
                  localizationsDelegates: GlobalMaterialLocalizations.delegates,
                  home: const _SplashScreen(),
                  builder: (context, child) => Directionality(textDirection: TextDirection.rtl, child: child!),
                ),
              ),
            );
          },
        );
      },
    );
  }
}

// شاشة تحميل مبدئية
class _SplashScreen extends StatefulWidget {
  const _SplashScreen({super.key});

  @override
  State<_SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<_SplashScreen> with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(vsync: this, duration: const Duration(seconds: 2))..forward();

    _processInitialUri();

    Future.wait([
      Future.delayed(const Duration(milliseconds: 2500)), // محاكاة تحميل الموارد
    ]).then((_) async {
      if (!mounted) return;
      // إذا تم استبدال الشاشة بالفعل بسبب رابط التفعيل فلا تتابع
      if (Navigator.of(context).canPop()) return;

      final user = Supabase.instance.client.auth.currentUser;
      Widget next;
      if (user == null) {
        next = const LoginPage();
      } else {
        final prefs = await SharedPreferences.getInstance();
        final seen = prefs.getBool('suggestions_done') ?? false;
        next = seen ? const HomePage() : const SuggestUsersPage();
      }
      Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (_) => next));
    });
  }

  // معالجة الرابط عند الإطلاق البارد (ويب)
  void _processInitialUri() {
    final uri = Uri.base; // مثال: https://arzawo.com/welcome#access_token=..&refresh_token=..&type=signup
    if (uri.fragment.isEmpty) return;
    final frag = Uri.splitQueryString(uri.fragment);
    final accessToken = frag['access_token'];
    final refreshToken = frag['refresh_token'];
    final type = frag['type'];
    if (accessToken != null && refreshToken != null) {
      Navigator.of(context).pushReplacement(MaterialPageRoute(
        builder: (_) => AuthCallbackPage(
          accessToken: accessToken,
          refreshToken: refreshToken,
          actionType: type,
        ),
      ));
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return Scaffold(
      body: Container(
        width: size.width,
        height: size.height,
        color: Colors.white,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(kLogoAsset, height: 140),
            const SizedBox(height: 24),
            const Text(
              'جاري تحميل أرزاوو...',
              style: TextStyle(color: Colors.red, fontSize: 18, fontWeight: FontWeight.w500),
            ),
          ],
        ),
      ),
    );
  }
}

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              kPrimaryColor.withOpacity(0.6),
              kPrimaryColor,
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              spreadRadius: 5,
                              blurRadius: 15,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            Image.asset(kLogoAsset, height: 72),
                            const SizedBox(height: 12),
                            Text(
                              'أرزاوو',
                              style: TextStyle(
                                fontSize: 40,
                                fontWeight: FontWeight.bold,
                                color: kPrimaryColor,
                              ),
                            ),
                            const SizedBox(height: 30),
                            TextFormField(
                              controller: _emailController,
                              textAlign: TextAlign.right,
                              decoration: InputDecoration(
                                labelText: 'البريد الإلكتروني',
                                hintText: 'أدخل بريدك الإلكتروني',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  borderSide: BorderSide(color: kPrimaryColor.withOpacity(0.3)),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  borderSide: BorderSide(color: kPrimaryColor, width: 2),
                                ),
                                prefixIcon: Icon(Icons.email, color: kPrimaryColor),
                                filled: true,
                                fillColor: kPrimaryColor.withOpacity(0.05),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال البريد الإلكتروني';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 20),
                            TextFormField(
                              controller: _passwordController,
                              textAlign: TextAlign.right,
                              obscureText: true,
                              decoration: InputDecoration(
                                labelText: 'كلمة المرور',
                                hintText: 'أدخل كلمة المرور',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  borderSide: BorderSide(color: kPrimaryColor.withOpacity(0.3)),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  borderSide: BorderSide(color: kPrimaryColor, width: 2),
                                ),
                                prefixIcon: Icon(Icons.lock, color: kPrimaryColor),
                                filled: true,
                                fillColor: kPrimaryColor.withOpacity(0.05),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال كلمة المرور';
                                }
                                return null;
                              },
                            ),
                            Align(
                              alignment: Alignment.centerRight,
                              child: TextButton(
                                onPressed: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => const ForgotPasswordPage(),
                                    ),
                                  );
                                },
                                child: Text(
                                  'نسيت كلمة المرور؟',
                                  style: TextStyle(
                                    color: kPrimaryColor,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(height: 20),
                            SizedBox(
                              width: double.infinity,
                              height: 55,
                              child: ElevatedButton(
                                onPressed: () async {
                                  if (_formKey.currentState!.validate()) {
                                    final email = _emailController.text.trim();
                                    final pass = _passwordController.text.trim();
                                    try {
                                      await SupabaseService().signIn(email: email, password: pass);
                                      // إذا نجح الانتقال للصفحة الرئيسية
                                      if (mounted) {
                                        Navigator.pushReplacement(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => const HomePage(),
                                          ),
                                        );
                                      }
                                    } catch (e) {
                                      if (mounted) {
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(content: Text('فشل تسجيل الدخول: $e')),
                                        );
                                      }
                                    }
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: kPrimaryColor,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(15),
                                  ),
                                  elevation: 5,
                                ),
                                child: const Text(
                                  'تسجيل الدخول',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            'ليس لديك حساب؟',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const SignUpPage(),
                                ),
                              );
                            },
                            child: const Text(
                              'إنشاء حساب',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  final GlobalKey<_FeedContentState> _feedKey = GlobalKey<_FeedContentState>();

  int _selectedIndex = 0;
  int _unread = 0;
  late final PageController _pageController = PageController();
  late final Stream<List<AppNotification>> _notifStream = SupabaseService().notificationsStream();
  late final AppLinks _appLinks = AppLinks();
  StreamSubscription? _sub;
  StreamSubscription? _linkSub;

  late final List<Widget> _pages = [
    FeedContent(key: _feedKey),
    const UsersPage(),
    const PollsPage(), // نبض - التصويتات
    const MySpacesPage(),
    const VideosPage(),
    const CommunityPage(),
    const NotificationsPage(),
    const AppSettingsPage(), // صفحة الإعدادات
  ];

  @override
  void initState() {
    super.initState();
    _loadUnread();

    // استماع لروابط deep link
    _linkSub = _appLinks.uriLinkStream.listen((Uri? uri) {
      _handleUri(uri);
    }, onError: (_) {});

    // معالجة الرابط عند الإطلاق البارد
    _appLinks.getInitialLink().then(_handleUri).catchError((_) {});

    _sub = _notifStream.listen((list) {
      final c = list.where((n) => !(n.read)).length;
      if (mounted) setState(() => _unread = c);
    });
  }

  @override
  void dispose() {
    _sub?.cancel();
    _linkSub?.cancel();
    _pageController.dispose();
    super.dispose();
  }

  Future<void> _loadUnread() async {
    final c = await SupabaseService().unreadNotificationsCount();
    setState(() => _unread = c);
  }

  // عرض قائمة الوظائف بنمط Facebook
  void _showFacebookStyleMenu(BuildContext buttonContext) {
    FacebookMenuOverlay.show(
      buttonContext,
      onRefresh: () => _feedKey.currentState?.refresh(),
    );
  }

  // عرض قائمة الوظائف (القديمة)
  void _showFunctionsMenu() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مؤشر السحب
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),

            // الأقسام الأربعة بتصميم احترافي
            Column(
              children: [
                // الصف الأول
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // قسم الوظائف
                    _buildProfessionalNavItem(
                      icon: Icons.work_rounded,
                      title: 'الوظائف',
                      color: Colors.blue,
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const JobsPage()),
                        );
                      },
                    ),

                    // قسم العقارات
                    _buildProfessionalNavItem(
                      icon: Icons.home_rounded,
                      title: 'العقارات',
                      color: Colors.green,
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const RealEstatePage()),
                        );
                      },
                    ),

                    // قسم المتجر
                    _buildProfessionalNavItem(
                      icon: Icons.storefront_rounded,
                      title: 'المتجر',
                      color: Colors.orange,
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const ProductsPage()),
                        );
                      },
                    ),
                  ],
                ),

                const SizedBox(height: 20),

                // الصف الثاني
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    // قسم بيت الحلال
                    _buildProfessionalNavItem(
                      icon: Icons.favorite_rounded,
                      title: 'بيت الحلال',
                      color: Colors.pink,
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const MarriagePage()),
                        );
                      },
                    ),

                    // قسم المذكرة
                    _buildProfessionalNavItem(
                      icon: Icons.note_rounded,
                      title: 'المذكرة',
                      color: Colors.deepPurple,
                      onTap: () {
                        Navigator.pop(context);
                        _pageController.animateToPage(6, duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
                        setState(() => _selectedIndex = 6);
                      },
                    ),

                    // قسم البحث عن عمل
                    _buildProfessionalNavItem(
                      icon: Icons.person_search_rounded,
                      title: 'البحث عن عمل',
                      color: Colors.indigo,
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(
                          context,
                          MaterialPageRoute(builder: (_) => const JobSeekersPage()),
                        );
                      },
                    ),

                    // قسم إضافي جديد (يمكن تخصيصه لاحقاً)
                    _buildProfessionalNavItem(
                      icon: Icons.explore_rounded,
                      title: 'استكشاف',
                      color: Colors.teal,
                      onTap: () {
                        Navigator.pop(context);
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('قسم الاستكشاف قريباً...'),
                            backgroundColor: Colors.teal,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // بناء عنصر تنقل احترافي ملون (حجم مصغر)
  Widget _buildProfessionalNavItem({
    required IconData icon,
    required String title,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 80,
        height: 80,
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.2),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.4),
                    spreadRadius: 1,
                    blurRadius: 3,
                  ),
                ],
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(height: 6),
            Text(
              title,
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final bool showFab = _selectedIndex == 0;
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        title: const Text(''),
        automaticallyImplyLeading: false,
        actionsIconTheme: const IconThemeData(color: Colors.black87),
        actions: [
          // زر الوظائف الجديد
          Builder(
            builder: (buttonContext) => IconButton(
              icon: const Icon(Icons.add_circle_outline),
              onPressed: () => _showFacebookStyleMenu(buttonContext),
              tooltip: 'الوظائف',
            ),
          ),
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.notifications_rounded),
                onPressed: () {
                  _pageController.animateToPage(6, duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
                  setState(() => _selectedIndex = 6);
                },
              ),
              if (_unread > 0)
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    width: 10,
                    height: 10,
                    decoration: const BoxDecoration(shape: BoxShape.circle, color: kPrimaryColor),
                  ),
                ),
            ],
          ),
          IconButton(
            icon: const Icon(Icons.chat_bubble_rounded),
            onPressed: () {
              Navigator.push(context, MaterialPageRoute(builder: (_) => const ChatsPage()));
            },
          ),
          IconButton(
            icon: const Icon(Icons.person_rounded),
            onPressed: () {
              final uid = Supabase.instance.client.auth.currentUser?.id;
              if (uid != null) {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (_) => ProfilePage(userId: uid, username: '')),
                );
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              _pageController.animateToPage(7, duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
              setState(() => _selectedIndex = 7);
            },
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: PageView(
        controller: _pageController,
        physics: const BouncingScrollPhysics(),
        onPageChanged: (i) => setState(() => _selectedIndex = i),
        children: _pages,
      ),
      floatingActionButton: showFab
          ? Container(
              decoration: const BoxDecoration(shape: BoxShape.circle, color: kPrimaryColor),
              child: FloatingActionButton(
                backgroundColor: kPrimaryColor,
                onPressed: () async {
                  await showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
                    ),
                    builder: (context) => const NewPostSheet(),
                  );
                  _feedKey.currentState?.refresh();
                },
                child: const Icon(Icons.add, color: Colors.white, size: 32),
              ),
            )
          : null,
      floatingActionButtonLocation: showFab ? FloatingActionButtonLocation.centerDocked : null,
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              blurRadius: 10,
              spreadRadius: 0,
            ),
          ],
        ),
        child: BottomAppBar(
          height: 70,
          elevation: 0,
          notchMargin: showFab ? 8 : 0,
          shape: showFab ? const CircularNotchedRectangle() : null,
          color: Colors.white,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(0, Icons.home_rounded, 'الرئيسية', Colors.blue),
              _buildNavItem(1, Icons.people_alt_rounded, 'المستخدمين', Colors.green),
              _buildNavItem(2, Icons.poll_rounded, 'نبض', Colors.purple),
              if (showFab) const SizedBox(width: 70),
              _buildNavItem(3, Icons.space_dashboard_rounded, 'مساحتي', Colors.indigo),
              _buildNavItem(4, Icons.play_circle_fill_rounded, 'الفيديوهات', Colors.red),
              _buildNavItem(5, Icons.people_rounded, 'المجتمع', Colors.teal),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem(int index, IconData icon, String label, Color color, {int badge = 0}) {
    final isSelected = _selectedIndex == index;
    return InkWell(
      onTap: () {
        _pageController.animateToPage(index, duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
        setState(() => _selectedIndex = index);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Stack(
              clipBehavior: Clip.none,
              children: [
                // أيقونة احترافية مع خلفية ملونة عند التحديد
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: isSelected ? BoxDecoration(
                    color: color.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(10),
                    border: Border.all(color: color.withValues(alpha: 0.3)),
                  ) : null,
                  child: Icon(
                    icon,
                    color: isSelected ? color : Colors.grey[600],
                    size: 22,
                  ),
                ),
                if (badge > 0)
                  Positioned(
                    top: -2,
                    right: -2,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(color: color, shape: BoxShape.circle),
                      constraints: const BoxConstraints(minWidth: 14, minHeight: 14),
                      child: Text('$badge', style: const TextStyle(color: Colors.white, fontSize: 9), textAlign: TextAlign.center),
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? color : Colors.grey[600],
                fontSize: 10,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _handleUri(Uri? uri) {
    if (uri == null) return;
    // مثال: arzapress://auth-callback?access_token=...&type=signup
    if (uri.scheme == 'arzapress' && uri.host == 'auth-callback') {
      final accessToken = uri.queryParameters['access_token'];
      final refreshToken = uri.queryParameters['refresh_token'];
      if (accessToken != null && refreshToken != null) {
        Supabase.instance.client.auth.recoverSession(accessToken);
        // يمكنك استخدام supabase.auth.exchangeCodeForSession إذا كنت تستخدم auth code.
        setState(() {});
      }
    }
  }
}

// صفحة إضافة منشور جديد

// صفحة المنشورات (الرئيسية)
class FeedPage extends StatefulWidget {
  const FeedPage({super.key});

  @override
  State<FeedPage> createState() => _FeedPageState();
}

class _FeedPageState extends State<FeedPage> {
  final int _currentIndex = 0;
  final List<Widget> _pages = [
    const FeedContent(),
    const UsersPage(),
    const ChatsPage(),
    const NotificationsPage(),
  ];

  @override
  Widget build(BuildContext context) {
    return const FeedContent();
  }
}

class FeedContent extends StatefulWidget {
  const FeedContent({super.key});

  @override
  State<FeedContent> createState() => _FeedContentState();
}

class _FeedContentState extends State<FeedContent> {
  // تنفيذ جديد: تحميل مبدئى + Pagination + Skeleton + رسائل أخطاء
  final int _limit = 20;
  final ScrollController _controller = ScrollController();
  final List<Post> _posts = [];
  bool _loading = true;
  bool _loadingMore = false;
  bool _hasMore = true;
  String? _error;
  bool _showSuggestions = true;

  @override
  void initState() {
    super.initState();
    _controller.addListener(_onScroll);
    _loadInitial();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _loadInitial() async {
    setState(() {
      _loading = true;
      _error = null;
    });

    final connectivity = await Connectivity().checkConnectivity();
    final offline = connectivity == ConnectivityResult.none;

    if (offline) {
      // قراءة من الكاش
      final box = Hive.box('cache');
      final cached = box.get('posts') as List?;
      if (cached != null) {
        final list = cached.map((e) => Post.fromMap(Map<String, dynamic>.from(e))).toList();
        setState(() {
          _posts
            ..clear()
            ..addAll(list);
          _hasMore = false;
        });
      }
      if (mounted) setState(() => _loading = false);
      return;
    }

    try {
      final list = await SupabaseService().fetchPostsPaginated(limit: _limit, offset: 0);
      setState(() {
        _posts
          ..clear()
          ..addAll(list);
        _hasMore = list.length == _limit;
      });

      // تخزين فى الكاش
      final box = Hive.box('cache');
      box.put('posts', list.map((p) => p.toMap()).toList());
    } catch (e, st) {
      // سجّل الخطأ تفصيليًا للمطور
      print('❌ خطأ أثناء جلب المنشورات: $e\n$st');
      setState(() => _error = 'فشل جلب المنشورات: $e');
    } finally {
      if (mounted) setState(() => _loading = false);
    }
  }

  Future<void> _loadMore() async {
    if (_loadingMore || !_hasMore) return;
    setState(() => _loadingMore = true);
    try {
      final list = await SupabaseService().fetchPostsPaginated(limit: _limit, offset: _posts.length);
      setState(() {
        _posts.addAll(list);
        _hasMore = list.length == _limit;
      });
    } catch (e, st) {
      print('❌ خطأ أثناء تحميل المزيد من المنشورات: $e\n$st');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('حدث خطأ أثناء تحميل المزيد: $e')));
      }
    } finally {
      if (mounted) setState(() => _loadingMore = false);
    }
  }

  void _onScroll() {
    if (_controller.position.pixels >= _controller.position.maxScrollExtent - 300) {
      _loadMore();
    }
  }

  Future<void> _onRefresh() async {
    await _loadInitial();
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم تحديث المحتوى')),
      );
    }
  }

  void refresh() => _loadInitial();

  @override
  Widget build(BuildContext context) {
        final offline = ConnectivityBannerExt.isOffline(context);
    if (_loading || offline) {
          return ListView.builder(
            padding: EdgeInsets.zero, // إزالة الهوامش
            itemCount: 6,
        itemBuilder: (_, __) => const PostSkeleton(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(_error!, textAlign: TextAlign.center),
            const SizedBox(height: 16),
            ElevatedButton(onPressed: _loadInitial, child: const Text('إعادة المحاولة')),
          ],
        ),
      );
    }

        return RefreshIndicator(
        onRefresh: _onRefresh,
           child: Builder(builder: (context) {
             final bool showSugg = _showSuggestions;
             final int extra = 1 + (showSugg ? 1 : 0);
             return ListView.builder(
               controller: _controller,
               padding: EdgeInsets.zero, // إزالة الهوامش
               itemCount: _posts.length + extra + 1,
            itemBuilder: (context, index) {
              if (index == 0) return const StoriesBar();
              if (showSugg && index == 1) {
                return FollowSuggestionsCard(onClose: () => setState(() => _showSuggestions = false));
              }
              if (index == _posts.length + extra) {
                if (_loadingMore) {
                  return const Padding(
                    padding: EdgeInsets.symmetric(vertical: 16),
                    child: Center(child: CircularProgressIndicator()),
                  );
                }
                return const SizedBox.shrink();
              }
               final int offset = 1 + (showSugg ? 1 : 0);
               final post = _posts[index - offset];
              return PostCard(post: post, onRefresh: refresh);
            },
          );
        }),
      );
  }
}

class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});

  @override
  State<SignUpPage> createState() => _SignUpPageState();
}

class _SignUpPageState extends State<SignUpPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              kPrimaryColor.withOpacity(0.6),
              kPrimaryColor,
            ],
          ),
        ),
        child: SafeArea(
          child: Center(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.2),
                              spreadRadius: 5,
                              blurRadius: 15,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            Image.asset(kLogoAsset, height: 64),
                            const SizedBox(height: 12),
                            Text(
                              'إنشاء حساب',
                              style: TextStyle(
                                fontSize: 32,
                                fontWeight: FontWeight.bold,
                                color: kPrimaryColor,
                              ),
                            ),
                            const SizedBox(height: 30),
                            TextFormField(
                              controller: _nameController,
                              textAlign: TextAlign.right,
                              decoration: InputDecoration(
                                labelText: 'الاسم',
                                hintText: 'أدخل اسمك',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  borderSide: BorderSide(color: kPrimaryColor.withOpacity(0.3)),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  borderSide: BorderSide(color: kPrimaryColor, width: 2),
                                ),
                                prefixIcon: Icon(Icons.person, color: kPrimaryColor),
                                filled: true,
                                fillColor: kPrimaryColor.withOpacity(0.05),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال الاسم';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 20),
                            TextFormField(
                              controller: _emailController,
                              textAlign: TextAlign.right,
                              decoration: InputDecoration(
                                labelText: 'البريد الإلكتروني',
                                hintText: 'أدخل بريدك الإلكتروني',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  borderSide: BorderSide(color: kPrimaryColor.withOpacity(0.3)),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  borderSide: BorderSide(color: kPrimaryColor, width: 2),
                                ),
                                prefixIcon: Icon(Icons.email, color: kPrimaryColor),
                                filled: true,
                                fillColor: kPrimaryColor.withOpacity(0.05),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال البريد الإلكتروني';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 20),
                            TextFormField(
                              controller: _passwordController,
                              textAlign: TextAlign.right,
                              obscureText: true,
                              decoration: InputDecoration(
                                labelText: 'كلمة المرور',
                                hintText: 'أدخل كلمة المرور',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  borderSide: BorderSide(color: kPrimaryColor.withOpacity(0.3)),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(15),
                                  borderSide: BorderSide(color: kPrimaryColor, width: 2),
                                ),
                                prefixIcon: Icon(Icons.lock, color: kPrimaryColor),
                                filled: true,
                                fillColor: kPrimaryColor.withOpacity(0.05),
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'الرجاء إدخال كلمة المرور';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 30),
                            SizedBox(
                              width: double.infinity,
                              height: 55,
                              child: ElevatedButton(
                                onPressed: () async {
                                  if (_formKey.currentState!.validate()) {
                                    final name = _nameController.text.trim();
                                    final email = _emailController.text.trim();
                                    final pass = _passwordController.text.trim();
                                    try {
                                      final res = await SupabaseService().signUp(name: name, email: email, password: pass);
                                      if (!mounted) return;

                                      if (res.session != null) {
                                        // تم إنشاء الجلسة مباشرة (ربما لأن التحقق معطَّل)
                                        Navigator.pushReplacement(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => const HomePage(),
                                          ),
                                        );
                                      } else {
                                        // يحتاج المستخدم لتفعيل البريد أولاً
                                        Navigator.pushReplacement(
                                          context,
                                          MaterialPageRoute(
                                            builder: (context) => const EmailVerificationPage(),
                                          ),
                                        );
                                      }
                                    } catch (e) {
                                      if (mounted) {
                                        ScaffoldMessenger.of(context).showSnackBar(
                                          SnackBar(content: Text('فشل إنشاء الحساب: $e')),
                                        );
                                      }
                                    }
                                  }
                                },
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: kPrimaryColor,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(15),
                                  ),
                                  elevation: 5,
                                ),
                                child: const Text(
                                  'إنشاء حساب',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 20),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const Text(
                            'لديك حساب بالفعل؟',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                            ),
                          ),
                          TextButton(
                            onPressed: () {
                              Navigator.pushReplacement(
                                context,
                                MaterialPageRoute(
                                  builder: (context) => const LoginPage(),
                                ),
                              );
                            },
                            child: const Text(
                              'تسجيل الدخول',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                decoration: TextDecoration.underline,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class ForgotPasswordPage extends StatefulWidget {
  const ForgotPasswordPage({super.key});

  @override
  State<ForgotPasswordPage> createState() => _ForgotPasswordPageState();
}

class _ForgotPasswordPageState extends State<ForgotPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('استعادة كلمة المرور'),
        centerTitle: true,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
            colors: [
              kPrimaryColor.withOpacity(0.6),
              kPrimaryColor,
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.1),
                          spreadRadius: 5,
                          blurRadius: 15,
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Image.asset(kLogoAsset, height: 56),
                        const SizedBox(height: 8),
                        const Text(
                          'أدخل بريدك الإلكتروني لاستعادة كلمة المرور',
                          textAlign: TextAlign.center,
                          style: TextStyle(fontSize: 16),
                        ),
                        const SizedBox(height: 30),
                        TextFormField(
                          controller: _emailController,
                          textAlign: TextAlign.right,
                          decoration: InputDecoration(
                            labelText: 'البريد الإلكتروني',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(10),
                            ),
                            prefixIcon: const Icon(Icons.email),
                            filled: true,
                            fillColor: Colors.grey[50],
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'الرجاء إدخال البريد الإلكتروني';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: 30),
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: ElevatedButton(
                            onPressed: () async {
                              if (_formKey.currentState!.validate()) {
                                final email = _emailController.text.trim();
                                try {
                                  await SupabaseService().sendPasswordResetEmail(email);
                                  if (mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(content: Text('تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني')),
                                    );
                                    Navigator.pop(context);
                                  }
                                } catch (e) {
                                  if (mounted) {
                                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('فشل الإرسال: $e')));
                                  }
                                }
                              }
                            },
                            style: ElevatedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                            child: const Text(
                              'إرسال رابط الاستعادة',
                              style: TextStyle(fontSize: 18),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// صفحة إعلام المستخدم بضرورة التحقق من البريد الإلكتروني
class EmailVerificationPage extends StatelessWidget {
  const EmailVerificationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفعيل الحساب'),
        centerTitle: true,
        backgroundColor: Colors.white,
        foregroundColor: kPrimaryColor,
        elevation: 1,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.mark_email_read, size: 80, color: kPrimaryColor),
              const SizedBox(height: 24),
              const Text(
                'لقد أرسلنا رسالة إلى بريدك الإلكتروني تحتوي على رابط التفعيل.\n\nمن فضلك افتح الرسالة واضغط على الرابط، ثم عد لتسجيل الدخول.',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18),
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(builder: (_) => const LoginPage()),
                      (route) => false,
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: kPrimaryColor,
                    foregroundColor: Colors.white,
                    minimumSize: const Size(0, 48),
                  ),
                  child: const Text('العودة لتسجيل الدخول'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// صفحة تتعامل مع رابط التفعيل / إعادة التعيين
class AuthCallbackPage extends StatefulWidget {
  final String accessToken;
  final String refreshToken;
  final String? actionType; // signup | recovery | email_change

  const AuthCallbackPage({super.key, required this.accessToken, required this.refreshToken, this.actionType});

  @override
  State<AuthCallbackPage> createState() => _AuthCallbackPageState();
}

class _AuthCallbackPageState extends State<AuthCallbackPage> {
  bool _processing = true;

  @override
  void initState() {
    super.initState();
    _handleAction();
  }

  Future<void> _handleAction() async {
    try {
      await Supabase.instance.client.auth.recoverSession(widget.accessToken);

      // تأكد من وجود صف الملف الشخصي وتحديث الاسم بعد الاستعادة
      final u = Supabase.instance.client.auth.currentUser;
      if (u != null) await SupabaseService().ensureProfileExists(u);

      // بعد استعادة الجلسة نفّذ الإجراء المناسب
      if (!mounted) return;

      if (widget.actionType == 'recovery') {
        Navigator.pushReplacement(context, MaterialPageRoute(builder: (_) => const PasswordResetPage()));
      } else {
        // signup أو email_change
        Navigator.pushReplacement(context, MaterialPageRoute(builder: (_) => const HomePage()));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('خطأ في التحقق: $e')));
        Navigator.pushReplacement(context, MaterialPageRoute(builder: (_) => const LoginPage()));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: _processing ? const CircularProgressIndicator() : const SizedBox.shrink(),
      ),
    );
  }
}

// صفحة تعيين كلمة مرور جديدة بعد recovery
class PasswordResetPage extends StatefulWidget {
  const PasswordResetPage({super.key});

  @override
  State<PasswordResetPage> createState() => _PasswordResetPageState();
}

class _PasswordResetPageState extends State<PasswordResetPage> {
  final _formKey = GlobalKey<FormState>();
  final _pass1 = TextEditingController();
  final _pass2 = TextEditingController();
  bool _saving = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('تعيين كلمة مرور جديدة')),
      body: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextFormField(
                controller: _pass1,
                obscureText: true,
                decoration: const InputDecoration(labelText: 'كلمة المرور الجديدة'),
                validator: (v) => (v == null || v.length < 6) ? 'الحد الأدنى 6 أحرف' : null,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _pass2,
                obscureText: true,
                decoration: const InputDecoration(labelText: 'تأكيد كلمة المرور'),
                validator: (v) => v != _pass1.text ? 'الكلمتان غير متطابقتين' : null,
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: _saving
                      ? null
                      : () async {
                          if (_formKey.currentState!.validate()) {
                            setState(() => _saving = true);
                            try {
                              await SupabaseService().updatePassword(_pass1.text.trim());
                              if (!mounted) return;
                              ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم تحديث كلمة المرور بنجاح')));
                              Navigator.pushAndRemoveUntil(context, MaterialPageRoute(builder: (_) => const HomePage()), (_) => false);
                            } catch (e) {
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('فشل التحديث: $e')));
                              }
                            } finally {
                              if (mounted) setState(() => _saving = false);
                            }
                          }
                        },
                  child: const Text('حفظ'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
