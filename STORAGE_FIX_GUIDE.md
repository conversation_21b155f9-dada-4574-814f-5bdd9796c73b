# دليل إصلاح مشكلة رفع الصور

## 🚨 المشكلة
عند رفع الصور تظهر رسالة خطأ:
```
StorageException(message: new row violates row-level security policy, statusCode: 403, error: Unauthorized)
```

## 🔧 الحل

### الخطوة 1: تشغيل سكريبت إصلاح سياسات التخزين

1. اذهب إلى لوحة Supabase
2. افتح **SQL Editor**
3. انسخ والصق محتوى ملف `supabase/fix_storage_policies.sql`
4. اضغط **Run**

### الخطوة 2: التحقق من إعدادات Storage

1. اذهب إلى **Storage** في لوحة Supabase
2. تأكد من وجود bucket اسمه `community-images`
3. تأكد من أن الـ bucket **عام (Public)**

### الخطوة 3: التحقق من السياسات

في SQL Editor، شغل هذا الاستعلام للتحقق من السياسات:

```sql
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'objects' AND schemaname = 'storage';
```

يجب أن ترى 4 سياسات:
- `Public can view community images`
- `Authenticated users can upload community images`
- `Authenticated users can update community images`
- `Authenticated users can delete community images`

### الخطوة 4: إعادة بناء التطبيق

```bash
flutter clean
flutter pub get
flutter build apk --release
```

## 🔍 استكشاف الأخطاء

### إذا استمرت المشكلة:

1. **تحقق من تسجيل الدخول**: تأكد من أن المستخدم مسجل دخول
2. **تحقق من الملكية**: تأكد من أن المستخدم هو مالك المجتمع
3. **تحقق من الشبكة**: تأكد من وجود اتصال إنترنت

### رسائل خطأ شائعة:

- **"خطأ في الصلاحيات"**: شغل سكريبت إصلاح السياسات
- **"غير مصرح"**: تحقق من تسجيل الدخول
- **"المجتمع غير موجود"**: تحقق من ID المجتمع
- **"فقط مالك المجتمع"**: تحقق من ملكية المجتمع

## 📝 ملاحظات مهمة

- الصور يتم رفعها في مجلد منفصل لكل مجتمع
- أسماء الملفات تحتوي على timestamp لتجنب التضارب
- الصور القديمة لا يتم حذفها تلقائياً (لتوفير مساحة)
- حجم الصور محدود (400x400 للشخصية، 1200x600 للغلاف)

## ✅ التحقق من نجاح الإصلاح

بعد تطبيق الإصلاح:
1. افتح التطبيق
2. اذهب لإعدادات أي مجتمع تملكه
3. اضغط على تبويب "الصور"
4. جرب رفع صورة شخصية أو غلاف
5. يجب أن تظهر رسالة "تم رفع الصورة بنجاح"
