-- =============================================================
--  التحقق من نجاح الحل النهائي
--  Verify Ultimate Fix Success
-- =============================================================

-- هذا الاستعلام يتحقق من أن الحل النهائي نجح

-- 1) فحص bucket شامل
-- -------------------------------------------------------

SELECT 
  '🔍 BUCKET VERIFICATION' as check_type,
  CASE 
    WHEN NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images')
    THEN '❌ CRITICAL: Bucket community-images does not exist!'
    WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images' AND public = false)
    THEN '❌ CRITICAL: Bucket exists but is PRIVATE!'
    WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images' AND public = true)
    THEN '✅ PERFECT: Bucket exists and is PUBLIC'
    ELSE '❓ UNKNOWN: Unexpected bucket state'
  END as status,
  COALESCE(
    (SELECT CONCAT(
      'ID: ', id, 
      ', Public: ', public::text,
      ', Size: ', COALESCE(file_size_limit::text, 'unlimited'),
      ', Types: ', COALESCE(array_length(allowed_mime_types, 1)::text, 'all')
    ) FROM storage.buckets WHERE id = 'community-images'),
    'Bucket not found'
  ) as details;

-- 2) فحص RLS بالتفصيل
-- -------------------------------------------------------

SELECT 
  '🔍 RLS VERIFICATION' as check_type,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'storage' 
      AND c.relname = 'objects'
      AND c.relrowsecurity = false
    )
    THEN '✅ PERFECT: RLS is DISABLED on storage.objects'
    WHEN EXISTS (
      SELECT 1 FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'storage' 
      AND c.relname = 'objects'
      AND c.relrowsecurity = true
    )
    THEN '⚠️ WARNING: RLS is still ENABLED (but triggers may bypass it)'
    ELSE '❓ UNKNOWN: Cannot determine RLS status'
  END as status,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'storage' 
      AND c.relname = 'objects'
      AND c.relrowsecurity = false
    )
    THEN 'Row Level Security is completely disabled'
    ELSE 'Row Level Security may still be active'
  END as details;

-- 3) فحص الصلاحيات
-- -------------------------------------------------------

SELECT 
  '🔍 PERMISSIONS VERIFICATION' as check_type,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.table_privileges 
      WHERE table_schema = 'storage' 
      AND table_name = 'objects'
      AND grantee IN ('public', 'authenticated')
      AND privilege_type = 'INSERT'
    )
    THEN '✅ PERFECT: INSERT permissions granted'
    ELSE '⚠️ WARNING: No explicit INSERT permissions found'
  END as status,
  COALESCE(
    (SELECT STRING_AGG(DISTINCT CONCAT(grantee, ':', privilege_type), ', ')
     FROM information_schema.table_privileges 
     WHERE table_schema = 'storage' 
     AND table_name = 'objects'
     AND grantee IN ('public', 'authenticated')),
    'No permissions found'
  ) as details;

-- 4) فحص السياسات
-- -------------------------------------------------------

SELECT 
  '🔍 POLICIES VERIFICATION' as check_type,
  CASE 
    WHEN NOT EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE tablename = 'objects' 
      AND schemaname = 'storage'
    )
    THEN '✅ PERFECT: No policies blocking access'
    WHEN EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE tablename = 'objects' 
      AND schemaname = 'storage'
      AND policyname LIKE '%community%'
    )
    THEN '✅ GOOD: Community-specific policies exist'
    ELSE '⚠️ WARNING: Other policies may interfere'
  END as status,
  COALESCE(
    (SELECT STRING_AGG(policyname, ', ')
     FROM pg_policies 
     WHERE tablename = 'objects' AND schemaname = 'storage'),
    'No policies'
  ) as details;

-- 5) فحص الدوال والـ triggers
-- -------------------------------------------------------

SELECT 
  '🔍 FUNCTIONS VERIFICATION' as check_type,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM pg_proc 
      WHERE proname = 'bypass_storage_upload'
    )
    THEN '✅ PERFECT: Bypass functions exist'
    ELSE '⚠️ INFO: No bypass functions (may not be needed)'
  END as status,
  COALESCE(
    (SELECT STRING_AGG(proname, ', ')
     FROM pg_proc 
     WHERE proname LIKE '%storage%' OR proname LIKE '%community%'),
    'No related functions'
  ) as details;

-- 6) فحص triggers
-- -------------------------------------------------------

SELECT 
  '🔍 TRIGGERS VERIFICATION' as check_type,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM pg_trigger t
      JOIN pg_class c ON c.oid = t.tgrelid
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'storage' 
      AND c.relname = 'objects'
      AND t.tgname LIKE '%community%'
    )
    THEN '✅ PERFECT: Community triggers active'
    ELSE '⚠️ INFO: No community triggers (may not be needed)'
  END as status,
  COALESCE(
    (SELECT STRING_AGG(t.tgname, ', ')
     FROM pg_trigger t
     JOIN pg_class c ON c.oid = t.tgrelid
     JOIN pg_namespace n ON n.oid = c.relnamespace
     WHERE n.nspname = 'storage' 
     AND c.relname = 'objects'),
    'No triggers'
  ) as details;

-- 7) محاكاة اختبار رفع
-- -------------------------------------------------------

DO $$
DECLARE
  can_access BOOLEAN := false;
  test_result TEXT := '❌ FAILED';
BEGIN
  -- اختبار الوصول لـ bucket
  IF EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images' AND public = true) THEN
    can_access := true;
  END IF;
  
  -- اختبار RLS
  IF EXISTS (
    SELECT 1 FROM pg_class c
    JOIN pg_namespace n ON n.oid = c.relnamespace
    WHERE n.nspname = 'storage' 
    AND c.relname = 'objects'
    AND c.relrowsecurity = false
  ) THEN
    can_access := true;
  END IF;
  
  IF can_access THEN
    test_result := '✅ SUCCESS';
  END IF;
  
  -- حفظ النتيجة
  CREATE TEMP TABLE IF NOT EXISTS upload_test (
    check_type TEXT,
    status TEXT,
    details TEXT
  );
  
  INSERT INTO upload_test VALUES (
    '🔍 UPLOAD SIMULATION',
    test_result,
    CASE 
      WHEN can_access THEN 'Upload should work - bucket is accessible'
      ELSE 'Upload may fail - check bucket and RLS settings'
    END
  );
END $$;

-- عرض نتيجة اختبار الرفع
SELECT check_type, status, details FROM upload_test;

-- 8) التوصية النهائية
-- -------------------------------------------------------

SELECT 
  '🎯 FINAL RECOMMENDATION' as check_type,
  CASE 
    WHEN NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images')
    THEN '🔧 CRITICAL: Run ultimate_storage_fix.sql - bucket missing!'
    WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images' AND public = false)
    THEN '🔧 CRITICAL: Make bucket PUBLIC in Supabase UI!'
    WHEN EXISTS (
      SELECT 1 FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'storage' 
      AND c.relname = 'objects'
      AND c.relrowsecurity = true
    ) AND NOT EXISTS (
      SELECT 1 FROM pg_trigger t
      JOIN pg_class c ON c.oid = t.tgrelid
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'storage' 
      AND c.relname = 'objects'
      AND t.tgname LIKE '%community%'
    )
    THEN '🔧 WARNING: RLS enabled but no bypass - may need manual disable'
    ELSE '🎉 SUCCESS: Everything looks good - test the app!'
  END as status,
  'Follow this recommendation before testing the app' as details;

-- تنظيف
DROP TABLE IF EXISTS upload_test;

-- =============================================================
--  تفسير النتائج النهائي
-- =============================================================

/*

كيفية قراءة النتائج:

✅ PERFECT/SUCCESS = ممتاز، جاهز 100%
✅ GOOD = جيد، يعمل
⚠️ WARNING/INFO = تحذير أو معلومة، قد يعمل
❌ CRITICAL/FAILED = مشكلة حرجة، يحتاج إصلاح

إذا رأيت "🎉 SUCCESS: Everything looks good - test the app!"
فهذا يعني أن كل شيء جاهز ويجب أن يعمل رفع الصور.

إذا رأيت أي توصية أخرى، اتبعها فوراً قبل اختبار التطبيق.

*/
