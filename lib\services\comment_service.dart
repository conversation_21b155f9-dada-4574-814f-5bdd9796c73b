import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/comment.dart';

class CommentService {
  final SupabaseClient _client = Supabase.instance.client;

  // دالة مساعدة لجلب بيانات المعلق
  Future<Map<String, dynamic>?> _getCommenterProfile(String userId) async {
    try {
      print('🔍 جلب بيانات المعلق للمستخدم: $userId');
      
      final response = await _client
          .from('profiles')
          .select('username, name, avatar_url, is_verified')
          .eq('id', userId)
          .maybeSingle();
      
      print('📋 بيانات المعلق المحصل عليها: $response');
      return response;
    } catch (e) {
      print('❌ خطأ في جلب بيانات المعلق: $e');
      return null;
    }
  }

  // دالة مساعدة لتحديد الاسم الحقيقي للمعلق
  String _getCommenterName(Map<String, dynamic>? profile) {
    if (profile == null) return 'مستخدم';
    
    // أولوية للاسم
    if (profile['name'] != null && 
        profile['name'].toString().trim().isNotEmpty) {
      return profile['name'].toString().trim();
    } 
    // ثم اسم المستخدم
    else if (profile['username'] != null && 
             profile['username'].toString().trim().isNotEmpty) {
      return profile['username'].toString().trim();
    }
    
    return 'مستخدم';
  }

  // الحصول على تعليقات التصويت
  Future<List<Comment>> getPollComments(String pollId) async {
    try {
      final response = await _client
          .from('poll_comments')
          .select('*')
          .eq('poll_id', pollId)
          .eq('is_active', true)
          .order('created_at', ascending: false);

      List<Comment> comments = [];
      
      for (var comment in response as List) {
        // جلب بيانات المعلق بشكل منفصل
        final commenterProfile = await _getCommenterProfile(comment['user_id']);
        
        // Debug prints لمراقبة بيانات المعلق
        print('💬 بيانات المعلق للتعليق ${comment['id']}:');
        print('   - User ID: ${comment['user_id']}');
        print('   - Commenter Profile: $commenterProfile');
        print('   - Name: ${commenterProfile?['name']}');
        print('   - Username: ${commenterProfile?['username']}');
        print('   - Is Verified: ${commenterProfile?['is_verified']}');
        print('   - Commenter Name: ${_getCommenterName(commenterProfile)}');
        
        comments.add(Comment(
          id: comment['id'],
          postId: comment['poll_id'],
          userId: comment['user_id'],
          userName: _getCommenterName(commenterProfile),
          userAvatar: commenterProfile?['avatar_url'] ?? '',
          content: comment['content'],
          createdAt: DateTime.parse(comment['created_at']),
          type: CommentType.text,
          isVerified: commenterProfile?['is_verified'] ?? false,
        ));
      }
      
      return comments;
    } catch (e) {
      throw Exception('فشل في جلب التعليقات: $e');
    }
  }

  // إضافة تعليق على التصويت
  Future<void> addPollComment({
    required String pollId,
    required String content,
    String? parentId,
  }) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client.from('poll_comments').insert({
        'poll_id': pollId,
        'user_id': userId,
        'content': content,
        'parent_id': parentId,
      });
    } catch (e) {
      throw Exception('فشل في إضافة التعليق: $e');
    }
  }

  // حذف تعليق
  Future<void> deleteComment(String commentId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('poll_comments')
          .update({'is_active': false})
          .eq('id', commentId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في حذف التعليق: $e');
    }
  }

  // الإعجاب بالتعليق (يمكن تطويره لاحقاً)
  Future<void> toggleCommentLike(String commentId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // يمكن إضافة جدول comment_likes لاحقاً
      // مؤقتاً نعرض رسالة نجاح
    } catch (e) {
      throw Exception('فشل في التفاعل: $e');
    }
  }

  // الإبلاغ عن تعليق
  Future<void> reportComment(String commentId, String reason) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // يمكن إضافة جدول comment_reports لاحقاً
      // مؤقتاً نعرض رسالة نجاح
    } catch (e) {
      throw Exception('فشل في الإبلاغ: $e');
    }
  }

  // تحديث تعليق
  Future<void> updateComment(String commentId, String newContent) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('poll_comments')
          .update({
            'content': newContent,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', commentId)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في تحديث التعليق: $e');
    }
  }

  // الحصول على عدد التعليقات لتصويت معين
  Future<int> getPollCommentsCount(String pollId) async {
    try {
      final response = await _client
          .from('poll_comments')
          .select('id')
          .eq('poll_id', pollId)
          .eq('is_active', true);

      return (response as List).length;
    } catch (e) {
      throw Exception('فشل في جلب عدد التعليقات: $e');
    }
  }

  // الحصول على ردود تعليق معين
  Future<List<Comment>> getCommentReplies(String parentCommentId) async {
    try {
      final response = await _client
          .from('poll_comments')
          .select('*')
          .eq('parent_id', parentCommentId)
          .eq('is_active', true)
          .order('created_at', ascending: true);

      List<Comment> replies = [];
      
      for (var comment in response as List) {
        // جلب بيانات المعلق بشكل منفصل
        final commenterProfile = await _getCommenterProfile(comment['user_id']);
        
        // Debug prints لمراقبة بيانات المعلق
        print('💬 بيانات المعلق للرد ${comment['id']}:');
        print('   - User ID: ${comment['user_id']}');
        print('   - Commenter Profile: $commenterProfile');
        print('   - Name: ${commenterProfile?['name']}');
        print('   - Username: ${commenterProfile?['username']}');
        print('   - Is Verified: ${commenterProfile?['is_verified']}');
        print('   - Commenter Name: ${_getCommenterName(commenterProfile)}');
        
        replies.add(Comment(
          id: comment['id'],
          postId: comment['poll_id'],
          userId: comment['user_id'],
          userName: _getCommenterName(commenterProfile),
          userAvatar: commenterProfile?['avatar_url'] ?? '',
          content: comment['content'],
          createdAt: DateTime.parse(comment['created_at']),
          type: CommentType.text,
          parentId: comment['parent_id'],
          isVerified: commenterProfile?['is_verified'] ?? false,
        ));
      }
      
      return replies;
    } catch (e) {
      throw Exception('فشل في جلب الردود: $e');
    }
  }
}
