import 'package:flutter/material.dart';
import '../models/product.dart';
import '../supabase_service.dart';
import 'package:share_plus/share_plus.dart';
import 'package:visibility_detector/visibility_detector.dart';
import 'product_comments_sheet.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../pages/chat_page.dart';
import 'package:readmore/readmore.dart';
import 'profile_avatar.dart';
import '../pages/profile_page.dart';
import '../widgets/interactive_verified_badge.dart';

import 'follow_button.dart';

class ProductCard extends StatefulWidget {
  final Product product;
  const ProductCard({super.key, required this.product});

  @override
  State<ProductCard> createState() => _ProductCardState();
}

class _ProductCardState extends State<ProductCard> {
  late Product _product;
  bool _processing = false;
  bool _viewSent = false;
  int _selectedImg = 0;
  bool _showOptions = false;


  @override
  void initState() {
    super.initState();
    _product = widget.product;
    _ensureImages();
  }

  Future<void> _ensureImages() async {
    if (_product.images.length < 2) {
      final imgs = await SupabaseService().fetchProductImages(_product.id);
      if (mounted && imgs.isNotEmpty) {
        setState(() {
          _product.images
            ..clear()
            ..addAll(imgs);
        });
      }
    }
  }

  Future<void> _toggleLike() async {
    if (_processing) return;
    setState(() {
      _product.likedByMe = !_product.likedByMe;
      _product.likesCount += _product.likedByMe ? 1 : -1;
    });
    _processing = true;
    await SupabaseService().toggleProductLike(_product.id).whenComplete(() => _processing = false);
  }

  void _openComments() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) => ProductCommentsSheet(
        product: _product,
        onCommentAdded: () {
          // تحديث عدد التعليقات في الواجهة
          setState(() {
            _product.commentsCount += 1;
          });
        },
      ),
    );
  }

  Future<void> _shareProduct() async {
    if (_processing) return;
    _processing = true;
    await SupabaseService().incrementProductShares(_product.id);
    setState(() {
      _product.sharesCount++;
    });
    // Share via system sheet
    try {
      final text = '${_product.name} - ${_product.price.toStringAsFixed(2)} ${_product.currency}';
      await Share.share(text);
    } catch (_) {}
    _processing = false;
  }

  Future<void> _startChat() async {
    final myId = Supabase.instance.client.auth.currentUser?.id;
    if (myId == null || myId == _product.userId) return; // لا تراسل نفسك

    final chatId = await SupabaseService().getOrCreateChat(_product.userId);
    if (!mounted) return;
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => ChatPage(
          chatId: chatId,
          otherId: _product.userId,
          username: _product.userName,
          avatarUrl: _product.userAvatar,
        ),
      ),
    );
  }

  void _openGallery([int initial = 0]) {
    showDialog(
      context: context,
      barrierColor: Colors.black.withOpacity(0.9),
      builder: (_) => GestureDetector(
        onTap: () => Navigator.pop(context),
        child: Dismissible(
          direction: DismissDirection.vertical,
          onDismissed: (_) => Navigator.pop(context),
          key: const ValueKey('gallery'),
          child: PageView.builder(
            controller: PageController(initialPage: initial),
            itemCount: _product.images.length,
            itemBuilder: (ctx, i) => InteractiveViewer(
              child: Image.network(_product.images[i].url, fit: BoxFit.contain),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final firstImage = _product.images.isNotEmpty ? _product.images.first.url : null;
    return VisibilityDetector(
      key: Key('prod-${_product.id}'),
      onVisibilityChanged: (info) {
        if (!_viewSent && info.visibleFraction >= 0.5) {
          _viewSent = true;
          SupabaseService().incrementProductViews(_product.id);
          setState(() {
            _product.viewsCount++;
          });
        }
      },
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 4),
        color: Colors.white,
        child: InkWell(
          onTap: () {
            // TODO: navigate to product details page
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Seller info row
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () {
                        Navigator.push(context, MaterialPageRoute(builder: (_) => ProfilePage(userId: _product.userId, username: _product.userName)));
                      },
                      child: CircleAvatar(
                        radius: 16,
                        backgroundColor: Colors.grey.shade300,
                        backgroundImage: _product.userAvatar.isNotEmpty ? NetworkImage(_product.userAvatar) : null,
                        child: _product.userAvatar.isEmpty ? const Icon(Icons.person, size: 16) : null,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          Navigator.push(context, MaterialPageRoute(builder: (_) => ProfilePage(userId: _product.userId, username: _product.userName)));
                        },
                        child: ValueListenableBuilder<Map<String, Map<String,dynamic>>>(
                          valueListenable: SupabaseService().profilesCache,
                          builder: (_, cache, __) {
                            final prof = cache[_product.userId];
                            final name = prof?['name'] ?? prof?['username'] ?? _product.userName;

                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Flexible(
                                      child: Text(
                                        name,
                                        style: const TextStyle(fontWeight: FontWeight.w600),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    // شارة التحقق
                                    ValueListenableBuilder<Map<String, Map<String,dynamic>>>(
                                      valueListenable: SupabaseService().profilesCache,
                                      builder: (_, cache, __) {
                                        final prof = cache[_product.userId];
                                        final isVerified = prof?['is_verified'] ?? false;
                                        
                                        if (isVerified) {
                                          return Row(
                                            children: [
                                              const SizedBox(width: 4),
                                              InteractiveVerifiedBadge(
                                                size: 12,
                                                userName: name,
                                              ),
                                            ],
                                          );
                                        }
                                        return const SizedBox.shrink();
                                      },
                                    ),
                                    const SizedBox(width: 8),
                                    FollowButton(
                                      userId: _product.userId,
                                      userName: _product.userName,
                                      height: 22,
                                      fontSize: 10,
                                    ),
                                  ],
                                ),
                              ],
                            );
                          },
                        ),
                      ),
                    ),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green[800],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                      ),
                      onPressed: _startChat,
                      child: const Text('التواصل مع البائع', style: TextStyle(fontSize: 12)),
                    ),
                  ],
                ),
              ),
              // Main image
              if (_product.images.isNotEmpty)
                GestureDetector(
                  onTap: () => _openGallery(),
                  child: AspectRatio(
                    aspectRatio: 4 / 3,
                    child: Image.network(_product.images[_selectedImg].url, fit: BoxFit.cover),
                  ),
                )
              else
                FutureBuilder<List<ProductImage>>(
                  future: SupabaseService().fetchProductImages(_product.id),
                  builder: (ctx, snap) {
                    if (!snap.hasData || snap.data!.isEmpty) return const SizedBox.shrink();
                    final url = snap.data!.first.url;
                    return AspectRatio(
                      aspectRatio: 4 / 3,
                      child: Image.network(url, fit: BoxFit.cover),
                    );
                  },
                ),
              // Thumbnails
              if (_product.images.length > 1)
                SizedBox(
                  height: 70,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: _product.images.length,
                    itemBuilder: (ctx, i) {
                      final img = _product.images[i];
                      return Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                        child: GestureDetector(
                          onTap: () {
                            setState(() => _selectedImg = i);
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              border: i == _selectedImg ? Border.all(color: Colors.blue, width: 2) : null,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(4),
                              child: Image.network(img.url, width: 70, height: 70, fit: BoxFit.cover),
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            _product.name,
                            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
                          ),
                        ),
                        if (_product.userId == Supabase.instance.client.auth.currentUser?.id)
                          PopupMenuButton<String>(
                            onSelected: (v) async {
                              if (v == 'delete') {
                                final confirm = await showDialog<bool>(
                                  context: context,
                                  builder: (_) => AlertDialog(
                                    title: const Text('حذف المنتج؟'),
                                    content: const Text('سيتم حذف المنتج نهائياً، هل أنت متأكد؟'),
                                    actions: [
                                      TextButton(onPressed: () => Navigator.pop(context, false), child: const Text('إلغاء')),
                                      TextButton(onPressed: () => Navigator.pop(context, true), child: const Text('حذف', style: TextStyle(color: Colors.red))),
                                    ],
                                  ),
                                );
                                if (confirm == true) {
                                  await SupabaseService().deleteProduct(_product.id);
                                }
                              }
                            },
                            itemBuilder: (_) => [
                              const PopupMenuItem(value: 'delete', child: Text('حذف', style: TextStyle(color: Colors.red))),
                            ],
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text('${_product.price.toStringAsFixed(2)} ${_product.currency}', style: TextStyle(color: Colors.green.shade800, fontSize: 20, fontWeight: FontWeight.bold)),
                    const SizedBox(height: 6),
                    ReadMoreText(
                      _product.description,
                      trimLines: 3,
                      textAlign: TextAlign.start,
                      colorClickableText: Colors.blue,
                      trimMode: TrimMode.Line,
                      trimCollapsedText: ' عرض المزيد',
                      trimExpandedText: ' عرض أقل',
                      moreStyle: const TextStyle(color: Colors.blue),
                      lessStyle: const TextStyle(color: Colors.blue),
                    ),
                    const SizedBox(height: 6),
                    Align(
                      alignment: Alignment.centerRight,
                      child: TextButton(
                        onPressed: () => setState(() => _showOptions = !_showOptions),
                        child: Text(_showOptions ? 'إخفاء الخيارات' : 'عرض الخيارات'),
                      ),
                    ),
                    if (_showOptions) ...[
                    // Attributes table
                    const SizedBox(height: 4),
                    _buildAttr('الحالة', _product.condition == ProductCondition.newItem ? 'جديد' : 'مستعمل', Icons.inventory_2),
                    if (_product.negotiable) _buildAttr('قابل للتفاوض', 'نعم', Icons.price_change),
                    _buildAttr('التصنيف', _product.category, Icons.category),
                    if (_product.brand != null && _product.brand!.isNotEmpty) _buildAttr('الماركة', _product.brand!, Icons.branding_watermark),
                    if (_product.country.isNotEmpty) _buildAttr('الدولة', _product.country, Icons.public),
                    if (_product.city.isNotEmpty) _buildAttr('المدينة', _product.city, Icons.location_city),
                    if (_product.address != null && _product.address!.isNotEmpty) _buildAttr('العنوان', _product.address!, Icons.place),
                    _buildAttr('التواصل', _product.contactMethod == ContactMethod.phone ? 'هاتف (${_product.phone ?? ''})' : 'مراسلة داخل التطبيق', Icons.phone),
                    _buildAttr('الكمية', '${_product.quantity}', Icons.format_list_numbered),
                    _buildAttr('البائع', _product.sellerType == SellerType.individual ? 'فرد' : 'تاجر', Icons.person),
                    _buildAttr('التوصيل', _product.deliveryMethod == DeliveryMethod.delivery ? 'توصيل' : 'استلام', Icons.local_shipping),
                    if (_product.deliveryCost != null) _buildAttr('تكلفة التوصيل', '${_product.deliveryCost} ${_product.currency}', Icons.monetization_on),
                    _buildAttr('الدفع', _product.paymentMethods.map(_paymentText).join('، '), Icons.payment),
                    ],
                    const SizedBox(height: 6),
                    Row(
                      children: [
                        IconButton(
                          icon: Icon(Icons.thumb_up, color: _product.likedByMe ? Colors.blue : Colors.grey),
                          onPressed: _toggleLike,
                        ),
                        Text('${_product.likesCount}'),
                        const SizedBox(width: 12),
                        IconButton(
                          icon: Icon(Icons.mode_comment, size: 20, color: Colors.grey.shade600),
                          onPressed: _openComments,
                        ),
                        Text('${_product.commentsCount}'),
                        const SizedBox(width: 12),
                        IconButton(
                          icon: Icon(Icons.ios_share, size: 20, color: Colors.grey.shade600),
                          onPressed: _shareProduct,
                        ),
                        Text('${_product.sharesCount}'),
                        const Spacer(),
                        const Icon(Icons.visibility, size: 20, color: Colors.grey),
                        Text('${_product.viewsCount}'),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Chip _buildChip(String text) => Chip(label: Text(text, style: const TextStyle(fontSize: 12)));

  Widget _buildAttr(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(icon, size: 18, color: Colors.grey.shade600),
          const SizedBox(width: 6),
          Text('$label: ', style: const TextStyle(fontWeight: FontWeight.bold)),
          Expanded(child: Text(value, overflow: TextOverflow.ellipsis)),
        ],
      ),
    );
  }

  String _paymentText(PaymentChannel p) {
    switch (p) {
      case PaymentChannel.cashOnDelivery:
        return 'نقداً عند الاستلام';
      case PaymentChannel.bankTransfer:
        return 'تحويل بنكي';
      default:
        return 'أخرى';
    }
  }
} 