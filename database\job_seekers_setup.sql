-- ===================================
-- إعداد قسم البحث عن عمل - تطبيق أرزاوو
-- ===================================

-- 1. إنشاء جدول الباحثين عن عمل
CREATE TABLE IF NOT EXISTS job_seekers (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    full_name TEXT NOT NULL,
    profile_image TEXT,
    age INTEGER NOT NULL CHECK (age >= 16 AND age <= 70),
    gender TEXT NOT NULL CHECK (gender IN ('ذكر', 'أنثى')),
    marital_status TEXT NOT NULL CHECK (marital_status IN ('single', 'married', 'divorced', 'widowed')),
    current_country TEXT NOT NULL,
    current_city TEXT NOT NULL,
    nationality TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN ('construction', 'teaching', 'driving', 'barbering', 'programming', 'delivery', 'design', 'carpentry', 'blacksmithing', 'tailoring', 'painting', 'plastering', 'electrical', 'mechanics', 'cleaning', 'cooking', 'healthcare', 'sales', 'accounting', 'security', 'other')),
    skills TEXT[] DEFAULT '{}',
    languages TEXT[] DEFAULT '{}',
    experience_years INTEGER DEFAULT 0 CHECK (experience_years >= 0),
    description TEXT NOT NULL,
    preferred_job_type TEXT NOT NULL CHECK (preferred_job_type IN ('fullTime', 'partTime', 'remote', 'freelance', 'contract')),
    preferred_location TEXT NOT NULL,
    phone_number TEXT NOT NULL,
    email TEXT,
    social_links TEXT,
    cv_url TEXT,
    portfolio_images TEXT[] DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    views_count INTEGER DEFAULT 0,
    likes_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- 2. إنشاء جدول إعجابات الباحثين عن عمل
CREATE TABLE IF NOT EXISTS job_seeker_likes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    seeker_id UUID REFERENCES job_seekers(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(seeker_id, user_id)
);

-- 3. إنشاء جدول حفظ الباحثين عن عمل
CREATE TABLE IF NOT EXISTS job_seeker_saves (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    seeker_id UUID REFERENCES job_seekers(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(seeker_id, user_id)
);

-- ===================================
-- إنشاء الفهارس لتحسين الأداء
-- ===================================

CREATE INDEX IF NOT EXISTS idx_job_seekers_category ON job_seekers(category);
CREATE INDEX IF NOT EXISTS idx_job_seekers_city ON job_seekers(current_city);
CREATE INDEX IF NOT EXISTS idx_job_seekers_country ON job_seekers(current_country);
CREATE INDEX IF NOT EXISTS idx_job_seekers_job_type ON job_seekers(preferred_job_type);
CREATE INDEX IF NOT EXISTS idx_job_seekers_active ON job_seekers(is_active);
CREATE INDEX IF NOT EXISTS idx_job_seekers_created_at ON job_seekers(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_job_seekers_user_id ON job_seekers(user_id);
CREATE INDEX IF NOT EXISTS idx_job_seeker_likes_seeker_id ON job_seeker_likes(seeker_id);
CREATE INDEX IF NOT EXISTS idx_job_seeker_likes_user_id ON job_seeker_likes(user_id);
CREATE INDEX IF NOT EXISTS idx_job_seeker_saves_seeker_id ON job_seeker_saves(seeker_id);
CREATE INDEX IF NOT EXISTS idx_job_seeker_saves_user_id ON job_seeker_saves(user_id);

-- فهرس مركب للبحث المتقدم
CREATE INDEX IF NOT EXISTS idx_job_seekers_city_category ON job_seekers(current_city, category);
CREATE INDEX IF NOT EXISTS idx_job_seekers_country_job_type ON job_seekers(current_country, preferred_job_type);

-- ===================================
-- تفعيل Row Level Security (RLS)
-- ===================================

ALTER TABLE job_seekers ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_seeker_likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_seeker_saves ENABLE ROW LEVEL SECURITY;

-- ===================================
-- سياسات الأمان
-- ===================================

-- سياسات job_seekers
DROP POLICY IF EXISTS "Anyone can view active job seekers" ON job_seekers;
CREATE POLICY "Anyone can view active job seekers" ON job_seekers
    FOR SELECT USING (is_active = true);

DROP POLICY IF EXISTS "Authenticated users can insert job seekers" ON job_seekers;
CREATE POLICY "Authenticated users can insert job seekers" ON job_seekers
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update own job seeker profile" ON job_seekers;
CREATE POLICY "Users can update own job seeker profile" ON job_seekers
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete own job seeker profile" ON job_seekers;
CREATE POLICY "Users can delete own job seeker profile" ON job_seekers
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات job_seeker_likes
DROP POLICY IF EXISTS "Authenticated users can like job seekers" ON job_seeker_likes;
CREATE POLICY "Authenticated users can like job seekers" ON job_seeker_likes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can remove own likes" ON job_seeker_likes;
CREATE POLICY "Users can remove own likes" ON job_seeker_likes
    FOR DELETE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can view likes" ON job_seeker_likes;
CREATE POLICY "Users can view likes" ON job_seeker_likes
    FOR SELECT USING (true);

-- سياسات job_seeker_saves
DROP POLICY IF EXISTS "Authenticated users can save job seekers" ON job_seeker_saves;
CREATE POLICY "Authenticated users can save job seekers" ON job_seeker_saves
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can remove own saves" ON job_seeker_saves;
CREATE POLICY "Users can remove own saves" ON job_seeker_saves
    FOR DELETE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can view own saves" ON job_seeker_saves;
CREATE POLICY "Users can view own saves" ON job_seeker_saves
    FOR SELECT USING (auth.uid() = user_id);

-- ===================================
-- الدوال المساعدة
-- ===================================

-- دالة تحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- دالة زيادة عدد الإعجابات
CREATE OR REPLACE FUNCTION update_job_seeker_likes_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE job_seekers 
        SET likes_count = likes_count + 1 
        WHERE id = NEW.seeker_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE job_seekers 
        SET likes_count = GREATEST(likes_count - 1, 0) 
        WHERE id = OLD.seeker_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- دالة زيادة عدد المشاهدات
CREATE OR REPLACE FUNCTION increment_job_seeker_views(seeker_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE job_seekers 
    SET views_count = views_count + 1 
    WHERE id = seeker_id;
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- إنشاء المحفزات (Triggers)
-- ===================================

-- محفز تحديث updated_at
DROP TRIGGER IF EXISTS update_job_seekers_updated_at ON job_seekers;
CREATE TRIGGER update_job_seekers_updated_at
    BEFORE UPDATE ON job_seekers
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- محفز تحديث عدد الإعجابات
DROP TRIGGER IF EXISTS job_seeker_likes_count_trigger ON job_seeker_likes;
CREATE TRIGGER job_seeker_likes_count_trigger
    AFTER INSERT OR DELETE ON job_seeker_likes
    FOR EACH ROW EXECUTE FUNCTION update_job_seeker_likes_count();

-- ===================================
-- إدراج بيانات تجريبية
-- ===================================

-- إدراج بيانات تجريبية حقيقية ومتنوعة
-- ملاحظة: هذه البيانات للاختبار فقط، سيتم استبدالها ببيانات المستخدمين الحقيقية

-- 1. مطور تطبيقات
INSERT INTO job_seekers (
    user_id, full_name, age, gender, marital_status, current_country,
    current_city, nationality, category, skills, languages, experience_years,
    description, preferred_job_type, preferred_location, phone_number, email
)
VALUES (
    gen_random_uuid(),
    'أحمد محمد الأحمد',
    28,
    'ذكر',
    'married',
    'السعودية',
    'الرياض',
    'سعودي',
    'programming',
    ARRAY['Flutter', 'Dart', 'Firebase', 'Supabase', 'React Native', 'JavaScript', 'Python'],
    ARRAY['العربية', 'الإنجليزية'],
    5,
    'مطور تطبيقات محترف مع خبرة 5 سنوات في تطوير تطبيقات الهاتف المحمول والويب. أتقن العمل مع قواعد البيانات والخدمات السحابية. لدي خبرة في تطوير أكثر من 20 تطبيق ناجح.',
    'fullTime',
    'الرياض أو عن بعد',
    '0501234567',
    '<EMAIL>'
);

-- 2. معلمة لغة عربية
INSERT INTO job_seekers (
    user_id, full_name, age, gender, marital_status, current_country,
    current_city, nationality, category, skills, languages, experience_years,
    description, preferred_job_type, preferred_location, phone_number, email
)
VALUES (
    gen_random_uuid(),
    'فاطمة علي السالم',
    25,
    'أنثى',
    'single',
    'السعودية',
    'جدة',
    'سعودية',
    'teaching',
    ARRAY['تدريس اللغة العربية', 'تدريس القرآن الكريم', 'التعليم الابتدائي', 'التعليم التفاعلي', 'إعداد المناهج'],
    ARRAY['العربية', 'الإنجليزية'],
    3,
    'معلمة لغة عربية مع خبرة 3 سنوات في التدريس. حاصلة على بكالوريوس في اللغة العربية وآدابها مع مرتبة الشرف. أتقن طرق التدريس الحديثة والتعليم التفاعلي.',
    'partTime',
    'جدة أو المدينة المنورة',
    '0509876543',
    '<EMAIL>'
);

-- 3. نجار محترف
INSERT INTO job_seekers (
    user_id, full_name, age, gender, marital_status, current_country,
    current_city, nationality, category, skills, languages, experience_years,
    description, preferred_job_type, preferred_location, phone_number
)
VALUES (
    gen_random_uuid(),
    'محمد عبدالله النجار',
    35,
    'ذكر',
    'married',
    'السعودية',
    'الدمام',
    'سعودي',
    'carpentry',
    ARRAY['نجارة الأثاث', 'نجارة المطابخ', 'الديكور الخشبي', 'إصلاح الأثاث', 'النجارة المعمارية', 'استخدام الآلات الحديثة'],
    ARRAY['العربية'],
    12,
    'نجار محترف مع خبرة 12 سنة في صناعة وإصلاح الأثاث. أتقن جميع أنواع النجارة من المطابخ إلى غرف النوم والديكورات الخشبية. أعمل بدقة عالية وأسعار منافسة.',
    'freelance',
    'المنطقة الشرقية',
    '0551234567'
);

-- 4. مصمم جرافيك
INSERT INTO job_seekers (
    user_id, full_name, age, gender, marital_status, current_country,
    current_city, nationality, category, skills, languages, experience_years,
    description, preferred_job_type, preferred_location, phone_number, email
)
VALUES (
    gen_random_uuid(),
    'سارة أحمد المطيري',
    26,
    'أنثى',
    'single',
    'السعودية',
    'الرياض',
    'سعودية',
    'design',
    ARRAY['Adobe Photoshop', 'Adobe Illustrator', 'Adobe InDesign', 'Figma', 'تصميم الهوية البصرية', 'تصميم المواقع'],
    ARRAY['العربية', 'الإنجليزية'],
    4,
    'مصممة جرافيك مبدعة مع خبرة 4 سنوات في تصميم الهويات البصرية والمطبوعات. أتقن جميع برامج التصميم وأقدم حلول إبداعية مميزة للعلامات التجارية.',
    'remote',
    'أي مكان - عمل عن بعد',
    '0556789012',
    '<EMAIL>'
);

-- 5. سائق محترف
INSERT INTO job_seekers (
    user_id, full_name, age, gender, marital_status, current_country,
    current_city, nationality, category, skills, languages, experience_years,
    description, preferred_job_type, preferred_location, phone_number
)
VALUES (
    gen_random_uuid(),
    'خالد سعد الغامدي',
    32,
    'ذكر',
    'married',
    'السعودية',
    'مكة المكرمة',
    'سعودي',
    'driving',
    ARRAY['قيادة السيارات الصغيرة', 'قيادة الحافلات', 'معرفة الطرق', 'الصيانة الأساسية', 'خدمة العملاء'],
    ARRAY['العربية', 'الإنجليزية الأساسية'],
    8,
    'سائق محترف مع خبرة 8 سنوات في القيادة الآمنة. أتقن جميع طرق المملكة ولدي سجل قيادة نظيف. أقدم خدمة مميزة ومواعيد دقيقة.',
    'fullTime',
    'مكة المكرمة والمدينة المنورة',
    '0543210987'
);

-- 6. طباخ متخصص
INSERT INTO job_seekers (
    user_id, full_name, age, gender, marital_status, current_country,
    current_city, nationality, category, skills, languages, experience_years,
    description, preferred_job_type, preferred_location, phone_number
)
VALUES (
    gen_random_uuid(),
    'عبدالرحمن محمد الشيف',
    29,
    'ذكر',
    'single',
    'السعودية',
    'جدة',
    'سعودي',
    'cooking',
    ARRAY['الطبخ العربي', 'الطبخ الإيطالي', 'إدارة المطبخ', 'تخطيط الوجبات', 'الطبخ الصحي', 'تزيين الأطباق'],
    ARRAY['العربية', 'الإنجليزية', 'الإيطالية الأساسية'],
    6,
    'طباخ محترف متخصص في الأكلات العربية والعالمية. خبرة 6 سنوات في المطاعم الفاخرة. أتقن إعداد الوجبات الصحية والتقديم المميز.',
    'fullTime',
    'جدة أو الرياض',
    '0567890123'
);

-- 7. كهربائي محترف
INSERT INTO job_seekers (
    user_id, full_name, age, gender, marital_status, current_country,
    current_city, nationality, category, skills, languages, experience_years,
    description, preferred_job_type, preferred_location, phone_number
)
VALUES (
    gen_random_uuid(),
    'يوسف علي الكهربائي',
    38,
    'ذكر',
    'married',
    'السعودية',
    'الخبر',
    'سعودي',
    'electrical',
    ARRAY['تمديد الكهرباء', 'صيانة الأجهزة', 'أنظمة الإنارة', 'الأنظمة الذكية', 'قراءة المخططات', 'السلامة المهنية'],
    ARRAY['العربية'],
    15,
    'كهربائي محترف مع خبرة 15 سنة في جميع أعمال الكهرباء. أتقن تمديد الكهرباء للمنازل والمباني التجارية. ملتزم بمعايير السلامة العالية.',
    'contract',
    'المنطقة الشرقية',
    '0534567890'
);

-- 8. مدرس رياضيات
INSERT INTO job_seekers (
    user_id, full_name, age, gender, marital_status, current_country,
    current_city, nationality, category, skills, languages, experience_years,
    description, preferred_job_type, preferred_location, phone_number, email
)
VALUES (
    gen_random_uuid(),
    'نورا سالم المعلمة',
    30,
    'أنثى',
    'married',
    'السعودية',
    'الطائف',
    'سعودية',
    'teaching',
    ARRAY['تدريس الرياضيات', 'الفيزياء', 'التعليم الثانوي', 'التعليم الجامعي', 'البحث العلمي', 'التدريس الإلكتروني'],
    ARRAY['العربية', 'الإنجليزية'],
    7,
    'مدرسة رياضيات وفيزياء مع خبرة 7 سنوات. حاصلة على ماجستير في الرياضيات التطبيقية. أتقن التدريس التقليدي والإلكتروني بطرق مبتكرة.',
    'partTime',
    'الطائف أو عن بعد',
    '0578901234',
    '<EMAIL>'
);

-- 9. حلاق رجالي
INSERT INTO job_seekers (
    user_id, full_name, age, gender, marital_status, current_country,
    current_city, nationality, category, skills, languages, experience_years,
    description, preferred_job_type, preferred_location, phone_number
)
VALUES (
    gen_random_uuid(),
    'عمر حسن الحلاق',
    27,
    'ذكر',
    'single',
    'السعودية',
    'أبها',
    'سعودي',
    'barbering',
    ARRAY['قص الشعر الكلاسيكي', 'القص العصري', 'تهذيب اللحية', 'العناية بالشعر', 'استخدام الأدوات الحديثة'],
    ARRAY['العربية'],
    4,
    'حلاق محترف متخصص في القصات العصرية والكلاسيكية. خبرة 4 سنوات في أفضل صالونات المنطقة. أقدم خدمة مميزة وأسعار منافسة.',
    'fullTime',
    'أبها أو خميس مشيط',
    '0589012345'
);

-- 10. عامل نظافة محترف
INSERT INTO job_seekers (
    user_id, full_name, age, gender, marital_status, current_country,
    current_city, nationality, category, skills, languages, experience_years,
    description, preferred_job_type, preferred_location, phone_number
)
VALUES (
    gen_random_uuid(),
    'أحمد سعيد العامل',
    24,
    'ذكر',
    'single',
    'السعودية',
    'تبوك',
    'سعودي',
    'cleaning',
    ARRAY['تنظيف المنازل', 'تنظيف المكاتب', 'تنظيف السجاد', 'استخدام المعدات الحديثة', 'المواد الآمنة'],
    ARRAY['العربية'],
    2,
    'عامل نظافة محترف ومتفاني في العمل. خبرة سنتان في تنظيف المنازل والمكاتب. أستخدم أفضل المواد والمعدات لضمان النظافة التامة.',
    'partTime',
    'تبوك والمناطق المجاورة',
    '0590123456'
);

-- ===================================
-- التحقق من نجاح الإعداد
-- ===================================

-- عرض إحصائيات سريعة
SELECT 
    'تم إنشاء قسم البحث عن عمل بنجاح!' as message,
    (SELECT COUNT(*) FROM job_seekers) as total_job_seekers,
    (SELECT COUNT(DISTINCT category) FROM job_seekers) as categories_count,
    (SELECT COUNT(*) FROM job_seeker_likes) as total_likes,
    (SELECT COUNT(*) FROM job_seeker_saves) as total_saves;
