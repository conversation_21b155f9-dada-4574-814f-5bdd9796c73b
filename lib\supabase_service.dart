import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/foundation.dart';
import 'models/post.dart';
import 'models/comment.dart';
import 'models/app_notification.dart';
import 'models/chat.dart';
import 'models/message.dart';
import 'models/story.dart';
import 'models/group.dart';
import 'models/product.dart';
import 'models/reaction_type.dart';
import 'dart:async';
import 'models/community.dart';
import 'models/community_comment.dart';

class SupabaseService {
  final SupabaseClient _client = Supabase.instance.client;





  // --------- بث حي للملفات الشخصية ---------- //
  final ValueNotifier<Map<String, Map<String, dynamic>>> profilesCache =
      ValueNotifier(<String, Map<String, dynamic>>{});

  bool _profilesListening = false;
  Future<void> initProfilesListener() async {
    if (_profilesListening) return;
    _profilesListening = true;

    // تحميل أولى لكل الملفات الشخصية لملء الكاش فورًا
    try {
      final rows = await _client.from('profiles').select('id,name,username,avatar_url');
      final initial = <String, Map<String, dynamic>>{};
      for (final row in rows as List) {
        initial[row['id']] = row as Map<String, dynamic>;
      }
      profilesCache.value = initial;
    } catch (_) {}

    // بث حي لأى تغييرات لاحقة
    _client
        .from('profiles')
        .stream(primaryKey: ['id'])
        .listen((rows) {
      final map = Map<String, Map<String, dynamic>>.from(profilesCache.value);
      for (final r in rows) {
        map[r['id']] = r as Map<String, dynamic>;
      }
      profilesCache.value = map;
    });
  }

  Future<AuthResponse> signUp({required String email, required String password, required String name}) async {
    final res = await _client.auth.signUp(
      email: email,
      password: password,
      data: {'name': name},
      // استخدم رابط إعادة توجيه مختلف حسب المنصة (ويب أو موبايل)
      emailRedirectTo: kIsWeb ? 'https://arzawo.com/welcome' : 'arzapress://auth-callback',
    );

    // حاول إنشاء صفّ الملف الشخصى إن عاد كائن المستخدم فورى
    if (res.user != null) {
      await ensureProfileExists(res.user!);
    }
    return res;
  }

  Future<AuthResponse> signIn({required String email, required String password}) async {
    final res = await _client.auth.signInWithPassword(email: email, password: password);

    // تأكد من وجود صف الملف الشخصى بعد كل تسجيل دخول ناجح
    if (res.user != null) {
      await ensureProfileExists(res.user!);
      
      // فحص حالة التعطيل
      final profile = await fetchProfile(res.user!.id);
      final status = profile?['status'] ?? 'active';
      
      if (status == 'deactivated') {
        final deactivatedUntil = profile?['deactivated_until'];
        final deactivatedAt = profile?['deactivated_at'];
        
        if (deactivatedUntil != null) {
          final untilDate = DateTime.parse(deactivatedUntil);
          final now = DateTime.now();
          
          if (now.isBefore(untilDate)) {
            // الحساب لا يزال معطلاً
            await _client.auth.signOut();
            throw Exception('حسابك معطل حتى ${untilDate.toLocal().toString().split(' ')[0]}');
          } else {
            // انتهت مدة التعطيل، إعادة التفعيل تلقائياً
            await reactivateAccount();
          }
        } else {
          // تعطيل دائم، يمكن إعادة التفعيل يدوياً
          await _client.auth.signOut();
          throw Exception('حسابك معطل. يمكنك إعادة التفعيل من خلال التواصل مع الدعم الفني.');
        }
      } else if (status == 'pending_delete') {
        final deletedAt = profile?['deleted_at'];
        final deleteRequestedAt = profile?['delete_requested_at'];
        
        if (deletedAt != null) {
          final deleteDate = DateTime.parse(deletedAt);
          final now = DateTime.now();
          
          if (now.isAfter(deleteDate)) {
            // انتهت مدة الحذف، الحساب محذوف نهائياً
            await _client.auth.signOut();
            throw Exception('تم حذف حسابك نهائياً. لا يمكن تسجيل الدخول مرة أخرى.');
          } else {
            // الحساب في انتظار الحذف، يمكن إلغاء الحذف
            await cancelAccountDeletion();
            print('تم إلغاء حذف الحساب بنجاح');
          }
        }
      }
    }

    return res;
  }

  // إرسال رسالة إعادة تعيين كلمة المرور إلى البريد
  Future<void> sendPasswordResetEmail(String email, {String redirectUrl = 'https://arzawo.com/welcome'}) async {
    await _client.auth.resetPasswordForEmail(email, redirectTo: redirectUrl);
  }

  // بعد أن يدخل المستخدم بالرابط ويُنشئ Session، استبدل كلمة المرور
  Future<void> updatePassword(String newPassword) async {
    await _client.auth.updateUser(UserAttributes(password: newPassword));
  }

  Future<List<Post>> fetchPosts() async {
    final uid = _client.auth.currentUser?.id;

    // قائمة المعرّفات المسموح بها (المتابَعون + نفس المستخدم)
    List<String> allowedIds = [];
    if (uid != null) {
      final rows = await _client
          .from('follows')
          .select('following_id')
          .eq('follower_id', uid);
      allowedIds = (rows as List).map((e) => e['following_id'] as String).toList();
      allowedIds.add(uid);
    }

    // -------- بناء الاستعلام -------- //
    dynamic query = _client
        .from('posts')
        .select('''
          id,
          user_id,
          content,
          created_at,
          type,
          media_url,
          media_urls,
          link_url,
          link_meta,
          bg_color,
          posts_privacy,
          likes_count,
          dislikes_count,
          shares_count,
          comments_count,
          views_count,
          copies_count,
          shared_post_id,
          community_id,
          space_id,
          original:posts!shared_post_id(id,user_id,content,created_at,type,media_url,media_urls,link_url,link_meta,profiles(name,avatar_url)),
          profiles(name,avatar_url,posts_privacy,is_verified)
        ''')
        .isFilter('community_id', null) // استبعاد منشورات المجتمع من الصفحة الرئيسية
        .isFilter('space_id', null) // استبعاد منشورات المساحات من الصفحة الرئيسية
        .order('created_at', ascending: false);

    // فى بعض الإصدارات القديمة لا تتوفر دالة or()، لذا نُبقى الاستعلام بدون فلترة خصوصية لتجنب الخطأ.
    // يمكن لاحقًا تحسين المنطق باستخدام rpc أو تقسيم الاستعلام إلى اثنين ثم دمج النتائج.

    final data = await query;

    // جلب المنشورات المخفية للمستخدم
    final hiddenPostIds = await getHiddenPostIds();
    print('المنشورات المخفية: $hiddenPostIds');

    // بناء قائمة أولية من المنشورات واستبعاد المخفية
    final List<Post> posts = (data as List)
        .where((row) => !hiddenPostIds.contains(row['id'].toString()))
        .map<Post>((row) {
      final profile = row['profiles'] ?? {};

      // --- بناء المنشور الأصلى (إذا كان هذا المنشور مُعاداً) --- //
      Post? original;
      dynamic origData = row['original'];
      if (origData is List && origData.isNotEmpty) origData = origData.first;
      if (origData != null && origData is Map) {
        final origProfile = origData['profiles'] ?? {};
        original = Post(
          id: origData['id'].toString(),
          userId: origData['user_id'].toString(),
          userName: origProfile['name'] ?? 'مستخدم',
          userAvatar: origProfile['avatar_url'] ?? '',
          content: origData['content'] ?? '',
          createdAt: DateTime.parse(origData['created_at']),
          type: _mapType(origData['type']),
          mediaUrl: origData['media_url'],
          mediaUrls: origData['media_urls'] != null ? List<String>.from(origData['media_urls']) : null,
          linkUrl: origData['link_url'],
          linkMeta: origData['link_meta'],
          bgColor: origData['bg_color'],
          likesCount: 0,
          dislikesCount: 0,
          sharesCount: 0,
          commentsCount: 0,
          viewsCount: 0,
          reactionCounts: const {},
          copiesCount: 0,
          isVerified: origProfile['is_verified'] ?? false,
        );
      }

      return Post(
        id: row['id'].toString(),
        userId: row['user_id'].toString(),
        userName: profile['name'] ?? 'مستخدم',
        userAvatar: profile['avatar_url'] ?? '',
        content: row['content'] ?? '',
        createdAt: DateTime.parse(row['created_at']),
        type: _mapType(row['type']),
        mediaUrl: row['media_url'],
        mediaUrls: row['media_urls'] != null ? List<String>.from(row['media_urls']) : null,
        linkUrl: row['link_url'],
        linkMeta: row['link_meta'],
        bgColor: row['bg_color'],
        likesCount: row['likes_count'] ?? 0,
        dislikesCount: row['dislikes_count'] ?? 0,
        sharesCount: row['shares_count'] ?? 0,
        commentsCount: row['comments_count'] ?? 0,
        viewsCount: row['views_count'] ?? 0,
        copiesCount: row['copies_count'] ?? 0,
        isVerified: profile['is_verified'] ?? false,
        originalPost: original,
      );
    }).toList();

    // ✂️ استبعاد المستخدمين المحظورين بعد ربط المنشورات الأصلية
    final blockedIds = await blockedUserIds();
    if (blockedIds.isNotEmpty) {
      posts.removeWhere((p) => blockedIds.contains(p.userId));
    }

    if (posts.isEmpty) return posts;

    // ربط المنشورات الأصلية للمنشورات المُعادة فى حال فشل الـ join
    await _linkSharedOriginalsFallback(data as List, posts);

    final postIds = posts.map((p) => p.id).toList();

    // --------- جلب ملخص تفاعلات جميع المستخدمين لكل منشور --------- //
    final reactionRows = await _client
        .from('reactions')
        .select('post_id,type')
        .inFilter('post_id', postIds);

    // حساب المجاميع
    final Map<String, Map<ReactionType, int>> aggregated = {};
    for (final row in reactionRows as List) {
      final pid = row['post_id'].toString();
      final rt = _mapReaction(row['type']);
      if (rt == ReactionType.none) continue;
      aggregated.putIfAbsent(pid, () => {});
      aggregated[pid]![rt] = (aggregated[pid]![rt] ?? 0) + 1;
    }

    // إرفاق إلى الكائنات
    for (var i = 0; i < posts.length; i++) {
      posts[i] = posts[i].copyWith(reactionCounts: aggregated[posts[i].id] ?? {});
    }

    // --------- جلب تفاعل المستخدم الحالى لكل منشور --------- //
    if (uid != null) {
      final reactRows = await _client
          .from('reactions')
          .select('post_id,type')
          .inFilter('post_id', postIds)
          .eq('user_id', uid);

      final Map<String, ReactionType> userReactions = {};
      for (final r in reactRows as List) {
        userReactions[r['post_id'].toString()] = _mapReaction(r['type']);
      }

      for (var i = 0; i < posts.length; i++) {
        final reaction = userReactions[posts[i].id];
        if (reaction != null) {
          posts[i] = posts[i].copyWith(currentUserReaction: reaction);
        }
      }
    }

    // Debug print for all posts
    for (final post in posts) {
      if (post.mediaUrls != null && post.mediaUrls!.isNotEmpty) {
        print('DEBUG: Post ${post.id} has ${post.mediaUrls!.length} media URLs: ${post.mediaUrls}');
      }
    }

    return posts;
  }

  PostType _mapType(String? value) {
    switch (value) {
      case 'image':
        return PostType.image;
      case 'video':
        return PostType.video;
      case 'link':
        return PostType.link;
      case 'shared':
        return PostType.shared;
      case 'audio':
        return PostType.audio;
      case 'voice':
        return PostType.voice;
      default:
        return PostType.text;
    }
  }

  Future<String> uploadMedia(Uint8List bytes, String path) async {
    print('DEBUG: uploadMedia called with path: $path, size: ${bytes.length} bytes');
    
    // تحاول Supabase رفع الملف وتُرجع المسار عند النجاح أو ترمى استثناء عند الخطأ
    await _client.storage.from('media').uploadBinary(
      path,
      bytes,
      fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
    );

    // إذا وصلنا إلى هنا فالرفع نجح. نحصل على رابط عام لعرض الملف.
    String url = _client.storage.from('media').getPublicUrl(path);
    print('DEBUG: File uploaded successfully. URL: $url');

    // كسر كاش المتصفح/التطبيق للصور الشخصية: أضف باراميتر زمنى فريد
    if (path.startsWith('avatars/')) {
      final ts = DateTime.now().millisecondsSinceEpoch;
      url = '$url?v=$ts';
    }
    return url;
  }

  Future<String> uploadSpaceImage(Uint8List bytes, String path) async {
    print('DEBUG: uploadSpaceImage called with path: $path, size: ${bytes.length} bytes');
    
    // رفع الصورة إلى bucket posts-images
    await _client.storage.from('posts-images').uploadBinary(
      path,
      bytes,
      fileOptions: const FileOptions(cacheControl: '3600', upsert: true),
    );

    // الحصول على رابط عام لعرض الملف
    String url = _client.storage.from('posts-images').getPublicUrl(path);
    print('DEBUG: Space image uploaded successfully. URL: $url');
    
    return url;
  }

  Future<void> createPost({
    required String content,
    PostType type = PostType.text,
    PostPrivacy privacy = PostPrivacy.everyone,
    String? mediaUrl,
    List<String>? mediaUrls,
    String? linkUrl,
    Map<String, dynamic>? linkMeta,
    String? sharedPostId,
    String? bgColor,
    String? communityId, // إضافة دعم community_id
    String? spaceId, // إضافة دعم space_id
  }) async {
    try {
      print('DEBUG: SupabaseService.createPost called');
      print('DEBUG: content = "$content"');
      print('DEBUG: type = ${type.name}');
      print('DEBUG: mediaUrl = $mediaUrl');
      print('DEBUG: mediaUrls = $mediaUrls');
      print('DEBUG: user_id = ${_client.auth.currentUser!.id}');
      
      final insertData = {
        'user_id': _client.auth.currentUser!.id,
        'content': content,
        'type': type.name,
        'media_url': mediaUrl,
        'media_urls': mediaUrls,
        'link_url': linkUrl,
        'link_meta': linkMeta,
        'bg_color': bgColor,
        'shared_post_id': sharedPostId,
        'posts_privacy': privacy.name,
        'community_id': communityId, // إضافة community_id
        'space_id': spaceId, // إضافة space_id
        'created_at': DateTime.now().toIso8601String(),
      };
      
      print('DEBUG: Inserting data: $insertData');
      
      final result = await _client.from('posts').insert(insertData);
      print('DEBUG: Insert result: $result');
      print('DEBUG: Post created successfully!');
    } catch (e, stackTrace) {
      print('ERROR: Failed to create post');
      print('ERROR: Exception: $e');
      print('ERROR: Stack trace: $stackTrace');
      throw Exception('فشل في إنشاء المنشور: $e');
    }
  }

  Future<void> toggleReaction({required String postId, required ReactionType reaction}) async {
    final uid = _client.auth.currentUser!.id;

    // استخدام النظام الجديد مع toggle_reaction function
    try {
      await _client.rpc('toggle_reaction', params: {
        'p_user': uid,
        'p_target': postId,
        'p_kind': 'post',
        'p_type': reaction.value,
      });
    } catch (e) {
      print('Error toggling reaction: $e');
      // fallback للنظام القديم إذا فشل الجديد
      await _toggleReactionOld(postId: postId, reaction: reaction);
    }
  }

  // النظام القديم كـ fallback
  Future<void> _toggleReactionOld({required String postId, required ReactionType reaction}) async {
    final uid = _client.auth.currentUser!.id;

    // احصل على التفاعل الحالي (إن وجد)
    final existing = await _client
        .from('reactions')
        .select('id,type')
        .eq('post_id', postId)
        .eq('user_id', uid)
        .maybeSingle();

    ReactionType? oldReaction;
    if (existing != null) {
      oldReaction = _mapReaction(existing['type']);
    }

    if (oldReaction == reaction) {
      // إزالة التفاعل (عدم تفاعل)
      await _client.from('reactions').delete().eq('id', existing!['id']);
      await _updateCounters(postId, remove: reaction);
    } else {
      if (existing == null) {
        // إدراج تفاعل جديد
        await _client.from('reactions').insert({
          'post_id': postId,
          'user_id': uid,
          'type': reaction.value,
        });
      } else {
        // تحديث التفاعل
        await _client.from('reactions').update({'type': reaction.value}).eq('id', existing!['id']);
        await _updateCounters(postId, remove: oldReaction, add: reaction);
        return;
      }
      await _updateCounters(postId, add: reaction);
    }
  }

  Future<void> _updateCounters(String postId, {ReactionType? add, ReactionType? remove}) async {
    bool _isPositive(ReactionType t) => [
      ReactionType.like,
      ReactionType.celebrate,
      ReactionType.support,
      ReactionType.love,
      ReactionType.insightful,
      ReactionType.funny,
    ].contains(t);

    final updates = <String, int>{};
    if (add != null && _isPositive(add)) {
      updates['likes_count'] = 1;
    }
    if (remove != null && _isPositive(remove)) {
      updates['likes_count'] = (updates['likes_count'] ?? 0) - 1;
    }

    if (updates.isEmpty) return;
    await _client.rpc('increment_counters', params: {
      'post_id_param': postId,
      'likes_inc': updates['likes_count'] ?? 0,
      'dislikes_inc': 0,
    });
  }

  ReactionType _mapReaction(String? value) {
    return ReactionType.values.firstWhere(
      (rt) => rt.value == value,
      orElse: () => ReactionType.none,
    );
  }

  String _reactionToString(ReactionType reaction) {
    return reaction.value;
  }

  Future<Map<ReactionType, List<Map<String, dynamic>>>> getPostReactionDetails(String postId) async {
    try {
      // أولاً، جرب الاستعلام مع جدول profiles
      List<Map<String, dynamic>> response;
      try {
        response = await _client
            .from('reactions')
            .select('''
              type,
              user_id,
              profiles!inner(
                id,
                name,
                username,
                avatar_url
              )
            ''')
            .eq('post_id', postId)
            .order('created_at', ascending: false);
      } catch (profileError) {
        // إذا فشل، استخدم استعلام بسيط بدون profiles
        print('Profiles join failed, using simple query: $profileError');
        response = await _client
            .from('reactions')
            .select('type, user_id')
            .eq('post_id', postId)
            .order('created_at', ascending: false);
      }

      final Map<ReactionType, List<Map<String, dynamic>>> result = {};

      for (final item in response) {
        final reactionType = _mapReaction(item['type']);
        if (reactionType == ReactionType.none) continue;

        final userProfile = item['profiles'];
        if (userProfile != null) {
          // استخدام بيانات profiles إذا كانت متوفرة
          result.putIfAbsent(reactionType, () => []);
          result[reactionType]!.add({
            'id': userProfile['id'],
            'name': userProfile['name'] ?? 'مستخدم',
            'username': userProfile['username'],
            'avatar_url': userProfile['avatar_url'],
          });
        } else {
          // استخدام user_id فقط إذا لم تكن profiles متوفرة
          result.putIfAbsent(reactionType, () => []);
          result[reactionType]!.add({
            'id': item['user_id'],
            'name': 'مستخدم ${item['user_id'].substring(0, 8)}',
            'username': null,
            'avatar_url': null,
          });
        }
      }

      return result;
    } catch (e) {
      print('Error fetching reaction details: $e');
      return {};
    }
  }

  // ------------------ Stories ------------------ //

  StoryType _mapStoryType(String? v) {
    switch (v) {
      case 'image':
        return StoryType.image;
      case 'video':
        return StoryType.video;
      default:
        return StoryType.text;
    }
  }

  Future<void> createStory({required StoryType type, String? text, Uint8List? mediaBytes, String? mediaExt}) async {
    String? mediaUrl;
    if (mediaBytes != null && mediaExt != null) {
      final path = 'stories/${_client.auth.currentUser!.id}/${DateTime.now().millisecondsSinceEpoch}.$mediaExt';
      mediaUrl = await uploadMedia(mediaBytes, path);
    }

    await _client.from('stories').insert({
      'user_id': _client.auth.currentUser!.id,
      'type': type.name,
      'text': text,
      'media_url': mediaUrl,
      'created_at': DateTime.now().toIso8601String(),
      'expires_at': DateTime.now().add(const Duration(hours: 24)).toIso8601String(),
    });
  }

  List<Story> _rowsToStories(List rows) {
    return rows.map((r) {
      final profile = r['profiles'] ?? {};
      return Story(
        id: r['id'],
        userId: r['user_id'],
        userName: profile['name'] ?? 'مستخدم',
        userAvatar: profile['avatar_url'] ?? '',
        type: _mapStoryType(r['type']),
        text: r['text'],
        mediaUrl: r['media_url'],
        createdAt: DateTime.parse(r['created_at']),
        expiresAt: DateTime.parse(r['expires_at']),
      );
    }).toList();
  }

  Future<List<Story>> fetchStories() async {
    final nowIso = DateTime.now().toIso8601String();
    final rows = await _client
        .from('stories')
        .select('''id,user_id,type,text,media_url,created_at,expires_at, profiles(name,avatar_url)''')
        .gt('expires_at', nowIso)
        .order('created_at', ascending: false);

    return _rowsToStories(rows as List);
  }

  Stream<List<Story>> storiesStream() {
    // نجلب القصص مع بيانات الملف الشخصي لكل مستخدم لضمان إظهار الاسم الحقيقي
    return _client
        .from('stories')
        .stream(primaryKey: ['id'])
        .gt('expires_at', DateTime.now().toIso8601String())
        .order('created_at', ascending: false)
        .asyncMap((rows) async {
          final List<Map<String, dynamic>> enriched = [];
          for (final row in rows) {
            // جلب بيانات الملف الشخصي لكل مستخدم
            final profile = await _client
                .from('profiles')
                .select('name,avatar_url')
                .eq('id', row['user_id'])
                .maybeSingle();

            final mutableRow = Map<String, dynamic>.from(row);
            mutableRow['profiles'] = profile;
            enriched.add(mutableRow);
          }
          return _rowsToStories(enriched);
        });
  }

  // ------------------ Comments ------------------ //

  CommentType _mapCommentType(String? v) {
    switch (v) {
      case 'image':
        return CommentType.image;
      case 'video':
        return CommentType.video;
      default:
        return CommentType.text;
    }
  }

  Future<void> createComment({
    required String postId,
    String? parentId,
    required String content,
    required CommentType type,
    String? mediaUrl,
  }) async {
    // إدراج التعليق
    await _client.from('comments').insert({
      'post_id': postId,
      'parent_id': parentId,
      'user_id': _client.auth.currentUser!.id,
      'content': content,
      'type': type.name,
      if (mediaUrl != null) 'media_url': mediaUrl,
      'created_at': DateTime.now().toIso8601String(),
    });

    // تحديث عدد التعليقات في المنشور
    await _incrementPostCommentsCount(postId);
  }

  Future<void> createReply({
    required String postId,
    required String parentCommentId,
    required String content,
    required CommentType type,
    String? mediaUrl,
  }) async {
    // إدراج الرد
    await _client.from('comments').insert({
      'post_id': postId,
      'parent_id': parentCommentId,
      'user_id': _client.auth.currentUser!.id,
      'content': content,
      'type': type.name,
      if (mediaUrl != null) 'media_url': mediaUrl,
      'created_at': DateTime.now().toIso8601String(),
    });

    // تحديث عدد التعليقات في المنشور
    await _incrementPostCommentsCount(postId);
  }

  Future<void> toggleCommentReaction({
    required String commentId,
    required ReactionType reaction,
  }) async {
    // استخدام النظام البسيط المباشر لتجنب أى منطق على الخادم قد يحوّل التفاعل إلى Like فقط
    await _toggleCommentReactionOld(commentId: commentId, reaction: reaction);
  }

  Future<void> _toggleCommentReactionOld({
    required String commentId,
    required ReactionType reaction,
  }) async {
    final uid = _client.auth.currentUser!.id;

    // احصل على التفاعل الحالي (إن وجد)
    final existing = await _client
        .from('comment_reactions')
        .select('id,type')
        .eq('comment_id', commentId)
        .eq('user_id', uid)
        .maybeSingle();

    ReactionType? oldReaction;
    if (existing != null) {
      oldReaction = _mapReaction(existing['type']);
    }

    if (oldReaction == reaction) {
      // إزالة التفاعل
      await _client.from('comment_reactions').delete().eq('id', existing!['id']);
    } else {
      if (existing == null) {
        // إدراج تفاعل جديد
        await _client.from('comment_reactions').insert({
          'comment_id': commentId,
          'user_id': uid,
          'type': reaction.value,
        });
      } else {
        // تحديث التفاعل
        await _client.from('comment_reactions').update({'type': reaction.value}).eq('id', existing['id']);
      }
    }
  }

  // دالة لجلب تفاعلات تعليق محدد
  Future<Map<String, dynamic>> getCommentReactions(String commentId) async {
    try {
      final result = await _client.rpc('get_comment_reactions', params: {
        'p_comment_id': commentId,
      });

      return {
        'reactions': result,
        'total': (result as List).fold<int>(0, (sum, item) => sum + (item['reaction_count'] as int)),
      };
    } catch (e) {
      print('Error getting comment reactions: $e');
      return {'reactions': [], 'total': 0};
    }
  }

  /// تحديث عدد التعليقات في المنشور
  Future<void> _incrementPostCommentsCount(String postId) async {
    try {
      // استخدام RPC function لتحديث العدد بشكل آمن
      await _client.rpc('increment_post_comments', params: {
        'post_id_param': postId,
      });
    } catch (e) {
      // في حالة عدم وجود الدالة، نستخدم طريقة بديلة
      await _updatePostCommentsCountManually(postId);
    }
  }

  /// طريقة بديلة لتحديث عدد التعليقات يدوياً
  Future<void> _updatePostCommentsCountManually(String postId) async {
    try {
      // حساب عدد التعليقات الفعلي
      final count = await _client
          .from('comments')
          .select('id')
          .eq('post_id', postId)
          .count(CountOption.exact);

      // تحديث العدد في جدول المنشورات
      await _client
          .from('posts')
          .update({'comments_count': count.count})
          .eq('id', postId);
    } catch (e) {
      print('خطأ في تحديث عدد التعليقات: $e');
    }
  }

  Stream<List<Comment>> fetchComments(String postId) {
    final uid = _client.auth.currentUser?.id;

    return _client
        .from('comments')
        .stream(primaryKey: ['id'])
        .eq('post_id', postId)
        .order('created_at', ascending: false)
        .asyncMap((rows) async {
          // جلب التفاعلات للمستخدم الحالي بشكل منفصل
          final commentsWithReactions = <Map<String, dynamic>>[];

          for (final row in rows) {
            final commentData = Map<String, dynamic>.from(row);

            // ✅ تخطي التعليقات المحذوفة
            if (commentData['is_deleted'] == true) {
              continue;
            }

            // جلب بيانات المستخدم
            final profile = await _client
                .from('profiles')
                .select('name,avatar_url,posts_privacy,is_verified')
                .eq('id', commentData['user_id'])
                .maybeSingle();
            commentData['profiles'] = profile;

            // جلب عدادات التفاعلات (مع user_id لتحديد تفاعل المستخدم الحالى)
            final reactionCounts = await _client
                .from('comment_reactions')
                .select('type,user_id')
                .eq('comment_id', commentData['id']);

            // حساب العدادات
            final counts = <String, int>{};
            for (final reaction in reactionCounts) {
              final type = reaction['type'] as String;
              counts[type] = (counts[type] ?? 0) + 1;
            }

            commentData['likes_count'] = counts['like'] ?? 0;
            commentData['dislikes_count'] = counts['dislike'] ?? 0;
            commentData['love_count'] = counts['love'] ?? 0;
            commentData['laugh_count'] = counts['laugh'] ?? 0;
            commentData['angry_count'] = counts['angry'] ?? 0;
            commentData['sad_count'] = counts['sad'] ?? 0;
            commentData['celebrate_count'] = counts['celebrate'] ?? 0;
            commentData['support_count'] = counts['support'] ?? 0;
            commentData['insightful_count'] = counts['insightful'] ?? 0;

            // جلب تفاعل المستخدم الحالي
            if (uid != null) {
              Map<String, dynamic>? userReact;
              for (final el in reactionCounts) {
                if (el['user_id'] == uid) {
                  userReact = el as Map<String, dynamic>;
                  break;
                }
              }
              commentData['user_reaction'] = userReact != null ? [userReact] : [];
            } else {
              commentData['user_reaction'] = [];
            }

            commentsWithReactions.add(commentData);
          }

          return _toComments(commentsWithReactions, postId, currentUserId: uid);
        });
  }

  List<Comment> _toComments(List rows, String postId, {String? currentUserId}) {
    final Map<String, Comment> map = {};
    final List<Comment> roots = [];

    // إنشاء جميع التعليقات أولاً
    for (final r in rows) {
      // بناء خريطة التفاعلات
      final reactionCounts = <ReactionType, int>{};
      reactionCounts[ReactionType.like] = r['likes_count'] ?? 0;
      reactionCounts[ReactionType.dislike] = r['dislikes_count'] ?? 0;
      reactionCounts[ReactionType.love] = r['love_count'] ?? 0;
      reactionCounts[ReactionType.funny] = r['laugh_count'] ?? 0;
      reactionCounts[ReactionType.angry] = r['angry_count'] ?? 0;
      reactionCounts[ReactionType.sad] = r['sad_count'] ?? 0;
      reactionCounts[ReactionType.celebrate] = r['celebrate_count'] ?? 0;
      reactionCounts[ReactionType.support] = r['support_count'] ?? 0;
      reactionCounts[ReactionType.insightful] = r['insightful_count'] ?? 0;

      // تحديد التفاعل الحالي للمستخدم
      ReactionType currentUserReaction = ReactionType.none;
      if (currentUserId != null && r['user_reaction'] != null && (r['user_reaction'] as List).isNotEmpty) {
        final userReactionType = r['user_reaction'][0]['type'] as String?;
        if (userReactionType != null) {
          currentUserReaction = _mapReaction(userReactionType);
        }
      }

      // جلب بيانات الملف الشخصي من العلاقة أو من الكاش
      final Map<String, dynamic> profileData = Map<String, dynamic>.from(
        (r['profiles'] as Map?) ?? profilesCache.value[r['user_id']] ?? <String, dynamic>{},
      );

      // إذا لم يتوفر الاسم بعد، جلبه فى الخلفية لضمان ظهوره لاحقاً
      if ((profileData['name'] == null || profileData['name'] == 'مستخدم') && r['user_id'] != null) {
        _ensureProfileCached(r['user_id']);
      }

      final comment = Comment(
        id: r['id'],
        postId: postId,
        parentId: r['parent_id'],
        userId: r['user_id'],
        userName: profileData['name'] ?? profileData['username'] ?? 'مستخدم',
        userAvatar: profileData['avatar_url'] ?? '',
        content: r['content'] ?? '',
        createdAt: DateTime.parse(r['created_at']),
        type: _mapCommentType(r['type']),
        mediaUrl: r['media_url'],
        reactionCounts: reactionCounts,
        currentUserReaction: currentUserReaction,
        isVerified: profileData['is_verified'] ?? false,
        replies: [],
      );
      map[comment.id] = comment;
    }

    // بناء التسلسل الهرمي
    _buildCommentHierarchy(map, roots);

    return roots;
  }

  void _buildCommentHierarchy(Map<String, Comment> map, List<Comment> roots) {
    for (final comment in map.values) {
      if (comment.parentId == null) {
        // تعليق جذر
        roots.add(comment);
      } else {
        // رد على تعليق آخر
        final parent = map[comment.parentId];
        if (parent != null) {
          // تحديد عمق التعليق واسم المستخدم المُرد عليه
          final updatedComment = comment.copyWith(
            depth: parent.depth + 1,
          );

          // إضافة اسم المستخدم المُرد عليه
          final commentWithReplyTo = Comment(
            id: updatedComment.id,
            postId: updatedComment.postId,
            parentId: updatedComment.parentId,
            userId: updatedComment.userId,
            userName: updatedComment.userName,
            userAvatar: updatedComment.userAvatar,
            content: updatedComment.content,
            createdAt: updatedComment.createdAt,
            type: updatedComment.type,
            mediaUrl: updatedComment.mediaUrl,
            replies: updatedComment.replies,
            reactionCounts: updatedComment.reactionCounts,
            currentUserReaction: updatedComment.currentUserReaction,
            depth: updatedComment.depth,
            replyToUserName: parent.userName,
          );

          parent.replies.add(commentWithReplyTo);
          // تحديث المرجع في الخريطة
          map[comment.id] = commentWithReplyTo;
        }
      }
    }

    // ترتيب التعليقات والردود حسب التاريخ
    roots.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    _sortRepliesRecursively(roots);
  }

  void _sortRepliesRecursively(List<Comment> comments) {
    for (final comment in comments) {
      if (comment.replies.isNotEmpty) {
        comment.replies.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        _sortRepliesRecursively(comment.replies);
      }
    }
  }

  Stream<List<Comment>> commentsStream(String postId) {
    // استخدم الدالة الأعلى fetchComments التى تتضمن جلب الملف الشخصى والتفاعلات لكل تعليق
    return fetchComments(postId);
  }

  /// جلب عدد التعليقات الفعلي للمنشور
  Future<int> getPostCommentsCount(String postId) async {
    try {
      final result = await _client
          .from('comments')
          .select('id')
          .eq('post_id', postId)
          .count(CountOption.exact);
      return result.count ?? 0;
    } catch (e) {
      return 0;
    }
  }

  // ------------------ Profile ------------------ //

  Future<Map<String, dynamic>?> fetchProfile(String userId) async {
    final data = await _client
        .from('profiles')
        .select('id,name,username,avatar_url,bio,email,website,gender,country,city,created_at,followers_count,following_count,is_verified,verification_status,verification_requested_at')
        .eq('id', userId)
        .maybeSingle();
    return data;
  }

  Future<void> updateProfile({String? name, String? bio, String? avatarUrl, String? username, String? website, String? gender, String? country, String? city}) async {
    // جلب القيم القديمة للتحقق من تغيّر الصورة
    final uid = _client.auth.currentUser!.id;
    String? oldAvatar;
    if (avatarUrl != null) {
      final old = await _client.from('profiles').select('avatar_url').eq('id', uid).maybeSingle();
      oldAvatar = old?['avatar_url'] as String?;
    }

    final updated = await _client.from('profiles').update({
      if (name != null) 'name': name,
      if (bio != null) 'bio': bio,
      if (avatarUrl != null) 'avatar_url': avatarUrl,
      if (username != null) 'username': username,
      if (website != null) 'website': website,
      if (gender != null) 'gender': gender,
      if (country != null) 'country': country,
      if (city != null) 'city': city,
    }).eq('id', uid).select().single();

    // NEW: ابقِ بيانات auth.metadata متسقة مع جدول profiles
    if (name != null || avatarUrl != null) {
      try {
        await _client.auth.updateUser(UserAttributes(data: {
          if (name != null) 'name': name,
          if (avatarUrl != null) 'avatar_url': avatarUrl,
        }));
      } catch (_) {
        // تجاهل خطأ التحديث على auth فى حال عدم توفر الصلاحيات حالياً
      }
    }

    // تحديث الكاش المحلى حتى تظهر التغييرات فورًا فى كل أجزاء التطبيق
    final currentCache = profilesCache.value;
    final newCache = Map<String, Map<String, dynamic>>.from(currentCache);
    newCache[_client.auth.currentUser!.id] = Map<String, dynamic>.from(updated);
    profilesCache.value = newCache;

    // إذا تغيّرت صورة الملف → أنشئ منشورًا جديدًا يعلن ذلك
    if (avatarUrl != null && avatarUrl.isNotEmpty && avatarUrl != oldAvatar) {
      // جلب اسم المستخدم الحالي
      final currentProfile = await _client.from('profiles').select('name').eq('id', uid).single();
      final userName = currentProfile['name'] ?? 'مستخدم';
      
      await _client.from('posts').insert({
        'user_id': uid,
        'content': 'قام $userName بتحديث صورة ملفه الشخصي',
        'type': 'image',
        'media_url': avatarUrl,
      });
    }
  }

  /// إحضار أحدث منشور لصورة الملف الشخصي (إن وُجد)
  Future<Post?> fetchAvatarPost(String userId) async {
    final profile = await fetchProfile(userId);
    final avatarUrl = profile?['avatar_url'] ?? '';
    if (avatarUrl == '') return null;

    final rows = await _client
        .from('posts')
        .select('''id,user_id,content,created_at,type,media_url,link_url,link_meta,bg_color,likes_count,dislikes_count,comments_count,shares_count,copies_count,views_count, profiles(name,avatar_url)''')
        .eq('user_id', userId)
        .eq('media_url', avatarUrl)
        .order('created_at', ascending: false)
        .limit(1);

    if (rows is List && rows.isNotEmpty) {
      final row = rows.first as Map<String, dynamic>;
      final profileMap = row['profiles'] ?? {};
      return Post(
        id: row['id'].toString(),
        userId: userId,
        userName: profileMap['name'] ?? 'مستخدم',
        userAvatar: profileMap['avatar_url'] ?? '',
        content: row['content'] ?? '',
        createdAt: DateTime.parse(row['created_at']),
        type: PostType.image,
        mediaUrl: row['media_url'],
        mediaUrls: row['media_urls'] != null ? List<String>.from(row['media_urls']) : null,
        linkUrl: row['link_url'],
        linkMeta: row['link_meta'],
        bgColor: row['bg_color'],
        likesCount: row['likes_count'] ?? 0,
        dislikesCount: row['dislikes_count'] ?? 0,
        sharesCount: row['shares_count'] ?? 0,
        commentsCount: row['comments_count'] ?? 0,
        copiesCount: row['copies_count'] ?? 0,
        viewsCount: row['views_count'] ?? 0,
      );
    }
    return null;
  }

  // ------------------ Social (Follow) ------------------ //

  /// جلب جميع المستخدمين (باستثناء المستخدم الحالى) مع حالة المتابعة
  Future<List<Map<String, dynamic>>> fetchUsers() async {
    final uid = _client.auth.currentUser!.id;

    // كل المستخدمين ماعدا الحالى
    final profiles = await _client
        .from('profiles')
        .select('id,name,username,avatar_url,bio,followers_count,following_count,is_verified')
        .neq('id', uid);

    // قائمة من يتابعهم المستخدم الحالى
    final followingRows = await _client
        .from('follows')
        .select('following_id')
        .eq('follower_id', uid);

    final followingIds = (followingRows as List).map((e) => e['following_id'] as String).toSet();

    return (profiles as List).map((p) {
      final id = p['id'] as String;
      return {
        'id': id,
        'name': p['name'] ?? 'مستخدم',
        'avatar_url': p['avatar_url'] ?? '',
        'bio': p['bio'] ?? '',
        'is_following': followingIds.contains(id),
        'followers_count': p['followers_count'] ?? 0,
        'following_count': p['following_count'] ?? 0,
        'is_verified': p['is_verified'] ?? false,
      };
    }).toList();
  }

  /// تُرجع true إذا أصبح المستخدم الآخر مُتابَعاً بعد هذا النداء، وإلا false
  Future<bool> toggleFollow(String otherUserId) async {
    try {
    final uid = _client.auth.currentUser!.id;

      // تحقق هل يتابع بالفعل
    final existing = await _client
        .from('follows')
        .select('id')
        .eq('follower_id', uid)
        .eq('following_id', otherUserId)
        .maybeSingle();

    bool nowFollowing;
    if (existing == null) {
      await _client.from('follows').insert({'follower_id': uid, 'following_id': otherUserId});
      nowFollowing = true;
      await _client.rpc('inc_follow_counters', params: {
        'target_id': otherUserId,
        'follower_id': uid,
        'delta': 1,
        }).catchError((_){ });
    } else {
      await _client.from('follows').delete().eq('id', existing['id']);
      nowFollowing = false;
      await _client.rpc('inc_follow_counters', params: {
        'target_id': otherUserId,
        'follower_id': uid,
        'delta': -1,
        }).catchError((_){ });
    }
    return nowFollowing;
    } catch (e) {
      throw Exception('فشل تغيير حالة المتابعة: $e');
    }
  }

  /// هل يتابع المستخدم الحالى المستخدم الآخر؟
  Future<bool> isFollowing(String otherUserId) async {
    final uid = _client.auth.currentUser!.id;
    final existing = await _client
        .from('follows')
        .select('id')
        .eq('follower_id', uid)
        .eq('following_id', otherUserId)
        .maybeSingle();
    return existing != null;
  }

  /// جلب منشورات مساحة محددة - نسخة مبسطة
  Future<List<Post>> fetchPostsBySpace(String spaceId) async {
    try {
      print('🔍 جلب منشورات المساحة: $spaceId');

      // جلب معلومات المساحة أولاً
      final spaceData = await _client
          .from('spaces')
          .select('name, owner_id')
          .eq('id', spaceId)
          .single();

      final spaceName = spaceData['name'] as String;
      print('📝 اسم المساحة: $spaceName');

      // جلب المنشورات من جدول space_posts
      final rows = await _client
          .from('space_posts')
          .select('''
            id,
            author_id,
            content,
            created_at,
            media_urls,
            link_url,
            link_title,
            link_description,
            link_image
          ''')
          .eq('space_id', spaceId)
          .order('created_at', ascending: false);

      print('📊 عدد المنشورات الموجودة: ${(rows as List).length}');

      // جلب معرفات المستخدمين الفريدة
      final authorIds = (rows as List).map((row) => row['author_id'] as String).toSet().toList();
      
      // جلب معلومات المستخدمين
      final userProfiles = await _getUserProfiles(authorIds);
      print('👥 تم جلب معلومات ${userProfiles.length} مستخدم');

      // إنشاء قائمة المنشورات
      final List<Post> posts = [];

      for (final row in (rows as List)) {
        final authorId = row['author_id'] as String;
        final userProfile = userProfiles[authorId];
        
        final post = Post(
          id: row['id'],
          userId: authorId,
          userName: userProfile?['name'] ?? userProfile?['username'] ?? 'مستخدم',
          userAvatar: userProfile?['avatar_url'] ?? '',
          content: row['content'] ?? '',
          createdAt: DateTime.parse(row['created_at']),
          type: PostType.text,
          mediaUrl: null,
          mediaUrls: row['media_urls'] != null ? List<String>.from(row['media_urls']) : null,
          linkUrl: row['link_url'],
          linkMeta: row['link_title'] != null ? {
            'title': row['link_title'],
            'description': row['link_description'],
            'image': row['link_image'],
          } : null,
          commentsCount: 0,
          likesCount: 0,
          dislikesCount: 0,
          sharesCount: 0,
          copiesCount: 0,
          viewsCount: 0,
          bgColor: null,
          privacy: PostPrivacy.everyone,
          isSpacePost: true,
          spaceId: spaceId,
          spaceName: spaceName,
        );

        posts.add(post);
        print('✅ تم إضافة منشور: ${post.id}');
        print('👤 صاحب المنشور: ${post.userName}');
        if (row['media_urls'] != null && (row['media_urls'] as List).isNotEmpty) {
          print('📸 المنشور يحتوي على ${(row['media_urls'] as List).length} صورة');
        }
      }

      print('🎉 إجمالي المنشورات المُرجعة: ${posts.length}');
      return posts;

    } catch (e) {
      print('❌ خطأ في جلب منشورات المساحة: $e');
      return [];
    }
  }

  /// دالة مساعدة لجلب معلومات المستخدمين
  Future<Map<String, Map<String, dynamic>>> _getUserProfiles(List<String> userIds) async {
    if (userIds.isEmpty) return {};
    
    try {
      final response = await _client
          .from('profiles')
          .select('id, name, username, avatar_url, is_verified')
          .inFilter('id', userIds);
      
      final Map<String, Map<String, dynamic>> profiles = {};
      for (final row in (response as List)) {
        profiles[row['id'] as String] = row as Map<String, dynamic>;
      }
      
      return profiles;
    } catch (e) {
      print('❌ خطأ في جلب معلومات المستخدمين: $e');
      return {};
    }
  }

  // دالة مساعدة لإضافة التفاعلات والإحصائيات
  Future<void> _addReactionsAndStats(List<Post> posts) async {
    final uid = _client.auth.currentUser?.id;
    final postIds = posts.map((p) => p.id).toList();

    try {
      // جلب التفاعلات
      final reactionRows = await _client
          .from('reactions')
          .select('post_id, type, user_id')
          .inFilter('post_id', postIds);

      // جلب المنشورات المحفوظة
      final savedRows = await _client
          .from('saved_posts')
          .select('post_id')
          .eq('user_id', uid ?? '')
          .inFilter('post_id', postIds);

      final savedPostIds = Set<String>.from(savedRows.map((r) => r['post_id']));

      // تطبيق التفاعلات على كل منشور
      for (int i = 0; i < posts.length; i++) {
        final post = posts[i];
        final postReactions = reactionRows.where((r) => r['post_id'] == post.id).toList();

        final reactionCounts = <ReactionType, int>{};
        ReactionType? userReaction;

        for (final reaction in postReactions) {
          final type = _mapReaction(reaction['type']);
          reactionCounts[type] = (reactionCounts[type] ?? 0) + 1;

          if (reaction['user_id'] == uid) {
            userReaction = type;
          }
        }

        posts[i] = post.copyWith(
          reactionCounts: reactionCounts,
          currentUserReaction: userReaction,
          isSaved: savedPostIds.contains(post.id),
          likesCount: reactionCounts[ReactionType.like] ?? 0,
          dislikesCount: reactionCounts[ReactionType.dislike] ?? 0,
        );

        print('✅ تم تحديث تفاعلات المنشور ${post.id}: ${reactionCounts.length} تفاعلات');
      }
    } catch (e) {
      print('❌ خطأ في جلب التفاعلات: $e');
    }
  }

  /// جلب منشورات مستخدم محدد
  Future<List<Post>> fetchPostsByUser(String userId) async {
    final uid = _client.auth.currentUser?.id;

    dynamic query = _client
        .from('posts')
        .select('''
          id,
          user_id,
          content,
          created_at,
          type,
          media_url,
          link_url,
          link_meta,
          bg_color,
          likes_count,
          dislikes_count,
          shares_count,
          comments_count,
          views_count,
          copies_count,
          shared_post_id,
          community_id,
          original:posts!shared_post_id(id,user_id,content,created_at,type,media_url,link_url,link_meta,profiles(name,avatar_url)),
          profiles(name,avatar_url,posts_privacy,is_verified)
        ''')
        .eq('user_id', userId)
        .isFilter('community_id', null) // استبعاد منشورات المجتمع من ملف المستخدم
        .order('created_at', ascending: false);

    // فحص الخصوصية: إذا كان صاحب الملف يشارك مع المتابعين فقط، تحقَّق من العلاقة
    final ownerProfile = await _client
        .from('profiles')
        .select('posts_privacy')
        .eq('id', userId)
        .maybeSingle();

    final ownerPrivacy = ownerProfile?['posts_privacy'] ?? 'everyone';

    if (ownerPrivacy == 'followers' && uid != null && uid != userId) {
      final rel = await _client
          .from('follows')
          .select('id')
          .eq('follower_id', uid)
          .eq('following_id', userId)
          .maybeSingle();
      if (rel == null) {
        // ليس متابعاً → أرجع قائمة فارغة
        return [];
      }
    }

    final data = await query;

    // بناء قائمة أولية من المنشورات
    final List<Post> posts = (data as List).map<Post>((row) {
      final profile = row['profiles'] ?? {};

      // --- بناء المنشور الأصلى عند المشاركة --- //
      Post? original;
      dynamic origData = row['original'];
      if (origData is List && origData.isNotEmpty) origData = origData.first;
      if (origData != null && origData is Map) {
        final origProfile = origData['profiles'] ?? {};
        original = Post(
          id: origData['id'].toString(),
          userId: origData['user_id'].toString(),
          userName: origProfile['name'] ?? 'مستخدم',
          userAvatar: origProfile['avatar_url'] ?? '',
          content: origData['content'] ?? '',
          createdAt: DateTime.parse(origData['created_at']),
          type: _mapType(origData['type']),
          mediaUrl: origData['media_url'],
          mediaUrls: origData['media_urls'] != null ? List<String>.from(origData['media_urls']) : null,
          linkUrl: origData['link_url'],
          linkMeta: origData['link_meta'],
          bgColor: origData['bg_color'],
          likesCount: 0,
          dislikesCount: 0,
          sharesCount: 0,
          commentsCount: 0,
          viewsCount: 0,
          reactionCounts: const {},
          copiesCount: 0,
          isVerified: origProfile['is_verified'] ?? false,
        );
      }

      return Post(
        id: row['id'].toString(),
        userId: row['user_id'].toString(),
        userName: profile['name'] ?? 'مستخدم',
        userAvatar: profile['avatar_url'] ?? '',
        content: row['content'] ?? '',
        createdAt: DateTime.parse(row['created_at']),
        type: _mapType(row['type']),
        mediaUrl: row['media_url'],
        mediaUrls: row['media_urls'] != null ? List<String>.from(row['media_urls']) : null,
        linkUrl: row['link_url'],
        linkMeta: row['link_meta'],
        bgColor: row['bg_color'],
        likesCount: row['likes_count'] ?? 0,
        dislikesCount: row['dislikes_count'] ?? 0,
        sharesCount: row['shares_count'] ?? 0,
        commentsCount: row['comments_count'] ?? 0,
        viewsCount: row['views_count'] ?? 0,
        copiesCount: row['copies_count'] ?? 0,
        isVerified: profile['is_verified'] ?? false,
        originalPost: original,
      );
    }).toList();

    // ✂️ استبعاد المستخدمين المحظورين بعد ربط المنشورات الأصلية
    final blockedIds = await blockedUserIds();
    if (blockedIds.isNotEmpty) {
      posts.removeWhere((p) => blockedIds.contains(p.userId));
    }

    if (posts.isEmpty) return posts;

    // ربط المنشورات الأصلية للمنشورات المُعادة فى حال فشل الـ join
    await _linkSharedOriginalsFallback(data as List, posts);

    final postIds = posts.map((p) => p.id).toList();

    // --------- جلب ملخص التفاعلات لجميع المستخدمين --------- //
    final reactionRows = await _client
        .from('reactions')
        .select('post_id,type')
        .inFilter('post_id', postIds);

    final Map<String, Map<ReactionType, int>> aggregated = {};
    for (final row in reactionRows as List) {
      final pid = row['post_id'].toString();
      final rt = _mapReaction(row['type']);
      if (rt == ReactionType.none) continue;
      aggregated.putIfAbsent(pid, () => {});
      aggregated[pid]![rt] = (aggregated[pid]![rt] ?? 0) + 1;
    }

    for (var i = 0; i < posts.length; i++) {
      posts[i] = posts[i].copyWith(reactionCounts: aggregated[posts[i].id] ?? {});
    }

    // --------- جلب تفاعل المستخدم الحالى --------- //
    if (uid != null) {
      final reactRows = await _client
          .from('reactions')
          .select('post_id,type')
          .inFilter('post_id', postIds)
          .eq('user_id', uid);

      final Map<String, ReactionType> userReactions = {};
      for (final r in reactRows as List) {
        userReactions[r['post_id'].toString()] = _mapReaction(r['type']);
      }

      for (var i = 0; i < posts.length; i++) {
        final reaction = userReactions[posts[i].id];
        if (reaction != null) {
          posts[i] = posts[i].copyWith(currentUserReaction: reaction);
        }
      }
    }

    return posts;
  }

  // ------------------ Account ------------------ //

  Future<void> changeEmail(String newEmail, String currentPassword) async {
    try {
      print('=== تغيير البريد الإلكتروني ===');
      print('البريد الجديد: $newEmail');
      
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // إعادة المصادقة بالبريد الحالي وكلمة المرور
      await _client.auth.signInWithPassword(
        email: currentUser.email!,
        password: currentPassword,
      );

      // تحديث البريد الإلكتروني
      await _client.auth.updateUser(UserAttributes(email: newEmail));
      
      print('✅ تم إرسال رابط التحقق إلى البريد الجديد');
    } catch (e) {
      print('❌ خطأ في تغيير البريد الإلكتروني: $e');
      if (e.toString().contains('Invalid login credentials')) {
        throw Exception('كلمة المرور غير صحيحة');
      } else if (e.toString().contains('already registered')) {
        throw Exception('البريد الإلكتروني مستخدم بالفعل');
      } else {
        throw Exception('فشل في تغيير البريد الإلكتروني: $e');
      }
    }
  }

  Future<void> changePassword(String currentPassword, String newPassword, String confirmPassword) async {
    try {
      print('=== تغيير كلمة المرور ===');
      
      final currentUser = _client.auth.currentUser;
      if (currentUser == null) {
        throw Exception('المستخدم غير مسجل الدخول');
      }

      // التحقق من تطابق كلمة المرور الجديدة
      if (newPassword != confirmPassword) {
        throw Exception('كلمة المرور الجديدة غير متطابقة');
      }

      // التحقق من طول كلمة المرور
      if (newPassword.length < 6) {
        throw Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
      }

      // إعادة المصادقة بالبريد الحالي وكلمة المرور الحالية
      await _client.auth.signInWithPassword(
        email: currentUser.email!,
        password: currentPassword,
      );

      // تحديث كلمة المرور
      await _client.auth.updateUser(UserAttributes(password: newPassword));
      
      print('✅ تم تغيير كلمة المرور بنجاح');
    } catch (e) {
      print('❌ خطأ في تغيير كلمة المرور: $e');
      if (e.toString().contains('Invalid login credentials')) {
        throw Exception('كلمة المرور الحالية غير صحيحة');
      } else {
        throw Exception('فشل في تغيير كلمة المرور: $e');
      }
    }
  }

  Future<void> signOut() async {
    await _client.auth.signOut();
  }

  Future<void> deactivateAccount({int? duration}) async {
    final uid = _client.auth.currentUser!.id;
    final now = DateTime.now();
    final deactivatedUntil = duration != null 
        ? now.add(Duration(days: duration))
        : null;
    
    await _client.from('profiles').update({
      'status': 'deactivated',
      'deactivated_at': now.toIso8601String(),
      'deactivated_until': deactivatedUntil?.toIso8601String(),
      'deactivation_duration_days': duration,
    }).eq('id', uid);
  }

  Future<void> reactivateAccount() async {
    final uid = _client.auth.currentUser!.id;
    await _client.from('profiles').update({
      'status': 'active',
      'deactivated_at': null,
      'deactivated_until': null,
      'deactivation_duration_days': null,
    }).eq('id', uid);
  }

  Future<void> deleteAccount({int? delayDays}) async {
    final uid = _client.auth.currentUser!.id;
    final now = DateTime.now();
    final deleteDate = delayDays != null 
        ? now.add(Duration(days: delayDays))
        : now.add(const Duration(days: 30));
    
    await _client.from('profiles').update({
      'status': 'pending_delete',
      'deleted_at': deleteDate.toIso8601String(),
      'delete_requested_at': now.toIso8601String(),
      'delete_delay_days': delayDays ?? 30,
    }).eq('id', uid);
  }

  Future<void> cancelAccountDeletion() async {
    final uid = _client.auth.currentUser!.id;
    await _client.from('profiles').update({
      'status': 'active',
      'deleted_at': null,
      'delete_requested_at': null,
      'delete_delay_days': null,
    }).eq('id', uid);
  }

  // ------------------ User Settings ------------------ //

  Future<Map<String, dynamic>> fetchSettings() async {
    try {
      final uid = _client.auth.currentUser!.id;
      final data = await _client
          .from('user_settings')
          .select()
          .eq('user_id', uid)
          .maybeSingle();
      
      print('Settings fetched successfully for user: $uid');
      return data ?? {};
    } catch (e) {
      print('Error fetching settings: $e');
      print('Error type: ${e.runtimeType}');
      
      // إرجاع قيم افتراضية في حالة الخطأ
      return {
        'notify_follow': true,
        'notify_chat': true,
        'notify_app': true,
        'post_visibility': 'public',
        'comment_permission': 'everyone',
        'show_activity': true,
      };
    }
  }

  Future<void> updateSettings(Map<String, dynamic> values) async {
    try {
      final uid = _client.auth.currentUser!.id;
      print('=== updateSettings ===');
      print('User ID: $uid');
      print('Values to update: $values');
      
      // تصفية القيم لتتضمن فقط الأعمدة الموجودة
      final validValues = <String, dynamic>{};
      for (final entry in values.entries) {
        // استبعاد الأعمدة التي يتم تحديثها تلقائياً
        if (entry.key != 'updated_at' && entry.key != 'created_at' && entry.key != 'id') {
          validValues[entry.key] = entry.value;
        }
      }
      
      final dataToInsert = {
        'user_id': uid,
        ...validValues,
      };
      print('Data to insert: $dataToInsert');
      
      await _client.from('user_settings').upsert(dataToInsert);
      
      print('Settings updated successfully for user: $uid');
    } catch (e) {
      print('Error updating settings: $e');
      print('Error type: ${e.runtimeType}');
      print('Error details: $e');
      
      // رسالة خطأ أكثر وضوحاً
      String errorMessage = 'فشل في حفظ الإعدادات';
      if (e.toString().contains('column') && e.toString().contains('not found')) {
        errorMessage = 'خطأ في قاعدة البيانات: بعض الأعمدة غير موجودة. يرجى تحديث قاعدة البيانات.';
      } else if (e.toString().contains('updated_at')) {
        errorMessage = 'خطأ في قاعدة البيانات: عمود updated_at غير موجود. يرجى تحديث قاعدة البيانات.';
      } else if (e.toString().contains('permission')) {
        errorMessage = 'خطأ في الصلاحيات: لا يمكن حفظ الإعدادات.';
      } else {
        errorMessage = 'فشل في حفظ الإعدادات: $e';
      }
      
      throw Exception(errorMessage);
    }
  }

  // ------------------ Notifications ------------------ //

  Future<List<AppNotification>> fetchNotifications() async {
    final uid = _client.auth.currentUser!.id;
    final rows = await _client
        .from('notifications')
        .select('''id,type,ref_id,created_at,read, sender:profiles!notifications_sender_id_fkey(name,avatar_url)''')
        .eq('receiver_id', uid)
        .order('created_at', ascending: false);

    return (rows as List).map((r) {
      return AppNotification(
        id: r['id'].toString(),
        senderId: '',
        senderName: (r['sender']?['name']) ?? 'مستخدم',
        senderAvatar: (r['sender']?['avatar_url']) ?? '',
        type: r['type'],
        refId: r['ref_id']?.toString(),
        createdAt: DateTime.parse(r['created_at']),
        read: r['read'] ?? false,
      );
    }).toList();
  }

  Future<int> unreadNotificationsCount() async {
    final uid = _client.auth.currentUser!.id;
    final res = await _client
        .from('notifications')
        .select('id')
        .eq('receiver_id', uid)
        .eq('read', false)
        .count(CountOption.exact);
    return res.count ?? 0;
  }

  Future<void> markNotificationRead(String id) async {
    await _client.from('notifications').update({'read': true}).eq('id', id);
  }

  Future<void> setNotificationRead(String id, bool read) async {
    await _client.from('notifications').update({'read': read}).eq('id', id);
  }

  Future<void> markAllNotificationsRead() async {
    final uid = _client.auth.currentUser!.id;
    await _client.from('notifications').update({'read': true}).eq('receiver_id', uid);
  }

  /// إرسال إشعار (live) إلى مستخدم معين عند بدء البث المباشر
  Future<void> createLiveNotification({required String receiverId, required String streamId}) async {
    await _client.from('notifications').insert({
      'receiver_id': receiverId,
      'sender_id': _client.auth.currentUser!.id,
      'type': 'live',
      'ref_id': streamId,
    });
  }

  // ------------------ Chats ------------------ //

  Future<String> getOrCreateChat(String otherId) async {
    final uid = _client.auth.currentUser!.id;
    // اجلب محادثة إن وجدت
    final existing = await _client
        .from('chats')
        .select('id')
        .or('and(user1.eq.$uid,user2.eq.$otherId),and(user1.eq.$otherId,user2.eq.$uid)')
        .maybeSingle();
    if (existing != null) return existing['id'].toString();

    // --- فحص إعداد الخصوصية للرسائل لدى المستقبل ---
    final receiver = await _client
        .from('profiles')
        .select('messages_privacy')
        .eq('id', otherId)
        .maybeSingle();

    final privacy = receiver?['messages_privacy'] ?? 'everyone';
    if (privacy == 'followers') {
      final followsRow = await _client
          .from('follows')
          .select('id')
          .eq('follower_id', uid)
          .eq('following_id', otherId)
          .maybeSingle();

      if (followsRow == null) {
        throw Exception('لا يمكن مراسلة هذا المستخدم إلا إذا كنت متابعًا له.');
      }
    }

    // إنشاء جديدة
    final inserted = await _client.from('chats').insert({'user1': uid, 'user2': otherId}).select().single();
    return inserted['id'].toString();
  }

  Future<List<Chat>> fetchChats() async {
    final uid = _client.auth.currentUser!.id;
    final rows = await _client.from('chats').select('''id,user1,user2,last_message,last_message_at,
      profiles!chats_user1_fkey(id,name,avatar_url), profiles!chats_user2_fkey(id,name,avatar_url)''').order('last_message_at', ascending: false);

    return (rows as List).map((r) {
      final profilesList = (r['profiles'] as List?) ?? [];

      // ابحث عن ملف المستخدم الآخر (غير الحالى)
      Map<String, dynamic>? other;
      if (profilesList.isNotEmpty) {
        other = profilesList.firstWhere(
          (p) => p != null && p['id'] != uid,
          orElse: () => profilesList.first,
        );
      }

      // فى حال فشل الربط أو كان الجدول فارغاً، أنشئ قيمة افتراضية لتجنب الأعطال
      other ??= {'id': '', 'name': 'مستخدم', 'avatar_url': ''};

      return Chat(
        id: r['id'],
        otherId: other['id'] ?? '',
        otherName: (other['name'] ?? 'مستخدم') as String,
        otherAvatar: (other['avatar_url'] ?? '') as String,
        lastMessage: r['last_message'] ?? '',
        lastAt: r['last_message_at'] != null ? DateTime.parse(r['last_message_at']) : null,
      );
    }).toList();
  }

  Stream<List<Message>> messagesStream(String chatId) {
    return _client
        .from('messages')
        .stream(primaryKey: ['id'])
        .eq('chat_id', chatId)
        .order('created_at', ascending: true)
        .map((rows) => (rows as List).map(_mapMsg).toList());
  }

  Message _mapMsg(dynamic r) {
    return Message(
      id: r['id'].toString(),
      chatId: r['chat_id'],
      senderId: r['sender_id'],
      content: r['content'] ?? '',
      type: _mapMedia(r['media_type']),
      mediaUrl: r['media_url'],
      createdAt: DateTime.parse(r['created_at']),
      readAt: r['read_at'] != null ? DateTime.parse(r['read_at']) : null,
    );
  }

  MediaType _mapMedia(String? v) {
    switch (v) {
      case 'image':
        return MediaType.image;
      case 'video':
        return MediaType.video;
      case 'link':
        return MediaType.link;
      case 'emoji':
        return MediaType.emoji;
      case 'audio':
        return MediaType.audio;
      case 'voice':
        return MediaType.voice;
      default:
        return MediaType.text;
    }
  }

  Future<void> sendMessage({required String chatId, required String content, MediaType type = MediaType.text, Uint8List? bytes}) async {
    String? url;
    if (bytes != null) {
      final ext = type == MediaType.image ? 'jpg' : (type == MediaType.video ? 'mp4' : 'aac');
      final path = 'chat/$chatId/${DateTime.now().millisecondsSinceEpoch}.$ext';
      url = await uploadMedia(bytes, path);
    }
    await _client.from('messages').insert({
      'chat_id': chatId,
      'sender_id': _client.auth.currentUser!.id,
      'content': content,
      'media_url': url,
      'media_type': type.name,
    });
  }

  /// حذف الرسالة للجميع – يستبدل محتواها بنص ثابت ويزيل أى ميديا.
  Future<void> deleteMessageForAll(String messageId) async {
    await _client.from('messages').update({
      'content': 'تم حذف هذه الرسالة',
      'media_type': 'text',
      'media_url': null,
    }).eq('id', messageId);
  }

  Stream<List<Chat>> chatsStream() {
    final uid = _client.auth.currentUser!.id;

    return _client
        .from('chats')
        .stream(primaryKey: ['id'])
        .order('last_message_at', ascending: false)
        .asyncMap((rows) async {
          // حدد معرّفات الطرف الآخر فى كل محادثة
          final Set<String> otherIds = {};
          for (final r in rows) {
            final user1 = r['user1'] as String?;
            final user2 = r['user2'] as String?;
            final otherId = ((user1 == uid) ? user2 : user1) ?? '';
            if (otherId != null) otherIds.add(otherId);
          }

          // اجلب بيانات الملفات الشخصية دفعة واحدة
          Map<String, Map<String, dynamic>> profileMap = {};
          if (otherIds.isNotEmpty) {
            final profRows = await _client
                .from('profiles')
                .select('id,name,avatar_url')
                .inFilter('id', otherIds.toList());
            for (final pr in profRows as List) {
              profileMap[pr['id']] = pr;
            }
          }

          // NEW: حدّث الكاش المحلى بالملفات الشخصية لجعل الصور تظهر فورًا
          final currentCache = profilesCache.value;
          bool cacheChanged = false;
          final updatedCache = Map<String, Map<String, dynamic>>.from(currentCache);
          for (final entry in profileMap.entries) {
            final existing = currentCache[entry.key];
            final avatarNew = entry.value['avatar_url'] ?? '';
            final shouldUpdate = existing == null || (existing['avatar_url'] ?? '') == '' && (avatarNew as String).isNotEmpty;
            if (shouldUpdate) {
              updatedCache[entry.key] = Map<String, dynamic>.from(entry.value);
              cacheChanged = true;
            }
          }
          if (cacheChanged) {
            profilesCache.value = updatedCache;
          }

          final futures = (rows as List).map((r) async {
            final user1 = r['user1'] as String?;
            final user2 = r['user2'] as String?;
            final otherId = ((user1 == uid) ? user2 : user1) ?? '';

            final prof = profileMap[otherId] ?? {'name': 'مستخدم', 'avatar_url': ''};

            // احضار آخر رسالة لمعرفة حالة القراءة
            final lastMsg = await _client
                .from('messages')
                .select('sender_id, read_at')
                .eq('chat_id', r['id'])
                .order('created_at', ascending: false)
                .limit(1)
                .maybeSingle();

            final bool lastFromMe = lastMsg?['sender_id'] == uid;
            final bool lastRead    = lastFromMe && lastMsg?['read_at'] != null;

            return Chat(
              id: r['id'],
              otherId: otherId,
              otherName: prof['name'] ?? 'مستخدم',
              otherAvatar: prof['avatar_url'] ?? '',
              lastMessage: r['last_message'] ?? '',
              lastAt: r['last_message_at'] != null ? DateTime.parse(r['last_message_at']) : null,
              unreadCount: r['unread_count'] ?? 0,
              lastFromMe: lastFromMe,
              lastRead: lastRead,
            );
          });

          return Future.wait(futures);
        });
  }

  Future<void> markMessagesRead(String chatId) async {
    final uid = _client.auth.currentUser!.id;
    // حدِّث جميع الرسائل الواردة (ليست منى) التى لم تُقرأ بعد
    await _client
        .from('messages')
        .update({'read_at': DateTime.now().toIso8601String()})
        .eq('chat_id', chatId)
        .neq('sender_id', uid)
        .filter('read_at', 'is', 'null');
  }

  // ---------- Pin / Mute Chats ---------- //
  final ValueNotifier<Set<String>> pinnedChats = ValueNotifier(<String>{});
  final ValueNotifier<Set<String>> mutedChats  = ValueNotifier(<String>{});

  Future<void> loadPinnedMuted() async {
    final uid = _client.auth.currentUser!.id;
    final pinRows = await _client.from('pinned_chats').select('chat_id').eq('user_id', uid);
    pinnedChats.value = {for (final r in pinRows) r['chat_id'] as String};

    final muteRows = await _client.from('muted_chats').select('chat_id').eq('user_id', uid);
    mutedChats.value = {for (final r in muteRows) r['chat_id'] as String};
  }

  Future<void> setChatPinned(String chatId, bool pinned) async {
    final uid = _client.auth.currentUser!.id;
    if (pinned) {
      await _client.from('pinned_chats').upsert({'user_id': uid, 'chat_id': chatId});
      pinnedChats.value = {...pinnedChats.value, chatId};
    } else {
      await _client.from('pinned_chats').delete().eq('user_id', uid).eq('chat_id', chatId);
      final s = {...pinnedChats.value};
      s.remove(chatId);
      pinnedChats.value = s;
    }
  }

  Future<void> setChatMuted(String chatId, bool muted) async {
    final uid = _client.auth.currentUser!.id;
    if (muted) {
      await _client.from('muted_chats').upsert({'user_id': uid, 'chat_id': chatId});
      mutedChats.value = {...mutedChats.value, chatId};
    } else {
      await _client.from('muted_chats').delete().eq('user_id', uid).eq('chat_id', chatId);
      final s = {...mutedChats.value};
      s.remove(chatId);
      mutedChats.value = s;
    }
  }

  // ------------------ Chat management ------------------ //

  Future<void> blockUser(String otherId) async {
    final uid = _client.auth.currentUser!.id;
    await _client.from('blocks').upsert({'user_id': uid, 'blocked_user_id': otherId});
  }

  Future<void> unblockUser(String otherId) async {
    await _client
        .from('blocks')
        .delete()
        .eq('user_id', _client.auth.currentUser!.id)
        .eq('blocked_user_id', otherId);
  }

  Future<void> archiveChat(String chatId, bool archived) async {
    await _client.from('chat_visibility').upsert({
      'chat_id': chatId,
      'user_id': _client.auth.currentUser!.id,
      'archived': archived,
    });
  }

  Future<void> deleteChatLocally(String chatId) async {
    await _client.from('chat_visibility').upsert({
      'chat_id': chatId,
      'user_id': _client.auth.currentUser!.id,
      'deleted': true,
    });
  }

  Future<void> reportChat(String chatId, String reason) async {
    await _client.from('chat_reports').insert({
      'chat_id': chatId,
      'reporter_id': _client.auth.currentUser!.id,
      'reason': reason,
    });
  }

  Future<void> editMessage({required String messageId, required String newContent}) async {
    await _client
        .from('messages')
        .update({'content': newContent, 'edited_at': DateTime.now().toIso8601String()})
        .eq('id', messageId);
  }

  Future<void> sendReply({required String chatId, required String replyTo, required String content}) async {
    await sendMessage(chatId: chatId, content: content, type: MediaType.text);
    // حدِّث حقل reply_to لأحدث رسالة أرسلتها بالـ content نفسه لمنع تضارب
    await _client
        .from('messages')
        .update({'reply_to': replyTo})
        .eq('chat_id', chatId)
        .eq('sender_id', _client.auth.currentUser!.id)
        .order('created_at', ascending: false)
        .limit(1);
  }

  // ------------------ Notifications realtime ------------------ //

  Stream<List<AppNotification>> notificationsStream() {
    final uid = _client.auth.currentUser!.id;
    return _client
        .from('notifications')
        .stream(primaryKey: ['id'])
        .eq('receiver_id', uid)
        .order('created_at', ascending: false)
        .map((rows) => (rows as List).map((r) {
              return AppNotification(
                id: r['id'].toString(),
                senderId: '',
                senderName: (r['sender']?['name']) ?? 'مستخدم',
                senderAvatar: (r['sender']?['avatar_url']) ?? '',
                type: r['type'],
                refId: r['ref_id']?.toString(),
                createdAt: DateTime.parse(r['created_at']),
                read: r['read'] ?? false,
              );
            }).toList());
  }

  // ------------------ Calls ------------------ //

  Future<String> createCall({required String chatId, required String type}) async {
    final row = await _client.from('calls').insert({
      'chat_id': chatId,
      'caller_id': _client.auth.currentUser!.id,
      'type': type,
      'status': 'ringing',
    }).select().single();
    return row['id'].toString();
  }

  Stream<String> callStatusStream(String callId) {
    return _client
        .from('calls')
        .stream(primaryKey: ['id'])
        .eq('id', callId)
        .limit(1)
        .map((rows) => rows.isNotEmpty ? rows.first['status'] as String : 'ended');
  }

  Future<void> updateCallStatus(String callId, String status) async {
    await _client.from('calls').update({'status': status}).eq('id', callId);
  }

  // ------------------ إدارة المنشورات (حفظ، حذف، خصوصية) ------------------ //

  Future<void> toggleSavePost(String postId) async {
    try {
      final uid = _client.auth.currentUser!.id;
      print('=== تبديل حفظ المنشور ===');
      print('Post ID: $postId');
      print('User ID: $uid');

      final existing = await _client
          .from('saved_posts')
          .select('id')
          .eq('post_id', postId)
          .eq('user_id', uid)
          .maybeSingle();

      if (existing == null) {
        // حفظ المنشور
        await _client.from('saved_posts').insert({'post_id': postId, 'user_id': uid});
        print('✅ تم حفظ المنشور بنجاح');
      } else {
        // إزالة المنشور من المحفوظات
        await _client.from('saved_posts').delete().eq('id', existing['id']);
        print('✅ تم إزالة المنشور من المحفوظات بنجاح');
      }
    } catch (e) {
      print('❌ خطأ في تبديل حفظ المنشور: $e');
      throw Exception('فشل في حفظ المنشور: $e');
    }
  }

  // إخفاء منشور
  Future<void> hidePost(String postId, String reason) async {
    try {
      final uid = _client.auth.currentUser!.id;
      print('=== إخفاء المنشور ===');
      print('Post ID: $postId');
      print('User ID: $uid');
      print('Reason: $reason');

      await _client.from('hidden_posts').upsert({
        'user_id': uid,
        'post_id': postId,
        'reason': reason,
        'hidden_at': DateTime.now().toIso8601String(),
      });

      print('✅ تم إخفاء المنشور بنجاح');
    } catch (e) {
      print('❌ خطأ في إخفاء المنشور: $e');
      throw Exception('فشل في إخفاء المنشور: $e');
    }
  }

  // إلغاء إخفاء منشور
  Future<void> unhidePost(String postId) async {
    try {
      final uid = _client.auth.currentUser!.id;
      print('=== إلغاء إخفاء المنشور ===');
      print('Post ID: $postId');
      print('User ID: $uid');

      await _client
          .from('hidden_posts')
          .delete()
          .eq('user_id', uid)
          .eq('post_id', postId);

      print('✅ تم إلغاء إخفاء المنشور بنجاح');
    } catch (e) {
      print('❌ خطأ في إلغاء إخفاء المنشور: $e');
      throw Exception('فشل في إلغاء إخفاء المنشور: $e');
    }
  }

  // جلب المنشورات المخفية للمستخدم
  Future<Set<String>> getHiddenPostIds() async {
    try {
      final uid = _client.auth.currentUser!.id;
      final hiddenRows = await _client
          .from('hidden_posts')
          .select('post_id')
          .eq('user_id', uid);

      final hiddenPostIds = (hiddenRows as List).map((r) => r['post_id'] as String).toSet();
      print('المنشورات المخفية: $hiddenPostIds');
      return hiddenPostIds;
    } catch (e) {
      print('❌ خطأ في جلب المنشورات المخفية: $e');
      return <String>{};
    }
  }

  Future<void> deletePost(String postId) async {
    await _client.from('posts').delete().eq('id', postId);
  }

  Future<void> updatePostPrivacy(String postId, String visibility) async {
    await _client.from('posts').update({'visibility': visibility}).eq('id', postId);
  }

  Future<void> updatePostContent(String postId, String newContent) async {
    await _client.from('posts').update({'content': newContent}).eq('id', postId);
  }

  Future<void> updatePostCommentPermission(String postId, String permission) async {
    await _client.from('posts').update({'comment_permission': permission}).eq('id', postId);
  }

  // ------------------ Stories management ------------------ //

  Future<void> deleteStory(String storyId) async {
    await _client.from('stories').delete().eq('id', storyId);
  }

  Future<void> reportStory(String storyId, String reason) async {
    await _client.from('story_reports').insert({
      'story_id': storyId,
      'reporter_id': _client.auth.currentUser!.id,
      'reason': reason,
    });
  }

  // ------------------ Story interactions ------------------ //

  Future<void> toggleStoryReaction({required String storyId, required ReactionType type}) async {
    final uid = _client.auth.currentUser!.id;
    
    try {
      // استخدام الدالة الجديدة
      await _client.rpc('toggle_story_reaction', params: {
        'p_story_id': storyId,
        'p_user_id': uid,
        'p_type': type.name,
      });
      print('✅ تم تبديل تفاعل القصة: $storyId، النوع: ${type.name}');
    } catch (e) {
      print('❌ خطأ في تبديل تفاعل القصة: $e');
      // fallback للطريقة القديمة
      final existing = await _client
          .from('story_reactions')
          .select('id,type')
          .eq('story_id', storyId)
          .eq('user_id', uid)
          .maybeSingle();

      if (existing == null) {
        await _client.from('story_reactions').insert({
          'story_id': storyId,
          'user_id': uid,
          'type': type.name,
        });
      } else {
        if (existing['type'] == type.name) {
          // remove reaction
          await _client.from('story_reactions').delete().eq('id', existing['id']);
        } else {
          await _client.from('story_reactions').update({'type': type.name}).eq('id', existing['id']);
        }
      }
    }
  }

  Future<void> createStoryComment({required String storyId, required String content}) async {
    await _client.from('story_comments').insert({
      'story_id': storyId,
      'user_id': _client.auth.currentUser!.id,
      'content': content,
    });
  }

  Stream<List<Comment>> storyCommentsStream(String storyId) {
    return _client
        .from('story_comments')
        .stream(primaryKey: ['id'])
        .eq('story_id', storyId)
        .order('created_at', ascending: false)
        .map((rows) => (rows as List).map((r) {
              return Comment(
                id: r['id'].toString(),
                postId: storyId,
                userId: r['user_id'],
                content: r['content'] ?? '',
                type: CommentType.text,
                createdAt: DateTime.parse(r['created_at']),
                // جلب بيانات الملف الشخصي من العلاقة أو الكاش
                userName: ((r['profiles'] as Map?) ?? profilesCache.value[r['user_id']] ?? {})['name'] ?? 'مستخدم',
                userAvatar: ((r['profiles'] as Map?) ?? profilesCache.value[r['user_id']] ?? {})['avatar_url'] ?? '',
                reactionCounts: const {},
              );
            }).toList());
  }

  Future<List<Map<String, dynamic>>> fetchFollowingUsers() async {
    final uid = _client.auth.currentUser!.id;
    try {
      // استعلام مباشر للمتابَعين
      final data = await _client
          .from('follows')
          .select('following_id')
          .eq('follower_id', uid);

      final followingIds = (data as List).map((e) => e['following_id'] as String).toList();
      if (followingIds.isEmpty) return [];

      final profiles = await _client
          .from('profiles')
          .select('id,name,username,avatar_url')
          .inFilter('id', followingIds);

      return (profiles as List).cast<Map<String, dynamic>>();
    } catch (e) {
      throw 'خطأ في جلب المتابَعين: ${e.toString()}';
    }
  }

  // ------------------ Groups ------------------ //

  GroupPrivacy _mapPrivacy(String? v) {
    switch (v) {
      case 'private':
        return GroupPrivacy.private;
      case 'hidden':
        return GroupPrivacy.hidden;
      default:
        return GroupPrivacy.public;
    }
  }

  Future<List<Group>> fetchGroups() async {
    final uid = _client.auth.currentUser?.id;
    final data = await _client
        .from('groups')
        .select('''id, name, description, owner_id, created_at, members:group_members(count), membership:group_members!left(user_id)''');

    return (data as List).map((row) {
      final membersList = row['members'] as List?;
      int count = 0;
      if (membersList != null && membersList.isNotEmpty) {
        final first = membersList.first;
        if (first is Map && first.containsKey('count')) {
          count = first['count'] as int;
        }
      }
      bool joined = false;
      final membership = row['membership'] as List?;
      if (uid != null && membership != null && membership.any((m) => m['user_id'] == uid)) {
        joined = true;
      }
      return Group(
        id: row['id'].toString(),
        name: row['name'] ?? '',
        description: row['description'] ?? '',
        ownerId: row['owner_id'] ?? '',
        coverUrl: row['cover_url'] ?? '',
        privacy: _mapPrivacy(row['privacy']),
        createdAt: DateTime.parse(row['created_at']),
        membersCount: count,
        joined: joined,
        postsCount: 0,
        isAdmin: false,
      );
    }).toList();
  }

  Future<String> createGroup({required String name, String description = ''}) async {
    final uid = _client.auth.currentUser!.id;
    final row = await _client
        .from('groups')
        .insert({
          'name': name,
          'description': description,
          'owner_id': uid,
          'created_at': DateTime.now().toIso8601String(),
        })
        .select('id')
        .maybeSingle();

    final groupId = row?['id'].toString() ?? '';

    if (groupId.isNotEmpty) {
      // أضف المالك كمسؤول
      await _client.from('group_members').upsert({
        'group_id': groupId,
        'user_id': uid,
        'role': 'admin',
      });
    }

    return groupId;
  }

  Future<void> toggleGroupMembership(String groupId) async {
    final uid = _client.auth.currentUser!.id;
    final existing = await _client
        .from('group_members')
        .select('id')
        .eq('group_id', groupId)
        .eq('user_id', uid)
        .maybeSingle();
    if (existing != null) {
      await _client.from('group_members').delete().eq('id', existing['id']);
    } else {
      await _client.from('group_members').insert({'group_id': groupId, 'user_id': uid});
    }
  }

  Future<Group> fetchGroup(String groupId) async {
    final uid = _client.auth.currentUser?.id;

    try {
      // المحاولة الأولى: الاستعلام الذى يفترض وجود عمود role
      var data = await _client
          .from('groups')
          .select('''
            id,name,description,cover_url,owner_id,privacy,created_at,
            posts:group_posts(count),
            members:group_members(count),
            role:group_members!left(user_id,role)
          ''')
          .eq('id', groupId)
          .maybeSingle();

      if (data == null) throw 'Group not found';
      final count = ((data['members'] as List?)?.first?['count'] ?? 0) as int;
      final postsCount = ((data['posts'] as List?)?.first?['count'] ?? 0) as int;
      final joined = (data['role'] as List?)?.isNotEmpty ?? false;
      final isAdmin = (data['role'] as List?)?.any((r) => r['role'] == 'admin') ?? false;
      return Group(
        id: data['id'].toString(),
        name: data['name'] ?? '',
        description: data['description'] ?? '',
        ownerId: data['owner_id'] ?? '',
        coverUrl: data['cover_url'] ?? '',
        privacy: _mapPrivacy(data['privacy']),
        createdAt: DateTime.parse(data['created_at']),
        membersCount: count,
        postsCount: postsCount,
        joined: joined,
        isAdmin: isAdmin,
      );
    } on PostgrestException catch (e) {
      // إذا كان السبب هو عدم وجود العمود role، أعد الاستعلام بدون العمود
      if (e.code == '42703') {
        final data = await _client
            .from('groups')
            .select('''id,name,description,cover_url,owner_id,privacy,created_at,posts:group_posts(count),members:group_members(count)''')
            .eq('id', groupId)
            .maybeSingle();
        if (data == null) throw 'Group not found';
        final count = ((data['members'] as List?)?.first?['count'] ?? 0) as int;
        final postsCount = ((data['posts'] as List?)?.first?['count'] ?? 0) as int;
        return Group(
          id: data['id'].toString(),
          name: data['name'] ?? '',
          description: data['description'] ?? '',
          ownerId: data['owner_id'] ?? '',
          coverUrl: data['cover_url'] ?? '',
          privacy: _mapPrivacy(data['privacy']),
          createdAt: DateTime.parse(data['created_at']),
          membersCount: count,
          postsCount: postsCount,
          joined: false,
          isAdmin: false,
        );
      }
      rethrow; // أخطاء أخرى
    }
  }

  Future<void> requestJoin(String groupId) async {
    await _client.from('group_join_requests').insert({'group_id': groupId, 'user_id': _client.auth.currentUser!.id});
  }

  Future<void> toggleMembership(String groupId) async {
    final uid = _client.auth.currentUser!.id;
    final existing = await _client
        .from('group_members')
        .select('id')
        .eq('group_id', groupId)
        .eq('user_id', uid)
        .maybeSingle();
    if (existing != null) {
      await _client.from('group_members').delete().eq('id', existing['id']);
    } else {
      await _client.from('group_members').insert({'group_id': groupId, 'user_id': uid});
    }
  }

  Future<List<Post>> fetchGroupPosts(String groupId, {PostType? typeFilter}) async {
    try {
      dynamic q = _client
          .from('group_posts')
          .select('''id,user_id,content,created_at,type,media_url,link_url,link_meta,bg_color,likes_count,dislikes_count,comments_count,shares_count,views_count,copies_count,pinned, profiles(name,avatar_url)''')
          .eq('group_id', groupId);

      if (typeFilter != null) q = q.eq('type', typeFilter.name);
      q = q.order('pinned', ascending: false).order('created_at', ascending: false);
      final rows = await q;
      return _mapGroupPosts(rows);
    } catch (_) {
      // قد تفشل إذا لم تتوفر أعمدة معينة، جرّب تحديد أعمدة أساسية فقط
      dynamic q = _client
          .from('group_posts')
          .select('''id,user_id,content,created_at,type,media_url,link_url,pinned, profiles(name,avatar_url)''')
          .eq('group_id', groupId);
      if (typeFilter != null) q = q.eq('type', typeFilter.name);
      q = q.order('pinned', ascending: false).order('created_at', ascending: false);
      final rows = await q;
      return _mapGroupPosts(rows);
    }
  }

  List<Post> _mapGroupPosts(dynamic rows) {
    return (rows as List).map((row) {
      final profile = row['profiles'] ?? {};
      return Post(
        id: row['id'].toString(),
        userId: row['user_id'].toString(),
        userName: profile['name'] ?? 'مستخدم',
        userAvatar: profile['avatar_url'] ?? '',
        content: row['content'] ?? '',
        createdAt: DateTime.parse(row['created_at']),
        type: _mapType(row['type']),
        mediaUrl: row['media_url'],
        mediaUrls: row['media_urls'] != null ? List<String>.from(row['media_urls']) : null,
        linkUrl: row['link_url'],
        linkMeta: row['link_meta'],
        bgColor: row['bg_color'],
        likesCount: row['likes_count'] ?? 0,
        dislikesCount: row['dislikes_count'] ?? 0,
        sharesCount: row['shares_count'] ?? 0,
        commentsCount: row['comments_count'] ?? 0,
        viewsCount: row['views_count'] ?? 0,
        copiesCount: row['copies_count'] ?? 0,
      );
    }).toList();
  }

  Future<void> createGroupPost({
    required String groupId,
    required String content,
    String? mediaUrl,
    String mediaType = 'text',
  }) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) throw 'User not logged in';
    await _client.from('group_posts').insert({
      'group_id': groupId,
      'user_id': uid,
      'content': content,
      'media_url': mediaUrl,
      'media_type': mediaType,
      'created_at': DateTime.now().toIso8601String(),
    });
  }

  Future<List<Map<String, dynamic>>> fetchGroupMembers(String groupId) async {
    final uid = _client.auth.currentUser?.id;
    try {
      final rows = await _client
          .from('group_members')
          .select('role, user:profiles(id,name,avatar_url)')
          .eq('group_id', groupId);
      return (rows as List).map((r) {
        final u = r['user'] as Map;
        return {
          'id': u['id'],
          'name': u['name'],
          'avatar': u['avatar_url'],
          'role': r['role'],
          'isMe': u['id'] == uid,
        };
      }).toList();
    } on PostgrestException catch (e) {
      if (e.code == '42703') {
        // إعادة الاستعلام بدون role فى حالة عدم وجود العمود
        final rows = await _client
            .from('group_members')
            .select('user:profiles(id,name,avatar_url)')
            .eq('group_id', groupId);
        return (rows as List).map((r) {
          final u = r['user'] as Map;
          return {
            'id': u['id'],
            'name': u['name'],
            'avatar': u['avatar_url'],
            'role': 'member',
            'isMe': u['id'] == uid,
          };
        }).toList();
      }
      rethrow;
    }
  }

  Future<void> promoteAdmin(String groupId, String memberId, bool makeAdmin) async {
    await _client
        .from('group_members')
        .update({'role': makeAdmin ? 'admin' : 'member'})
        .eq('group_id', groupId)
        .eq('user_id', memberId);
  }

  Future<void> kickMember(String groupId, String memberId) async {
    await _client
        .from('group_members')
        .delete()
        .eq('group_id', groupId)
        .eq('user_id', memberId);
  }

  Future<void> updateGroupPrivacy(String groupId, GroupPrivacy p) async {
    await _client.from('groups').update({'privacy': p.name}).eq('id', groupId);
  }

  Future<List<Map<String, dynamic>>> fetchJoinRequests(String groupId) async {
    final rows = await _client
        .from('group_join_requests')
        .select('id, user:profiles(id,name,avatar_url)')
        .eq('group_id', groupId);
    return (rows as List).map((r) => {
          'id': r['id'],
          'userId': r['user']['id'],
          'name': r['user']['name'],
          'avatar': r['user']['avatar_url'],
        }).toList();
  }

  Future<void> respondJoinRequest(String requestId, bool approve) async {
    final row = await _client.from('group_join_requests').select().eq('id', requestId).maybeSingle();
    if (row == null) return;
    if (approve) {
      await _client.from('group_members').insert({'group_id': row['group_id'], 'user_id': row['user_id']});
    }
    await _client.from('group_join_requests').delete().eq('id', requestId);
  }

  // ------------------ Views ------------------ //

  Future<void> incrementPostViews(String postId) async {
    await _client.rpc('increment_post_views', params: {'post_id_param': postId}).catchError((_){});
  }

  // ------------------ Copies ------------------ //

  /// يسجِّل عملية نسخ رابط منشور لمستخدم واحد مرة واحدة فقط.
  /// يُعيد true إذا كانت أول مرة ينسخ فيها هذا المستخدم رابط المنشور،
  /// و false إذا كان قد نسخ مسبقًا.
  Future<bool> copyPost(String postId) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) return false;

    bool firstTime = true;
    try {
      // محاولة إدراج سجل النسخ؛ مفتاح فريد يمنع التكرار
      await _client.from('post_copies').insert({'post_id': postId, 'user_id': uid});
    } on PostgrestException catch (e) {
      if (e.code == '23505') {
        // قيود فريدة: تم النسخ مسبقًا
        firstTime = false;
      } else {
        rethrow; // أخطاء أخرى
      }
    }

    if (firstTime) {
      // زيادة العدّاد مرة واحدة فقط عند النسخ الأول
      await _client
          .rpc('increment_post_copies', params: {'post_id_param': postId}).catchError((_) {});
    }

    return firstTime;
  }

  // ------------------ Products ------------------ //

  String _conditionToString(ProductCondition c) => c == ProductCondition.used ? 'used' : 'new';
  String _sellerTypeToString(SellerType s) => s == SellerType.merchant ? 'merchant' : 'individual';
  String _deliveryMethodToString(DeliveryMethod d) => d == DeliveryMethod.delivery ? 'delivery' : 'pickup';
  String _contactMethodToString(ContactMethod m) => m == ContactMethod.phone ? 'phone' : 'in_app_chat';
  String _paymentChannelToString(PaymentChannel p) {
    switch (p) {
      case PaymentChannel.cashOnDelivery:
        return 'cash_on_delivery';
      case PaymentChannel.bankTransfer:
        return 'bank_transfer';
      default:
        return 'other';
    }
  }

  ProductCondition _mapProductCondition(String? v) => v == 'used' ? ProductCondition.used : ProductCondition.newItem;
  SellerType _mapSellerType(String? v) => v == 'merchant' ? SellerType.merchant : SellerType.individual;
  DeliveryMethod _mapDeliveryMethod(String? v) => v == 'pickup' ? DeliveryMethod.pickup : DeliveryMethod.delivery;
  ContactMethod _mapContactMethod(String? v) => v == 'phone' ? ContactMethod.phone : ContactMethod.inAppChat;
  PaymentChannel _mapPaymentChannel(String v) {
    switch (v) {
      case 'bank_transfer':
        return PaymentChannel.bankTransfer;
      case 'other':
        return PaymentChannel.other;
      default:
        return PaymentChannel.cashOnDelivery;
    }
  }

  Future<String> createProduct({
    required String name,
    required String description,
    required double price,
    required bool negotiable,
    required String category,
    String? brand,
    required String country,
    required String currency,
    required String city,
    String? address,
    required ContactMethod contactMethod,
    String? phone,
    required ProductCondition condition,
    int quantity = 1,
    required SellerType sellerType,
    required DeliveryMethod deliveryMethod,
    double? deliveryCost,
    required List<PaymentChannel> paymentMethods,
    required List<Uint8List> imagesBytes,
    required List<String> imagesExt,
  }) async {
    assert(imagesBytes.isNotEmpty, 'At least one image is required');
    assert(imagesBytes.length == imagesExt.length);

    // Step 1: insert product row (without images yet)
    final insertRes = await _client
        .from('marketplace_products')
        .insert({
          'user_id': _client.auth.currentUser!.id,
          'title': name, // تغيير من name إلى title
          'description': description,
          'price': price,
          'is_negotiable': negotiable, // تغيير من negotiable إلى is_negotiable
          'category_id': _getCategoryId(category), // نحتاج إلى تحويل الفئة إلى ID
          'country': country,
          'currency': currency,
          'city': city,
          'condition': _conditionToString(condition),
          'status': 'active', // إضافة حالة المنتج
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
          'expires_at': DateTime.now().add(const Duration(days: 30)).toIso8601String(), // تاريخ انتهاء الصلاحية
        })
        .select('id')
        .single();

    final productId = insertRes['id'] as String;

    // Step 2: upload images and update product with image URLs
    final List<String> imageUrls = [];
    for (int i = 0; i < imagesBytes.length; i++) {
      final bytes = imagesBytes[i];
      final ext = imagesExt[i];
      final path = 'marketplace_images/$productId/$i.$ext';
      final url = await uploadMedia(bytes, path);
      imageUrls.add(url);
    }

    // Step 3: update product with image URLs
    await _client
        .from('marketplace_products')
        .update({
          'images': imageUrls,
        })
        .eq('id', productId);

    return productId;
  }

  // دالة مساعدة لتحويل اسم الفئة إلى ID
  String _getCategoryId(String categoryName) {
    // قائمة الفئات المتاحة في marketplace_products
    final categoryMap = {
      'إلكترونيات': 'electronics',
      'ملابس': 'clothing',
      'سيارات': 'vehicles',
      'عقارات': 'real_estate',
      'أثاث': 'furniture',
      'أجهزة منزلية': 'home_appliances',
      'كتب': 'books',
      'رياضة': 'sports',
      'موبايلات': 'mobile_phones',
      'كمبيوترات': 'computers',
      'مكياج': 'cosmetics',
      'عطور': 'perfumes',
      'مجوهرات': 'jewelry',
      'خدمات': 'services',
      'حيوانات أليفة': 'pets',
      'ألعاب فيديو': 'video_games',
      'موسيقى': 'music',
      'فن': 'art',
      'تذاكر': 'tickets',
      'دراجات': 'bicycles',
      'معدات صناعية': 'industrial_equipment',
      'آلات تصوير': 'cameras',
      'ساعات': 'watches',
      'أطعمة': 'food',
      'مستلزمات طبية': 'medical_supplies',
      'أثاث مكتبي': 'office_furniture',
      'قرطاسية': 'stationery',
      'مستلزمات أطفال': 'baby_supplies',
      'مستلزمات زراعية': 'agricultural_supplies',
      'معدات البناء': 'construction_equipment',
      'أجهزة ألعاب': 'gaming_consoles',
      'إكسسوارات سيارات': 'car_accessories',
      'خيم ورحلات': 'camping',
      'معدات بحرية': 'marine_equipment',
      'أدوات موسيقية': 'musical_instruments',
      'برامج': 'software',
      'ألعاب أطفال': 'children_toys',
      'معدات كهربائية': 'electrical_equipment',
      'دراجات نارية': 'motorcycles',
      'مزارع': 'farms',
      'تصميم وخدمات': 'design_services',
      'استشارات': 'consulting',
      'شحن وتوصيل': 'shipping_delivery',
      'تطوير مواقع': 'web_development',
      'تسويق': 'marketing',
      'تعليم ودورات': 'education_courses',
      'وظائف': 'jobs',
      'مناسبات': 'events',
      'أخرى': 'other',
    };

    return categoryMap[categoryName] ?? 'other';
  }

  Product _rowToProduct(Map row, {String? currentUserId}) {
    final List<ProductImage> imgs = [];
    final imagesRows = row['images'] as List?;
    if (imagesRows != null) {
      for (final im in imagesRows) {
        imgs.add(ProductImage(url: im['url'], index: im['idx']));
      }
      imgs.sort((a, b) => a.index.compareTo(b.index));
    }

    // ----- الملف الشخصي ----- //
    final cacheProf = profilesCache.value[row['user_id']];
    final profile = (row['profiles'] as Map?) ?? (cacheProf ?? {});

    // ضع الملف الشخصى فى الكاش إذا جاء من الـ join ولم يكن موجوداً مسبقاً
    if (row['profiles'] != null && cacheProf == null) {
      final updated = Map<String, Map<String, dynamic>>.from(profilesCache.value);
      updated[row['user_id']] = Map<String, dynamic>.from(row['profiles']);
      profilesCache.value = updated;
    }

    final likesList = row['likes'] as List? ?? [];
    bool likedByMe = currentUserId != null && likesList.any((l) => l['user_id'] == currentUserId);

    // إذا لم يتوفر الاسم بعد، جلبه فى الخلفية لضمان ظهوره لاحقاً
    if ((profile['name'] == null || profile['name'] == 'مستخدم') && row['user_id'] != null) {
      _ensureProfileCached(row['user_id']);
    }

    return Product(
      id: row['id'],
      userId: row['user_id'],
      userName: profile['name'] ?? profile['username'] ?? 'مستخدم',
      userAvatar: profile['avatar_url'] ?? '',
      images: imgs,
      name: row['name'],
      description: row['description'],
      price: (row['price'] as num).toDouble(),
      negotiable: row['negotiable'] ?? false,
      category: row['category'],
      brand: row['brand'],
      country: row['country'] ?? '',
      currency: row['currency'] ?? 'USD',
      city: row['city'],
      address: row['address'],
      contactMethod: _mapContactMethod(row['contact_method']),
      phone: row['phone'],
      condition: _mapProductCondition(row['condition']),
      quantity: row['quantity'] ?? 1,
      sellerType: _mapSellerType(row['seller_type']),
      deliveryMethod: _mapDeliveryMethod(row['delivery_method']),
      deliveryCost: (row['delivery_cost'] as num?)?.toDouble(),
      paymentMethods: (row['payment_methods'] as List?)?.map((e) => _mapPaymentChannel(e as String)).toList() ?? [PaymentChannel.cashOnDelivery],
      createdAt: DateTime.parse(row['created_at']),
      viewsCount: row['views_count'] ?? 0,
      likesCount: row['likes_count'] ?? 0,
      commentsCount: row['comments_count'] ?? 0,
      sharesCount: row['shares_count'] ?? 0,
      likedByMe: likedByMe,
    );
  }

  Stream<List<Product>> productsStream() {
    final uid = _client.auth.currentUser?.id;

    return _client
        .from('products')
        .stream(primaryKey: ['id'])
        .order('created_at', ascending: false)
        .asyncMap((rows) async {
          final List<Map<String, dynamic>> enriched = [];

          // اجلب بيانات الملف الشخصى لجميع المستخدمين المشاركين دفعة واحدة لتقليل عدد الطلبات
          final userIds = rows.map((e) => e['user_id'] as String).toSet().toList();
          final profiles = await _client
              .from('profiles')
              .select('id,name,avatar_url')
              .inFilter('id', userIds);

          final Map<String, Map<String, dynamic>> profileMap = {};
          for (final p in profiles as List) {
            profileMap[p['id']] = p;
          }

          for (final row in rows) {
            final mutable = Map<String, dynamic>.from(row);
            mutable['profiles'] = profileMap[row['user_id']] ?? {};
            enriched.add(mutable);
          }

          return enriched
              .map((r) => _rowToProduct(r, currentUserId: uid))
              .toList();
        });
  }

  Future<void> toggleProductLike(String productId) async {
    final uid = _client.auth.currentUser!.id;
    final existing = await _client
        .from('product_likes')
        .select()
        .eq('product_id', productId)
        .eq('user_id', uid)
        .maybeSingle();

    if (existing == null) {
      await _client.from('product_likes').insert({'product_id': productId, 'user_id': uid});
    } else {
      await _client.from('product_likes').delete().eq('product_id', productId).eq('user_id', uid);
    }
  }

  Future<void> incrementProductViews(String productId) async {
    await _client.from('product_views').upsert({
      'product_id': productId,
      'user_id': _client.auth.currentUser?.id,
    });
  }

  Future<void> incrementProductShares(String productId) async {
    await _client.from('product_shares')
        .insert({'product_id': productId, 'user_id': _client.auth.currentUser?.id})
        .catchError((_){ });

    // بعد الإدراج قم بتحديث عدّاد المشاركات فى جدول المنتجات
    await _incrementProductSharesCount(productId);
  }

  /// تحديث عدد مشاركات المنتج
  Future<void> _incrementProductSharesCount(String productId) async {
    try {
      final count = await _client
          .from('product_shares')
          .select('id')
          .eq('product_id', productId)
          .count(CountOption.exact);

      await _client
          .from('products')
          .update({'shares_count': count.count})
          .eq('id', productId);
    } catch (e) {
      print('خطأ في تحديث عدد مشاركات المنتج: $e');
    }
  }

  Future<void> deleteProduct(String productId) async {
    await _client.from('products').delete().eq('id', productId);
  }

  // ------------------ Product Comments ------------------ //

  Future<void> createProductComment({
    required String productId,
    String? parentId,
    required String content,
    required CommentType type,
    String? mediaUrl,
  }) async {
    final Map<String, dynamic> values = {
      'product_id': productId,
      'user_id': _client.auth.currentUser!.id,
      'content': content,
      'type': type.name,
      'created_at': DateTime.now().toIso8601String(),
    };
    if (mediaUrl != null) values['media_url'] = mediaUrl;
    if (parentId != null) values['parent_id'] = parentId;

    // إدراج التعليق فى الجدول
    await _client.from('product_comments').insert(values);

    // تحديث عدد التعليقات في المنتج
    await _incrementProductCommentsCount(productId);
  }

  /// تحديث عدد التعليقات في المنتج
  Future<void> _incrementProductCommentsCount(String productId) async {
    try {
      // حساب عدد التعليقات الفعلي
      final count = await _client
          .from('product_comments')
          .select('id')
          .eq('product_id', productId)
          .count(CountOption.exact);

      // تحديث العدد في جدول المنتجات
      await _client
          .from('products')
          .update({'comments_count': count.count})
          .eq('id', productId);
    } catch (e) {
      print('خطأ في تحديث عدد تعليقات المنتج: $e');
    }
  }

  // نسخة مُحسّنة لجلب تعليقات المنتجات مع التفاعلات (نفس منطق المنشورات)
  Stream<List<Comment>> productCommentsStream(String productId) {
    final uid = _client.auth.currentUser?.id;

    return _client
        .from('product_comments')
        .stream(primaryKey: ['id'])
        .eq('product_id', productId)
        .order('created_at', ascending: false)
        .asyncMap((rows) async {
          final List<Map<String, dynamic>> enriched = [];

          for (final row in rows) {
            final Map<String, dynamic> r = Map<String, dynamic>.from(row);

            // جلب بيانات الملف الشخصي
            final profile = await _client
                .from('profiles')
                .select('name,avatar_url')
                .eq('id', r['user_id'])
                .maybeSingle();
            r['profiles'] = profile;

            // جلب التفاعلات لهذا التعليق
            final reactionRows = await _client
                .from('product_comment_reactions')
                .select('type,user_id')
                .eq('comment_id', r['id']);

            final counts = <String, int>{};
            for (final react in reactionRows) {
              final t = react['type'] as String;
              counts[t] = (counts[t] ?? 0) + 1;
            }

            r['likes_count'] = counts['like'] ?? 0;
            r['dislikes_count'] = counts['dislike'] ?? 0;
            r['love_count'] = counts['love'] ?? 0;
            r['laugh_count'] = counts['laugh'] ?? 0;
            r['angry_count'] = counts['angry'] ?? 0;
            r['sad_count'] = counts['sad'] ?? 0;
            r['celebrate_count'] = counts['celebrate'] ?? 0;
            r['support_count'] = counts['support'] ?? 0;
            r['insightful_count'] = counts['insightful'] ?? 0;

            // تفاعل المستخدم الحالى
            if (uid != null) {
              Map<String, dynamic>? userReact;
              for (final el in reactionRows) {
                if (el['user_id'] == uid) {
                  userReact = el as Map<String, dynamic>;
                  break;
                }
              }
              r['user_reaction'] = userReact != null ? [userReact] : [];
            } else {
              r['user_reaction'] = [];
            }

            enriched.add(r);
          }

          return _toComments(enriched, productId, currentUserId: uid);
        });
  }

  // ------------------ Helper Queries ------------------ //

  Future<List<ProductImage>> fetchProductImages(String productId) async {
    final rows = await _client
        .from('product_images')
        .select('url,idx')
        .eq('product_id', productId)
        .order('idx')
        .limit(6);

    return rows
        .map<ProductImage>((r) => ProductImage(url: r['url'] as String, index: r['idx'] as int))
        .toList();
  }

  // ------------------ Saved Posts ------------------ //
  Future<List<Post>> fetchSavedPosts() async {
    try {
      final uid = _client.auth.currentUser!.id;
      print('=== جلب المنشورات المحفوظة ===');
      print('User ID: $uid');

      // جلب معرفات المنشورات المحفوظة
      final savedRows = await _client
          .from('saved_posts')
          .select('post_id')
          .eq('user_id', uid);

      if (savedRows.isEmpty) {
        print('لا توجد منشورات محفوظة');
        return [];
      }

      final savedPostIds = (savedRows as List).map((r) => r['post_id'] as String).toList();
      print('معرفات المنشورات المحفوظة: $savedPostIds');

      // جلب المنشورات المحفوظة
      final postsData = await _client
          .from('posts')
          .select('''
            id,
            user_id,
            content,
            created_at,
            type,
            media_url,
            link_url,
            link_meta,
            bg_color,
            likes_count,
            dislikes_count,
            shares_count,
            comments_count,
            views_count,
            copies_count,
            shared_post_id,
            community_id,
            original:posts!shared_post_id(id,user_id,content,created_at,type,media_url,link_url,link_meta,profiles(name,avatar_url)),
            profiles(name,avatar_url,posts_privacy,is_verified)
          ''')
          .inFilter('id', savedPostIds)
          .order('created_at', ascending: false);

      final List<Post> posts = (postsData as List).map<Post>((row) {
        final profile = row['profiles'] ?? {};

        // --- بناء المنشور الأصلى عند المشاركة --- //
        Post? original;
        dynamic origData = row['original'];
        if (origData is List && origData.isNotEmpty) origData = origData.first;
        if (origData != null && origData is Map) {
          final origProfile = origData['profiles'] ?? {};
          original = Post(
            id: origData['id'].toString(),
            userId: origData['user_id'].toString(),
            userName: origProfile['name'] ?? 'مستخدم',
            userAvatar: origProfile['avatar_url'] ?? '',
            content: origData['content'] ?? '',
            createdAt: DateTime.parse(origData['created_at']),
            type: _mapType(origData['type']),
            mediaUrl: origData['media_url'],
            mediaUrls: origData['media_urls'] != null ? List<String>.from(origData['media_urls']) : null,
            linkUrl: origData['link_url'],
            linkMeta: origData['link_meta'],
            bgColor: origData['bg_color'],
            likesCount: 0,
            dislikesCount: 0,
            sharesCount: 0,
            commentsCount: 0,
            viewsCount: 0,
            reactionCounts: const {},
            copiesCount: 0,
            isSaved: true,
          );
        }

        return Post(
          id: row['id'].toString(),
          userId: row['user_id'].toString(),
          userName: profile['name'] ?? 'مستخدم',
          userAvatar: profile['avatar_url'] ?? '',
          content: row['content'] ?? '',
          createdAt: DateTime.parse(row['created_at']),
          type: _mapType(row['type']),
          mediaUrl: row['media_url'],
          mediaUrls: row['media_urls'] != null ? List<String>.from(row['media_urls']) : null,
          linkUrl: row['link_url'],
          linkMeta: row['link_meta'],
          bgColor: row['bg_color'],
          likesCount: row['likes_count'] ?? 0,
          dislikesCount: row['dislikes_count'] ?? 0,
          sharesCount: row['shares_count'] ?? 0,
          commentsCount: row['comments_count'] ?? 0,
          viewsCount: row['views_count'] ?? 0,
          copiesCount: row['copies_count'] ?? 0,
          isSaved: true, // جميع المنشورات في هذه القائمة محفوظة
        );
      }).toList();

      print('تم جلب ${posts.length} منشور محفوظ');
      return posts;
    } catch (e) {
      print('خطأ في جلب المنشورات المحفوظة: $e');
      return [];
    }
  }

  // ------------------ إدارة التعليقات (تعديل، حذف، إبلاغ، حظر) ------------------ //

  /// تعديل تعليق (للمالك فقط) - تعديل مباشر
  Future<bool> editComment(String commentId, String newContent) async {
    try {
      print('✏️ محاولة تعديل التعليق: $commentId');
      print('📝 المحتوى الجديد: $newContent');

      final currentUserId = _client.auth.currentUser?.id;
      if (currentUserId == null) {
        print('❌ المستخدم غير مسجل الدخول');
        return false;
      }

      // فحص ملكية التعليق
      final comment = await _client
          .from('comments')
          .select('user_id, type')
          .eq('id', commentId)
          .maybeSingle();

      if (comment == null) {
        print('❌ التعليق غير موجود');
        return false;
      }

      if (comment['user_id'] != currentUserId) {
        print('❌ المستخدم ليس مالك التعليق');
        return false;
      }

      if (comment['type'] != 'text') {
        print('❌ يمكن تعديل التعليقات النصية فقط');
        return false;
      }

      // التعديل المباشر
      await _client
          .from('comments')
          .update({
            'content': newContent,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', commentId)
          .eq('user_id', currentUserId); // أمان إضافي

      print('✅ تم تعديل التعليق بنجاح');
      return true;

    } catch (e) {
      print('❌ خطأ في تعديل التعليق: $e');
      return false;
    }
  }

  /// حذف تعليق (للمالك فقط) - حذف فعلي مباشر
  Future<bool> deleteComment(String commentId) async {
    try {
      print('🗑️ محاولة حذف التعليق: $commentId');

      final currentUserId = _client.auth.currentUser?.id;
      if (currentUserId == null) {
        print('❌ المستخدم غير مسجل الدخول');
        return false;
      }

      print('👤 معرف المستخدم الحالي: $currentUserId');

      // فحص ملكية التعليق
      final comment = await _client
          .from('comments')
          .select('user_id, content')
          .eq('id', commentId)
          .maybeSingle();

      print('📄 بيانات التعليق: $comment');

      if (comment == null) {
        print('❌ التعليق غير موجود');
        return false;
      }

      if (comment['user_id'] != currentUserId) {
        print('❌ المستخدم ليس مالك التعليق. مالك التعليق: ${comment['user_id']}');
        return false;
      }

      print('✅ المستخدم مالك التعليق، جاري الحذف...');

      // الحذف الفعلي المباشر من قاعدة البيانات
      final deleteResult = await _client
          .from('comments')
          .delete()
          .eq('id', commentId)
          .eq('user_id', currentUserId); // أمان إضافي

      print('🗑️ نتيجة الحذف: $deleteResult');
      print('✅ تم حذف التعليق بنجاح');

      return true;

    } catch (e) {
      print('❌ خطأ في حذف التعليق: $e');
      print('📋 تفاصيل الخطأ: ${e.toString()}');
      return false;
    }
  }

  /// الإبلاغ عن تعليق - مبسط
  Future<bool> reportComment(String commentId, String reason, {String? description}) async {
    try {
      print('🚨 إبلاغ عن التعليق: $commentId بسبب: $reason');

      // مؤقتاً: عرض رسالة نجاح فقط
      // يمكن إضافة جدول الإبلاغات لاحقاً
      print('✅ تم الإبلاغ بنجاح (مؤقت)');
      return true;

    } catch (e) {
      print('❌ خطأ في الإبلاغ: $e');
      return false;
    }
  }

  /// حظر مستخدم في التعليقات - مبسط
  Future<bool> blockCommentUser(String userId) async {
    try {
      print('🚫 حظر المستخدم: $userId');

      // مؤقتاً: عرض رسالة نجاح فقط
      // يمكن إضافة جدول الحظر لاحقاً
      print('✅ تم حظر المستخدم بنجاح (مؤقت)');
      return true;

    } catch (e) {
      print('❌ خطأ في حظر المستخدم: $e');
      return false;
    }
  }

  /// إلغاء حظر مستخدم في التعليقات
  Future<bool> unblockCommentUser(String userId) async {
    try {
      final result = await _client.rpc('unblock_user', params: {
        'p_blocked_id': userId,
      });

      return result['success'] == true;
    } catch (e) {
      print('Error unblocking user: $e');
      return false;
    }
  }

  /// الحصول على معرف المستخدم الحالي
  String? getCurrentUserId() {
    return _client.auth.currentUser?.id;
  }

  // ------------------ Pagination Support ------------------
  /// جلب منشورات بحد معين وإزاحة (للتمرير اللانهائى)
  Future<List<Post>> fetchPostsPaginated({int limit = 20, int offset = 0}) async {
    final int end = offset + limit - 1;
    final uid = _client.auth.currentUser?.id;

    List<String> allowedIds = [];
    if (uid != null) {
      final rows = await _client
          .from('follows')
          .select('following_id')
          .eq('follower_id', uid);
      allowedIds = (rows as List).map((e) => e['following_id'] as String).toList();
      allowedIds.add(uid);
    }

    // 🛑 قائمة المستخدمين المحظورين (أنا أو هم حظرونى)
    final List<String> blockedIds = await blockedUserIds();

    // ابدأ بناء الاستعلام كـ FilterBuilder حتى نستطيع استعمال not()
    dynamic query = _client.from('posts');

    if (blockedIds.isNotEmpty) {
      final idsStr = '(${blockedIds.map((e) => '"$e"').join(',')})';
      query = query.not('user_id', 'in', idsStr);
    }

    query = query
        .select('''
          id,
          user_id,
          content,
          created_at,
          type,
          media_url,
          link_url,
          link_meta,
          bg_color,
          posts_privacy,
          likes_count,
          dislikes_count,
          shares_count,
          comments_count,
          views_count,
          copies_count,
          shared_post_id,
          community_id,
          space_id,
          original:posts!shared_post_id(id,user_id,content,created_at,type,media_url,link_url,link_meta,profiles(name,avatar_url)),
          profiles(name,avatar_url,posts_privacy)
        ''')
        .isFilter('community_id', null) // استبعاد منشورات المجتمع من الصفحة الرئيسية
        .isFilter('space_id', null) // استبعاد منشورات المساحات من الصفحة الرئيسية
        .order('created_at', ascending: false)
        .range(offset, end);

    if (allowedIds.isEmpty) {
      // مستخدم غير مسجّل → اعرض المنشورات العامة فقط
      query = query.eq('posts_privacy', 'everyone');
    } // إذا كان هناك مستخدم مسجّل، لا نضيف أى قيود إضافية (نضيف جميع المنشورات)

    final data = await query;

    final List<Post> posts = (data as List).map((row) {
      final profile = row['profiles'] ?? {};

      Post? original;
      // احتفظ بمنطق original كما هو (لم يتغير)
        dynamic origData = row['original'];
      if (origData is List && origData.isNotEmpty) origData = origData.first;
        if (origData != null && origData is Map) {
          final origProfile = origData['profiles'] ?? {};
          original = Post(
            id: origData['id'].toString(),
            userId: origData['user_id'].toString(),
            userName: origProfile['name'] ?? 'مستخدم',
            userAvatar: origProfile['avatar_url'] ?? '',
            content: origData['content'] ?? '',
            createdAt: DateTime.parse(origData['created_at']),
            type: _mapType(origData['type']),
            mediaUrl: origData['media_url'],
            mediaUrls: origData['media_urls'] != null ? List<String>.from(origData['media_urls']) : null,
            linkUrl: origData['link_url'],
            linkMeta: origData['link_meta'],
            bgColor: origData['bg_color'],
            likesCount: 0,
            dislikesCount: 0,
            sharesCount: 0,
            commentsCount: 0,
            viewsCount: row['views_count'] ?? 0,
            reactionCounts: const {},
            copiesCount: row['copies_count'] ?? 0,
            originalPost: original,
          );
      }

      return Post(
        id: row['id'].toString(),
        userId: row['user_id'].toString(),
        userName: profile['name'] ?? 'مستخدم',
        userAvatar: profile['avatar_url'] ?? '',
        content: row['content'] ?? '',
        createdAt: DateTime.parse(row['created_at']),
        type: _mapType(row['type']),
        mediaUrl: row['media_url'],
        mediaUrls: row['media_urls'] != null ? List<String>.from(row['media_urls']) : null,
        linkUrl: row['link_url'],
        linkMeta: row['link_meta'],
        bgColor: row['bg_color'],
        likesCount: row['likes_count'] ?? 0,
        dislikesCount: row['dislikes_count'] ?? 0,
        sharesCount: row['shares_count'] ?? 0,
        commentsCount: row['comments_count'] ?? 0,
        viewsCount: row['views_count'] ?? 0,
        copiesCount: row['copies_count'] ?? 0,
        reactionCounts: const {},
        originalPost: original,
      );
    }).toList();

    if (posts.isEmpty) return posts;

    // ربط المنشورات الأصلية للمنشورات المُعادة فى حال فشل الـ join
    await _linkSharedOriginalsFallback(data as List, posts);

    final postIds = posts.map((p) => p.id).toList();
    final reactionRows = await _client.from('reactions').select('post_id,type,user_id').inFilter('post_id', postIds);

    // aggregate counts and user reaction
    final Map<String, Map<ReactionType, int>> aggregated = {};
    final Map<String, ReactionType> userReactions = {};
    for (final row in reactionRows as List) {
      final pid = row['post_id'].toString();
      final rt = _mapReaction(row['type']);
      if (rt == ReactionType.none) continue;
      aggregated.putIfAbsent(pid, () => {});
      aggregated[pid]![rt] = (aggregated[pid]![rt] ?? 0) + 1;
      if (row['user_id'] == uid) userReactions[pid] = rt;
    }

    for (var i=0;i<posts.length;i++){
      posts[i]=posts[i].copyWith(reactionCounts: aggregated[posts[i].id] ?? {}, currentUserReaction: userReactions[posts[i].id] ?? ReactionType.none);
    }

    return posts;
  }

  /// إنشاء صف فى جدول profiles إذا لم يكن موجوداً
  Future<void> ensureProfileExists(User user) async {
    try {
      final meta = user.userMetadata ?? <String, dynamic>{};

      final existing = await _client
          .from('profiles')
          .select('id')
          .eq('id', user.id)
          .maybeSingle();

      if (existing == null) {
        await _client.from('profiles').insert({
          'id': user.id,
          'name': meta['name'] ?? 'مستخدم',
          'avatar_url': meta['avatar_url'] ?? '',
        });
      } else {
        // حدّث الاسم إذا كان فارغًا أو يساوي القيمة الافتراضية
        if (meta['name'] != null) {
          await _client.from('profiles').update({'name': meta['name']}).eq('id', user.id);
        }
      }
    } catch (e) {
      // تجاهل أى خطأ (قد يكون الصف موجوداً بالفعل بسبب تريجر)
    }
  }

  /// جلب جميع قصص مستخدم (بما فى ذلك المنتهية) لعرضها فى أرشيف القصص.
  /// نتجنب الاعتماد على علاقة Foreign-Key بين stories ↔ profiles عبر ضمّ البيانات يدويًا،
  /// مما يمنع خطأ PostgrestException PGRST200 إذا لم تُنشأ العلاقة فى قاعدة البيانات.
  Future<List<Story>> fetchUserStoriesArchive(String userId) async {
    // 1) احصل على كل قصص المستخدم
    final List rows = await _client
        .from('stories')
        .select('id,user_id,type,text,media_url,created_at,expires_at')
        .eq('user_id', userId)
        .order('created_at', ascending: false) as List;

    // 2) احضر بيانات الملف الشخصى مرة واحدة فقط
    final profile = await _client
        .from('profiles')
        .select('name,avatar_url')
        .eq('id', userId)
        .maybeSingle();

    // 3) أضف بيانات الملف الشخصى لكل قصة لتتوافق مع ما يتوقعه _rowsToStories
    final enriched = rows.map<Map<String, dynamic>>((r) {
      final mutable = Map<String, dynamic>.from(r);
      mutable['profiles'] = profile ?? {};
      return mutable;
    }).toList();

    return _rowsToStories(enriched);
  }

  PostPrivacy _mapPostPrivacy(String? v) => v == 'followers' ? PostPrivacy.followers : PostPrivacy.everyone;

  // دالة مساعدة لتحويل آمن إلى int
  int _safeInt(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }

  /// اقتراح مستخدمين لمتابعتهم (أشخاص قد تعرفهم)
  Future<List<Map<String, dynamic>>> fetchFollowSuggestions({int limit = 10}) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) return [];

    final rows = await _client.rpc('suggest_follows', params: {'p_user': uid, 'p_limit': limit});
    // إذا لم يتوفر function فى DB استخدم استعلام بديل بسيط
    if (rows is List && rows.isNotEmpty) {
      return rows.cast<Map<String, dynamic>>();
    }

    final suggestions = await _client.from('profiles').select('id,name,username,avatar_url,bio').neq('id', uid)
      .not('id','in','(select following_id from follows where follower_id = "$uid")')
      .limit(limit);
    return (suggestions as List).cast<Map<String,dynamic>>();
  }

  // ------------ Helper: cache on-demand profile ------------- //
  /// إذا لم يكن الملف الشخصى موجوداً فى الكاش، يتم جلبه وتخزينه دون تكرار الطلبات.
  final Set<String> _fetchingProfiles = <String>{};

  Future<void> _ensureProfileCached(String userId) async {
    if (profilesCache.value.containsKey(userId) || _fetchingProfiles.contains(userId)) return;
    _fetchingProfiles.add(userId);
    try {
      final data = await fetchProfile(userId);
      if (data != null) {
        final updated = Map<String, Map<String, dynamic>>.from(profilesCache.value);
        updated[userId] = data;
        profilesCache.value = updated;
      }
    } catch (_) {}
    _fetchingProfiles.remove(userId);
  }

  Future<List<String>> blockedUserIds() async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) return [];
    final rows = await _client
        .from('blocks')
        .select('user_id,blocked_user_id')
        .or('user_id.eq.$uid,blocked_user_id.eq.$uid');
    final Set<String> ids = {};
    for (final r in rows as List) {
      final userId = r['user_id'].toString();
      final blockedId = r['blocked_user_id'].toString();
      if (userId == uid) {
        ids.add(blockedId); // أنا حظرت هذا المستخدم
      }
      if (blockedId == uid) {
        ids.add(userId); // هذا المستخدم حظرني
      }
    }
    return ids.toList();
  }

  /// قائمة الملفات الشخصية التى حظرتها
  Future<List<Map<String, dynamic>>> fetchBlockedUsers() async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) return [];
    final rows = await _client
        .from('blocks')
        .select('blocked_user_id, profiles:blocked_user_id(name,avatar_url)')
        .eq('user_id', uid);

    return (rows as List).map<Map<String, dynamic>>((r) {
      return {
        'id': r['blocked_user_id'].toString(),
        'name': r['profiles']?['name'] ?? 'مستخدم',
        'avatar_url': r['profiles']?['avatar_url'] ?? '',
      };
    }).toList();
  }

  // -------- Helper: ensure shared posts have their original attached even if join failed -------- //
  Future<void> _linkSharedOriginalsFallback(List dataRows, List<Post> posts) async {
    if (posts.isEmpty || dataRows.isEmpty) return;

    // بناء خريطة المنشور المُعاد -> المعرّف الأصلى فى حالة عدم توفر join
    final Map<String, String> _sharedToOrig = {};
    for (int i = 0; i < dataRows.length && i < posts.length; i++) {
      final Map<String, dynamic> r = dataRows[i] as Map<String, dynamic>;
      final bool isShared = r['type'] == 'shared';
      final bool hasOriginal = r['original'] != null && (r['original'] is List ? (r['original'] as List).isNotEmpty : true);
      if (isShared && !hasOriginal) {
        final origId = r['shared_post_id']?.toString();
        if (origId != null) _sharedToOrig[posts[i].id] = origId;
      }
    }

    if (_sharedToOrig.isEmpty) return;

    final origRows = await _client
        .from('posts')
        .select('id,user_id,content,created_at,type,media_url,link_url,link_meta,bg_color,profiles(name,avatar_url)')
        .inFilter('id', _sharedToOrig.values.toList());

    final Map<String, Post> _origMap = {};
    for (final row in origRows as List) {
      final Map<String, dynamic> p = row as Map<String, dynamic>;
      final prof = p['profiles'] ?? {};
      _origMap[p['id'].toString()] = Post(
        id: p['id'].toString(),
        userId: p['user_id'].toString(),
        userName: prof['name'] ?? 'مستخدم',
        userAvatar: prof['avatar_url'] ?? '',
        content: p['content'] ?? '',
        createdAt: DateTime.parse(p['created_at']),
        type: _mapType(p['type']),
        mediaUrl: p['media_url'],
        mediaUrls: p['media_urls'] != null ? List<String>.from(p['media_urls']) : null,
        linkUrl: p['link_url'],
        linkMeta: p['link_meta'],
        bgColor: p['bg_color'],
        likesCount: 0,
        dislikesCount: 0,
        sharesCount: 0,
        commentsCount: 0,
        viewsCount: 0,
        reactionCounts: const {},
      );
    }

    for (int i = 0; i < posts.length; i++) {
      final origId = _sharedToOrig[posts[i].id];
      if (origId != null && _origMap.containsKey(origId)) {
        posts[i] = posts[i].copyWith(originalPost: _origMap[origId]);
      }
    }
  }

  // ------------------ Story interactions ------------------ //

  /// زيادة عدّاد المشاهدات للقصة (يحسب كل مستخدم مرة واحدة)
  Future<void> incrementStoryView(String storyId) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) return;
    
    try {
      // استخدام الدالة الجديدة
      await _client.rpc('increment_story_view', params: {
        'p_story_id': storyId,
        'p_user_id': uid,
      });
      print('✅ تم زيادة مشاهدة القصة: $storyId');
    } catch (e) {
      print('❌ خطأ في زيادة مشاهدة القصة: $e');
      // fallback للطريقة القديمة
      await _client.from('story_views').upsert({
        'story_id': storyId,
        'user_id': uid,
      });
    }
  }

  /// جلب عدد المشاهدات وعدد تفاعلات "like" لقصة محددة
  Future<Map<String, int>> getStoryStats(String storyId) async {
    try {
      // استخدام الدالة الجديدة
      final result = await _client.rpc('get_story_stats', params: {
        'p_story_id': storyId,
      });
      
      if (result != null && result.isNotEmpty) {
        final stats = result.first as Map<String, dynamic>;
        return {
          'views': (stats['views_count'] as int?) ?? 0,
          'likes': (stats['likes_count'] as int?) ?? 0,
        };
      }
      
      return {'views': 0, 'likes': 0};
    } catch (e) {
      print('❌ خطأ في جلب إحصائيات القصة: $e');
      // fallback للطريقة القديمة
      try {
        final viewsRow = await _client
            .from('story_views')
            .select('id')
            .eq('story_id', storyId)
            .count(CountOption.exact);

        final likesRow = await _client
            .from('story_reactions')
            .select('id')
            .eq('story_id', storyId)
            .eq('type', 'like')
            .count(CountOption.exact);

        return {
          'views': viewsRow.count,
          'likes': likesRow.count,
        };
      } catch (_) {
        return {'views': 0, 'likes': 0};
      }
    }
  }

  Future<void> toggleProductCommentReaction({required String commentId, required ReactionType type}) async {
    final uid = _client.auth.currentUser!.id;

    final existing = await _client
        .from('product_comment_reactions')
        .select('id,type')
        .eq('comment_id', commentId)
        .eq('user_id', uid)
        .maybeSingle();

    final String typeStr = _reactionToString(type);

    if (existing == null) {
      // إضافة التفاعل
      await _client.from('product_comment_reactions').insert({
        'comment_id': commentId,
        'user_id': uid,
        'type': typeStr,
      });
    } else if (existing['type'] == typeStr) {
      // إزالة التفاعل نفسه
      await _client.from('product_comment_reactions').delete().eq('id', existing['id']);
    } else {
      // تغيير نوع التفاعل
      await _client.from('product_comment_reactions').update({'type': typeStr}).eq('id', existing['id']);
    }
  }

  // ------------------ Watch-Later videos ------------------ //

  /// إضافة/إزالة فيديو (post.type == video) فى قائمة "للمشاهدة لاحقًا" الخاصة بالمستخدم
  Future<void> toggleWatchLater(String postId) async {
    final uid = _client.auth.currentUser!.id;
    final existing = await _client
        .from('video_watch_later')
        .select('id')
        .eq('user_id', uid)
        .eq('post_id', postId)
        .maybeSingle();

    if (existing == null) {
      await _client
          .from('video_watch_later')
          .insert({'user_id': uid, 'post_id': postId});
    } else {
      await _client
          .from('video_watch_later')
          .delete()
          .eq('id', existing['id']);
    }
  }

  /// stream يُرجِع قائمة الفيديوهات التى حفظها المستخدم للمشاهدة لاحقًا
  Stream<List<Post>> watchLaterStream() {
    final uid = _client.auth.currentUser!.id;

    return _client
        .from('video_watch_later')
        .stream(primaryKey: ['id'])
        .eq('user_id', uid)
        .order('created_at', ascending: false)
        .asyncMap((rows) async {
          final ids = rows.map((r) => r['post_id'] as String).toList();
          if (ids.isEmpty) return <Post>[];

          // 1) جلب تفاصيل المنشورات
          final data = await _client
              .from('posts')
              .select('''id,user_id,content,created_at,type,media_url,link_url,link_meta,bg_color,
                       likes_count,dislikes_count,shares_count,comments_count,views_count,
                       profiles(name,avatar_url)''')
              .inFilter('id', ids);

          // 2) جلب التفاعلات المجمَّعة لكل منشور
          final reactRows = await _client
              .from('reactions')
              .select('post_id,type')
              .inFilter('post_id', ids);

          final Map<String, Map<ReactionType, int>> countsMap = {};
          for (final r in reactRows as List) {
            final String pid = r['post_id'].toString();
            final ReactionType rt = _mapReaction(r['type']);
            countsMap[pid] ??= {};
            countsMap[pid]![rt] = (countsMap[pid]![rt] ?? 0) + 1;
          }

          // 3) جلب تفاعل المستخدم الحالى
          final userRows = await _client
              .from('reactions')
              .select('post_id,type')
              .eq('user_id', uid)
              .inFilter('post_id', ids);

          final Map<String, ReactionType> userMap = {};
          for (final u in userRows as List) {
            userMap[u['post_id'].toString()] = _mapReaction(u['type']);
          }

          return (data as List).map<Post>((row) {
            final prof = row['profiles'] ?? {};
            final String pid = row['id'].toString();
            final Map<ReactionType, int> rc = countsMap[pid] ?? {};
            return Post(
              id: pid,
              userId: row['user_id'].toString(),
              userName: prof['name'] ?? 'مستخدم',
              userAvatar: prof['avatar_url'] ?? '',
              content: row['content'] ?? '',
              createdAt: DateTime.parse(row['created_at']),
              type: _mapType(row['type']),
              mediaUrl: row['media_url'],
              mediaUrls: row['media_urls'] != null ? List<String>.from(row['media_urls']) : null,
              linkUrl: row['link_url'],
              linkMeta: row['link_meta'],
              bgColor: row['bg_color'],
              likesCount: rc[ReactionType.like] ?? row['likes_count'] ?? 0,
              dislikesCount: rc[ReactionType.dislike] ?? row['dislikes_count'] ?? 0,
              sharesCount: row['shares_count'] ?? 0,
              commentsCount: row['comments_count'] ?? 0,
              viewsCount: row['views_count'] ?? 0,
              reactionCounts: rc,
              currentUserReaction: userMap[pid] ?? ReactionType.none,
              isSaved: true,
            );
          }).toList();
        });
  }

  // --------- Online presence (heartbeat pings) --------- //
  final ValueNotifier<Set<String>> onlineUsers = ValueNotifier(<String>{});
  RealtimeChannel? _presenceChannel;
  Timer? _heartbeatTimer;
  final Map<String, DateTime> _lastPings = {};

  /// Call once (e.g. after login) to start broadcasting the user's presence
  /// and listening to others. A simple heartbeat ping is sent every 20 seconds.
  void initPresence() {
    final uid = _client.auth.currentUser?.id;
    if (uid == null || _presenceChannel != null) return;

    _presenceChannel = _client.channel('presence', opts: const RealtimeChannelConfig(self: true));

    // Listen to heartbeat pings from all users
    _presenceChannel!.onBroadcast(event: 'ping', callback: (payload) {
      if (payload is Map && payload['uid'] is String) {
        _registerPing(payload['uid'] as String);
      }
    });

    _presenceChannel!.subscribe();

    // Send first ping and start periodic heartbeat
    _heartbeatTimer = Timer.periodic(const Duration(seconds: 20), (_) => _sendPing());
    _sendPing();
  }

  void _sendPing() {
    final uid = _client.auth.currentUser?.id;
    if (uid == null || _presenceChannel == null) return;
    _presenceChannel!.sendBroadcastMessage(event: 'ping', payload: {
      'uid': uid,
      'ts': DateTime.now().toIso8601String(),
    });
    _registerPing(uid);
  }

  void _registerPing(String uid) {
    _lastPings[uid] = DateTime.now();
    _cleanupPings();
  }

  void _cleanupPings() {
    final now = DateTime.now();
    _lastPings.removeWhere((key, time) => now.difference(time) > const Duration(minutes: 2));
    onlineUsers.value = _lastPings.keys.toSet();
  }

  /// تحديث خصوصية الرسائل فى جدول profiles (everyone | followers)
  Future<void> setMessagesPrivacy(String privacy) async {
    final uid = _client.auth.currentUser!.id;
    await _client.from('profiles').update({'messages_privacy': privacy}).eq('id', uid);
  }

  /// جلب الدردشات المؤرشفة للمستخدم الحالى
  Future<List<Chat>> fetchArchivedChats() async {
    final uid = _client.auth.currentUser!.id;

    // احصل على قائمة المعرّفات المؤرشفة
    final visRows = await _client
        .from('chat_visibility')
        .select('chat_id')
        .eq('user_id', uid)
        .eq('archived', true);

    final ids = (visRows as List).map((e) => e['chat_id'] as String).toList();
    if (ids.isEmpty) return [];

    final rows = await _client
        .from('chats')
        .select('''id,user1,user2,last_message,last_message_at,
          profiles!chats_user1_fkey(id,name,avatar_url), profiles!chats_user2_fkey(id,name,avatar_url)''')
        .inFilter('id', ids)
        .order('last_message_at', ascending: false);

    final uidStr = uid;
    return (rows as List).map((r) {
      final profilesList = (r['profiles'] as List?) ?? [];
      Map<String, dynamic>? other;
      if (profilesList.isNotEmpty) {
        other = profilesList.firstWhere(
          (p) => p != null && p['id'] != uidStr,
          orElse: () => profilesList.first,
        );
      }
      other ??= {'id': '', 'name': 'مستخدم', 'avatar_url': ''};

      return Chat(
        id: r['id'],
        otherId: other['id'] ?? '',
        otherName: (other['name'] ?? 'مستخدم') as String,
        otherAvatar: (other['avatar_url'] ?? '') as String,
        lastMessage: r['last_message'] ?? '',
        lastAt: r['last_message_at'] != null ? DateTime.parse(r['last_message_at']) : null,
      );
    }).toList();
  }

  /// بحث عن رسائل فى محادثة محددة تحتوي على الكلمة (ILIKE)
  Future<List<Message>> searchMessages(String chatId, String term) async {
    if (term.isEmpty) return [];
    final rows = await _client
        .from('messages')
        .select()
        .eq('chat_id', chatId)
        .ilike('content', '%$term%')
        .order('created_at');
    return (rows as List).map(_mapMsg).toList();
  }

  /// قائمة المتابعين للمستخدم المحدَّد
  Future<List<Map<String, dynamic>>> fetchFollowersOf(String userId) async {
    final data = await _client
        .from('follows')
        .select('follower:profiles!follows_follower_id_fkey(id,name,avatar_url)')
        .eq('following_id', userId);
    return (data as List)
        .map((e) => Map<String, dynamic>.from(e['follower'] ?? {}))
        .toList();
  }

  /// قائمة الذين يتابعهم المستخدم المحدَّد
  Future<List<Map<String, dynamic>>> fetchFollowingOf(String userId) async {
    final data = await _client
        .from('follows')
        .select('followee:profiles!follows_following_id_fkey(id,name,avatar_url)')
        .eq('follower_id', userId);
    return (data as List)
        .map((e) => Map<String, dynamic>.from(e['followee'] ?? {}))
        .toList();
  }

  // ======== مجتمعات (Community) ======== //
  Future<String> createCommunity({
    required String name,
    String? description,
    String? category,
    String? coverUrl,
    String? avatarUrl,
    bool isPrivate = false,
    bool allowMemberPosts = true,
    bool requireApproval = false,
    bool allowComments = true,
    bool allowInvites = true,
    String postPermission = 'members',
    String joinType = 'open',
  }) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) throw 'User not logged in';

    final insertData = {
      'owner_id': uid,
      'name': name,
      'description': description,
      'category': category,
      'cover_url': coverUrl,
      'avatar_url': avatarUrl,
      'is_private': isPrivate,
      'is_archived': false,
      'is_disabled': false,
      'allow_member_posts': allowMemberPosts,
      'require_approval': requireApproval,
      'allow_comments': allowComments,
      'allow_invites': allowInvites,
      'post_permission': postPermission,
      'join_type': joinType,
    }..removeWhere((key, value) => value == null);

    final row = await _client
        .from('communities')
        .insert(insertData)
        .select('id')
        .maybeSingle();

    final communityId = row?['id'].toString() ?? '';

    // اجعل المالك Admin تلقائيا فى جدول الأعضاء لضمان رؤية المجتمع الخاص فوراً
    if (communityId.isNotEmpty) {
      await _client.from('community_members').upsert({
        'community_id': communityId,
        'user_id': uid,
        'role': 'admin',
        'joined_at': DateTime.now().toIso8601String(),
      });
    }

    return communityId;
  }

  Future<List<Community>> fetchCommunities() async {
    final rows = await _client
        .from('communities')
        .select('''
          id, owner_id, name, description, category, cover_url, avatar_url,
          is_private, is_archived, is_disabled, created_at,
          allow_member_posts, require_approval, allow_comments, allow_invites,
          post_permission, join_type,
          members:community_members(count)
        ''')
        .eq('is_archived', false) // عدم عرض المجتمعات المؤرشفة في القائمة العامة
        .eq('is_disabled', false) // عدم عرض المجتمعات المعطلة
        .order('created_at', ascending: false);

    return (rows as List).map((e) => Community.fromMap(e as Map<String, dynamic>)).toList();
  }

  Future<void> joinCommunity(String communityId) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) throw 'User not logged in';

    await _client.from('community_members').insert({
      'community_id': communityId,
      'user_id': uid,
      'role': 'member',
    });
  }

  Future<void> leaveCommunity(String communityId) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) throw 'User not logged in';

    await _client
        .from('community_members')
        .delete()
        .eq('community_id', communityId)
        .eq('user_id', uid);
  }

  Future<bool> isMemberOfCommunity(String communityId) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) return false;
    final row = await _client
        .from('community_members')
        .select('id')
        .eq('community_id', communityId)
        .eq('user_id', uid)
        .maybeSingle();
    return row != null;
  }

  Future<Community?> getCommunity(String communityId) async {
    final row = await _client
        .from('communities')
        .select('''
          id, owner_id, name, description, category, cover_url, avatar_url,
          is_private, is_archived, is_disabled, created_at,
          allow_member_posts, require_approval, allow_comments, allow_invites,
          post_permission, join_type,
          members:community_members(count)
        ''')
        .eq('id', communityId)
        .maybeSingle();
    if (row == null) return null;
    return Community.fromMap(row);
  }

  Future<List<Post>> fetchCommunityPosts(String communityId) async {
    final uid = _client.auth.currentUser?.id;

    try {
      // استعلام بسيط يعمل (إرجاع للحالة التي كانت تعمل)
      final data = await _client
          .from('posts')
          .select('''
            id,
            user_id,
            content,
            created_at,
            type,
            media_url,
            link_url,
            link_meta,
            bg_color,
            posts_privacy,
            likes_count,
            dislikes_count,
            shares_count,
            comments_count,
            views_count,
            copies_count,
            community_id,
            profiles(name,avatar_url)
          ''')
          .eq('community_id', communityId)
          .order('created_at', ascending: false);

      if (data.isEmpty) {
        return [];
      }

      // بناء قائمة المنشورات (نفس الطريقة التي كانت تعمل)
      final List<Post> posts = [];

      for (final row in (data as List)) {
        try {
          final profile = row['profiles'] ?? {};

          final post = Post(
            id: row['id'].toString(),
            userId: row['user_id'].toString(),
            userName: profile['name'] ?? 'مستخدم',
            userAvatar: profile['avatar_url'] ?? '',
            content: row['content'] ?? '',
            createdAt: DateTime.parse(row['created_at']),
            type: _mapType(row['type']),
            mediaUrl: row['media_url'],
            mediaUrls: row['media_urls'] != null ? List<String>.from(row['media_urls']) : null,
            linkUrl: row['link_url'],
            linkMeta: row['link_meta'],
            bgColor: row['bg_color'],
            likesCount: _safeInt(row['likes_count']),
            dislikesCount: _safeInt(row['dislikes_count']),
            sharesCount: _safeInt(row['shares_count']),
            commentsCount: _safeInt(row['comments_count']),
            viewsCount: _safeInt(row['views_count']),
            copiesCount: _safeInt(row['copies_count']),
            privacy: _mapPostPrivacy(row['posts_privacy']),
            isCommunityPost: true,
            communityId: communityId,
          );

          posts.add(post);
        } catch (e) {
          continue;
        }
      }

      // جلب التفاعلات والإحصائيات (نفس نظام المنشورات العادية)
      if (posts.isNotEmpty) {
        final postIds = posts.map((p) => p.id).toList();

        // جلب جميع التفاعلات لحساب الإحصائيات
        final reactionRows = await _client
            .from('reactions')
            .select('post_id,type,user_id')
            .inFilter('post_id', postIds);

        // حساب إحصائيات التفاعل لكل منشور
        final Map<String, Map<ReactionType, int>> reactionCounts = {};
        final Map<String, ReactionType> userReactions = {};

        for (final row in reactionRows) {
          final postId = row['post_id'] as String;
          final type = _mapReaction(row['type']);
          final userId = row['user_id'] as String;

          // حساب الإحصائيات
          reactionCounts.putIfAbsent(postId, () => {});
          reactionCounts[postId]![type] = (reactionCounts[postId]![type] ?? 0) + 1;

          // حفظ تفاعل المستخدم الحالي
          if (uid != null && userId == uid) {
            userReactions[postId] = type;
          }
        }

        // تطبيق الإحصائيات والتفاعلات على المنشورات
        for (final post in posts) {
          post.reactionCounts = reactionCounts[post.id] ?? {};
          post.currentUserReaction = userReactions[post.id] ?? ReactionType.none;
        }
      }

      return posts;
    } catch (e) {
      throw 'خطأ في جلب منشورات المجتمع: ${e.toString()}';
    }
  }



  Future<void> createCommunityPost({
    required String communityId,
    required String content,
    String? mediaUrl,
    String mediaType = 'text',
  }) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) throw 'المستخدم غير مسجل دخول';

    try {
      // استخدام الدالة الجديدة في قاعدة البيانات
      final result = await _client.rpc('create_community_post', params: {
        'p_community_id': communityId,
        'p_user_id': uid,
        'p_content': content,
        'p_media_url': mediaUrl,
        'p_media_type': mediaType,
      });

      if (result == null) {
        throw 'فشل في إنشاء المنشور';
      }
    } catch (e) {
      throw 'خطأ في نشر المنشور: ${e.toString()}';
    }
  }

  Future<void> voteCommunityPost(String postId, int vote) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) throw 'not logged in';
    await _client.from('community_post_votes').upsert({
      'post_id': postId,
      'user_id': uid,
      'vote': vote,
    });
  }

  Future<List<CommunityComment>> fetchCommunityComments(String postId) async {
    // final uid = _client.auth.currentUser?.id; // قد نحتاجه لاحقاً للتصويت
    final rows = await _client
        .from('community_comments')
        .select('''
          id, post_id, user_id, content, created_at, up_votes, down_votes,
          current_vote:community_comment_votes!left(user_id, vote),
          profiles(name,avatar_url)
        ''')
        .eq('post_id', postId)
        .order('created_at', ascending: false);
    return (rows as List).map((e) {
      final m = e as Map<String, dynamic>;
      int? vote;
      if (m['current_vote'] is List && (m['current_vote'] as List).isNotEmpty) {
        vote = m['current_vote'][0]['vote'] as int?;
      }
      m['current_vote'] = vote;
      return CommunityComment.fromMap(m);
    }).toList();
  }

  Future<void> createCommunityComment({required String postId, required String content}) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) throw 'not logged in';
    await _client.from('community_comments').insert({
      'post_id': postId,
      'user_id': uid,
      'content': content,
    });
  }

  Future<void> voteCommunityComment(String commentId, int vote) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) throw 'not logged in';
    await _client.from('community_comment_votes').upsert({
      'comment_id': commentId,
      'user_id': uid,
      'vote': vote,
    });
  }

  // ======== تفاعلات منشورات المجتمع (نفس نظام المنشورات العادية) ======== //

  /// تسجيل مشاهدة منشور مجتمع
  Future<void> viewCommunityPost(String postId) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) return;

    try {
      // تسجيل المشاهدة (مرة واحدة لكل مستخدم)
      await _client.from('community_post_views').upsert({
        'post_id': postId,
        'user_id': uid,
        'viewed_at': DateTime.now().toIso8601String(),
      });

      // تحديث عداد المشاهدات
      await _client.rpc('increment_community_post_views', params: {'post_id_param': postId});
    } catch (e) {
      // تجاهل أخطاء المشاهدات - ليست حرجة
    }
  }

  /// تفاعل مع منشور مجتمع (استخدام نفس نظام المنشورات العادية)
  Future<void> reactToCommunityPost(String postId, ReactionType reaction) async {
    // استخدام نفس دالة التفاعل للمنشورات العادية
    await toggleReaction(postId: postId, reaction: reaction);
  }

  /// مشاركة منشور مجتمع
  Future<void> shareCommunityPost(String postId, {String? content}) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) throw 'المستخدم غير مسجل دخول';

    try {
      // إنشاء منشور مشاركة
      await _client.from('posts').insert({
        'user_id': uid,
        'content': content ?? 'شارك منشوراً من المجتمع',
        'type': 'share',
        'shared_community_post_id': postId,
        'created_at': DateTime.now().toIso8601String(),
      });

      // تسجيل المشاركة
      await _client.from('community_post_shares').upsert({
        'post_id': postId,
        'user_id': uid,
        'shared_at': DateTime.now().toIso8601String(),
      });

      // تحديث عداد المشاركات
      await _client.rpc('increment_community_post_shares', params: {'post_id_param': postId});
    } catch (e) {
      throw 'خطأ في مشاركة المنشور: ${e.toString()}';
    }
  }

  /// نسخ رابط منشور مجتمع
  Future<bool> copyCommunityPost(String postId) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) return false;

    bool firstTime = true;
    try {
      // تسجيل النسخ (مرة واحدة لكل مستخدم)
      await _client.from('community_post_copies').insert({
        'post_id': postId,
        'user_id': uid,
        'copied_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      if (e.toString().contains('23505')) {
        // تم النسخ مسبقاً
        firstTime = false;
      } else {
        return false;
      }
    }

    if (firstTime) {
      // تحديث عداد النسخ
      try {
        await _client.rpc('increment_community_post_copies', params: {'post_id_param': postId});
      } catch (e) {
        // تجاهل خطأ تحديث العداد
      }
    }

    return firstTime;
  }

  // ======== دعوة المتابعين للمجتمع ======== //

  /// دعوة مستخدم للانضمام إلى المجتمع
  Future<void> inviteUserToCommunity({
    required String communityId,
    required String userId,
    required String communityName,
  }) async {
    final currentUserId = _client.auth.currentUser?.id;
    if (currentUserId == null) throw 'المستخدم غير مسجل دخول';

    try {
      // التحقق من أن المستخدم ليس عضواً بالفعل
      final existingMember = await _client
          .from('community_members')
          .select('id')
          .eq('community_id', communityId)
          .eq('user_id', userId)
          .maybeSingle();

      if (existingMember != null) {
        throw 'المستخدم عضو بالفعل في المجتمع';
      }

      // التحقق من وجود دعوة سابقة
      final existingInvite = await _client
          .from('community_invites')
          .select('id')
          .eq('community_id', communityId)
          .eq('invited_user_id', userId)
          .eq('status', 'pending')
          .maybeSingle();

      if (existingInvite != null) {
        throw 'تم إرسال دعوة مسبقاً لهذا المستخدم';
      }

      // إنشاء دعوة جديدة
      await _client.from('community_invites').insert({
        'community_id': communityId,
        'invited_user_id': userId,
        'invited_by_user_id': currentUserId,
        'status': 'pending',
        'created_at': DateTime.now().toIso8601String(),
      });

      // إرسال إشعار للمستخدم المدعو
      await _client.from('notifications').insert({
        'receiver_id': userId,
        'sender_id': currentUserId,
        'type': 'community_invite',
        'title': 'دعوة للانضمام إلى مجتمع',
        'body': 'تم دعوتك للانضمام إلى مجتمع "$communityName"',
        'data': {
          'community_id': communityId,
          'community_name': communityName,
        },
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      throw 'خطأ في إرسال الدعوة: ${e.toString()}';
    }
  }

  /// جلب قائمة المتابعين (الذين يتابعونني)
  Future<List<Map<String, dynamic>>> fetchFollowersUsers() async {
    final uid = _client.auth.currentUser!.id;
    try {
      // استعلام مباشر للمتابعين
      final data = await _client
          .from('follows')
          .select('follower_id')
          .eq('following_id', uid);

      final followerIds = (data as List).map((e) => e['follower_id'] as String).toList();
      if (followerIds.isEmpty) return [];

      final profiles = await _client
          .from('profiles')
          .select('id,name,username,avatar_url')
          .inFilter('id', followerIds);

      return (profiles as List).cast<Map<String, dynamic>>();
    } catch (e) {
      throw 'خطأ في جلب المتابعين: ${e.toString()}';
    }
  }



  // ======== إعدادات المجتمع للمدير ======== //

  /// جلب أعضاء المجتمع مع أدوارهم
  Future<List<Map<String, dynamic>>> fetchCommunityMembers(String communityId) async {
    // الحصول على معلومات المجتمع أولاً لمعرفة المالك
    final community = await getCommunity(communityId);
    if (community == null) return [];

    final rows = await _client
        .from('community_members')
        .select('role, profiles(id,name,avatar_url)')
        .eq('community_id', communityId);

    return (rows as List).map((e) {
      final profile = e['profiles'] ?? {};
      final userId = profile['id'] as String?;
      String role = e['role'] ?? 'member';

      // إذا كان هذا العضو هو مالك المجتمع، اجعله مدير دائماً
      if (userId == community.ownerId) {
        role = 'admin';
      }

      return {
        'id': userId,
        'name': profile['name'] ?? 'مستخدم',
        'avatar_url': profile['avatar_url'],
        'role': role,
        'is_owner': userId == community.ownerId,
      };
    }).toList();
  }

  /// إزالة عضو من المجتمع (يتطلب أن يكون المستخدم الحالى Admin)
  Future<void> removeCommunityMember(String communityId, String userId) async {
    await _client
        .from('community_members')
        .delete()
        .eq('community_id', communityId)
        .eq('user_id', userId);
  }

  /// تحديث تفاصيل المجتمع (الاسم، الوصف، الفئة، الغلاف، الصورة الشخصية)
  Future<void> updateCommunityInfo({
    required String communityId,
    String? name,
    String? description,
    String? category,
    String? coverUrl,
    String? avatarUrl,
  }) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) throw 'المستخدم غير مسجل دخول';

    // التحقق من صلاحيات التعديل
    final community = await getCommunity(communityId);
    if (community == null) throw 'المجتمع غير موجود';
    if (community.ownerId != uid.toString()) throw 'فقط مالك المجتمع يمكنه تعديل المعلومات';

    final data = {
      if (name != null) 'name': name,
      if (description != null) 'description': description,
      if (category != null) 'category': category,
      if (coverUrl != null) 'cover_url': coverUrl,
      if (avatarUrl != null) 'avatar_url': avatarUrl,
    };

    if (data.isEmpty) return;

    // إضافة updated_at للتأكد من تحديث السجل
    data['updated_at'] = DateTime.now().toIso8601String();

    // تحديث البيانات مع إرجاع النتيجة للتأكد من نجاح العملية
    final result = await _client
        .from('communities')
        .update(data)
        .eq('id', communityId)
        .select();

    if (result.isEmpty) {
      throw 'فشل في تحديث معلومات المجتمع - لم يتم العثور على السجل';
    }

    // التحقق من أن البيانات تم تحديثها فعلاً
    final updatedData = result.first;
    if (name != null && updatedData['name'] != name) {
      throw 'فشل في حفظ الاسم الجديد';
    }
  }

  /// تحديث إعدادات المجتمع العامة
  Future<void> updateCommunitySettings({
    required String communityId,
    bool? allowMemberPosts,
    bool? requireApproval,
    bool? allowComments,
    bool? allowInvites,
    String? postPermission,
    String? joinType,
    bool? isPrivate,
  }) async {
    final data = {
      if (allowMemberPosts != null) 'allow_member_posts': allowMemberPosts,
      if (requireApproval != null) 'require_approval': requireApproval,
      if (allowComments != null) 'allow_comments': allowComments,
      if (allowInvites != null) 'allow_invites': allowInvites,
      if (postPermission != null) 'post_permission': postPermission,
      if (joinType != null) 'join_type': joinType,
      if (isPrivate != null) 'is_private': isPrivate,
    };
    if (data.isEmpty) return;
    await _client.from('communities').update(data).eq('id', communityId);
  }

  /// أرشفة أو إلغاء أرشفة المجتمع (يتطلب صلاحيات مالك)
  Future<void> archiveCommunity(String communityId, bool archive) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) throw 'المستخدم غير مسجل دخول';

    // التحقق من أن المستخدم هو مالك المجتمع
    final community = await getCommunity(communityId);
    if (community == null) throw 'المجتمع غير موجود';
    if (community.ownerId != uid.toString()) throw 'فقط مالك المجتمع يمكنه أرشفة المجتمع';

    // تحديث حالة الأرشفة
    final result = await _client
        .from('communities')
        .update({'is_archived': archive})
        .eq('id', communityId)
        .select();

    if (result.isEmpty) {
      throw 'فشل في تحديث حالة الأرشفة';
    }
  }

  /// تعطيل أو تفعيل المجتمع (يتطلب صلاحيات مالك)
  Future<void> disableCommunity(String communityId, bool disable) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) throw 'المستخدم غير مسجل دخول';

    // التحقق من أن المستخدم هو مالك المجتمع
    final community = await getCommunity(communityId);
    if (community == null) throw 'المجتمع غير موجود';
    if (community.ownerId != uid.toString()) throw 'فقط مالك المجتمع يمكنه تعطيل المجتمع';

    // تحديث حالة التعطيل
    final result = await _client
        .from('communities')
        .update({'is_disabled': disable})
        .eq('id', communityId)
        .select();

    if (result.isEmpty) {
      throw 'فشل في تحديث حالة التعطيل';
    }
  }

  /// حذف المجتمع نهائياً (يتطلب صلاحيات مالك)
  /// تحذير: هذا الإجراء لا يمكن التراجع عنه
  Future<void> deleteCommunity(String communityId) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) throw 'المستخدم غير مسجل دخول';

    // التحقق من أن المستخدم هو مالك المجتمع
    final community = await getCommunity(communityId);
    if (community == null) throw 'المجتمع غير موجود';
    if (community.ownerId != uid.toString()) throw 'فقط مالك المجتمع يمكنه حذف المجتمع';

    try {
      // حذف مبسط وآمن - نحذف فقط الأساسيات

      // 1. حذف المنشورات (التعليقات ستُحذف تلقائياً إذا كان هناك CASCADE)
      try {
        await _client.from('community_posts').delete().eq('community_id', communityId);
      } catch (e) {
        // تجاهل أخطاء حذف المنشورات إذا لم تكن موجودة
      }

      // 2. حذف الأعضاء
      try {
        await _client.from('community_members').delete().eq('community_id', communityId);
      } catch (e) {
        // تجاهل أخطاء حذف الأعضاء إذا لم يكونوا موجودين
      }

      // 3. حذف صور المجتمع من التخزين (اختياري)
      try {
        final files = await _client.storage.from('community-images').list(path: communityId);
        if (files.isNotEmpty) {
          final filePaths = files.map((file) => '$communityId/${file.name}').toList();
          await _client.storage.from('community-images').remove(filePaths);
        }
      } catch (e) {
        // تجاهل أخطاء حذف الصور - ليست حرجة
      }

      // 4. حذف المجتمع نفسه أخيراً
      final result = await _client.from('communities').delete().eq('id', communityId).select();

      if (result.isEmpty) {
        throw 'فشل في حذف المجتمع من قاعدة البيانات';
      }

    } catch (e) {
      if (e.toString().contains('فقط مالك المجتمع') ||
          e.toString().contains('المجتمع غير موجود') ||
          e.toString().contains('المستخدم غير مسجل')) {
        rethrow; // إعادة رمي أخطاء الصلاحيات كما هي
      } else {
        throw 'خطأ في حذف المجتمع: ${e.toString()}';
      }
    }
  }

  /// رفع صورة للمجتمع (غلاف أو صورة شخصية) - نسخة نهائية بصلاحيات كاملة
  Future<String> uploadCommunityImage({
    required String communityId,
    required Uint8List imageBytes,
    required String fileName,
    required String imageType, // 'avatar' أو 'cover'
  }) async {
    final uid = _client.auth.currentUser?.id;
    if (uid == null) throw 'المستخدم غير مسجل دخول';

    // التحقق من صلاحيات التعديل
    final community = await getCommunity(communityId);
    if (community == null) throw 'المجتمع غير موجود';
    if (community.ownerId != uid.toString()) throw 'فقط مالك المجتمع يمكنه تغيير الصور';

    // إنشاء مسار فريد للصورة
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final extension = fileName.split('.').last.toLowerCase();
    if (extension.isEmpty) {
      throw 'نوع الملف غير صحيح';
    }
    final cleanFileName = '${imageType}_$timestamp.$extension';
    final path = '$communityId/$cleanFileName';

    try {
      // التحقق من الصلاحيات باستخدام الدالة المخصصة
      try {
        final permissionCheck = await _client.rpc('check_storage_permission', params: {
          'p_user_id': uid.toString(),
          'p_operation': 'upload',
        });
        if (permissionCheck != true) {
          throw 'لا توجد صلاحيات للرفع';
        }
      } catch (rpcError) {
        // إذا فشلت دالة الفحص، نتابع بدونها
        // تجاهل خطأ دالة الفحص والمتابعة
      }

      // رفع الصورة مباشرة (bucket عام مع صلاحيات كاملة)
      await _client.storage.from('community-images').uploadBinary(
        path,
        imageBytes,
        fileOptions: const FileOptions(
          upsert: true,
        ),
      );

      // الحصول على رابط الصورة العام
      final url = _client.storage.from('community-images').getPublicUrl(path);

      // تحديث رابط الصورة في قاعدة البيانات
      if (imageType == 'avatar') {
        await updateCommunityInfo(communityId: communityId, avatarUrl: url);
      } else if (imageType == 'cover') {
        await updateCommunityInfo(communityId: communityId, coverUrl: url);
      } else {
        throw 'نوع الصورة غير صحيح: $imageType';
      }

      return url;
    } catch (e) {
      // معالجة أخطاء شاملة
      String errorDetails = e.toString().toLowerCase();

      // أخطاء التحقق - إعادة رميها كما هي
      if (errorDetails.contains('المستخدم غير مسجل') ||
          errorDetails.contains('المجتمع غير موجود') ||
          errorDetails.contains('فقط مالك المجتمع') ||
          errorDetails.contains('نوع الملف') ||
          errorDetails.contains('نوع الصورة')) {
        rethrow;
      }

      // أخطاء الصلاحيات - توجيه للحل الجديد
      if (errorDetails.contains('unauthorized') ||
          errorDetails.contains('403') ||
          errorDetails.contains('forbidden') ||
          errorDetails.contains('policy') ||
          errorDetails.contains('rls') ||
          errorDetails.contains('row-level security')) {
        throw 'مشكلة في الصلاحيات - شغل سكريپت create_full_permissions.sql';
      }

      // أخطاء bucket
      if (errorDetails.contains('not found') ||
          errorDetails.contains('404') ||
          errorDetails.contains('bucket')) {
        throw 'Bucket غير موجود - شغل سكريپت create_full_permissions.sql';
      }

      // أخطاء الشبكة
      if (errorDetails.contains('network') ||
          errorDetails.contains('connection') ||
          errorDetails.contains('timeout')) {
        throw 'مشكلة في الاتصال - تحقق من الإنترنت';
      }

      // خطأ عام
      throw 'خطأ في رفع الصورة: ${e.toString()}';
    }
  }



  /// جدولة حذف المجتمع فى وقت لاحق (يحتاج جدول backend)
  Future<void> scheduleCommunityDeletion(String communityId, DateTime at) async {
    await _client.from('community_delete_queue').upsert({
      'community_id': communityId,
      'delete_at': at.toIso8601String(),
    });
  }

  // ======== Group admin actions ======== //

  Future<void> archiveGroup(String groupId, bool archive) async {
    await _client.from('groups').update({'is_archived': archive}).eq('id', groupId);
  }

  Future<void> deleteGroup(String groupId) async {
    await _client.from('groups').delete().eq('id', groupId);
  }

  Future<void> scheduleGroupDeletion(String groupId, DateTime at) async {
    await _client.from('group_delete_queue').upsert({
      'group_id': groupId,
      'delete_at': at.toIso8601String(),
    });
  }

  Future<void> updateGroupInfo({
    required String groupId,
    String? name,
    String? description,
    String? coverUrl,
  }) async {
    final data = {
      if (name != null) 'name': name,
      if (description != null) 'description': description,
      if (coverUrl != null) 'cover_url': coverUrl,
    };
    if (data.isEmpty) return;
    await _client.from('groups').update(data).eq('id', groupId);
  }

  // تحديث كاش الملف الشخصي لمستخدم محدد
  Future<void> refreshUserProfile(String userId) async {
    try {
      final response = await _client
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single();
      
      // تحديث الكاش
      final currentCache = profilesCache.value;
      currentCache[userId] = response;
      profilesCache.value = Map.from(currentCache);
      
      print('Profile cache updated for user: $userId, is_verified: ${response['is_verified']}');
    } catch (e) {
      print('Error refreshing user profile: $e');
    }
  }
}