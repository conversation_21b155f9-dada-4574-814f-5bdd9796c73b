-- إنشاء نظام العقارات المتكامل

-- جدول العقارات الرئيسي
CREATE TABLE IF NOT EXISTS real_estate_properties (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    property_type TEXT NOT NULL CHECK (property_type IN ('apartment', 'house', 'land', 'shop', 'office', 'villa', 'warehouse', 'farm')),
    purpose TEXT NOT NULL CHECK (purpose IN ('sale', 'rent', 'exchange')),
    category TEXT NOT NULL CHECK (category IN ('residential', 'commercial', 'land', 'industrial')),
    
    -- معلومات الموقع
    country TEXT NOT NULL,
    city TEXT NOT NULL,
    district TEXT,
    address TEXT,
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    
    -- معلومات العقار
    price DECIMAL(15, 2) NOT NULL,
    currency TEXT DEFAULT 'USD' CHECK (currency IN ('USD', 'EUR', 'SAR', 'AED', 'EGP', 'MAD', 'TND', 'DZD')),
    area DECIMAL(10, 2), -- المساحة بالمتر المربع
    bedrooms INTEGER DEFAULT 0,
    bathrooms INTEGER DEFAULT 0,
    floors INTEGER DEFAULT 1,
    parking_spaces INTEGER DEFAULT 0,
    
    -- ميزات إضافية
    features JSONB DEFAULT '[]', -- مثل: ["مصعد", "حديقة", "مسبح", "أمن"]
    amenities JSONB DEFAULT '[]', -- مثل: ["مدرسة قريبة", "مستشفى", "مواصلات"]
    
    -- معلومات التواصل
    contact_phone TEXT,
    contact_whatsapp TEXT,
    allow_app_messages BOOLEAN DEFAULT TRUE,
    
    -- حالة الإعلان
    is_active BOOLEAN DEFAULT TRUE,
    is_featured BOOLEAN DEFAULT FALSE,
    is_verified BOOLEAN DEFAULT FALSE,
    views_count INTEGER DEFAULT 0,
    
    -- تواريخ
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '90 days')
);

-- جدول صور العقارات
CREATE TABLE IF NOT EXISTS property_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    property_id UUID NOT NULL REFERENCES real_estate_properties(id) ON DELETE CASCADE,
    image_url TEXT NOT NULL,
    image_order INTEGER DEFAULT 0,
    is_main BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المفضلة
CREATE TABLE IF NOT EXISTS property_favorites (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    property_id UUID NOT NULL REFERENCES real_estate_properties(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, property_id)
);

-- جدول الإبلاغات
CREATE TABLE IF NOT EXISTS property_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    reporter_id UUID NOT NULL,
    property_id UUID NOT NULL REFERENCES real_estate_properties(id) ON DELETE CASCADE,
    reason TEXT NOT NULL CHECK (reason IN ('fraud', 'inappropriate', 'duplicate', 'wrong_info', 'spam')),
    details TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- جدول المحادثات العقارية
CREATE TABLE IF NOT EXISTS property_chats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    property_id UUID NOT NULL REFERENCES real_estate_properties(id) ON DELETE CASCADE,
    buyer_id UUID NOT NULL,
    seller_id UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_message_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    UNIQUE(property_id, buyer_id)
);

-- جدول رسائل العقارات
CREATE TABLE IF NOT EXISTS property_messages (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    chat_id UUID NOT NULL REFERENCES property_chats(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL,
    content TEXT NOT NULL,
    message_type TEXT DEFAULT 'text' CHECK (message_type IN ('text', 'image', 'location')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_read BOOLEAN DEFAULT FALSE
);

-- جدول اهتمامات المستخدمين (للإشعارات)
CREATE TABLE IF NOT EXISTS user_property_interests (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL,
    property_type TEXT,
    purpose TEXT,
    city TEXT,
    min_price DECIMAL(15, 2),
    max_price DECIMAL(15, 2),
    min_bedrooms INTEGER,
    max_bedrooms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_properties_user_id ON real_estate_properties(user_id);
CREATE INDEX IF NOT EXISTS idx_properties_type ON real_estate_properties(property_type);
CREATE INDEX IF NOT EXISTS idx_properties_purpose ON real_estate_properties(purpose);
CREATE INDEX IF NOT EXISTS idx_properties_category ON real_estate_properties(category);
CREATE INDEX IF NOT EXISTS idx_properties_city ON real_estate_properties(city);
CREATE INDEX IF NOT EXISTS idx_properties_price ON real_estate_properties(price);
CREATE INDEX IF NOT EXISTS idx_properties_bedrooms ON real_estate_properties(bedrooms);
CREATE INDEX IF NOT EXISTS idx_properties_is_active ON real_estate_properties(is_active);
CREATE INDEX IF NOT EXISTS idx_properties_created_at ON real_estate_properties(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_properties_location ON real_estate_properties(latitude, longitude);

CREATE INDEX IF NOT EXISTS idx_property_images_property_id ON property_images(property_id);
CREATE INDEX IF NOT EXISTS idx_property_images_order ON property_images(image_order);

CREATE INDEX IF NOT EXISTS idx_property_favorites_user_id ON property_favorites(user_id);
CREATE INDEX IF NOT EXISTS idx_property_favorites_property_id ON property_favorites(property_id);

CREATE INDEX IF NOT EXISTS idx_property_reports_property_id ON property_reports(property_id);
CREATE INDEX IF NOT EXISTS idx_property_reports_reporter_id ON property_reports(reporter_id);

CREATE INDEX IF NOT EXISTS idx_property_chats_property_id ON property_chats(property_id);
CREATE INDEX IF NOT EXISTS idx_property_chats_buyer_id ON property_chats(buyer_id);
CREATE INDEX IF NOT EXISTS idx_property_chats_seller_id ON property_chats(seller_id);

CREATE INDEX IF NOT EXISTS idx_property_messages_chat_id ON property_messages(chat_id);
CREATE INDEX IF NOT EXISTS idx_property_messages_sender_id ON property_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_property_messages_created_at ON property_messages(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_user_interests_user_id ON user_property_interests(user_id);
CREATE INDEX IF NOT EXISTS idx_user_interests_type ON user_property_interests(property_type);
CREATE INDEX IF NOT EXISTS idx_user_interests_city ON user_property_interests(city);

-- دالة لزيادة عدد المشاهدات
CREATE OR REPLACE FUNCTION increment_property_views(property_uuid UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE real_estate_properties 
    SET views_count = views_count + 1 
    WHERE id = property_uuid;
END;
$$ LANGUAGE plpgsql;

-- دالة لتحديث وقت آخر رسالة في المحادثة
CREATE OR REPLACE FUNCTION update_property_chat_last_message()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE property_chats 
    SET 
        last_message_at = NEW.created_at,
        updated_at = NEW.created_at
    WHERE id = NEW.chat_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- تريجر لتحديث وقت آخر رسالة
DROP TRIGGER IF EXISTS trigger_update_property_chat_last_message ON property_messages;
CREATE TRIGGER trigger_update_property_chat_last_message
    AFTER INSERT ON property_messages
    FOR EACH ROW
    EXECUTE FUNCTION update_property_chat_last_message();

-- دالة للبحث عن العقارات المطابقة لاهتمامات المستخدم
CREATE OR REPLACE FUNCTION find_matching_properties(interest_id UUID)
RETURNS TABLE(property_id UUID) AS $$
DECLARE
    interest_record user_property_interests%ROWTYPE;
BEGIN
    SELECT * INTO interest_record FROM user_property_interests WHERE id = interest_id;
    
    RETURN QUERY
    SELECT p.id
    FROM real_estate_properties p
    WHERE p.is_active = true
    AND (interest_record.property_type IS NULL OR p.property_type = interest_record.property_type)
    AND (interest_record.purpose IS NULL OR p.purpose = interest_record.purpose)
    AND (interest_record.city IS NULL OR p.city ILIKE '%' || interest_record.city || '%')
    AND (interest_record.min_price IS NULL OR p.price >= interest_record.min_price)
    AND (interest_record.max_price IS NULL OR p.price <= interest_record.max_price)
    AND (interest_record.min_bedrooms IS NULL OR p.bedrooms >= interest_record.min_bedrooms)
    AND (interest_record.max_bedrooms IS NULL OR p.bedrooms <= interest_record.max_bedrooms);
END;
$$ LANGUAGE plpgsql;

-- تفعيل Row Level Security
ALTER TABLE real_estate_properties ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_favorites ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_chats ENABLE ROW LEVEL SECURITY;
ALTER TABLE property_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_property_interests ENABLE ROW LEVEL SECURITY;

-- حذف السياسات الموجودة إذا كانت موجودة
DROP POLICY IF EXISTS "الجميع يمكنهم قراءة العقارات النشطة" ON real_estate_properties;
DROP POLICY IF EXISTS "المستخدم يمكنه إنشاء عقاراته" ON real_estate_properties;
DROP POLICY IF EXISTS "المستخدم يمكنه تحديث عقاراته" ON real_estate_properties;
DROP POLICY IF EXISTS "المستخدم يمكنه حذف عقاراته" ON real_estate_properties;

DROP POLICY IF EXISTS "الجميع يمكنهم قراءة صور العقارات النشطة" ON property_images;
DROP POLICY IF EXISTS "صاحب العقار يمكنه إدارة الصور" ON property_images;

DROP POLICY IF EXISTS "المستخدم يمكنه قراءة مفضلته" ON property_favorites;
DROP POLICY IF EXISTS "المستخدم يمكنه إدارة مفضلته" ON property_favorites;

DROP POLICY IF EXISTS "المستخدم يمكنه الإبلاغ" ON property_reports;

DROP POLICY IF EXISTS "المستخدم يمكنه قراءة محادثاته" ON property_chats;
DROP POLICY IF EXISTS "المستخدم يمكنه إنشاء محادثة" ON property_chats;
DROP POLICY IF EXISTS "المستخدم يمكنه تحديث محادثاته" ON property_chats;

DROP POLICY IF EXISTS "المستخدم يمكنه قراءة رسائل محادثاته" ON property_messages;
DROP POLICY IF EXISTS "المستخدم يمكنه إرسال رسائل" ON property_messages;

DROP POLICY IF EXISTS "المستخدم يمكنه إدارة اهتماماته" ON user_property_interests;

-- سياسات الأمان للعقارات
CREATE POLICY "الجميع يمكنهم قراءة العقارات النشطة" ON real_estate_properties
    FOR SELECT USING (is_active = true);

CREATE POLICY "المستخدم يمكنه إنشاء عقاراته" ON real_estate_properties
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه تحديث عقاراته" ON real_estate_properties
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "تحديث المشاهدات للجميع" ON real_estate_properties
    FOR UPDATE USING (is_active = true)
    WITH CHECK (is_active = true);

CREATE POLICY "المستخدم يمكنه حذف عقاراته" ON real_estate_properties
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان لصور العقارات
CREATE POLICY "الجميع يمكنهم قراءة صور العقارات النشطة" ON property_images
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM real_estate_properties
            WHERE id = property_images.property_id AND is_active = true
        )
    );

CREATE POLICY "صاحب العقار يمكنه إدارة الصور" ON property_images
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM real_estate_properties
            WHERE id = property_images.property_id AND user_id = auth.uid()
        )
    );

-- سياسات الأمان للمفضلة
CREATE POLICY "المستخدم يمكنه قراءة مفضلته" ON property_favorites
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه إدارة مفضلته" ON property_favorites
    FOR ALL USING (auth.uid() = user_id);

-- سياسات الأمان للإبلاغات
CREATE POLICY "المستخدم يمكنه الإبلاغ" ON property_reports
    FOR INSERT WITH CHECK (auth.uid() = reporter_id);

-- سياسات الأمان للمحادثات
CREATE POLICY "المستخدم يمكنه قراءة محادثاته" ON property_chats
    FOR SELECT USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

CREATE POLICY "المستخدم يمكنه إنشاء محادثة" ON property_chats
    FOR INSERT WITH CHECK (auth.uid() = buyer_id);

CREATE POLICY "المستخدم يمكنه تحديث محادثاته" ON property_chats
    FOR UPDATE USING (auth.uid() = buyer_id OR auth.uid() = seller_id);

-- سياسات الأمان للرسائل
CREATE POLICY "المستخدم يمكنه قراءة رسائل محادثاته" ON property_messages
    FOR SELECT USING (
        auth.uid() IN (
            SELECT buyer_id FROM property_chats WHERE id = property_messages.chat_id
            UNION
            SELECT seller_id FROM property_chats WHERE id = property_messages.chat_id
        )
    );

CREATE POLICY "المستخدم يمكنه إرسال رسائل" ON property_messages
    FOR INSERT WITH CHECK (auth.uid() = sender_id);

-- سياسات الأمان لاهتمامات المستخدمين
CREATE POLICY "المستخدم يمكنه إدارة اهتماماته" ON user_property_interests
    FOR ALL USING (auth.uid() = user_id);

-- دالة لتحديث المشاهدات
CREATE OR REPLACE FUNCTION increment_property_views(property_id UUID)
RETURNS void AS $$
BEGIN
  UPDATE real_estate_properties
  SET views_count = views_count + 1,
      updated_at = NOW()
  WHERE id = property_id AND is_active = true;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- منح الصلاحيات
GRANT ALL ON real_estate_properties TO authenticated;
GRANT ALL ON property_images TO authenticated;
GRANT ALL ON property_favorites TO authenticated;
GRANT ALL ON property_reports TO authenticated;
GRANT ALL ON property_chats TO authenticated;
GRANT ALL ON property_messages TO authenticated;
GRANT ALL ON user_property_interests TO authenticated;
GRANT EXECUTE ON FUNCTION increment_property_views(UUID) TO authenticated;

GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON FUNCTION increment_property_views(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION update_property_chat_last_message() TO authenticated;
GRANT EXECUTE ON FUNCTION find_matching_properties(UUID) TO authenticated;

-- رسالة نجاح
SELECT 'تم إنشاء نظام العقارات بنجاح!' as message;
