import 'package:flutter/material.dart';

enum JobType {
  fullTime,     // دوام كامل
  partTime,     // دوام جزئي
  remote,       // عن بعد
  freelance,    // عمل حر
  contract,     // عقد مؤقت
}

enum MaritalStatus {
  single,       // أعزب
  married,      // متزوج
  divorced,     // مطلق
  widowed,      // أرمل
}

enum JobCategory {
  construction,     // البناء
  teaching,         // التدريس
  driving,          // السياقة
  barbering,        // الحلاقة
  programming,      // البرمجة
  delivery,         // التوصيل
  design,           // التصميم
  carpentry,        // النجارة
  blacksmithing,    // الحداد
  tailoring,        // الخياطة
  painting,         // الصباغة
  plastering,       // الجبص
  electrical,       // الكهرباء
  mechanics,        // الميكانيك
  cleaning,         // النظافة
  cooking,          // الطبخ
  healthcare,       // الرعاية الصحية
  sales,            // المبيعات
  accounting,       // المحاسبة
  security,         // الأمن
  other,            // أخرى
}

extension JobTypeExtension on JobType {
  String get arabicName {
    switch (this) {
      case JobType.fullTime:
        return 'دوام كامل';
      case JobType.partTime:
        return 'دوام جزئي';
      case JobType.remote:
        return 'عن بعد';
      case JobType.freelance:
        return 'عمل حر';
      case JobType.contract:
        return 'عقد مؤقت';
    }
  }

  IconData get icon {
    switch (this) {
      case JobType.fullTime:
        return Icons.work;
      case JobType.partTime:
        return Icons.schedule;
      case JobType.remote:
        return Icons.home_work;
      case JobType.freelance:
        return Icons.person_outline;
      case JobType.contract:
        return Icons.assignment;
    }
  }

  Color get color {
    switch (this) {
      case JobType.fullTime:
        return Colors.blue;
      case JobType.partTime:
        return Colors.orange;
      case JobType.remote:
        return Colors.green;
      case JobType.freelance:
        return Colors.purple;
      case JobType.contract:
        return Colors.teal;
    }
  }
}

extension MaritalStatusExtension on MaritalStatus {
  String get arabicName {
    switch (this) {
      case MaritalStatus.single:
        return 'أعزب';
      case MaritalStatus.married:
        return 'متزوج';
      case MaritalStatus.divorced:
        return 'مطلق';
      case MaritalStatus.widowed:
        return 'أرمل';
    }
  }
}

extension JobCategoryExtension on JobCategory {
  String get arabicName {
    switch (this) {
      case JobCategory.construction:
        return 'البناء';
      case JobCategory.teaching:
        return 'التدريس';
      case JobCategory.driving:
        return 'السياقة';
      case JobCategory.barbering:
        return 'الحلاقة';
      case JobCategory.programming:
        return 'البرمجة';
      case JobCategory.delivery:
        return 'التوصيل';
      case JobCategory.design:
        return 'التصميم';
      case JobCategory.carpentry:
        return 'النجارة';
      case JobCategory.blacksmithing:
        return 'الحداد';
      case JobCategory.tailoring:
        return 'الخياطة';
      case JobCategory.painting:
        return 'الصباغة';
      case JobCategory.plastering:
        return 'الجبص';
      case JobCategory.electrical:
        return 'الكهرباء';
      case JobCategory.mechanics:
        return 'الميكانيك';
      case JobCategory.cleaning:
        return 'النظافة';
      case JobCategory.cooking:
        return 'الطبخ';
      case JobCategory.healthcare:
        return 'الرعاية الصحية';
      case JobCategory.sales:
        return 'المبيعات';
      case JobCategory.accounting:
        return 'المحاسبة';
      case JobCategory.security:
        return 'الأمن';
      case JobCategory.other:
        return 'أخرى';
    }
  }

  IconData get icon {
    switch (this) {
      case JobCategory.construction:
        return Icons.construction;
      case JobCategory.teaching:
        return Icons.school;
      case JobCategory.driving:
        return Icons.drive_eta;
      case JobCategory.barbering:
        return Icons.content_cut;
      case JobCategory.programming:
        return Icons.code;
      case JobCategory.delivery:
        return Icons.delivery_dining;
      case JobCategory.design:
        return Icons.design_services;
      case JobCategory.carpentry:
        return Icons.handyman;
      case JobCategory.blacksmithing:
        return Icons.build;
      case JobCategory.tailoring:
        return Icons.checkroom;
      case JobCategory.painting:
        return Icons.format_paint;
      case JobCategory.plastering:
        return Icons.home_repair_service;
      case JobCategory.electrical:
        return Icons.electrical_services;
      case JobCategory.mechanics:
        return Icons.car_repair;
      case JobCategory.cleaning:
        return Icons.cleaning_services;
      case JobCategory.cooking:
        return Icons.restaurant;
      case JobCategory.healthcare:
        return Icons.medical_services;
      case JobCategory.sales:
        return Icons.point_of_sale;
      case JobCategory.accounting:
        return Icons.calculate;
      case JobCategory.security:
        return Icons.security;
      case JobCategory.other:
        return Icons.work_outline;
    }
  }

  Color get color {
    switch (this) {
      case JobCategory.construction:
        return Colors.brown;
      case JobCategory.teaching:
        return Colors.blue;
      case JobCategory.driving:
        return Colors.green;
      case JobCategory.barbering:
        return Colors.orange;
      case JobCategory.programming:
        return Colors.purple;
      case JobCategory.delivery:
        return Colors.red;
      case JobCategory.design:
        return Colors.pink;
      case JobCategory.carpentry:
        return Colors.amber;
      case JobCategory.blacksmithing:
        return Colors.grey;
      case JobCategory.tailoring:
        return Colors.indigo;
      case JobCategory.painting:
        return Colors.cyan;
      case JobCategory.plastering:
        return Colors.lime;
      case JobCategory.electrical:
        return Colors.yellow;
      case JobCategory.mechanics:
        return Colors.deepOrange;
      case JobCategory.cleaning:
        return Colors.lightBlue;
      case JobCategory.cooking:
        return Colors.deepPurple;
      case JobCategory.healthcare:
        return Colors.teal;
      case JobCategory.sales:
        return Colors.lightGreen;
      case JobCategory.accounting:
        return Colors.blueGrey;
      case JobCategory.security:
        return Colors.black87;
      case JobCategory.other:
        return Colors.grey;
    }
  }
}

class JobSeeker {
  final String id;
  final String userId;
  final String fullName;
  final String? profileImage;
  final int age;
  final String gender; // ذكر/أنثى
  final MaritalStatus maritalStatus;
  final String currentCountry;
  final String currentCity;
  final String nationality;
  final JobCategory category;
  final List<String> skills;
  final List<String> languages;
  final int experienceYears;
  final String description;
  final JobType preferredJobType;
  final String preferredLocation;
  final String phoneNumber;
  final String? email;
  final String? socialLinks;
  final String? cvUrl;
  final List<String> portfolioImages;
  final bool isActive;
  final int viewsCount;
  final int likesCount;
  final DateTime createdAt;
  final DateTime updatedAt;

  const JobSeeker({
    required this.id,
    required this.userId,
    required this.fullName,
    this.profileImage,
    required this.age,
    required this.gender,
    required this.maritalStatus,
    required this.currentCountry,
    required this.currentCity,
    required this.nationality,
    required this.category,
    required this.skills,
    required this.languages,
    required this.experienceYears,
    required this.description,
    required this.preferredJobType,
    required this.preferredLocation,
    required this.phoneNumber,
    this.email,
    this.socialLinks,
    this.cvUrl,
    required this.portfolioImages,
    required this.isActive,
    required this.viewsCount,
    required this.likesCount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory JobSeeker.fromJson(Map<String, dynamic> json) {
    return JobSeeker(
      id: json['id'],
      userId: json['user_id'],
      fullName: json['full_name'],
      profileImage: json['profile_image'],
      age: json['age'],
      gender: json['gender'],
      maritalStatus: MaritalStatus.values.firstWhere(
        (s) => s.name == json['marital_status'],
        orElse: () => MaritalStatus.single,
      ),
      currentCountry: json['current_country'],
      currentCity: json['current_city'],
      nationality: json['nationality'],
      category: JobCategory.values.firstWhere(
        (c) => c.name == json['category'],
        orElse: () => JobCategory.other,
      ),
      skills: List<String>.from(json['skills'] ?? []),
      languages: List<String>.from(json['languages'] ?? []),
      experienceYears: json['experience_years'],
      description: json['description'],
      preferredJobType: JobType.values.firstWhere(
        (t) => t.name == json['preferred_job_type'],
        orElse: () => JobType.fullTime,
      ),
      preferredLocation: json['preferred_location'],
      phoneNumber: json['phone_number'],
      email: json['email'],
      socialLinks: json['social_links'],
      cvUrl: json['cv_url'],
      portfolioImages: List<String>.from(json['portfolio_images'] ?? []),
      isActive: json['is_active'] ?? true,
      viewsCount: json['views_count'] ?? 0,
      likesCount: json['likes_count'] ?? 0,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'full_name': fullName,
      'profile_image': profileImage,
      'age': age,
      'gender': gender,
      'marital_status': maritalStatus.name,
      'current_country': currentCountry,
      'current_city': currentCity,
      'nationality': nationality,
      'category': category.name,
      'skills': skills,
      'languages': languages,
      'experience_years': experienceYears,
      'description': description,
      'preferred_job_type': preferredJobType.name,
      'preferred_location': preferredLocation,
      'phone_number': phoneNumber,
      'email': email,
      'social_links': socialLinks,
      'cv_url': cvUrl,
      'portfolio_images': portfolioImages,
      'is_active': isActive,
      'views_count': viewsCount,
      'likes_count': likesCount,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  String get formattedExperience {
    if (experienceYears == 0) {
      return 'بدون خبرة';
    } else if (experienceYears == 1) {
      return 'سنة واحدة';
    } else if (experienceYears == 2) {
      return 'سنتان';
    } else if (experienceYears <= 10) {
      return '$experienceYears سنوات';
    } else {
      return '$experienceYears سنة';
    }
  }

  String get formattedAge {
    return '$age سنة';
  }

  String get skillsText {
    if (skills.isEmpty) return 'لا توجد مهارات محددة';
    return skills.join(' • ');
  }

  String get languagesText {
    if (languages.isEmpty) return 'لا توجد لغات محددة';
    return languages.join(' • ');
  }
}
