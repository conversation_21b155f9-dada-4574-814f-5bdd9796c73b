-- التحقق من هيكل جداول التصويت
-- Check polls table structure

-- 1. عرض أعمدة جدول polls
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'polls'
ORDER BY ordinal_position;

-- 2. عرض أعمدة جدول poll_options
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'poll_options'
ORDER BY ordinal_position;

-- 3. عرض أعمدة جدول poll_votes
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'poll_votes'
ORDER BY ordinal_position;

-- 4. عرض عينة من بيانات التصويتات
SELECT 
    id,
    question,
    user_id,
    type,
    category,
    is_active,
    created_at
FROM polls 
ORDER BY created_at DESC
LIMIT 5;

-- 5. عرض عينة من خيارات التصويت
SELECT 
    id,
    poll_id,
    text,
    votes,
    option_order
FROM poll_options 
ORDER BY poll_id, option_order
LIMIT 10;

-- 6. عرض عينة من الأصوات
SELECT 
    id,
    poll_id,
    option_id,
    user_id,
    created_at
FROM poll_votes 
ORDER BY created_at DESC
LIMIT 10; 