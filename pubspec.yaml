name: a<PERSON><PERSON><PERSON>
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+2

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  image_picker: ^1.1.2
  video_player: ^2.7.0
  path_provider: ^2.1.5
  path: ^1.9.1
  supabase_flutter: ^2.9.1
  json_annotation: ^4.8.1
  flutter_localizations:
    sdk: flutter
  metadata_fetch: ^0.4.2
  chewie: ^1.7.0
  url_launcher: ^6.2.4
  app_links: ^6.4.0
  livekit_client: ^2.4.1
  permission_handler: ^12.0.0+1
  connectivity_plus: ^6.1.4
  share_plus: ^11.0.0
  visibility_detector: ^0.4.0+2
  video_thumbnail: ^0.5.3
  photo_view: ^0.15.0
  shimmer: ^3.0.0
  readmore: ^3.0.0
  flutter_linkify: ^6.0.0
  # --- Audio & File utilities ---
  file_picker: ^10.2.0
  record: ^6.0.0
  audioplayers: ^6.5.0
  just_waveform: ^0.0.7
  http: ^1.1.0
  shared_preferences: ^2.2.2
  cached_network_image: ^3.3.1
  hive: ^2.2.3
  hive_flutter: ^1.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0
  flutter_launcher_icons: ^0.14.4
  image: ^4.0.17

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

  assets:
    - assets/app_icon.png
    - assets/preload_icon.png
    - assets/reactions/

# Configuration for flutter_launcher_icons
flutter_icons:
  android: true
  ios: true
  image_path: "assets/app_icon.png"  # أيقونة عالية الدقّة لكل المنصّات
  # استخدام أيقونة تكيُّفية للحصول على جودة أعلى على أندرويد
  adaptive_icon_foreground: "assets/app_icon.png"
  adaptive_icon_background: "#FFFFFF" # خلفية بيضاء ثابتة (غيّر اللون إن أردت)
  remove_alpha_ios: true
  min_sdk_android: 26

dependency_overrides:
  sign_in_with_apple: ^6.0.0
