import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/charity_item.dart';
import '../services/charity_service.dart';
import 'verified_badge.dart';
import 'interactive_verified_badge.dart';

class CharityCard extends StatelessWidget {
  final CharityItem item;
  final VoidCallback? onTap;
  final VoidCallback? onInterest;

  const CharityCard({
    super.key,
    required this.item,
    this.onTap,
    this.onInterest,
  });

  // دالة إجراء الاتصال
  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri phoneUri = Uri(scheme: 'tel', path: phoneNumber);
    try {
      if (await canLaunchUrl(phoneUri)) {
        await launchUrl(phoneUri);
      } else {
        throw 'لا يمكن إجراء الاتصال';
      }
    } catch (e) {
      debugPrint('خطأ في الاتصال: $e');
    }
  }

  // دالة إبداء الاهتمام
  Future<void> _showInterest() async {
    try {
      final charityService = CharityService();
      await charityService.showInterest(item.id);

      if (onInterest != null) {
        onInterest!();
      }
    } catch (e) {
      debugPrint('خطأ في إبداء الاهتمام: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // معلومات المستخدم والحالة
              _buildUserInfo(),
              
              const SizedBox(height: 12),
              
              // العنوان والوصف
              _buildContent(),
              
              const SizedBox(height: 12),
              
              // الصورة إذا كانت متوفرة
              if (item.images.isNotEmpty) _buildImage(),
              
              const SizedBox(height: 12),
              
              // معلومات إضافية
              _buildDetails(),
              
              const SizedBox(height: 12),
              
              // أزرار الإجراءات
              _buildActions(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUserInfo() {
    return Row(
      children: [
        // صورة المستخدم
        CircleAvatar(
          radius: 20,
          backgroundColor: Colors.teal[100],
          backgroundImage: item.userAvatar.isNotEmpty && !item.isAnonymous
              ? NetworkImage(item.userAvatar)
              : null,
          child: item.userAvatar.isEmpty || item.isAnonymous
              ? Icon(
                  item.isAnonymous ? Icons.person_outline : Icons.person,
                  color: Colors.teal[600],
                )
              : null,
        ),
        
        const SizedBox(width: 12),
        
        // اسم المستخدم والوقت
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    item.displayName,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  if (item.isVerified && !item.isAnonymous) ...[
                    const SizedBox(width: 4),
                    InteractiveVerifiedBadge(
                      size: 16,
                      userName: item.userName,
                    ),
                  ],
                  if (item.isAnonymous) ...[
                    const SizedBox(width: 4),
                    Icon(Icons.visibility_off, size: 14, color: Colors.grey[600]),
                  ],
                ],
              ),
              Text(
                item.formattedTime,
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        
        // نوع الصدقة والحالة
        Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getTypeColor().withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: _getTypeColor().withValues(alpha: 0.3)),
              ),
              child: Text(
                item.type.arabicName,
                style: TextStyle(
                  fontSize: 10,
                  color: _getTypeColor(),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            if (item.isUrgent) ...[
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.priority_high, size: 12, color: Colors.red[600]),
                    const SizedBox(width: 2),
                    Text(
                      'طارئ',
                      style: TextStyle(
                        fontSize: 9,
                        color: Colors.red[600],
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  Widget _buildContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          item.title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            height: 1.3,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          item.description,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[700],
            height: 1.4,
          ),
          maxLines: 3,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );
  }

  Widget _buildImage() {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Image.network(
        item.images.first,
        height: 200,
        width: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            height: 200,
            color: Colors.grey[200],
            child: Icon(
              Icons.image_not_supported,
              color: Colors.grey[400],
              size: 50,
            ),
          );
        },
      ),
    );
  }

  Widget _buildDetails() {
    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: [
        // الفئة
        _buildDetailChip(
          icon: item.category.icon,
          label: item.category.arabicName,
          color: item.category.color,
        ),
        
        // الحالة
        _buildDetailChip(
          icon: Icons.info_outline,
          label: item.condition.arabicName,
          color: item.condition.color,
        ),
        
        // المدينة
        _buildDetailChip(
          icon: Icons.location_on_outlined,
          label: item.city,
          color: Colors.grey[600]!,
        ),
        
        // طريقة التسليم
        _buildDetailChip(
          icon: Icons.local_shipping_outlined,
          label: item.deliveryMethod.arabicName,
          color: Colors.blue[600]!,
        ),
      ],
    );
  }

  Widget _buildDetailChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    return Row(
      children: [
        // عدد المهتمين
        if (item.interestCount > 0) ...[
          Icon(Icons.people_outline, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 4),
          Text(
            '${item.interestCount} مهتم',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ],
        
        const Spacer(),
        
        // رقم الهاتف إذا كان متوفراً
        if (item.phoneNumber != null && item.phoneNumber!.isNotEmpty) ...[
          TextButton.icon(
            onPressed: () => _makePhoneCall(item.phoneNumber!),
            icon: Icon(Icons.phone, size: 16, color: Colors.green[600]),
            label: Text(
              'اتصال',
              style: TextStyle(fontSize: 12, color: Colors.green[600]),
            ),
          ),
          const SizedBox(width: 8),
        ],
        
        // زر الاهتمام
        ElevatedButton.icon(
          onPressed: () => _showInterest(),
          icon: Icon(
            item.type == CharityType.donation
                ? Icons.volunteer_activism
                : Icons.help_outline,
            size: 16,
          ),
          label: Text(
            item.type == CharityType.donation
                ? 'أنا مهتم'
                : 'أريد المساعدة',
            style: const TextStyle(fontSize: 12),
          ),
          style: ElevatedButton.styleFrom(
            backgroundColor: _getTypeColor(),
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            minimumSize: Size.zero,
          ),
        ),
      ],
    );
  }

  Color _getTypeColor() {
    switch (item.type) {
      case CharityType.donation:
        return Colors.green;
      case CharityType.request:
        return Colors.blue;
      case CharityType.urgent:
        return Colors.red;
    }
  }
}
