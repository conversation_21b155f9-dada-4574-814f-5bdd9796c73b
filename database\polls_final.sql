-- نظام التصويتات "نبض" النهائي والصحيح

-- إنشاء الجداول
CREATE TABLE IF NOT EXISTS polls (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    question TEXT NOT NULL,
    type VARCHAR(20) NOT NULL DEFAULT 'public',
    category VARCHAR(20) NOT NULL DEFAULT 'general',
    duration VARCHAR(20) NOT NULL DEFAULT 'unlimited',
    allow_comments BOOLEAN NOT NULL DEFAULT true,
    allow_revote BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NULL
);

CREATE TABLE IF NOT EXISTS poll_options (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    text TEXT NOT NULL,
    votes INTEGER NOT NULL DEFAULT 0,
    option_order INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS poll_votes (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    option_id UUID NOT NULL REFERENCES poll_options(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(poll_id, user_id)
);

CREATE TABLE IF NOT EXISTS poll_comments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE IF NOT EXISTS poll_shares (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    poll_id UUID NOT NULL REFERENCES polls(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    share_type VARCHAR(20) NOT NULL DEFAULT 'general',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس
CREATE INDEX IF NOT EXISTS idx_polls_user_id ON polls(user_id);
CREATE INDEX IF NOT EXISTS idx_polls_category ON polls(category);
CREATE INDEX IF NOT EXISTS idx_polls_active ON polls(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_polls_created_at ON polls(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_poll_options_poll_id ON poll_options(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_poll_id ON poll_votes(poll_id);
CREATE INDEX IF NOT EXISTS idx_poll_votes_user_id ON poll_votes(user_id);

-- دالة تحديث updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers
DROP TRIGGER IF EXISTS update_polls_updated_at ON polls;
CREATE TRIGGER update_polls_updated_at
    BEFORE UPDATE ON polls
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_poll_votes_updated_at ON poll_votes;
CREATE TRIGGER update_poll_votes_updated_at
    BEFORE UPDATE ON poll_votes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- دالة تحديث عدد الأصوات
CREATE OR REPLACE FUNCTION update_poll_option_votes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE poll_options SET votes = votes + 1 WHERE id = NEW.option_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE poll_options SET votes = votes - 1 WHERE id = OLD.option_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS update_option_votes_trigger ON poll_votes;
CREATE TRIGGER update_option_votes_trigger
    AFTER INSERT OR DELETE ON poll_votes
    FOR EACH ROW
    EXECUTE FUNCTION update_poll_option_votes();

-- تفعيل Row Level Security
ALTER TABLE polls ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_options ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_votes ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE poll_shares ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان
DROP POLICY IF EXISTS "polls_select_policy" ON polls;
CREATE POLICY "polls_select_policy" ON polls
    FOR SELECT USING (is_active = true AND type = 'public');

DROP POLICY IF EXISTS "polls_insert_policy" ON polls;
CREATE POLICY "polls_insert_policy" ON polls
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "poll_options_select_policy" ON poll_options;
CREATE POLICY "poll_options_select_policy" ON poll_options
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM polls WHERE id = poll_options.poll_id AND is_active = true AND type = 'public')
    );

DROP POLICY IF EXISTS "poll_options_insert_policy" ON poll_options;
CREATE POLICY "poll_options_insert_policy" ON poll_options
    FOR INSERT WITH CHECK (
        EXISTS (SELECT 1 FROM polls WHERE id = poll_options.poll_id AND user_id = auth.uid())
    );

DROP POLICY IF EXISTS "poll_votes_select_policy" ON poll_votes;
CREATE POLICY "poll_votes_select_policy" ON poll_votes
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "poll_votes_insert_policy" ON poll_votes;
CREATE POLICY "poll_votes_insert_policy" ON poll_votes
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "poll_comments_select_policy" ON poll_comments;
CREATE POLICY "poll_comments_select_policy" ON poll_comments
    FOR SELECT USING (
        EXISTS (SELECT 1 FROM polls WHERE id = poll_comments.poll_id AND is_active = true AND type = 'public')
        AND is_active = true
    );

DROP POLICY IF EXISTS "poll_comments_insert_policy" ON poll_comments;
CREATE POLICY "poll_comments_insert_policy" ON poll_comments
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- منح الصلاحيات
GRANT ALL ON polls TO authenticated;
GRANT ALL ON poll_options TO authenticated;
GRANT ALL ON poll_votes TO authenticated;
GRANT ALL ON poll_comments TO authenticated;
GRANT ALL ON poll_shares TO authenticated;

-- رسالة نجاح
SELECT 'تم إنشاء نظام التصويتات "نبض" بنجاح!' as message;
