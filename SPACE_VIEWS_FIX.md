# إصلاح مشاهدات المساحات
# Space Views Fix

## المشكلة:
إحصائيات المشاهدات في المساحات لا تزيد إلا للمالك فقط، بينما يجب أن تزيد لكل شخص يزور المساحة.

## السبب:
- كانت المشاهدات تزيد فقط في `getSpaceDetails` وليس في `getSpaceById`
- لم يكن هناك تتبع للمشاهدات الفردية
- لم يكن هناك منع للتكرار

## الحل المطبق:

### ✅ **إنشاء جدول مشاهدات المساحات:**
```sql
CREATE TABLE IF NOT EXISTS space_views (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    space_id UUID NOT NULL REFERENCES spaces(id) ON DELETE CASCADE,
    viewer_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(space_id, viewer_id, DATE(created_at)) -- منع تكرار المشاهدات في نفس اليوم
);
```

### ✅ **إضافة دالة زيادة المشاهدات:**
```dart
Future<void> incrementSpaceViews(String spaceId) async {
  try {
    final currentUserId = _supabase.auth.currentUser?.id;
    if (currentUserId == null) return;

    // التحقق من أن المستخدم لم يزُر المساحة مؤخراً (لتجنب التكرار)
    final lastView = await _supabase
        .from('space_views')
        .select('created_at')
        .eq('space_id', spaceId)
        .eq('viewer_id', currentUserId)
        .order('created_at', ascending: false)
        .limit(1)
        .maybeSingle();

    // إذا لم يزُر المساحة في آخر 5 دقائق، أضف مشاهدة جديدة
    if (lastView == null || 
        DateTime.now().difference(DateTime.parse(lastView['created_at'])).inMinutes > 5) {
      
      // إضافة مشاهدة جديدة
      await _supabase
          .from('space_views')
          .insert({
            'space_id': spaceId,
            'viewer_id': currentUserId,
            'created_at': DateTime.now().toIso8601String(),
          });

      // زيادة عداد المشاهدات في المساحة
      try {
        await _supabase.rpc('increment_space_views', params: {
          'space_id': spaceId,
        });
      } catch (e) {
        // إذا لم تكن الدالة موجودة، استخدم الطريقة البديلة
        final currentViews = await _supabase
            .from('spaces')
            .select('views_count')
            .eq('id', spaceId)
            .single();
        
        await _supabase
            .from('spaces')
            .update({
              'views_count': (currentViews['views_count'] ?? 0) + 1,
              'updated_at': DateTime.now().toIso8601String(),
            })
            .eq('id', spaceId);
      }

      print('👁️ تم زيادة مشاهدات المساحة: $spaceId');
    }
  } catch (e) {
    print('❌ خطأ في زيادة مشاهدات المساحة: $e');
  }
}
```

### ✅ **تحديث جميع دوال جلب المساحات:**
```dart
// في getSpaceDetails
await incrementSpaceViews(spaceId);

// في getSpaceById
await incrementSpaceViews(spaceId);
```

### ✅ **إضافة دالة SQL لزيادة المشاهدات:**
```sql
CREATE OR REPLACE FUNCTION increment_space_views(space_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE spaces 
    SET views_count = COALESCE(views_count, 0) + 1,
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## التحسينات المطبقة:

### ✅ **تتبع دقيق للمشاهدات:**
- تسجيل كل مشاهدة في جدول منفصل
- منع تكرار المشاهدات في نفس اليوم
- منع تكرار المشاهدات في آخر 5 دقائق

### ✅ **زيادة المشاهدات لجميع الزوار:**
- المالك والزوار الآخرين
- تحديث في جميع دوال جلب المساحات

### ✅ **معالجة الأخطاء:**
- استخدام دالة SQL إذا كانت موجودة
- استخدام الطريقة البديلة إذا لم تكن موجودة
- رسائل تتبع للأخطاء

### ✅ **تحسين الأداء:**
- فهارس لتحسين الاستعلامات
- منع التكرار لتقليل الحمل

## النتائج المتوقعة:

### 🎯 **زيادة المشاهدات لجميع الزوار:**
- المالك والزوار الآخرين
- تحديث فوري للعداد

### 🎯 **تتبع دقيق:**
- منع التكرار
- تسجيل كل مشاهدة
- إحصائيات دقيقة

### 🎯 **أداء محسن:**
- استعلامات سريعة
- فهارس محسنة
- معالجة أخطاء

## اختبار الإصلاح:

### 1. **افتح التطبيق**
### 2. **اذهب إلى مساحة شخص آخر**
### 3. **تحقق من زيادة المشاهدات:**
- يجب أن تزيد المشاهدات
- يجب أن تظهر في console: `👁️ تم زيادة مشاهدات المساحة: ...`

### 4. **تحقق من عدم التكرار:**
- زُر نفس المساحة مرة أخرى خلال 5 دقائق
- يجب ألا تزيد المشاهدات مرة أخرى

**الآن مشاهدات المساحات ستعمل لجميع الزوار!** 