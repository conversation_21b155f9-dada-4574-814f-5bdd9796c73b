-- بيانات إضافية متنوعة لنظام التصويتات "نبض"

DO $$
DECLARE
    user1_id UUID := '11111111-1111-1111-1111-111111111111';
    user2_id UUID := '*************-2222-2222-************';
    user3_id UUID := '*************-3333-3333-************';
    user4_id UUID := '*************-4444-4444-************';
    user5_id UUID := '*************-5555-5555-************';
    user6_id UUID := '*************-6666-6666-************';
    user7_id UUID := '*************-7777-7777-************';
    user8_id UUID := '*************-8888-8888-************';
    
    poll_id UUID;
BEGIN
    
    -- تصويتات إضافية متنوعة
    
    -- 1. تصويت تقني - العملات الرقمية
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (user6_id, 'ما هي أفضل عملة رقمية للاستثمار طويل المدى؟', 'public', 'technology', 'oneWeek', true, true, NOW() + INTERVAL '7 days')
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'البيتكوين (Bitcoin)', 0),
    (poll_id, 'الإيثيريوم (Ethereum)', 1),
    (poll_id, 'بينانس كوين (BNB)', 2),
    (poll_id, 'كاردانو (Cardano)', 3),
    (poll_id, 'سولانا (Solana)', 4);
    
    -- إضافة أصوات
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user1_id FROM poll_options WHERE poll_id = poll_id AND option_order = 0;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user2_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user3_id FROM poll_options WHERE poll_id = poll_id AND option_order = 0;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user4_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user5_id FROM poll_options WHERE poll_id = poll_id AND option_order = 2;
    
    -- 2. تصويت مجتمعي - المرأة في العمل
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (user7_id, 'ما هو أهم عامل لتمكين المرأة في سوق العمل؟', 'public', 'community', 'unlimited', true, false)
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'توفير حضانات في أماكن العمل', 0),
    (poll_id, 'المرونة في ساعات العمل', 1),
    (poll_id, 'المساواة في الأجور', 2),
    (poll_id, 'فرص التدريب والتطوير', 3),
    (poll_id, 'بيئة عمل آمنة ومحترمة', 4);
    
    -- إضافة أصوات
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user1_id FROM poll_options WHERE poll_id = poll_id AND option_order = 2;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user2_id FROM poll_options WHERE poll_id = poll_id AND option_order = 4;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user3_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user6_id FROM poll_options WHERE poll_id = poll_id AND option_order = 2;
    
    -- 3. تصويت رياضي - الرياضات الأولمبية
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (user8_id, 'ما هي الرياضة الأولمبية الأكثر إثارة للمشاهدة؟', 'public', 'sports', 'threeDays', true, true, NOW() + INTERVAL '3 days')
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'السباحة', 0),
    (poll_id, 'ألعاب القوى', 1),
    (poll_id, 'الجمباز', 2),
    (poll_id, 'كرة السلة', 3),
    (poll_id, 'التنس', 4),
    (poll_id, 'الملاكمة', 5);
    
    -- إضافة أصوات
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user1_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user2_id FROM poll_options WHERE poll_id = poll_id AND option_order = 0;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user3_id FROM poll_options WHERE poll_id = poll_id AND option_order = 2;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user4_id FROM poll_options WHERE poll_id = poll_id AND option_order = 3;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user5_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user6_id FROM poll_options WHERE poll_id = poll_id AND option_order = 4;
    
    -- 4. تصويت صحي - النوم
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (user1_id, 'كم ساعة نوم تحتاجها يومياً للشعور بالراحة؟', 'public', 'health', 'unlimited', true, true)
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, '6 ساعات أو أقل', 0),
    (poll_id, '7-8 ساعات', 1),
    (poll_id, '9-10 ساعات', 2),
    (poll_id, 'أكثر من 10 ساعات', 3);
    
    -- إضافة أصوات
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user2_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user3_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user4_id FROM poll_options WHERE poll_id = poll_id AND option_order = 2;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user5_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user6_id FROM poll_options WHERE poll_id = poll_id AND option_order = 0;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user7_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    
    -- 5. تصويت تعليمي - التعليم الجامعي
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (user2_id, 'ما هو أهم تخصص جامعي للمستقبل؟', 'public', 'education', 'oneWeek', true, false, NOW() + INTERVAL '7 days')
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'علوم الحاسوب والذكاء الاصطناعي', 0),
    (poll_id, 'الطب والعلوم الصحية', 1),
    (poll_id, 'الهندسة والتكنولوجيا', 2),
    (poll_id, 'إدارة الأعمال والاقتصاد', 3),
    (poll_id, 'العلوم البيئية والطاقة المتجددة', 4),
    (poll_id, 'التصميم والإبداع الرقمي', 5);
    
    -- إضافة أصوات
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user1_id FROM poll_options WHERE poll_id = poll_id AND option_order = 0;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user3_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user4_id FROM poll_options WHERE poll_id = poll_id AND option_order = 0;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user5_id FROM poll_options WHERE poll_id = poll_id AND option_order = 2;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user6_id FROM poll_options WHERE poll_id = poll_id AND option_order = 4;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user7_id FROM poll_options WHERE poll_id = poll_id AND option_order = 0;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user8_id FROM poll_options WHERE poll_id = poll_id AND option_order = 3;
    
    -- 6. تصويت ترفيهي - المسلسلات
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (user3_id, 'ما هو أفضل مسلسل عربي في السنوات الأخيرة؟', 'public', 'entertainment', 'unlimited', true, true)
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'جعفر العمدة', 0),
    (poll_id, 'الاختيار', 1),
    (poll_id, 'نسل الأغراب', 2),
    (poll_id, 'الملك', 3),
    (poll_id, 'رشاش', 4),
    (poll_id, 'مدرسة الروابي للبنات', 5);
    
    -- إضافة أصوات
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user1_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user2_id FROM poll_options WHERE poll_id = poll_id AND option_order = 4;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user4_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user5_id FROM poll_options WHERE poll_id = poll_id AND option_order = 2;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user6_id FROM poll_options WHERE poll_id = poll_id AND option_order = 3;
    
    -- 7. تصويت أعمال - العمل عن بُعد
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote, expires_at)
    VALUES (user4_id, 'ما هو أفضل نموذج عمل بعد جائحة كورونا؟', 'public', 'business', 'oneDay', true, false, NOW() + INTERVAL '1 day')
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'العمل من المكتب بالكامل', 0),
    (poll_id, 'العمل عن بُعد بالكامل', 1),
    (poll_id, 'العمل المختلط (هجين)', 2),
    (poll_id, 'العمل المرن حسب الحاجة', 3);
    
    -- إضافة أصوات
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user1_id FROM poll_options WHERE poll_id = poll_id AND option_order = 2;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user2_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user3_id FROM poll_options WHERE poll_id = poll_id AND option_order = 2;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user5_id FROM poll_options WHERE poll_id = poll_id AND option_order = 3;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user6_id FROM poll_options WHERE poll_id = poll_id AND option_order = 2;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user7_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user8_id FROM poll_options WHERE poll_id = poll_id AND option_order = 2;
    
    -- 8. تصويت ديني - الأعمال الخيرية
    INSERT INTO polls (user_id, question, type, category, duration, allow_comments, allow_revote)
    VALUES (user5_id, 'ما هو أفضل شكل للعمل الخيري؟', 'public', 'religion', 'unlimited', true, false)
    RETURNING id INTO poll_id;
    
    INSERT INTO poll_options (poll_id, text, option_order) VALUES
    (poll_id, 'التبرع المالي المباشر', 0),
    (poll_id, 'التطوع بالوقت والجهد', 1),
    (poll_id, 'تقديم الخدمات المهنية مجاناً', 2),
    (poll_id, 'نشر الوعي والتثقيف', 3),
    (poll_id, 'كفالة الأيتام والمحتاجين', 4);
    
    -- إضافة أصوات
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user1_id FROM poll_options WHERE poll_id = poll_id AND option_order = 4;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user2_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user3_id FROM poll_options WHERE poll_id = poll_id AND option_order = 0;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user4_id FROM poll_options WHERE poll_id = poll_id AND option_order = 4;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user6_id FROM poll_options WHERE poll_id = poll_id AND option_order = 2;
    INSERT INTO poll_votes (poll_id, option_id, user_id) 
    SELECT poll_id, id, user7_id FROM poll_options WHERE poll_id = poll_id AND option_order = 1;
    
    -- إضافة تعليقات إضافية
    INSERT INTO poll_comments (poll_id, user_id, content) VALUES
    (poll_id, user8_id, 'العمل الخيري يجب أن يكون من القلب وبصدق'),
    (poll_id, user1_id, 'كفالة الأيتام لها أجر عظيم في الإسلام');

END $$;

-- رسالة نجاح
SELECT 'تم إضافة بيانات إضافية متنوعة لنظام التصويتات!' as message;
