-- =============================================================
--  إصلاح سياسات التخزين لصور المجتمعات
--  Fix Storage Policies for Community Images
-- =============================================================

-- 1) التأكد من وجود bucket للصور
-- -------------------------------------------------------

INSERT INTO storage.buckets (id, name, public)
VALUES ('community-images', 'community-images', true)
ON CONFLICT (id) DO NOTHING;

-- 2) حذف جميع السياسات القديمة
-- -------------------------------------------------------

DROP POLICY IF EXISTS "Public can view community images" ON storage.objects;
DROP POLICY IF EXISTS "Community owners can upload images" ON storage.objects;
DROP POLICY IF EXISTS "Community owners can update images" ON storage.objects;
DROP POLICY IF EXISTS "Community owners can delete images" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can upload community images" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can update community images" ON storage.objects;
DROP POLICY IF EXISTS "Anyone can delete community images" ON storage.objects;

-- 3) إنشاء سياسات مبسطة وآمنة
-- -------------------------------------------------------

-- السماح للجميع بقراءة الصور (لأن bucket عام)
CREATE POLICY "Public can view community images"
ON storage.objects FOR SELECT
USING (bucket_id = 'community-images');

-- السماح للمستخدمين المسجلين برفع الصور
CREATE POLICY "Authenticated users can upload community images"
ON storage.objects FOR INSERT
WITH CHECK (
  bucket_id = 'community-images' 
  AND auth.uid() IS NOT NULL
);

-- السماح للمستخدمين المسجلين بتحديث الصور
CREATE POLICY "Authenticated users can update community images"
ON storage.objects FOR UPDATE
USING (
  bucket_id = 'community-images' 
  AND auth.uid() IS NOT NULL
);

-- السماح للمستخدمين المسجلين بحذف الصور
CREATE POLICY "Authenticated users can delete community images"
ON storage.objects FOR DELETE
USING (
  bucket_id = 'community-images' 
  AND auth.uid() IS NOT NULL
);

-- 4) التأكد من تفعيل RLS
-- -------------------------------------------------------

ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- =============================================================
--  انتهى إصلاح سياسات التخزين
-- =============================================================

-- للتحقق من السياسات:
-- SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
-- FROM pg_policies 
-- WHERE tablename = 'objects' AND schemaname = 'storage';
