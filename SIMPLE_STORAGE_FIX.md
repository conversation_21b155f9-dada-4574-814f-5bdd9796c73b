# حل مشكلة رفع الصور - الطريقة المبسطة

## 🚨 المشكلة
```
StorageException(message: new row violates row-level security policy, statusCode: 403, error: Unauthorized)
```

## ✅ الحل السريع (5 دقائق)

### الخطوة 1: إنشاء Storage Bucket
1. اذهب إلى لوحة Supabase
2. اضغط على **Storage** من القائمة الجانبية
3. اضغط **Create bucket**
4. املأ البيانات:
   - **Name**: `community-images`
   - **Public bucket**: ✅ **فعّل هذا الخيار**
   - **File size limit**: اتركه فارغ أو ضع 50MB
5. اضغط **Save**

### الخطوة 2: إعداد سياسات بسيطة
1. في نفس صفحة Storage، اضغط على bucket `community-images`
2. اضغط على تبويب **Policies**
3. اضغط **New policy**
4. اختر **Custom policy**
5. املأ البيانات:
   - **Policy name**: `Allow all operations`
   - **Allowed operation**: اختر **All**
   - **Target roles**: `authenticated`
6. في **Policy definition**، ضع:
   ```sql
   true
   ```
7. اضغط **Save**

### الخطوة 3: التحقق من الإعدادات
1. تأكد من أن bucket `community-images` ظاهر في قائمة Storage
2. تأكد من أن الـ badge بجانبه يقول **Public**
3. تأكد من وجود policy واحد على الأقل

## 🔄 الحل البديل: إنشاء Bucket يدوياً

إذا لم تنجح الطريقة الأولى:

### في SQL Editor:
```sql
-- إنشاء bucket عام
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'community-images', 
  'community-images', 
  true, 
  52428800, -- 50MB
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 52428800;
```

## 🧪 اختبار الحل

1. أعد بناء التطبيق:
   ```bash
   flutter build apk --release
   ```

2. افتح التطبيق واذهب لإعدادات مجتمع

3. جرب رفع صورة شخصية أو غلاف

4. يجب أن تظهر رسالة "تم رفع الصورة بنجاح"

## 🔍 إذا استمرت المشكلة

### تحقق من هذه النقاط:
- [ ] هل bucket `community-images` موجود؟
- [ ] هل الـ bucket عام (Public)؟
- [ ] هل أنت مسجل دخول في التطبيق؟
- [ ] هل أنت مالك المجتمع؟
- [ ] هل حجم الصورة معقول (أقل من 10MB)؟

### رسائل خطأ شائعة:
- **"Bucket غير موجود"**: أنشئ bucket جديد
- **"خطأ في سياسات الأمان"**: تأكد من أن الـ bucket عام
- **"غير مصرح"**: تحقق من تسجيل الدخول
- **"فقط مالك المجتمع"**: تأكد من ملكية المجتمع

## 📞 إذا لم ينجح شيء

أرسل لي:
1. لقطة شاشة من صفحة Storage في Supabase
2. رسالة الخطأ الكاملة من التطبيق
3. هل تم إنشاء bucket `community-images`؟
