# دليل إصلاح مشكلة رفع الصور - الطريقة اليدوية

## 🚨 المشكلة
```
ERROR: 42501: must be owner of table objects
```

هذا يعني أنك لا تملك صلاحيات إدارية لتعديل سياسات Storage عبر SQL.

## ✅ الحل: استخدام واجهة Supabase

### الخطوة 1: تشغيل السكريبت المبسط
1. افتح **SQL Editor** في Supabase
2. انسخ والصق محتوى `supabase/simple_storage_fix.sql`
3. اضغط **Run**
4. يجب أن ترى رسالة "SUCCESS: Bucket created/updated"

### الخطوة 2: إنشاء Storage Bucket (إذا لم ينجح السكريبت)
1. اذهب إلى **Storage** من القائمة الجانبية
2. اضغط **Create bucket**
3. املأ البيانات:
   ```
   Name: community-images
   Public bucket: ✅ (مهم جداً!)
   File size limit: 50MB
   Allowed MIME types: image/jpeg, image/png, image/webp
   ```
4. اضغط **Save**

### الخطوة 3: إنشاء سياسة Storage
1. في صفحة **Storage**، اضغط على bucket `community-images`
2. اذهب إلى تبويب **Policies**
3. اضغط **New Policy**
4. اختر **Custom policy**
5. املأ البيانات:
   ```
   Policy name: community_images_all_access
   Allowed operation: All
   Target roles: authenticated
   Policy definition: true
   ```
6. اضغط **Save**

### الخطوة 4: التحقق من الإعدادات
1. تأكد من أن bucket `community-images` ظاهر في قائمة Storage
2. تأكد من وجود badge **"Public"** بجانب اسم الـ bucket
3. تأكد من وجود policy واحد على الأقل في تبويب Policies

## 🔄 الحل البديل: تعطيل RLS (غير آمن!)

إذا لم تنجح الطريقة أعلاه:

### تعطيل Row Level Security مؤقتاً:
1. اذهب إلى **Database** > **Tables**
2. ابحث عن `storage.objects`
3. اضغط على الجدول
4. اذهب إلى **Settings**
5. عطل **"Enable RLS"**
6. احفظ التغييرات

⚠️ **تحذير**: هذا يجعل جميع الملفات قابلة للوصول من الجميع!

## 🧪 اختبار الحل

### في SQL Editor، شغل هذا الاستعلام:
```sql
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'community-images' AND public = true)
    THEN '✅ Bucket exists and is public'
    ELSE '❌ Bucket missing or not public'
  END as bucket_status;
```

### يجب أن ترى: `✅ Bucket exists and is public`

## 📱 اختبار في التطبيق

1. أعد بناء التطبيق:
   ```bash
   flutter clean
   flutter pub get
   flutter build apk --release
   ```

2. افتح التطبيق واذهب لإعدادات مجتمع

3. اضغط على تبويب **"الصور"**

4. جرب رفع صورة شخصية أو غلاف

5. **النتيجة المتوقعة**: رسالة "تم رفع الصورة بنجاح" 🟢

## 🔍 استكشاف الأخطاء

### إذا استمرت المشكلة:

#### تحقق من هذه النقاط:
- [ ] هل bucket `community-images` موجود؟
- [ ] هل الـ bucket مُعلم كـ **Public**؟
- [ ] هل توجد policy واحدة على الأقل؟
- [ ] هل أنت مسجل دخول في التطبيق؟
- [ ] هل أنت مالك المجتمع؟

#### رسائل خطأ شائعة وحلولها:
- **"Bucket غير موجود"** → أنشئ bucket جديد
- **"خطأ في سياسات الأمان"** → أنشئ policy جديدة أو عطل RLS
- **"غير مصرح"** → تحقق من تسجيل الدخول
- **"فقط مالك المجتمع"** → تأكد من ملكية المجتمع

## 📞 إذا لم ينجح أي شيء

أرسل لي:
1. **لقطة شاشة** من صفحة Storage في Supabase
2. **لقطة شاشة** من تبويب Policies في bucket
3. **رسالة الخطأ الكاملة** من التطبيق
4. **نتيجة** استعلام التحقق أعلاه

## ✨ نصائح إضافية

### لتجنب المشاكل مستقبلاً:
1. **احتفظ بـ bucket عام دائماً**
2. **لا تغير سياسات Storage يدوياً**
3. **استخدم أسماء ملفات فريدة** (timestamp)
4. **تحقق من حجم الصور** قبل الرفع

### لتحسين الأداء:
1. **ضغط الصور** قبل الرفع
2. **استخدام WebP** بدلاً من JPEG
3. **حذف الصور القديمة** دورياً
4. **تحديد حد أقصى لحجم الملف**

---

**الخلاصة**: استخدم واجهة Supabase بدلاً من SQL لإنشاء bucket وسياسات Storage. هذا أسهل وأكثر أماناً! 🚀
