import 'package:flutter/material.dart';
import '../models/marriage_profile.dart';
import '../services/marriage_service.dart';
import '../widgets/interactive_verified_badge.dart';

class MarriageProfileCard extends StatelessWidget {
  final MarriageProfile profile;
  final VoidCallback? onTap;
  final bool showContactButton;

  const MarriageProfileCard({
    super.key,
    required this.profile,
    this.onTap,
    this.showContactButton = true,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                profile.gender == Gender.male 
                    ? Colors.blue[50]! 
                    : Colors.pink[50]!,
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  // صورة الملف الشخصي أو أيقونة افتراضية
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: profile.gender == Gender.male
                            ? [Colors.blue[400]!, Colors.blue[600]!]
                            : [Colors.pink[400]!, Colors.pink[600]!],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: (profile.gender == Gender.male 
                              ? Colors.blue 
                              : Colors.pink).withValues(alpha: 0.3),
                          spreadRadius: 2,
                          blurRadius: 8,
                        ),
                      ],
                    ),
                    child: profile.getDisplayImage(false) != null
                        ? ClipOval(
                            child: Image.network(
                              profile.getDisplayImage(false)!,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) =>
                                  _buildDefaultAvatar(),
                            ),
                          )
                        : _buildDefaultAvatar(),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  // معلومات أساسية
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // الاسم والعمر
                        Row(
                          children: [
                            Expanded(
                              child: Row(
                                children: [
                                  Text(
                                    profile.getDisplayName(false),
                                    style: const TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.black87,
                                    ),
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  // شارة التحقق
                                  if (profile.isVerified) ...[
                                    const SizedBox(width: 4),
                                    InteractiveVerifiedBadge(
                                      size: 16,
                                      userName: profile.getDisplayName(false),
                                    ),
                                  ],
                                ],
                              ),
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: profile.gender == Gender.male
                                    ? Colors.blue[100]
                                    : Colors.pink[100],
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '${profile.age} سنة',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: profile.gender == Gender.male
                                      ? Colors.blue[700]
                                      : Colors.pink[700],
                                ),
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 8),
                        
                        // المدينة والمهنة
                        Row(
                          children: [
                            Icon(
                              Icons.location_on,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                '${profile.city}, ${profile.country}',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        
                        const SizedBox(height: 4),
                        
                        Row(
                          children: [
                            Icon(
                              Icons.work,
                              size: 16,
                              color: Colors.grey[600],
                            ),
                            const SizedBox(width: 4),
                            Expanded(
                              child: Text(
                                profile.profession,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[600],
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // معلومات إضافية
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: [
                  _buildInfoChip(
                    icon: profile.gender == Gender.male 
                        ? Icons.male 
                        : Icons.female,
                    label: profile.genderText,
                    color: profile.gender == Gender.male 
                        ? Colors.blue 
                        : Colors.pink,
                  ),
                  _buildInfoChip(
                    icon: Icons.favorite,
                    label: profile.maritalStatusText,
                    color: Colors.purple,
                  ),
                  _buildInfoChip(
                    icon: Icons.flag,
                    label: profile.goalText,
                    color: Colors.green,
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // وصف مختصر
              Text(
                profile.description,
                style: TextStyle(
                  color: Colors.grey[700],
                  fontSize: 14,
                  height: 1.4,
                ),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 16),
              
              // أزرار الإجراءات
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: onTap,
                      icon: const Icon(Icons.visibility, size: 18),
                      label: const Text('عرض التفاصيل'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: profile.gender == Gender.male
                            ? Colors.blue[700]
                            : Colors.pink[700],
                        side: BorderSide(
                          color: profile.gender == Gender.male
                              ? Colors.blue[300]!
                              : Colors.pink[300]!,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  ),
                  
                  if (showContactButton) ...[
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _sendContactRequest(context),
                        icon: const Icon(Icons.message, size: 18),
                        label: const Text('طلب تواصل'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: profile.gender == Gender.male
                              ? Colors.blue[600]
                              : Colors.pink[600],
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultAvatar() {
    return Icon(
      profile.gender == Gender.male ? Icons.man : Icons.woman,
      size: 30,
      color: Colors.white,
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _sendContactRequest(BuildContext context) async {
    // التحقق من إمكانية إرسال طلب جديد
    final canSend = await MarriageService().canSendRequest();
    if (!canSend) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('لقد تجاوزت الحد اليومي للطلبات (3 طلبات). حاول غداً'),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    // إظهار حوار التأكيد مع إمكانية إضافة رسالة
    final TextEditingController messageController = TextEditingController();

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('طلب تواصل'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('هل تريد إرسال طلب تواصل إلى ${profile.getDisplayName(false)}؟'),
            const SizedBox(height: 16),
            TextField(
              controller: messageController,
              decoration: const InputDecoration(
                labelText: 'رسالة اختيارية',
                hintText: 'اكتب رسالة قصيرة...',
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              maxLength: 200,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: profile.gender == Gender.male ? Colors.blue : Colors.pink,
            ),
            child: const Text('إرسال', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      await MarriageService().sendContactRequest(
        receiverId: profile.userId,
        message: messageController.text.trim().isNotEmpty
            ? messageController.text.trim()
            : null,
      );

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إرسال طلب التواصل بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(e.toString().replaceAll('Exception: ', '')),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
