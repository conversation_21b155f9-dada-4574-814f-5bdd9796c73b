import 'package:flutter/material.dart';
import 'reaction_type.dart';

enum CommentType { text, image, video }

class Comment {
  final String id;
  final String postId;
  final String? parentId;
  final String userId;
  final String userName;
  final String userAvatar;
  final String content;
  final bool isVerified; // حالة التوثيق
  final DateTime createdAt;
  final CommentType type;
  final String? mediaUrl;
  List<Comment> replies;
  final Map<ReactionType, int> reactionCounts; // عدادات التفاعلات
  final ReactionType currentUserReaction;
  final int depth; // عمق التعليق في التسلسل الهرمي
  final String? replyToUserName; // اسم المستخدم المُرد عليه

  Comment({
    required this.id,
    required this.postId,
    required this.userId,
    required this.userName,
    required this.userAvatar,
    required this.content,
    required this.createdAt,
    required this.type,
    this.parentId,
    this.mediaUrl,
    this.replies = const [],
    this.reactionCounts = const {},
    this.currentUserReaction = ReactionType.none,
    this.depth = 0,
    this.replyToUserName,
    this.isVerified = false, // افتراضياً غير موثق
  });

  // للتوافق مع الكود القديم
  int get likesCount => reactionCounts[ReactionType.like] ?? 0;

  // إنشاء نسخة محدثة من التعليق
  Comment copyWith({
    String? content,
    List<Comment>? replies,
    Map<ReactionType, int>? reactionCounts,
    ReactionType? currentUserReaction,
    int? depth,
    bool? isVerified,
  }) {
    return Comment(
      id: id,
      postId: postId,
      parentId: parentId,
      userId: userId,
      userName: userName,
      userAvatar: userAvatar,
      content: content ?? this.content,
      createdAt: createdAt,
      type: type,
      mediaUrl: mediaUrl,
      replies: replies ?? this.replies,
      reactionCounts: reactionCounts ?? this.reactionCounts,
      currentUserReaction: currentUserReaction ?? this.currentUserReaction,
      depth: depth ?? this.depth,
      replyToUserName: replyToUserName,
      isVerified: isVerified ?? this.isVerified,
    );
  }

  // الحصول على جميع الردود بشكل مسطح (للعد)
  int get totalRepliesCount {
    int count = replies.length;
    for (final reply in replies) {
      count += reply.totalRepliesCount;
    }
    return count;
  }

  // البحث عن تعليق بالمعرف في التسلسل الهرمي
  Comment? findCommentById(String commentId) {
    if (id == commentId) return this;

    for (final reply in replies) {
      final found = reply.findCommentById(commentId);
      if (found != null) return found;
    }

    return null;
  }
}