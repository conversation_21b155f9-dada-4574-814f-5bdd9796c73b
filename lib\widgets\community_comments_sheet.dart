import 'package:flutter/material.dart';
import '../models/community_post.dart';
import '../models/community_comment.dart';
import '../widgets/community_comment_card.dart';
import '../supabase_service.dart';

class CommunityCommentsSheet extends StatefulWidget {
  final CommunityPost? post; // اختياري للتوافق مع النسخة القديمة
  final String? postId; // معرف المنشور
  final VoidCallback? onCommentAdded; // callback عند إضافة تعليق

  const CommunityCommentsSheet({
    super.key,
    this.post,
    this.postId,
    this.onCommentAdded,
  }) : assert(post != null || postId != null, 'Either post or postId must be provided');

  @override
  State<CommunityCommentsSheet> createState() => _CommunityCommentsSheetState();
}

class _CommunityCommentsSheetState extends State<CommunityCommentsSheet> {
  final _controller = TextEditingController();
  late Future<List<CommunityComment>> _future;
  bool _sending = false;

  String get _postId => widget.postId ?? widget.post!.id;

  @override
  void initState() {
    super.initState();
    _future = SupabaseService().fetchCommunityComments(_postId);
  }

  Future<void> _refresh() async {
    setState(() => _future = SupabaseService().fetchCommunityComments(_postId));
  }

  Future<void> _send() async {
    if (_sending) return;
    final txt = _controller.text.trim();
    if (txt.isEmpty) return;
    setState(() => _sending = true);
    try {
      await SupabaseService().createCommunityComment(postId: _postId, content: txt);
      _controller.clear();
      await _refresh();
      widget.onCommentAdded?.call(); // استدعاء callback
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في إرسال التعليق: $e')),
        );
      }
    }
    setState(() => _sending = false);
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: MediaQuery.of(context).viewInsets.add(const EdgeInsets.only(top:16,left:16,right:16,bottom:8)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(height: 4,width:40,margin:const EdgeInsets.only(bottom:8),decoration: BoxDecoration(color: Colors.grey[400],borderRadius: BorderRadius.circular(2))),
          Expanded(
            child: FutureBuilder<List<CommunityComment>>(
              future: _future,
              builder: (context, snap) {
                if (snap.connectionState == ConnectionState.waiting) return const Center(child: CircularProgressIndicator());
                final list = snap.data ?? [];
                if (list.isEmpty) return const Center(child: Text('لا توجد تعليقات'));
                return RefreshIndicator(
                  onRefresh: _refresh,
                  child: ListView.builder(
                    itemCount: list.length,
                    itemBuilder: (c,i)=> CommunityCommentCard(comment: list[i], onChanged: _refresh),
                  ),
                );
              },
            ),
          ),
          Row(
            children: [
              Expanded(child: TextField(controller: _controller, decoration: const InputDecoration(hintText: 'اكتب تعليق...'))),
              IconButton(icon: _sending? const CircularProgressIndicator(): const Icon(Icons.send), onPressed: _send),
            ],
          )
        ],
      ),
    );
  }
} 