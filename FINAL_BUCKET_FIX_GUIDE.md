# دليل إصلاح مشكلة Buckets النهائي
# Final Bucket Fix Guide

## المشكلة
بعد التعديلات الأخيرة، توقفت وظيفة رفع الوسائط عن العمل. المشكلة كانت أن الكود يبحث عن bucket `media` محدد، بينما قد يكون هناك buckets أخرى متاحة.

## الحل

### الخطوة 1: تنفيذ SQL الشامل
1. اذهب إلى Supabase Dashboard
2. افتح SQL Editor
3. انسخ والصق محتوى ملف `CREATE_ALL_BUCKETS.sql`
4. اضغط Run

### الخطوة 2: التحقق من النتائج
بعد تنفيذ SQL، يجب أن ترى:

#### في الجزء الأول (buckets المنشأة):
```
id            | name          | public | file_size_limit | mime_types_count
--------------|---------------|--------|-----------------|------------------
avatars       | avatars       | true   | 10485760       | 4
files         | files         | true   | 104857600      | 11
images        | images        | true   | 52428800       | 5
media         | media         | true   | 52428800       | 8
space-images  | space-images  | true   | 52428800       | 8
```

#### في الجزء الأخير:
```
result
----------------------------------------
تم إنشاء جميع buckets والسياسات بنجاح!
```

### الخطوة 3: اختبار التطبيق
1. افتح التطبيق الجديد
2. جرب إنشاء منشور مع صور متعددة
3. يجب أن تعمل الآن

## ما تم إصلاحه

### 1. تحديث دالة uploadMedia
- الآن تبحث عن أي bucket متاح بدلاً من bucket محدد
- تدعم `media`, `images`, `files`, أو أي bucket آخر
- رسائل debug مفصلة لمعرفة أي bucket يتم استخدامه

### 2. إنشاء جميع Buckets المطلوبة
- **media**: للمنشورات العامة
- **images**: للصور فقط
- **files**: للملفات العامة
- **avatars**: للصور الشخصية
- **space-images**: لمساحات المحادثة

### 3. إعداد سياسات الأمان
- جميع buckets لها سياسات SELECT, INSERT, UPDATE, DELETE
- المستخدمون المسجلون فقط يمكنهم رفع الملفات
- أي شخص يمكنه عرض الملفات

## رسائل DEBUG المتوقعة

عندما تعمل بشكل صحيح، يجب أن ترى:
```
DEBUG: uploadMedia called with path: post_1234567890_0.jpg, size: 123456 bytes
DEBUG: Checking available buckets...
DEBUG: Available buckets: [media, images, files, avatars, space-images]
DEBUG: Using bucket: media
DEBUG: Starting file upload to media bucket...
DEBUG: File upload completed successfully
DEBUG: File uploaded successfully. URL: https://...
```

## إذا استمرت المشكلة

### 1. تحقق من رسائل DEBUG
انظر إلى رسائل DEBUG لمعرفة:
- أي buckets متاحة
- أي bucket يتم استخدامه
- أي خطأ يحدث

### 2. تحقق من Supabase Dashboard
- اذهب إلى Storage
- تأكد من وجود buckets
- اذهب إلى Authentication > Policies
- تأكد من وجود السياسات

### 3. إذا لم توجد أي buckets
نفذ هذا SQL بسيط:
```sql
-- إنشاء bucket واحد على الأقل
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES ('media', 'media', true, 52428800, ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/mp4'])
ON CONFLICT (id) DO NOTHING;
```

## التحقق النهائي

بعد تنفيذ جميع الخطوات، جرب:

1. **إنشاء منشور نصي** - يجب أن يعمل
2. **إنشاء منشور بصورة واحدة** - يجب أن يعمل
3. **إنشاء منشور بصور متعددة** - يجب أن يعمل

إذا عملت جميع هذه الاختبارات، فالمشكلة محلولة!

## الدعم

إذا استمرت المشكلة:
1. انسخ رسائل DEBUG من التطبيق
2. انسخ نتائج SQL من Supabase
3. أرسل جميع المعلومات للمساعدة في التشخيص

**الآن نفذ `CREATE_ALL_BUCKETS.sql` واختبر التطبيق! 🚀** 