import 'package:flutter/material.dart';
import '../models/post.dart';
import '../supabase_service.dart';
import '../widgets/post_card.dart';

class ProfilePage extends StatefulWidget {
  final String userId;
  final String username;

  const ProfilePage({
    super.key,
    required this.userId,
    required this.username,
  });

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  Map<String, dynamic>? _profileInfo;
  List<Post> _posts = [];
  bool _isMe = false;
  bool _loading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadProfile();
  }

  Future<void> _loadProfile() async {
    try {
      setState(() {
        _loading = true;
        _error = null;
      });

      // تحميل معلومات الملف الشخصي
      final profile = await SupabaseService().fetchProfile(widget.userId);
      
      // تحميل منشورات المستخدم
      final posts = await SupabaseService().fetchPostsPaginated(limit: 20, offset: 0);
      
      // التحقق من أن هذا هو المستخدم الحالي
      final currentUserId = SupabaseService().getCurrentUserId();
      
      setState(() {
        _profileInfo = profile;
        _posts = posts;
        _isMe = currentUserId == widget.userId;
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _loading = false;
      });
    }
  }

  String _initials() {
    final name = _profileInfo?['name'] ?? widget.username;
    if (name.isEmpty) return '؟';
    final words = name.split(' ');
    if (words.length >= 2) {
      return '${words[0][0]}${words[1][0]}';
    }
    return name[0];
  }

  void _showEditDialog() {
    // TODO: إضافة حوار تعديل الملف الشخصي
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة التعديل قيد التطوير')),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return Scaffold(
        appBar: AppBar(title: Text(widget.username)),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: AppBar(title: Text(widget.username)),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(_error!),
              ElevatedButton(
                onPressed: _loadProfile,
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(_profileInfo?['name'] ?? widget.username),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: _isMe
            ? [
                IconButton(
                  icon: const Icon(Icons.edit),
                  onPressed: _showEditDialog,
                ),
              ]
            : null,
      ),
      body: RefreshIndicator(
        onRefresh: _loadProfile,
        child: ListView(
          children: [
            // معلومات الملف الشخصي
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: Colors.grey.shade300,
                    backgroundImage: (_profileInfo?['avatar_url'] ?? '').toString().isNotEmpty
                        ? NetworkImage(_profileInfo!['avatar_url'])
                        : null,
                    child: (_profileInfo?['avatar_url'] ?? '').toString().isEmpty
                        ? Text(_initials(), style: const TextStyle(fontSize: 20, color: Colors.black))
                        : null,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _profileInfo?['name'] ?? widget.username,
                          style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                        ),
                        if (_profileInfo?['bio'] != null && _profileInfo!['bio'].toString().isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top: 8),
                            child: Text(
                              _profileInfo!['bio'],
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                          ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            Text(
                              '${_posts.length} منشور',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(width: 16),
                            Text(
                              '${_profileInfo?['followers_count'] ?? 0} متابع',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                            const SizedBox(width: 16),
                            Text(
                              '${_profileInfo?['following_count'] ?? 0} يتابع',
                              style: const TextStyle(fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            const Divider(),
            
            // قائمة المنشورات
            if (_posts.isEmpty)
              const Padding(
                padding: EdgeInsets.all(32),
                child: Center(
                  child: Column(
                    children: [
                      Icon(Icons.post_add, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text(
                        'لا توجد منشورات',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              )
            else
              ...(_posts.map((post) => PostCard(post: post, onRefresh: _loadProfile)).toList()),
          ],
        ),
      ),
    );
  }
}
