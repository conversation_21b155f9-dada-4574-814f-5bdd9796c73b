import 'package:flutter/material.dart';
import 'package:readmore/readmore.dart';
import '../widgets/verified_badge.dart';
import '../widgets/interactive_verified_badge.dart';
import '../models/post.dart';
import '../supabase_service.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'settings_page.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../widgets/post_card.dart';
import 'follow_list_page.dart';
import 'avatar_post_page.dart';
import '../utils/number_format.dart';
import '../widgets/user_level_badge.dart';
import 'verify_account_screen.dart';


class ProfilePage extends StatefulWidget {
  final String userId;
  final String username;

  const ProfilePage({
    super.key,
    required this.userId,
    required this.username,
  });

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  Map<String, dynamic>? _profileInfo;
  List<Post> _posts = [];
  bool _isMe = false;
  bool _isFollowing = false;
  bool _loading = true;
  String? _error;
  final ImagePicker _picker = ImagePicker();
  bool _isBlocked = false;

  @override
  void initState() {
    super.initState();
    _loadProfile();
  }

  Future<void> _loadProfile() async {
    try {
      final service = SupabaseService();
      final data = await service.fetchProfile(widget.userId);
      final posts = await service.fetchPostsByUser(widget.userId);
      final following = await service.isFollowing(widget.userId);
      final current = Supabase.instance.client.auth.currentUser?.id;
      // تحقّق إذا كان أحد الطرفين قام بالحظر
      final blockedIds = await service.blockedUserIds();
      final isBlocked = blockedIds.contains(widget.userId);

      if (!mounted) return;
      setState(() {
        _profileInfo = data ?? {};
        _posts = posts;
        _isMe = current == widget.userId;
        _isFollowing = following;
        _isBlocked = isBlocked;
        _loading = false;
      });
    } catch (e, st) {
      debugPrint('PROFILE LOAD ERROR → $e');
      debugPrintStack(stackTrace: st);
      if (mounted) {
        setState(() {
          _error = 'فشل تحميل الملف الشخصي:\n$e';
          _loading = false;
        });
      }
    }
  }

  Future<void> _showEditDialog() async {
    final nameController = TextEditingController(text: _profileInfo?['name'] ?? '');
    final usernameController = TextEditingController(text: _profileInfo?['username'] ?? '');
    final bioController = TextEditingController(text: _profileInfo?['bio'] ?? '');
    String? avatarPath;
    final websiteController = TextEditingController(text: _profileInfo?['website'] ?? '');
    final countryController = TextEditingController(text: _profileInfo?['country'] ?? '');
    final cityController = TextEditingController(text: _profileInfo?['city'] ?? '');
    String? gender = _profileInfo?['gender'] as String?;
    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (ctx) => Padding(
        padding: EdgeInsets.only(bottom: MediaQuery.of(ctx).viewInsets.bottom, left:16,right:16,top:16),
        child: Column(mainAxisSize: MainAxisSize.min, children: [
          GestureDetector(
            onTap: () async {
              final file = await _picker.pickImage(source: ImageSource.gallery);
              if (file != null) setState(() => avatarPath = file.path);
            },
            child: Stack(
              alignment: Alignment.center,
              children: [
                CircleAvatar(
                  radius: 40,
                  backgroundImage: avatarPath != null
                      ? FileImage(File(avatarPath!))
                      : NetworkImage(_profileInfo?['avatar_url'] ?? 'https://via.placeholder.com/150') as ImageProvider,
                ),
                const Positioned(
                  bottom: 0,
                  right: 0,
                  child: CircleAvatar(
                    radius: 12,
                    backgroundColor: Colors.black54,
                    child: Icon(Icons.camera_alt, size: 16, color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
          TextField(controller: nameController, decoration: const InputDecoration(labelText:'الاسم')),
          TextField(controller: usernameController, decoration: const InputDecoration(labelText:'اسم المستخدم (فريد)')),
          TextField(
            controller: bioController,
            maxLength: 108,
            decoration: const InputDecoration(labelText:'السيرة الذاتية', counterText: ''),
          ),
          TextField(controller: websiteController, decoration: const InputDecoration(labelText:'الموقع')),
          DropdownButtonFormField<String>(
            decoration: const InputDecoration(labelText:'الجنس'),
            value: gender,
            items: const [
              DropdownMenuItem(value:'male',child: Text('ذكر')),
              DropdownMenuItem(value:'female',child: Text('أنثى')),
            ],
            onChanged:(v){gender=v;},
          ),
          TextField(controller: countryController, decoration: const InputDecoration(labelText:'البلد')),
          TextField(controller: cityController, decoration: const InputDecoration(labelText:'المدينة')),
          const SizedBox(height:12),
          Row(
            children:[
              ElevatedButton(
            onPressed: () async {
              String? uploaded;
              if (avatarPath!=null){
                final bytes=await File(avatarPath!).readAsBytes();
                final ext=avatarPath!.split('.').last;
                final url=await SupabaseService().uploadMedia(bytes,'avatars/${widget.userId}.$ext');
                uploaded=url;
              }
              if(bioController.text.trim().length>108){
                if(mounted){
                  ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('السيرة الذاتية يجب ألا تتجاوز 108 حرف'),backgroundColor: Colors.red));
                }
                return;
              }
              try {
                await SupabaseService().updateProfile(
                  name: nameController.text.trim(),
                  bio: bioController.text.trim(),
                  avatarUrl: uploaded,
                  username: usernameController.text.trim(),
                  website: websiteController.text.trim(),
                  gender: gender,
                  country: countryController.text.trim(),
                  city: cityController.text.trim(),
                );
                if(mounted) ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم تحديث الملف الشخصي')));
                Navigator.pop(ctx);
                _loadProfile();
              } catch (e) {
                if(mounted) ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('فشل التحديث: $e')));
              }
            }, child: const Text('حفظ')),
            const SizedBox(width:8),
            if((_profileInfo?['avatar_url'] ?? '').toString().isNotEmpty)
              OutlinedButton.icon(
                icon: const Icon(Icons.delete),
                label: const Text('حذف'),
                onPressed: () async {
                  await SupabaseService().updateProfile(avatarUrl: '');
                  Navigator.pop(ctx);
                  _loadProfile();
                },
              ),
            ]),
        ]),
      ),
    );
  }

  String _initials() {
    final n = (_profileInfo?['name'] ?? widget.username).toString().trim();
    if (n.isEmpty) return '';
    final parts = n.split(' ');
    if (parts.length == 1) return parts[0].substring(0, 1).toUpperCase();
    return (parts[0].substring(0, 1) + parts[1].substring(0, 1)).toUpperCase();
  }

  void _showVerificationInfo() {
    Navigator.push(context, MaterialPageRoute(builder: (_) => const VerifyAccountScreen()));
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }

    if (_isBlocked && !_isMe) {
      return Scaffold(
        appBar: AppBar(title: const Text('ملف مستخدم')),
        body: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(Icons.block, size: 64, color: Colors.grey),
              const SizedBox(height: 16),
              const Text('هذا المستخدم محظور', style: TextStyle(fontSize: 18)),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () async {
                  try {
                    await SupabaseService().unblockUser(widget.userId);
                    await _loadProfile();
                  } catch (e) {
                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('فشل إلغاء الحظر: $e')),
                      );
                    }
                  }
                },
                child: const Text('إلغاء الحظر'),
              ),
            ],
          ),
        ),
      );
    }

    final images = _posts.where((p)=>p.type==PostType.image).toList();
    final videos = _posts.where((p)=>p.type==PostType.video).toList();
    final audios = _posts.where((p)=>p.type==PostType.audio||p.type==PostType.voice).toList();
    final links  = _posts.where((p)=>p.type==PostType.link).toList();

    return DefaultTabController(
      length: 7,
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          title: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(_profileInfo?['name'] ?? widget.username, style: const TextStyle(color: Colors.black)),
              const SizedBox(width:4),

            ],
          ),
          backgroundColor: Colors.white,
          foregroundColor: Colors.black,
          elevation: 0,
          iconTheme: const IconThemeData(color: Colors.black),
          actions: _isMe
              ? [
                  IconButton(
                    icon: const Icon(Icons.verified_user),
                    onPressed: _showVerificationInfo,
                    tooltip: 'طلب توثيق الحساب',
                  ),
                  IconButton(icon: const Icon(Icons.edit), onPressed: _showEditDialog),
                  IconButton(icon: const Icon(Icons.settings), onPressed: ()=>Navigator.push(context, MaterialPageRoute(builder: (_)=>const SettingsPage()))),
                ]
              : null,
          bottom: const TabBar(
            isScrollable: true,
            indicatorColor: Colors.black,
            labelColor: Colors.black,
            unselectedLabelColor: Colors.black54,
            tabs: [
              Tab(text:'المنشورات'),
              Tab(text:'الصور'),
              Tab(text:'الفيديوهات'),
              Tab(text:'البث المباشر'),
              Tab(text:'الصوتيات'),
              Tab(text:'الروابط'),
              Tab(text:'حول'),
            ],
          ),
        ),
        body: Container(
          color: Colors.white,
          child: Column(
            children: [
            // معلومات الملف الشخصي
            Container(
              color: Colors.white,
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Stack(
                    children:[
                      Hero(
                        tag: 'avatar_${widget.userId}',
                        child: GestureDetector(
                          onTap: _openAvatarPost,
                          child: CircleAvatar(
                            radius: 40,
                            backgroundColor: Colors.grey.shade300,
                            backgroundImage: (_profileInfo?['avatar_url'] ?? '').toString().isNotEmpty
                                ? NetworkImage(_profileInfo!['avatar_url'])
                                : null,
                            child: (_profileInfo?['avatar_url'] ?? '').toString().isNotEmpty
                                ? null
                                : Text(_initials(), style: const TextStyle(fontSize: 20, color: Colors.black)),
                          ),
                        ),
                      ),

                    ],
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children:[
                            Row(
                              children: [
                                Text(
                                  _profileInfo?['name'] ?? widget.username,
                                  style: const TextStyle(fontSize: 20,fontWeight: FontWeight.bold),
                                ),
                                const SizedBox(width: 4),
                                // إضافة شارة التحقق إذا كان المستخدم موثقاً
                                if (_profileInfo?['is_verified'] == true)
                                  const InteractiveVerifiedBadge(
                                    size: 20,
                                  ),
                              ],
                            ),
                            const SizedBox(width:6),
                            if((_profileInfo?['username']??'').toString().isNotEmpty)
                              Text('@${_profileInfo?['username']}', style: TextStyle(fontSize:14,color: Colors.grey[700])),
                          ],
                        ),
                        if((_profileInfo?['bio']??'').toString().isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.only(top:4.0),
                            child: ReadMoreText(
                              _profileInfo!['bio'],
                              trimLines: 1,
                              trimMode: TrimMode.Line,
                              trimCollapsedText: ' عرض المزيد',
                              trimExpandedText: ' عرض أقل',
                              moreStyle: const TextStyle(color: Colors.blue,fontSize:14),
                              lessStyle: const TextStyle(color: Colors.blue,fontSize:14),
                              style: const TextStyle(fontSize:14,color: Colors.black87),
                            ),
                          ),
                        const SizedBox(height: 8),

                        // عرض مستوى المستخدم
                        if (_isMe)
                          Container(
                            margin: const EdgeInsets.only(bottom: 12),
                            child: UserLevelDetails(
                              userId: widget.userId,
                              showProgress: true,
                            ),
                          ),

                        Row(
                          children: [
                            _statItem('المنشورات', NumberFormatUtil.prettyCount(_posts.length), null),
                            _statItem('المتابعون', NumberFormatUtil.prettyCount(_profileInfo?['followers_count'] ?? 0), _openFollowers),
                            _statItem('يتابع', NumberFormatUtil.prettyCount(_profileInfo?['following_count'] ?? 0), _openFollowing),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            // زر المتابعة
            if (!_isMe)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: ElevatedButton(
                  onPressed: () async {
                    try {
                      final res = await SupabaseService().toggleFollow(widget.userId);
                      setState(() {
                        _isFollowing = res;
                        _profileInfo?['followers_count'] = (_profileInfo?['followers_count'] ?? 0) + (res ? 1 : -1);
                      });
                      if (mounted) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(content: Text(res ? 'تمت متابعة المستخدم' : 'تم إلغاء المتابعة')),
                        );
                      }
                    } catch (e) {
                      if(mounted) ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(e.toString())));
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isFollowing ? Colors.grey : Theme.of(context).primaryColor,
                    minimumSize: const Size(double.infinity, 40),
                  ),
                  child: Text(
                    _isFollowing ? 'إلغاء المتابعة' : 'متابعة',
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              ),
            const SizedBox(height: 16),
            Expanded(
              child: _loading
                  ? const Center(child:CircularProgressIndicator())
                  : _error!=null
                      ? Center(child: Text(_error!))
                      : TabBarView(children: [
                          // المنشورات
                          _buildPostsList(_posts),
                          // الصور
                          _buildPhotosGrid(images),
                          // الفيديوهات
                          _buildVideosGrid(videos),
                          // البث المباشر (حالياً يعاد استخدام بطاقة الفيديو)
                          _buildLiveList(videos.where((p)=>p.mediaUrl!=null && p.mediaUrl!.contains('live')).toList()),
                          // الصوتيات
                          _buildPostsList(audios),
                          // الروابط
                          _buildPostsList(links),
                          _buildAboutTab(),
                        ]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostsList(List<Post> posts){
    if(posts.isEmpty){return const Center(child: Text('لا يوجد محتوى'));}
    return ListView.builder(
      padding: EdgeInsets.zero, // إزالة الهوامش لعرض كامل الشاشة
      itemCount: posts.length,
      itemBuilder: (c,i)=>PostCard(post: posts[i], isInProfile: true),
    );
  }

  Widget _buildPhotosGrid(List<Post> posts){
    if(posts.isEmpty)return const Center(child: Text('لا توجد صور'));
    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount:3,crossAxisSpacing:4,mainAxisSpacing:4),
      itemCount: posts.length,
      itemBuilder:(c,i)=>ClipRRect(borderRadius:BorderRadius.circular(8),child: Image.network(posts[i].mediaUrl!,fit: BoxFit.cover)),
    );
  }

  Widget _buildVideosGrid(List<Post> posts){
    if(posts.isEmpty)return const Center(child: Text('لا توجد فيديوهات'));
    return ListView.builder(
      padding: EdgeInsets.zero, // إزالة الهوامش لعرض كامل الشاشة
      itemCount: posts.length,
      itemBuilder:(c,i)=>PostCard(post: posts[i], isInProfile: true),
    );
  }

  Widget _buildStatColumn(String label, String count) {
    return Column(
      children: [
        Text(
          count,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        Text(label, style: const TextStyle(color: Colors.black87)),
      ],
    );
  }

  Widget _statItem(String label, String count, VoidCallback? onTap) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 4),
          child: _buildStatColumn(label, count),
        ),
      ),
    );
  }

  void _openFollowers(){
    Navigator.push(context, MaterialPageRoute(builder: (_)=> FollowListPage(userId: widget.userId, showFollowers: true)));
  }

  void _openFollowing(){
    Navigator.push(context, MaterialPageRoute(builder: (_)=> FollowListPage(userId: widget.userId, showFollowers: false)));
  }

  Future<void> _openAvatarPost() async {
    final post = await SupabaseService().fetchAvatarPost(widget.userId);
    if (!mounted) return;
    if (post == null) return;
    Navigator.push(context, MaterialPageRoute(builder: (_) => AvatarPostPage(post: post)));
  }

  Widget _buildLiveList(List<Post> posts){
    if(posts.isEmpty)return const Center(child: Text('لا يوجد بث مباشر حالياً'));
    return ListView.builder(
      padding: EdgeInsets.zero, // إزالة الهوامش لعرض كامل الشاشة
      itemCount: posts.length,
      itemBuilder:(c,i)=>PostCard(post: posts[i], isInProfile: true),
    );
  }

  Widget _infoRow(String label, String? value){
    if(value==null||value.isEmpty)return const SizedBox.shrink();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical:4.0,horizontal:8),
      child: Row(
        children:[
          Text(label,style: const TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(width:8),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _genderName(String? g){
    switch(g){
      case 'male': return 'ذكر';
      case 'female': return 'أنثى';
      default: return 'غير محدد';
    }
  }

  Widget _buildAboutTab(){
    return ListView(
      padding: const EdgeInsets.all(16),
      children:[
        _infoRow('الموقع', _profileInfo?['website']??''),
        _infoRow('الجنس', _genderName(_profileInfo?['gender'])),
        _infoRow('البلد', _profileInfo?['country']??''),
        _infoRow('المدينة', _profileInfo?['city']??''),
        _infoRow('تاريخ الانضمام', _profileInfo!=null && _profileInfo!['created_at']!=null
            ? _profileInfo!['created_at'].toString().substring(0,10)
            : ''),
      ],
    );
  }
} 