  Any    Array    BasicMessageChannel    Boolean    Byte    	ByteArray    DoubleArray    Int    IntArray    IsEnabledMessage    JvmOverloads    List    Log    	LongArray    Map    String    Suppress    	Throwable    
ToggleMessage    WakelockPlusApi    WakelockPlusFlutterError    WakelockPlusMessagesPigeonCodec    all    
contentEquals    deepEqualsWakelockPlusMessages    getValue    indices    invoke    
isNotEmpty    	javaClass    lazy    let    listOf    provideDelegate    run    	wrapError    
wrapResult    Any IsEnabledMessage  Boolean IsEnabledMessage  Int IsEnabledMessage  IsEnabledMessage IsEnabledMessage  List IsEnabledMessage  deepEqualsWakelockPlusMessages IsEnabledMessage  enabled IsEnabledMessage  fromList IsEnabledMessage  !getDEEPEqualsWakelockPlusMessages IsEnabledMessage  !getDeepEqualsWakelockPlusMessages IsEnabledMessage  	getLISTOf IsEnabledMessage  	getListOf IsEnabledMessage  invoke IsEnabledMessage  listOf IsEnabledMessage  toList IsEnabledMessage  Any IsEnabledMessage.Companion  Boolean IsEnabledMessage.Companion  Int IsEnabledMessage.Companion  IsEnabledMessage IsEnabledMessage.Companion  List IsEnabledMessage.Companion  deepEqualsWakelockPlusMessages IsEnabledMessage.Companion  fromList IsEnabledMessage.Companion  !getDEEPEqualsWakelockPlusMessages IsEnabledMessage.Companion  !getDeepEqualsWakelockPlusMessages IsEnabledMessage.Companion  	getLISTOf IsEnabledMessage.Companion  	getListOf IsEnabledMessage.Companion  invoke IsEnabledMessage.Companion  listOf IsEnabledMessage.Companion  Any 
ToggleMessage  Boolean 
ToggleMessage  Int 
ToggleMessage  List 
ToggleMessage  
ToggleMessage 
ToggleMessage  deepEqualsWakelockPlusMessages 
ToggleMessage  enable 
ToggleMessage  fromList 
ToggleMessage  !getDEEPEqualsWakelockPlusMessages 
ToggleMessage  !getDeepEqualsWakelockPlusMessages 
ToggleMessage  	getLISTOf 
ToggleMessage  	getListOf 
ToggleMessage  invoke 
ToggleMessage  listOf 
ToggleMessage  toList 
ToggleMessage  Any ToggleMessage.Companion  Boolean ToggleMessage.Companion  Int ToggleMessage.Companion  List ToggleMessage.Companion  
ToggleMessage ToggleMessage.Companion  deepEqualsWakelockPlusMessages ToggleMessage.Companion  fromList ToggleMessage.Companion  !getDEEPEqualsWakelockPlusMessages ToggleMessage.Companion  !getDeepEqualsWakelockPlusMessages ToggleMessage.Companion  	getLISTOf ToggleMessage.Companion  	getListOf ToggleMessage.Companion  invoke ToggleMessage.Companion  listOf ToggleMessage.Companion  Any WakelockPlusApi  BasicMessageChannel WakelockPlusApi  BinaryMessenger WakelockPlusApi  	Companion WakelockPlusApi  IsEnabledMessage WakelockPlusApi  JvmOverloads WakelockPlusApi  List WakelockPlusApi  MessageCodec WakelockPlusApi  String WakelockPlusApi  	Throwable WakelockPlusApi  
ToggleMessage WakelockPlusApi  WakelockPlusApi WakelockPlusApi  WakelockPlusMessagesPigeonCodec WakelockPlusApi  equals WakelockPlusApi  getValue WakelockPlusApi  	isEnabled WakelockPlusApi  
isNotEmpty WakelockPlusApi  lazy WakelockPlusApi  listOf WakelockPlusApi  provideDelegate WakelockPlusApi  run WakelockPlusApi  setUp WakelockPlusApi  toggle WakelockPlusApi  	wrapError WakelockPlusApi  Any WakelockPlusApi.Companion  BasicMessageChannel WakelockPlusApi.Companion  BinaryMessenger WakelockPlusApi.Companion  IsEnabledMessage WakelockPlusApi.Companion  JvmOverloads WakelockPlusApi.Companion  List WakelockPlusApi.Companion  MessageCodec WakelockPlusApi.Companion  String WakelockPlusApi.Companion  	Throwable WakelockPlusApi.Companion  
ToggleMessage WakelockPlusApi.Companion  WakelockPlusApi WakelockPlusApi.Companion  WakelockPlusMessagesPigeonCodec WakelockPlusApi.Companion  codec WakelockPlusApi.Companion  getGETValue WakelockPlusApi.Companion  getGetValue WakelockPlusApi.Companion  
getISNotEmpty WakelockPlusApi.Companion  
getIsNotEmpty WakelockPlusApi.Companion  getLAZY WakelockPlusApi.Companion  	getLISTOf WakelockPlusApi.Companion  getLazy WakelockPlusApi.Companion  	getListOf WakelockPlusApi.Companion  getPROVIDEDelegate WakelockPlusApi.Companion  getProvideDelegate WakelockPlusApi.Companion  getRUN WakelockPlusApi.Companion  getRun WakelockPlusApi.Companion  getValue WakelockPlusApi.Companion  getWRAPError WakelockPlusApi.Companion  getWrapError WakelockPlusApi.Companion  
isNotEmpty WakelockPlusApi.Companion  lazy WakelockPlusApi.Companion  listOf WakelockPlusApi.Companion  provideDelegate WakelockPlusApi.Companion  run WakelockPlusApi.Companion  setUp WakelockPlusApi.Companion  	wrapError WakelockPlusApi.Companion  Any WakelockPlusFlutterError  String WakelockPlusFlutterError  code WakelockPlusFlutterError  details WakelockPlusFlutterError  message WakelockPlusFlutterError  Any WakelockPlusMessagesPigeonCodec  Byte WakelockPlusMessagesPigeonCodec  ByteArrayOutputStream WakelockPlusMessagesPigeonCodec  
ByteBuffer WakelockPlusMessagesPigeonCodec  IsEnabledMessage WakelockPlusMessagesPigeonCodec  List WakelockPlusMessagesPigeonCodec  
ToggleMessage WakelockPlusMessagesPigeonCodec  getLET WakelockPlusMessagesPigeonCodec  getLet WakelockPlusMessagesPigeonCodec  let WakelockPlusMessagesPigeonCodec  	readValue WakelockPlusMessagesPigeonCodec  
writeValue WakelockPlusMessagesPigeonCodec  Activity android.app  equals android.app.Activity  	getWINDOW android.app.Activity  	getWindow android.app.Activity  	setWindow android.app.Activity  window android.app.Activity  Log android.util  getStackTraceString android.util.Log  
WindowManager android.view  addFlags android.view.Window  
attributes android.view.Window  
clearFlags android.view.Window  
getATTRIBUTES android.view.Window  
getAttributes android.view.Window  
setAttributes android.view.Window  LayoutParams android.view.WindowManager  FLAG_KEEP_SCREEN_ON 'android.view.WindowManager.LayoutParams  flags 'android.view.WindowManager.LayoutParams  	Exception "dev.fluttercommunity.plus.wakelock  IsEnabledMessage "dev.fluttercommunity.plus.wakelock  NoActivityException "dev.fluttercommunity.plus.wakelock  Wakelock "dev.fluttercommunity.plus.wakelock  WakelockPlusApi "dev.fluttercommunity.plus.wakelock  WakelockPlusPlugin "dev.fluttercommunity.plus.wakelock  
WindowManager "dev.fluttercommunity.plus.wakelock  Activity +dev.fluttercommunity.plus.wakelock.Wakelock  IsEnabledMessage +dev.fluttercommunity.plus.wakelock.Wakelock  NoActivityException +dev.fluttercommunity.plus.wakelock.Wakelock  
ToggleMessage +dev.fluttercommunity.plus.wakelock.Wakelock  
WindowManager +dev.fluttercommunity.plus.wakelock.Wakelock  activity +dev.fluttercommunity.plus.wakelock.Wakelock  enabled +dev.fluttercommunity.plus.wakelock.Wakelock  invoke +dev.fluttercommunity.plus.wakelock.Wakelock  	isEnabled +dev.fluttercommunity.plus.wakelock.Wakelock  toggle +dev.fluttercommunity.plus.wakelock.Wakelock  ActivityPluginBinding 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  
FlutterPlugin 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  IsEnabledMessage 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  
ToggleMessage 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  Wakelock 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  WakelockPlusApi 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  onAttachedToActivity 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  onDetachedFromActivity 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  wakelock 5dev.fluttercommunity.plus.wakelock.WakelockPlusPlugin  
FlutterPlugin #io.flutter.embedding.engine.plugins  FlutterPluginBinding 1io.flutter.embedding.engine.plugins.FlutterPlugin  binaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBINARYMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  getBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  setBinaryMessenger Fio.flutter.embedding.engine.plugins.FlutterPlugin.FlutterPluginBinding  
ActivityAware ,io.flutter.embedding.engine.plugins.activity  ActivityPluginBinding ,io.flutter.embedding.engine.plugins.activity  activity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getACTIVITY Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  getActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  setActivity Bio.flutter.embedding.engine.plugins.activity.ActivityPluginBinding  BasicMessageChannel io.flutter.plugin.common  BinaryMessenger io.flutter.plugin.common  EventChannel io.flutter.plugin.common  MessageCodec io.flutter.plugin.common  StandardMessageCodec io.flutter.plugin.common  StandardMethodCodec io.flutter.plugin.common  Reply ,io.flutter.plugin.common.BasicMessageChannel  setMessageHandler ,io.flutter.plugin.common.BasicMessageChannel  <SAM-CONSTRUCTOR> ;io.flutter.plugin.common.BasicMessageChannel.MessageHandler  reply 2io.flutter.plugin.common.BasicMessageChannel.Reply  Any -io.flutter.plugin.common.StandardMessageCodec  Byte -io.flutter.plugin.common.StandardMessageCodec  ByteArrayOutputStream -io.flutter.plugin.common.StandardMessageCodec  
ByteBuffer -io.flutter.plugin.common.StandardMessageCodec  IsEnabledMessage -io.flutter.plugin.common.StandardMessageCodec  List -io.flutter.plugin.common.StandardMessageCodec  
ToggleMessage -io.flutter.plugin.common.StandardMessageCodec  let -io.flutter.plugin.common.StandardMessageCodec  	readValue -io.flutter.plugin.common.StandardMessageCodec  readValueOfType -io.flutter.plugin.common.StandardMessageCodec  
writeValue -io.flutter.plugin.common.StandardMessageCodec  ByteArrayOutputStream java.io  write java.io.ByteArrayOutputStream  write java.io.OutputStream  BasicMessageChannel 	java.lang  IsEnabledMessage 	java.lang  Log 	java.lang  NoActivityException 	java.lang  
ToggleMessage 	java.lang  Wakelock 	java.lang  WakelockPlusApi 	java.lang  WakelockPlusMessagesPigeonCodec 	java.lang  
WindowManager 	java.lang  all 	java.lang  
contentEquals 	java.lang  deepEqualsWakelockPlusMessages 	java.lang  getValue 	java.lang  indices 	java.lang  
isNotEmpty 	java.lang  	javaClass 	java.lang  lazy 	java.lang  let 	java.lang  listOf 	java.lang  provideDelegate 	java.lang  run 	java.lang  	wrapError 	java.lang  
getSIMPLEName java.lang.Class  
getSimpleName java.lang.Class  
setSimpleName java.lang.Class  
simpleName java.lang.Class  
ByteBuffer java.nio  Any kotlin  Array kotlin  BasicMessageChannel kotlin  Boolean kotlin  Byte kotlin  	ByteArray kotlin  DoubleArray kotlin  	Exception kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Int kotlin  IntArray kotlin  IsEnabledMessage kotlin  JvmOverloads kotlin  Lazy kotlin  Log kotlin  	LongArray kotlin  NoActivityException kotlin  Nothing kotlin  String kotlin  Suppress kotlin  	Throwable kotlin  
ToggleMessage kotlin  Wakelock kotlin  WakelockPlusApi kotlin  WakelockPlusMessagesPigeonCodec kotlin  
WindowManager kotlin  all kotlin  
contentEquals kotlin  deepEqualsWakelockPlusMessages kotlin  getValue kotlin  indices kotlin  
isNotEmpty kotlin  	javaClass kotlin  lazy kotlin  let kotlin  listOf kotlin  provideDelegate kotlin  run kotlin  	wrapError kotlin  getALL 
kotlin.Any  getAll 
kotlin.Any  getCONTENTEquals 
kotlin.Any  getContentEquals 
kotlin.Any  
getINDICES 
kotlin.Any  
getIndices 
kotlin.Any  
getINDICES kotlin.Array  
getIndices kotlin.Array  getCONTENTEquals kotlin.ByteArray  getContentEquals kotlin.ByteArray  getCONTENTEquals kotlin.DoubleArray  getContentEquals kotlin.DoubleArray  getCONTENTEquals kotlin.IntArray  getContentEquals kotlin.IntArray  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  getCONTENTEquals kotlin.LongArray  getContentEquals kotlin.LongArray  
getISNotEmpty 
kotlin.String  
getIsNotEmpty 
kotlin.String  
isNotEmpty 
kotlin.String  getJAVAClass kotlin.Throwable  getJavaClass kotlin.Throwable  BasicMessageChannel kotlin.annotation  	Exception kotlin.annotation  IsEnabledMessage kotlin.annotation  JvmOverloads kotlin.annotation  Log kotlin.annotation  NoActivityException kotlin.annotation  
ToggleMessage kotlin.annotation  Wakelock kotlin.annotation  WakelockPlusApi kotlin.annotation  WakelockPlusMessagesPigeonCodec kotlin.annotation  
WindowManager kotlin.annotation  all kotlin.annotation  
contentEquals kotlin.annotation  deepEqualsWakelockPlusMessages kotlin.annotation  getValue kotlin.annotation  indices kotlin.annotation  
isNotEmpty kotlin.annotation  	javaClass kotlin.annotation  lazy kotlin.annotation  let kotlin.annotation  listOf kotlin.annotation  provideDelegate kotlin.annotation  run kotlin.annotation  	wrapError kotlin.annotation  BasicMessageChannel kotlin.collections  	Exception kotlin.collections  IsEnabledMessage kotlin.collections  JvmOverloads kotlin.collections  List kotlin.collections  Log kotlin.collections  Map kotlin.collections  NoActivityException kotlin.collections  
ToggleMessage kotlin.collections  Wakelock kotlin.collections  WakelockPlusApi kotlin.collections  WakelockPlusMessagesPigeonCodec kotlin.collections  
WindowManager kotlin.collections  all kotlin.collections  
contentEquals kotlin.collections  contentEqualsNullable kotlin.collections  deepEqualsWakelockPlusMessages kotlin.collections  getValue kotlin.collections  indices kotlin.collections  
isNotEmpty kotlin.collections  	javaClass kotlin.collections  lazy kotlin.collections  let kotlin.collections  listOf kotlin.collections  provideDelegate kotlin.collections  run kotlin.collections  	wrapError kotlin.collections  
getINDICES kotlin.collections.List  
getIndices kotlin.collections.List  getLET kotlin.collections.List  getLet kotlin.collections.List  Entry kotlin.collections.Map  getALL kotlin.collections.Map  getAll kotlin.collections.Map  BasicMessageChannel kotlin.comparisons  	Exception kotlin.comparisons  IsEnabledMessage kotlin.comparisons  JvmOverloads kotlin.comparisons  Log kotlin.comparisons  NoActivityException kotlin.comparisons  
ToggleMessage kotlin.comparisons  Wakelock kotlin.comparisons  WakelockPlusApi kotlin.comparisons  WakelockPlusMessagesPigeonCodec kotlin.comparisons  
WindowManager kotlin.comparisons  all kotlin.comparisons  
contentEquals kotlin.comparisons  deepEqualsWakelockPlusMessages kotlin.comparisons  getValue kotlin.comparisons  indices kotlin.comparisons  
isNotEmpty kotlin.comparisons  	javaClass kotlin.comparisons  lazy kotlin.comparisons  let kotlin.comparisons  listOf kotlin.comparisons  provideDelegate kotlin.comparisons  run kotlin.comparisons  	wrapError kotlin.comparisons  BasicMessageChannel 	kotlin.io  	Exception 	kotlin.io  IsEnabledMessage 	kotlin.io  JvmOverloads 	kotlin.io  Log 	kotlin.io  NoActivityException 	kotlin.io  
ToggleMessage 	kotlin.io  Wakelock 	kotlin.io  WakelockPlusApi 	kotlin.io  WakelockPlusMessagesPigeonCodec 	kotlin.io  
WindowManager 	kotlin.io  all 	kotlin.io  
contentEquals 	kotlin.io  deepEqualsWakelockPlusMessages 	kotlin.io  getValue 	kotlin.io  indices 	kotlin.io  
isNotEmpty 	kotlin.io  	javaClass 	kotlin.io  lazy 	kotlin.io  let 	kotlin.io  listOf 	kotlin.io  provideDelegate 	kotlin.io  run 	kotlin.io  	wrapError 	kotlin.io  BasicMessageChannel 
kotlin.jvm  	Exception 
kotlin.jvm  IsEnabledMessage 
kotlin.jvm  JvmOverloads 
kotlin.jvm  Log 
kotlin.jvm  NoActivityException 
kotlin.jvm  
ToggleMessage 
kotlin.jvm  Wakelock 
kotlin.jvm  WakelockPlusApi 
kotlin.jvm  WakelockPlusMessagesPigeonCodec 
kotlin.jvm  
WindowManager 
kotlin.jvm  all 
kotlin.jvm  
contentEquals 
kotlin.jvm  deepEqualsWakelockPlusMessages 
kotlin.jvm  getValue 
kotlin.jvm  indices 
kotlin.jvm  
isNotEmpty 
kotlin.jvm  	javaClass 
kotlin.jvm  lazy 
kotlin.jvm  let 
kotlin.jvm  listOf 
kotlin.jvm  provideDelegate 
kotlin.jvm  run 
kotlin.jvm  	wrapError 
kotlin.jvm  BasicMessageChannel 
kotlin.ranges  	Exception 
kotlin.ranges  IsEnabledMessage 
kotlin.ranges  JvmOverloads 
kotlin.ranges  Log 
kotlin.ranges  NoActivityException 
kotlin.ranges  
ToggleMessage 
kotlin.ranges  Wakelock 
kotlin.ranges  WakelockPlusApi 
kotlin.ranges  WakelockPlusMessagesPigeonCodec 
kotlin.ranges  
WindowManager 
kotlin.ranges  all 
kotlin.ranges  
contentEquals 
kotlin.ranges  deepEqualsWakelockPlusMessages 
kotlin.ranges  getValue 
kotlin.ranges  indices 
kotlin.ranges  
isNotEmpty 
kotlin.ranges  	javaClass 
kotlin.ranges  lazy 
kotlin.ranges  let 
kotlin.ranges  listOf 
kotlin.ranges  provideDelegate 
kotlin.ranges  run 
kotlin.ranges  	wrapError 
kotlin.ranges  all kotlin.ranges.IntProgression  all kotlin.ranges.IntRange  getALL kotlin.ranges.IntRange  getAll kotlin.ranges.IntRange  BasicMessageChannel kotlin.sequences  	Exception kotlin.sequences  IsEnabledMessage kotlin.sequences  JvmOverloads kotlin.sequences  Log kotlin.sequences  NoActivityException kotlin.sequences  
ToggleMessage kotlin.sequences  Wakelock kotlin.sequences  WakelockPlusApi kotlin.sequences  WakelockPlusMessagesPigeonCodec kotlin.sequences  
WindowManager kotlin.sequences  all kotlin.sequences  
contentEquals kotlin.sequences  deepEqualsWakelockPlusMessages kotlin.sequences  getValue kotlin.sequences  indices kotlin.sequences  
isNotEmpty kotlin.sequences  	javaClass kotlin.sequences  lazy kotlin.sequences  let kotlin.sequences  listOf kotlin.sequences  provideDelegate kotlin.sequences  run kotlin.sequences  	wrapError kotlin.sequences  BasicMessageChannel kotlin.text  	Exception kotlin.text  IsEnabledMessage kotlin.text  JvmOverloads kotlin.text  Log kotlin.text  NoActivityException kotlin.text  
ToggleMessage kotlin.text  Wakelock kotlin.text  WakelockPlusApi kotlin.text  WakelockPlusMessagesPigeonCodec kotlin.text  
WindowManager kotlin.text  all kotlin.text  
contentEquals kotlin.text  deepEqualsWakelockPlusMessages kotlin.text  getValue kotlin.text  indices kotlin.text  
isNotEmpty kotlin.text  	javaClass kotlin.text  lazy kotlin.text  let kotlin.text  listOf kotlin.text  provideDelegate kotlin.text  run kotlin.text  	wrapError kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                               