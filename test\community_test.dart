import 'package:flutter_test/flutter_test.dart';
import 'package:arzawo/models/community.dart';

void main() {
  group('Community Model Tests', () {
    test('should create Community with all new properties', () {
      final community = Community(
        id: 'test-id',
        ownerId: 'owner-id',
        name: 'Test Community',
        description: 'Test Description',
        category: 'Technology',
        coverUrl: 'https://example.com/cover.jpg',
        avatarUrl: 'https://example.com/avatar.jpg',
        isPrivate: true,
        isArchived: false,
        isDisabled: false,
        createdAt: DateTime.now(),
        membersCount: 10,
        allowMemberPosts: true,
        requireApproval: false,
        allowComments: true,
        allowInvites: true,
        postPermission: 'members',
        joinType: 'open',
      );

      expect(community.id, 'test-id');
      expect(community.name, 'Test Community');
      expect(community.avatarUrl, 'https://example.com/avatar.jpg');
      expect(community.isArchived, false);
      expect(community.isDisabled, false);
      expect(community.allowMemberPosts, true);
      expect(community.postPermission, 'members');
      expect(community.joinType, 'open');
    });

    test('should create Community from map with new fields', () {
      final map = {
        'id': 'test-id',
        'owner_id': 'owner-id',
        'name': 'Test Community',
        'description': 'Test Description',
        'category': 'Technology',
        'cover_url': 'https://example.com/cover.jpg',
        'avatar_url': 'https://example.com/avatar.jpg',
        'is_private': true,
        'is_archived': false,
        'is_disabled': false,
        'created_at': '2025-01-20T10:00:00Z',
        'allow_member_posts': true,
        'require_approval': false,
        'allow_comments': true,
        'allow_invites': true,
        'post_permission': 'members',
        'join_type': 'open',
        'members': [{'count': 5}],
      };

      final community = Community.fromMap(map);

      expect(community.id, 'test-id');
      expect(community.name, 'Test Community');
      expect(community.avatarUrl, 'https://example.com/avatar.jpg');
      expect(community.isArchived, false);
      expect(community.isDisabled, false);
      expect(community.allowMemberPosts, true);
      expect(community.postPermission, 'members');
      expect(community.joinType, 'open');
      expect(community.membersCount, 5);
    });

    test('should convert Community to map with new fields', () {
      final community = Community(
        id: 'test-id',
        ownerId: 'owner-id',
        name: 'Test Community',
        description: 'Test Description',
        category: 'Technology',
        coverUrl: 'https://example.com/cover.jpg',
        avatarUrl: 'https://example.com/avatar.jpg',
        isPrivate: true,
        isArchived: false,
        isDisabled: false,
        createdAt: DateTime.parse('2025-01-20T10:00:00Z'),
        membersCount: 10,
        allowMemberPosts: true,
        requireApproval: false,
        allowComments: true,
        allowInvites: true,
        postPermission: 'members',
        joinType: 'open',
      );

      final map = community.toMap();

      expect(map['id'], 'test-id');
      expect(map['name'], 'Test Community');
      expect(map['avatar_url'], 'https://example.com/avatar.jpg');
      expect(map['is_archived'], false);
      expect(map['is_disabled'], false);
      expect(map['allow_member_posts'], true);
      expect(map['post_permission'], 'members');
      expect(map['join_type'], 'open');
    });

    test('should have correct helper methods', () {
      // Test active community
      final activeCommunity = Community(
        id: 'test-id',
        ownerId: 'owner-id',
        name: 'Active Community',
        isPrivate: false,
        isArchived: false,
        isDisabled: false,
        createdAt: DateTime.now(),
        allowMemberPosts: true,
        allowComments: true,
        allowInvites: true,
      );

      expect(activeCommunity.isActive, true);
      expect(activeCommunity.canPost, true);
      expect(activeCommunity.canComment, true);
      expect(activeCommunity.canInvite, true);

      // Test archived community
      final archivedCommunity = Community(
        id: 'test-id',
        ownerId: 'owner-id',
        name: 'Archived Community',
        isPrivate: false,
        isArchived: true,
        isDisabled: false,
        createdAt: DateTime.now(),
        allowMemberPosts: true,
        allowComments: true,
        allowInvites: true,
      );

      expect(archivedCommunity.isActive, false);
      expect(archivedCommunity.canPost, false);
      expect(archivedCommunity.canComment, false);
      expect(archivedCommunity.canInvite, false);

      // Test disabled community
      final disabledCommunity = Community(
        id: 'test-id',
        ownerId: 'owner-id',
        name: 'Disabled Community',
        isPrivate: false,
        isArchived: false,
        isDisabled: true,
        createdAt: DateTime.now(),
        allowMemberPosts: true,
        allowComments: true,
        allowInvites: true,
      );

      expect(disabledCommunity.isActive, false);
      expect(disabledCommunity.canPost, false);
      expect(disabledCommunity.canComment, false);
      expect(disabledCommunity.canInvite, false);
    });

    test('should handle default values correctly', () {
      final community = Community(
        id: 'test-id',
        ownerId: 'owner-id',
        name: 'Test Community',
        isPrivate: false,
        createdAt: DateTime.now(),
      );

      expect(community.isArchived, false);
      expect(community.isDisabled, false);
      expect(community.allowMemberPosts, true);
      expect(community.requireApproval, false);
      expect(community.allowComments, true);
      expect(community.allowInvites, true);
      expect(community.postPermission, 'members');
      expect(community.joinType, 'open');
      expect(community.membersCount, 0);
    });

    test('should handle null values in fromMap', () {
      final map = {
        'id': 'test-id',
        'owner_id': 'owner-id',
        'name': 'Test Community',
        'is_private': false,
        'created_at': '2025-01-20T10:00:00Z',
        // All other fields are null or missing
      };

      final community = Community.fromMap(map);

      expect(community.description, null);
      expect(community.category, null);
      expect(community.coverUrl, null);
      expect(community.avatarUrl, null);
      expect(community.isArchived, false);
      expect(community.isDisabled, false);
      expect(community.allowMemberPosts, true);
      expect(community.requireApproval, false);
      expect(community.allowComments, true);
      expect(community.allowInvites, true);
      expect(community.postPermission, 'members');
      expect(community.joinType, 'open');
    });
  });
}
