import 'message.dart';

class Chat {
  final String id;
  final String otherId;
  final String otherName;
  final String otherAvatar;
  String lastMessage;
  DateTime? lastAt;
  List<Message> messages;
  int unreadCount;
  // Indicates whether the last message in the chat was sent by the current user
  bool lastFromMe;

  // If the last message was sent by the current user, whether it has been read (double blue tick)
  bool lastRead;

  Chat({
    required this.id,
    required this.otherId,
    required this.otherName,
    required this.otherAvatar,
    required this.lastMessage,
    this.lastAt,
    this.messages = const [],
    this.unreadCount = 0,
    this.lastFromMe = false,
    this.lastRead = false,
  });
} 