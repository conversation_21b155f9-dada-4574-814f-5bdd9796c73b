-- =============================================================
--  إصلاح طارئ لمشكلة رفع الصور - الحل الأسرع
--  Emergency Storage Fix - Fastest Solution
-- =============================================================

-- هذا السكريبت يحل المشكلة في 30 ثانية!

-- 1) حذف bucket القديم وإنشاء جديد
-- -------------------------------------------------------

DELETE FROM storage.objects WHERE bucket_id = 'community-images';
DELETE FROM storage.buckets WHERE id = 'community-images';

INSERT INTO storage.buckets (id, name, public) 
VALUES ('community-images', 'community-images', true);

-- 2) إزالة جميع السياسات القديمة
-- -------------------------------------------------------

DO $$ 
DECLARE 
    policy_record RECORD;
BEGIN 
    FOR policy_record IN 
        SELECT policyname FROM pg_policies 
        WHERE tablename = 'objects' AND schemaname = 'storage'
        AND policyname LIKE '%community%'
    LOOP 
        EXECUTE 'DROP POLICY IF EXISTS "' || policy_record.policyname || '" ON storage.objects';
    END LOOP;
END $$;

-- 3) إنشاء سياسة واحدة بسيطة تسمح بكل شيء
-- -------------------------------------------------------

CREATE POLICY "community_images_allow_all"
ON storage.objects
FOR ALL
TO authenticated
USING (bucket_id = 'community-images')
WITH CHECK (bucket_id = 'community-images');

-- 4) تفعيل RLS
-- -------------------------------------------------------

ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- 5) إعطاء صلاحيات كاملة
-- -------------------------------------------------------

GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.buckets TO authenticated;

-- =============================================================
--  اختبار سريع
-- =============================================================

-- للتحقق من نجاح الإصلاح:
SELECT 
    'Bucket Created' as status,
    id, 
    name, 
    public 
FROM storage.buckets 
WHERE id = 'community-images'

UNION ALL

SELECT 
    'Policy Created' as status,
    policyname as id,
    cmd as name,
    'true' as public
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
AND policyname = 'community_images_allow_all';

-- =============================================================
--  تعليمات سريعة
-- =============================================================

-- 1. انسخ والصق هذا السكريبت في SQL Editor
-- 2. اضغط Run
-- 3. يجب أن ترى نتيجتين: "Bucket Created" و "Policy Created"
-- 4. اذهب إلى Storage وتأكد من وجود bucket "community-images"
-- 5. أعد بناء التطبيق واختبر رفع الصور

-- إذا لم ينجح هذا، فالمشكلة ليست في Storage!
