import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../supabase_service.dart';

class BlockedUsersPage extends StatefulWidget {
  const BlockedUsersPage({super.key});

  @override
  State<BlockedUsersPage> createState() => _BlockedUsersPageState();
}

class _BlockedUsersPageState extends State<BlockedUsersPage> {
  List<Map<String, dynamic>> _blocked = [];
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    setState(() => _loading = true);
    final list = await SupabaseService().fetchBlockedUsers();
    if (!mounted) return;
    setState(() {
      _blocked = list;
      _loading = false;
    });
  }

  Future<void> _unblock(String id) async {
    await SupabaseService().unblockUser(id);
    await _load();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('المستخدمون المحظورون')),
      body: _loading
          ? const Center(child: CircularProgressIndicator())
          : _blocked.isEmpty
              ? const Center(child: Text('لا يوجد مستخدمون محظورون'))
              : ListView.builder(
                  itemCount: _blocked.length,
                  itemBuilder: (ctx, i) {
                    final u = _blocked[i];
                    return ListTile(
                      leading: CircleAvatar(
                        backgroundImage: u['avatar_url'] != null && u['avatar_url'].toString().isNotEmpty
                            ? NetworkImage(u['avatar_url']) as ImageProvider
                            : const AssetImage('assets/avatar_placeholder.png'),
                      ),
                      title: Text(u['name'] ?? 'مستخدم'),
                      trailing: TextButton(
                        onPressed: () => _unblock(u['id'].toString()),
                        child: const Text('إلغاء الحظر'),
                      ),
                    );
                  },
                ),
    );
  }
} 