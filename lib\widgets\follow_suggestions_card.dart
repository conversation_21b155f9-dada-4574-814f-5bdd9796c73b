import 'package:flutter/material.dart';
import '../supabase_service.dart';
import '../widgets/profile_avatar.dart';
import '../pages/profile_page.dart';
import '../widgets/interactive_verified_badge.dart';

class FollowSuggestionsCard extends StatefulWidget {
  final VoidCallback? onClose;
  const FollowSuggestionsCard({super.key, this.onClose});

  @override
  State<FollowSuggestionsCard> createState() => _FollowSuggestionsCardState();
}

class _FollowSuggestionsCardState extends State<FollowSuggestionsCard> {
  List<Map<String, dynamic>> _suggestions = [];
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _load();
  }

  Future<void> _load() async {
    final list = await SupabaseService().fetchFollowSuggestions();
    setState(() {
      _suggestions = list;
      _loading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_loading || _suggestions.isEmpty) return const SizedBox.shrink();
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            child: Row(
              children: [
                const Text('أشخاص قد تعرفهم', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                const Spacer(),
                IconButton(onPressed: widget.onClose, icon: const Icon(Icons.close))
              ],
            ),
          ),
          SizedBox(
            height: 220,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _suggestions.length,
              itemBuilder: (ctx, i) {
                final u = _suggestions[i];
                return _UserSuggestionTile(user: u);
              },
            ),
          ),
        ],
      ),
    );
  }
}

class _UserSuggestionTile extends StatefulWidget {
  final Map<String, dynamic> user;
  const _UserSuggestionTile({required this.user});

  @override
  State<_UserSuggestionTile> createState() => _UserSuggestionTileState();
}

class _UserSuggestionTileState extends State<_UserSuggestionTile> {
  bool _processing = false;

  @override
  Widget build(BuildContext context) {
    final user = widget.user;
    final bool isFollowing = user['is_following'] == true;
    return Container(
      width: 140,
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Column(
        children: [
          GestureDetector(
            onTap: () => Navigator.push(context, MaterialPageRoute(builder: (_) => ProfilePage(userId: user['id'], username: user['name']))),
            child: ProfileAvatar(userId: user['id'], radius: 40),
          ),
          const SizedBox(height: 8),
          Text(user['name'], maxLines: 1, overflow: TextOverflow.ellipsis, style: const TextStyle(fontWeight: FontWeight.bold)),
          if ((user['username'] ?? '').toString().isNotEmpty)
            Text('@${user['username']}', style: const TextStyle(fontSize: 12, color: Colors.grey), maxLines: 1, overflow: TextOverflow.ellipsis),
          // شارة التحقق
          if (user['is_verified'] == true) ...[
            const SizedBox(height: 4),
            InteractiveVerifiedBadge(
              size: 12,
              userName: user['name'],
            ),
          ],
          const SizedBox(height: 8),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _processing ? null : () async {
                setState(() => _processing = true);
                final res = await SupabaseService().toggleFollow(user['id']);
                setState(() {
                  user['is_following'] = res;
                  _processing = false;
                });
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(res ? 'تمت متابعة المستخدم' : 'تم إلغاء المتابعة')));
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: isFollowing ? Colors.grey.shade400 : const Color(0xFF1877F2),
                minimumSize: const Size(100, 36),
              ),
              child: _processing
                  ? const SizedBox(height: 16, width: 16, child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white))
                  : Text(isFollowing ? 'إلغاء' : 'متابعة', style: const TextStyle(color: Colors.white)),
            ),
          ),
        ],
      ),
    );
  }
} 