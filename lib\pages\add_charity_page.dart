import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../models/charity_item.dart';
import '../services/charity_service.dart';
import '../supabase_service.dart';

class AddCharityPage extends StatefulWidget {
  final CharityType type;

  const AddCharityPage({super.key, required this.type});

  @override
  State<AddCharityPage> createState() => _AddCharityPageState();
}

class _AddCharityPageState extends State<AddCharityPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _phoneController = TextEditingController();
  final _cityController = TextEditingController();
  
  CharityCategory _selectedCategory = CharityCategory.other;
  CharityCondition _selectedCondition = CharityCondition.good;
  DeliveryMethod _selectedDeliveryMethod = DeliveryMethod.hand;
  
  bool _isUrgent = false;
  bool _isAnonymous = false;
  bool _loading = false;
  
  List<XFile> _selectedImages = [];
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    if (widget.type == CharityType.urgent) {
      _isUrgent = true;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _phoneController.dispose();
    _cityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text(
          widget.type == CharityType.donation 
              ? 'إضافة تبرع' 
              : 'طلب مساعدة',
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.teal[600],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // بطاقة معلومات النوع
            _buildTypeInfoCard(),
            
            const SizedBox(height: 16),
            
            // العنوان
            _buildTextField(
              controller: _titleController,
              label: 'العنوان',
              hint: widget.type == CharityType.donation 
                  ? 'مثال: ملابس شتوية جديدة'
                  : 'مثال: أحتاج ملابس شتوية',
              icon: Icons.title,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال العنوان';
                }
                if (value.trim().length < 5) {
                  return 'العنوان قصير جداً';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // الوصف
            _buildTextField(
              controller: _descriptionController,
              label: 'الوصف التفصيلي',
              hint: 'اكتب وصفاً مفصلاً...',
              icon: Icons.description,
              maxLines: 4,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال الوصف';
                }
                if (value.trim().length < 10) {
                  return 'الوصف قصير جداً';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // الفئة والحالة
            Row(
              children: [
                Expanded(child: _buildCategoryDropdown()),
                const SizedBox(width: 12),
                Expanded(child: _buildConditionDropdown()),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // المدينة وطريقة التسليم
            Row(
              children: [
                Expanded(child: _buildCityField()),
                const SizedBox(width: 12),
                Expanded(child: _buildDeliveryMethodDropdown()),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // رقم الهاتف (اختياري)
            _buildTextField(
              controller: _phoneController,
              label: 'رقم الهاتف (اختياري)',
              hint: '05xxxxxxxx',
              icon: Icons.phone,
              keyboardType: TextInputType.phone,
            ),
            
            const SizedBox(height: 16),
            
            // الصور
            _buildImageSection(),
            
            const SizedBox(height: 16),
            
            // الخيارات الإضافية
            _buildOptionsSection(),
            
            const SizedBox(height: 24),
            
            // زر الحفظ
            _buildSaveButton(),
            
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeInfoCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _getTypeColor().withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                widget.type == CharityType.donation 
                    ? Icons.volunteer_activism 
                    : Icons.help_outline,
                color: _getTypeColor(),
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.type.arabicName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    widget.type.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Colors.teal[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: Colors.teal[600]!),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
    );
  }

  Widget _buildCategoryDropdown() {
    return DropdownButtonFormField<CharityCategory>(
      value: _selectedCategory,
      decoration: InputDecoration(
        labelText: 'الفئة',
        prefixIcon: Icon(_selectedCategory.icon, color: Colors.teal[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      items: CharityCategory.values.map((category) {
        return DropdownMenuItem(
          value: category,
          child: Row(
            children: [
              Icon(category.icon, size: 20, color: category.color),
              const SizedBox(width: 8),
              Text(category.arabicName),
            ],
          ),
        );
      }).toList(),
      onChanged: (value) {
        setState(() => _selectedCategory = value!);
      },
    );
  }

  Widget _buildConditionDropdown() {
    return DropdownButtonFormField<CharityCondition>(
      value: _selectedCondition,
      decoration: InputDecoration(
        labelText: 'الحالة',
        prefixIcon: Icon(Icons.info_outline, color: Colors.teal[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      items: CharityCondition.values.map((condition) {
        return DropdownMenuItem(
          value: condition,
          child: Text(condition.arabicName),
        );
      }).toList(),
      onChanged: (value) {
        setState(() => _selectedCondition = value!);
      },
    );
  }

  Widget _buildCityField() {
    return TextFormField(
      controller: _cityController,
      decoration: InputDecoration(
        labelText: 'المدينة',
        hintText: 'الرياض',
        prefixIcon: Icon(Icons.location_city, color: Colors.teal[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      validator: (value) {
        if (value == null || value.trim().isEmpty) {
          return 'يرجى إدخال المدينة';
        }
        return null;
      },
    );
  }

  Widget _buildDeliveryMethodDropdown() {
    return DropdownButtonFormField<DeliveryMethod>(
      value: _selectedDeliveryMethod,
      decoration: InputDecoration(
        labelText: 'طريقة التسليم',
        prefixIcon: Icon(Icons.local_shipping, color: Colors.teal[600]),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      items: DeliveryMethod.values.map((method) {
        return DropdownMenuItem(
          value: method,
          child: Text(method.arabicName),
        );
      }).toList(),
      onChanged: (value) {
        setState(() => _selectedDeliveryMethod = value!);
      },
    );
  }

  Widget _buildImageSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.photo_camera, color: Colors.teal[600]),
                const SizedBox(width: 8),
                const Text(
                  'الصور',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: _pickImages,
                  icon: const Icon(Icons.add_photo_alternate),
                  label: const Text('إضافة صور'),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (_selectedImages.isEmpty)
              Container(
                height: 100,
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.add_photo_alternate, 
                           color: Colors.grey[400], size: 32),
                      const SizedBox(height: 8),
                      Text(
                        'اضغط لإضافة صور',
                        style: TextStyle(color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              )
            else
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _selectedImages.length,
                  itemBuilder: (context, index) {
                    return Container(
                      margin: const EdgeInsets.only(right: 8),
                      child: Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Image.network(
                              _selectedImages[index].path,
                              width: 100,
                              height: 100,
                              fit: BoxFit.cover,
                            ),
                          ),
                          Positioned(
                            top: 4,
                            right: 4,
                            child: GestureDetector(
                              onTap: () => _removeImage(index),
                              child: Container(
                                padding: const EdgeInsets.all(4),
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  color: Colors.white,
                                  size: 16,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildOptionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.settings, color: Colors.teal[600]),
                const SizedBox(width: 8),
                const Text(
                  'خيارات إضافية',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            // حالة طارئة
            if (widget.type != CharityType.donation)
              CheckboxListTile(
                title: const Text('حالة طارئة'),
                subtitle: const Text('سيتم عرض طلبك في قسم الحالات الطارئة'),
                value: _isUrgent,
                onChanged: (value) {
                  setState(() => _isUrgent = value!);
                },
                activeColor: Colors.red,
              ),
            
            // إخفاء الهوية
            CheckboxListTile(
              title: const Text('إخفاء هويتي'),
              subtitle: const Text('سيتم عرض اسمك كـ "متبرع كريم"'),
              value: _isAnonymous,
              onChanged: (value) {
                setState(() => _isAnonymous = value!);
              },
              activeColor: Colors.teal[600],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _loading ? null : _saveCharity,
        style: ElevatedButton.styleFrom(
          backgroundColor: _getTypeColor(),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _loading
            ? const CircularProgressIndicator(color: Colors.white)
            : Text(
                widget.type == CharityType.donation 
                    ? 'نشر التبرع' 
                    : 'نشر الطلب',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  Future<void> _pickImages() async {
    final images = await _picker.pickMultiImage();
    if (images.isNotEmpty) {
      setState(() {
        _selectedImages.addAll(images);
        if (_selectedImages.length > 5) {
          _selectedImages = _selectedImages.take(5).toList();
        }
      });
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  Future<void> _saveCharity() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _loading = true);

    try {
      // رفع الصور أولاً
      List<String> imageUrls = [];
      if (_selectedImages.isNotEmpty) {
        final supabaseService = SupabaseService();
        for (int i = 0; i < _selectedImages.length; i++) {
          final image = _selectedImages[i];
          final bytes = await File(image.path).readAsBytes();
          final fileName = 'charity_${DateTime.now().millisecondsSinceEpoch}_$i.jpg';
          final url = await supabaseService.uploadMedia(bytes, fileName);
          imageUrls.add(url);
        }
      }

      // إنشاء عنصر الصدقة
      final charityItem = CharityItem(
        id: '',
        userId: '',
        userName: '',
        userAvatar: '',
        isVerified: false,
        isAnonymous: _isAnonymous,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        type: widget.type,
        category: _selectedCategory,
        condition: _selectedCondition,
        deliveryMethod: _selectedDeliveryMethod,
        city: _cityController.text.trim(),
        country: 'السعودية',
        phoneNumber: _phoneController.text.trim().isEmpty 
            ? null 
            : _phoneController.text.trim(),
        images: imageUrls,
        isUrgent: _isUrgent,
        isCompleted: false,
        isActive: true,
        interestCount: 0,
        createdAt: DateTime.now(),
      );

      // حفظ في قاعدة البيانات
      await CharityService().addCharityItem(charityItem);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.type == CharityType.donation 
                  ? 'تم نشر التبرع بنجاح' 
                  : 'تم نشر الطلب بنجاح',
            ),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في النشر: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }

  Color _getTypeColor() {
    switch (widget.type) {
      case CharityType.donation:
        return Colors.green;
      case CharityType.request:
        return Colors.blue;
      case CharityType.urgent:
        return Colors.red;
    }
  }
}
