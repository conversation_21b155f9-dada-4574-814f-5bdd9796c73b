-- إصلاح مشاكل المساحات
-- تنفيذ هذا الملف لإصلاح مشاكل تحديث الصور والسياسات

-- 1. إضافة عمود space_id إلى جدول posts إذا لم يكن موجوداً
ALTER TABLE public.posts ADD COLUMN IF NOT EXISTS space_id UUID;

-- إضافة مرجع إلى جدول spaces
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'fk_posts_space_id'
    ) THEN
        ALTER TABLE public.posts ADD CONSTRAINT fk_posts_space_id 
        FOREIGN KEY (space_id) REFERENCES public.spaces(id) ON DELETE CASCADE;
    END IF;
END $$;

-- إنشاء فهرس للأداء
CREATE INDEX IF NOT EXISTS idx_posts_space_id ON public.posts(space_id);

-- 2. إض<PERSON><PERSON>ة المعرف الفريد للمساحات
ALTER TABLE public.spaces ADD COLUMN IF NOT EXISTS username TEXT;

-- إنشاء فهرس للمعرف الفريد
CREATE INDEX IF NOT EXISTS idx_spaces_username ON public.spaces(username);

-- إضافة قيد فريد للمعرف
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'spaces_username_unique'
    ) THEN
        ALTER TABLE public.spaces ADD CONSTRAINT spaces_username_unique UNIQUE (username);
    END IF;
END $$;

-- إضافة قيد للتأكد من صحة المعرف الفريد
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'spaces_username_format'
    ) THEN
        ALTER TABLE public.spaces ADD CONSTRAINT spaces_username_format 
        CHECK (username IS NULL OR username ~ '^[a-zA-Z0-9_]{3,30}$');
    END IF;
END $$;

-- 3. إعداد سياسات الأمان للمساحات
-- تمكين RLS على جدول المساحات
ALTER TABLE public.spaces ENABLE ROW LEVEL SECURITY;

-- حذف السياسات الموجودة لتجنب التكرار
DROP POLICY IF EXISTS "المساحات العامة قابلة للقراءة من الجميع" ON public.spaces;
DROP POLICY IF EXISTS "المالكون يمكنهم إدارة مساحاتهم" ON public.spaces;
DROP POLICY IF EXISTS "المستخدمون المسجلون يمكنهم إنشاء مساحات" ON public.spaces;
DROP POLICY IF EXISTS "Anyone can view public spaces" ON public.spaces;
DROP POLICY IF EXISTS "Authenticated users can create spaces" ON public.spaces;
DROP POLICY IF EXISTS "Space owners can update their spaces" ON public.spaces;
DROP POLICY IF EXISTS "Space owners can delete their spaces" ON public.spaces;
DROP POLICY IF EXISTS "spaces_select_policy" ON public.spaces;
DROP POLICY IF EXISTS "spaces_insert_policy" ON public.spaces;
DROP POLICY IF EXISTS "spaces_update_policy" ON public.spaces;
DROP POLICY IF EXISTS "spaces_delete_policy" ON public.spaces;

-- إنشاء سياسات جديدة بأسماء واضحة
CREATE POLICY "spaces_select_policy" ON public.spaces
FOR SELECT USING (
    privacy = 'public' OR 
    auth.uid() = owner_id OR
    EXISTS (
        SELECT 1 FROM public.space_followers 
        WHERE space_id = id AND follower_id = auth.uid()
    )
);

CREATE POLICY "spaces_insert_policy" ON public.spaces
FOR INSERT WITH CHECK (auth.uid() = owner_id);

CREATE POLICY "spaces_update_policy" ON public.spaces
FOR UPDATE USING (auth.uid() = owner_id);

CREATE POLICY "spaces_delete_policy" ON public.spaces
FOR DELETE USING (auth.uid() = owner_id);

-- 4. إضافة دوال لتحديث عداد المنشورات
CREATE OR REPLACE FUNCTION increment_space_posts(space_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.spaces 
    SET posts_count = posts_count + 1,
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION decrement_space_posts(space_id UUID)
RETURNS void AS $$
BEGIN
    UPDATE public.spaces 
    SET posts_count = GREATEST(posts_count - 1, 0),
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql;

-- محفز لتحديث عداد المنشورات عند الإدراج
CREATE OR REPLACE FUNCTION update_space_posts_count()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.space_id IS NOT NULL THEN
        PERFORM increment_space_posts(NEW.space_id);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- محفز لتحديث عداد المنشورات عند الحذف
CREATE OR REPLACE FUNCTION update_space_posts_count_on_delete()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.space_id IS NOT NULL THEN
        PERFORM decrement_space_posts(OLD.space_id);
    END IF;
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

-- إنشاء المحفزات
DROP TRIGGER IF EXISTS trigger_increment_space_posts ON public.posts;
CREATE TRIGGER trigger_increment_space_posts
    AFTER INSERT ON public.posts
    FOR EACH ROW EXECUTE FUNCTION update_space_posts_count();

DROP TRIGGER IF EXISTS trigger_decrement_space_posts ON public.posts;
CREATE TRIGGER trigger_decrement_space_posts
    AFTER DELETE ON public.posts
    FOR EACH ROW EXECUTE FUNCTION update_space_posts_count_on_delete();

-- 5. تحديث سياسات posts لتدعم المساحات
DROP POLICY IF EXISTS "Users can view posts based on privacy settings" ON public.posts;

CREATE POLICY "Users can view posts based on privacy settings" ON public.posts
FOR SELECT USING (
  -- المنشورات العامة
  posts_privacy = 'everyone' 
  OR 
  -- منشورات المتابعين إذا كان المستخدم يتابع صاحب المنشور
  (posts_privacy = 'followers' AND (
    auth.uid() = user_id OR
    EXISTS (
      SELECT 1 FROM public.follows 
      WHERE follower_id = auth.uid() AND following_id = user_id
    )
  ))
  OR
  -- منشورات المساحات العامة
  (space_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.spaces 
    WHERE id = space_id AND privacy = 'public'
  ))
  OR
  -- منشورات المساحات للمتابعين إذا كان المستخدم يتابع المساحة
  (space_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.space_followers 
    WHERE space_id = posts.space_id AND follower_id = auth.uid()
  ))
  OR
  -- مالك المساحة يمكنه رؤية جميع منشورات مساحته
  (space_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM public.spaces 
    WHERE id = space_id AND owner_id = auth.uid()
  ))
);

-- تحديث سياسة إنشاء المنشورات لتشمل المساحات
DROP POLICY IF EXISTS "Users can create posts" ON public.posts;

CREATE POLICY "Users can create posts" ON public.posts
FOR INSERT WITH CHECK (
  auth.uid() = user_id AND (
    -- منشور عادي
    space_id IS NULL
    OR
    -- منشور في مساحة يملكها المستخدم
    EXISTS (
      SELECT 1 FROM public.spaces 
      WHERE id = space_id AND owner_id = auth.uid()
    )
  )
);

-- رسالة نجاح
SELECT 'تم إصلاح جميع مشاكل المساحات بنجاح!' as result;
