import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/poll.dart';
import '../pages/poll_settings_page.dart';
import '../pages/poll_comments_page.dart';
import '../services/poll_service.dart';
import 'verified_badge.dart';
import 'interactive_verified_badge.dart';

class PollCard extends StatefulWidget {
  final Poll poll;
  final Function(String pollId, String optionId) onVote;
  final VoidCallback? onRefresh;

  const PollCard({
    super.key,
    required this.poll,
    required this.onVote,
    this.onRefresh,
  });

  @override
  State<PollCard> createState() => _PollCardState();
}

class _PollCardState extends State<PollCard> {
  final PollService _pollService = PollService();
  bool _loading = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // معلومات المؤلف مع إعدادات
            _buildAuthorInfo(),
            
            const SizedBox(height: 12),
            
            // السؤال
            _buildQuestion(),
            
            const SizedBox(height: 16),
            
            // خيارات التصويت
            _buildOptions(context),
            
            const SizedBox(height: 16),
            
            // معلومات التصويت
            _buildPollInfo(),
            
            const SizedBox(height: 12),
            
            // أزرار الإجراءات
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildAuthorInfo() {
    // Debug prints لمراقبة عرض بيانات المؤلف
    print('🎨 عرض بيانات المؤلف في PollCard:');
    print('   - Author Name: ${widget.poll.authorName}');
    print('   - Is Verified: ${widget.poll.isVerified}');
    print('   - Author Avatar: ${widget.poll.authorAvatar}');
    
    return Row(
      children: [
        CircleAvatar(
          radius: 20,
          backgroundColor: Colors.purple[100],
          backgroundImage: widget.poll.authorAvatar != null
              ? NetworkImage(widget.poll.authorAvatar!)
              : null,
          child: widget.poll.authorAvatar == null
              ? Icon(Icons.person, color: Colors.purple[600])
              : null,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    widget.poll.authorName ?? 'مستخدم',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  if (widget.poll.isVerified) ...[
                    const SizedBox(width: 4),
                    InteractiveVerifiedBadge(
                      size: 16,
                      userName: widget.poll.authorName,
                    ),
                  ],
                ],
              ),
              const SizedBox(height: 2),
              Text(
                _formatTime(widget.poll.createdAt),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        // فئة التصويت
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: widget.poll.category.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: widget.poll.category.color.withValues(alpha: 0.3)),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(widget.poll.category.icon, size: 12, color: widget.poll.category.color),
              const SizedBox(width: 4),
              Text(
                widget.poll.category.arabicName,
                style: TextStyle(
                  fontSize: 10,
                  color: widget.poll.category.color,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        // زر الإعدادات (للمؤلف فقط)
        if (_isAuthor()) ...[
          const SizedBox(width: 8),
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert, color: Colors.grey[600]),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings, size: 20),
                    SizedBox(width: 8),
                    Text('إعدادات التصويت'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 20, color: Colors.red),
                    SizedBox(width: 8),
                    Text('حذف التصويت', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildQuestion() {
    return Text(
      widget.poll.question,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.w600,
        height: 1.4,
      ),
    );
  }

  Widget _buildOptions(BuildContext context) {
    final hasVoted = widget.poll.userVote != null;
    final canVote = widget.poll.canVote;
    
    return Column(
      children: widget.poll.options.map((option) {
        final isSelected = widget.poll.userVote == option.id;
        final percentage = option.percentage;
        
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          child: InkWell(
            onTap: canVote ? () => widget.onVote(widget.poll.id, option.id) : null,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: isSelected 
                      ? Colors.blue[600]! 
                      : Colors.grey[300]!,
                  width: isSelected ? 2 : 1,
                ),
                color: hasVoted
                    ? (isSelected 
                        ? Colors.blue[50] 
                        : Colors.grey[50])
                    : (canVote 
                        ? Colors.white 
                        : Colors.grey[100]),
                boxShadow: isSelected ? [
                  BoxShadow(
                    color: Colors.blue[200]!.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  )
                ] : null,
              ),
              child: Stack(
                children: [
                  // شريط النسبة المئوية
                  if (hasVoted)
                    Positioned.fill(
                      child: FractionallySizedBox(
                        alignment: Alignment.centerLeft,
                        widthFactor: percentage / 100,
                        child: Container(
                          decoration: BoxDecoration(
                            color: isSelected
                                ? Colors.blue[200]!.withValues(alpha: 0.6)
                                : Colors.grey[200]!.withValues(alpha: 0.4),
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      ),
                    ),
                  
                  // محتوى الخيار
                  Row(
                    children: [
                      // أيقونة التصويت
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isSelected 
                                ? Colors.blue[600]! 
                                : Colors.grey[400]!,
                            width: 2,
                          ),
                          color: isSelected 
                              ? Colors.blue[600] 
                              : Colors.transparent,
                        ),
                        child: isSelected
                            ? const Icon(
                                Icons.check,
                                size: 14,
                                color: Colors.white,
                              )
                            : canVote
                                ? Icon(
                                    Icons.radio_button_unchecked,
                                    size: 14,
                                    color: Colors.grey[400],
                                  )
                                : null,
                      ),
                      
                      const SizedBox(width: 16),
                      
                      // نص الخيار
                      Expanded(
                        child: Text(
                          option.text,
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: isSelected 
                                ? FontWeight.w600 
                                : FontWeight.normal,
                            color: canVote 
                                ? Colors.black87 
                                : Colors.grey[700],
                          ),
                        ),
                      ),
                      
                      // النسبة المئوية والأصوات
                      if (hasVoted)
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: isSelected ? Colors.blue[100] : Colors.grey[100],
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                '${percentage.toStringAsFixed(1)}%',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                  color: isSelected 
                                      ? Colors.blue[700] 
                                      : Colors.grey[700],
                                ),
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${option.votes} صوت',
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.grey[600],
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPollInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          // إجمالي الأصوات
          Expanded(
            child: Row(
              children: [
                Icon(Icons.how_to_vote, size: 18, color: Colors.blue[600]),
                const SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إجمالي الأصوات',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      '${widget.poll.totalVotes}',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue[700],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // نوع التصويت
          Expanded(
            child: Row(
              children: [
                Icon(widget.poll.type.icon, size: 18, color: Colors.green[600]),
                const SizedBox(width: 8),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'النوع',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey[600],
                      ),
                    ),
                    Text(
                      widget.poll.type.arabicName,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Colors.green[700],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // الوقت المتبقي
          Expanded(
            child: Row(
              children: [
                if (!widget.poll.isExpired && widget.poll.expiresAt != null) ...[
                  Icon(Icons.timer, size: 18, color: Colors.orange[600]),
                  const SizedBox(width: 8),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الوقت المتبقي',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        widget.poll.formattedTimeRemaining,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.orange[700],
                        ),
                      ),
                    ],
                  ),
                ] else if (widget.poll.isExpired) ...[
                  Icon(Icons.timer_off, size: 18, color: Colors.red[600]),
                  const SizedBox(width: 8),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الحالة',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        'انتهى',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.red[700],
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  Icon(Icons.all_inclusive, size: 18, color: Colors.green[600]),
                  const SizedBox(width: 8),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'الحالة',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.grey[600],
                        ),
                      ),
                      Text(
                        'مفتوح',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Colors.green[700],
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    final hasVoted = widget.poll.userVote != null;
    final canVote = widget.poll.canVote;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          // أزرار التصويت الرئيسية
          if (canVote && !hasVoted)
            Container(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () => _showVoteOptions(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.blue[600],
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                icon: const Icon(Icons.how_to_vote, size: 20),
                label: const Text(
                  'تصويت الآن',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ),
            ),
          
          // زر إعادة التصويت
          if (hasVoted && widget.poll.allowRevote && canVote)
            Container(
              width: double.infinity,
              margin: const EdgeInsets.only(top: 8),
              child: OutlinedButton.icon(
                onPressed: _loading ? null : () => _showRevoteDialog(context),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.orange[600],
                  side: BorderSide(color: Colors.orange[600]!),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                icon: _loading
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2)
                      )
                    : const Icon(Icons.refresh, size: 20),
                label: Text(
                  _loading ? 'جاري...' : 'إعادة التصويت',
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ),
            ),
          
          // أزرار إضافية
          const SizedBox(height: 12),
          Row(
            children: [
              // زر التعليقات
              if (widget.poll.allowComments)
                Expanded(
                  child: TextButton.icon(
                    onPressed: () => _openComments(context),
                    style: TextButton.styleFrom(
                      foregroundColor: Colors.grey[700],
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                    icon: Icon(Icons.comment_outlined, size: 18, color: Colors.grey[600]),
                    label: Text(
                      'تعليق',
                      style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    ),
                  ),
                ),

              // زر المشاركة
              Expanded(
                child: TextButton.icon(
                  onPressed: () => _sharePoll(),
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.grey[700],
                    padding: const EdgeInsets.symmetric(vertical: 8),
                  ),
                  icon: Icon(Icons.share_outlined, size: 18, color: Colors.grey[600]),
                  label: Text(
                    'مشاركة',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // عرض خيارات التصويت
  void _showVoteOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // مقبض السحب
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 20),
            
            // العنوان
            Text(
              'اختر خيارك',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              widget.poll.question,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            
            // خيارات التصويت
            ...widget.poll.options.map((option) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: InkWell(
                onTap: () {
                  Navigator.pop(context);
                  widget.onVote(widget.poll.id, option.id);
                },
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.radio_button_unchecked, color: Colors.grey[400]),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          option.text,
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            )).toList(),
            
            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  // فتح صفحة التعليقات
  void _openComments(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PollCommentsPage(poll: widget.poll),
      ),
    );
  }

  // مشاركة التصويت
  void _sharePoll() {
    final shareText = '''
🗳️ ${widget.poll.question}

${widget.poll.options.map((option) => '• ${option.text}').join('\n')}

📊 إجمالي الأصوات: ${widget.poll.totalVotes}
🏷️ الفئة: ${widget.poll.category.arabicName}

شارك رأيك في تطبيق أرزاوو - قسم نبض
    ''';

    Share.share(shareText);
  }

  // إعادة التصويت
  void _showRevoteDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إعادة التصويت'),
        content: const Text(
          'هل تريد إلغاء تصويتك الحالي واختيار خيار جديد؟\n\n'
          'سيتم حذف تصويتك الحالي ويمكنك التصويت مرة أخرى.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performRevote();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.blue[600]),
            child: const Text('تأكيد', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  // تنفيذ إعادة التصويت
  Future<void> _performRevote() async {
    setState(() => _loading = true);
    try {
      print('🔄 بدء تنفيذ إعادة التصويت في الواجهة');
      
      await _pollService.revote(widget.poll.id);
      
      print('✅ تم إعادة التصويت بنجاح في الواجهة');
      
      if (widget.onRefresh != null) {
        print('🔄 تحديث الواجهة...');
        widget.onRefresh!();
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إلغاء تصويتك. يمكنك الآن اختيار خيار جديد'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print('❌ خطأ في إعادة التصويت في الواجهة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إعادة التصويت: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }

  // التعامل مع إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'settings':
        _openSettings();
        break;
      case 'delete':
        _confirmDelete();
        break;
    }
  }

  // فتح إعدادات التصويت
  void _openSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PollSettingsPage(poll: widget.poll),
      ),
    ).then((result) {
      if (result == true && widget.onRefresh != null) {
        widget.onRefresh!();
      }
    });
  }

  // تأكيد حذف التصويت
  void _confirmDelete() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف التصويت'),
        content: const Text(
          'هل أنت متأكد من حذف هذا التصويت؟\n\n'
          'سيتم حذف جميع الأصوات والتعليقات المرتبطة به نهائياً.'
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deletePoll();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red[600]),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );
  }

  // حذف التصويت
  Future<void> _deletePoll() async {
    setState(() => _loading = true);
    try {
      await _pollService.deletePoll(widget.poll.id);
      if (widget.onRefresh != null) {
        widget.onRefresh!();
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف التصويت بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف التصويت: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }

  // التحقق من كون المستخدم هو مؤلف التصويت
  bool _isAuthor() {
    final currentUserId = Supabase.instance.client.auth.currentUser?.id;
    return currentUserId != null && currentUserId == widget.poll.userId;
  }

  // تنسيق الوقت
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
