# 🕌 إعداد قسم الصدقات - تطبيق أرزاوو

## 📋 خطوات الإعداد

### 1. إنشاء الجداول في Supabase

1. افتح لوحة تحكم Supabase
2. اذهب إلى **SQL Editor**
3. انسخ والصق محتوى ملف `charity_tables.sql`
4. اضغط **Run** لتنفيذ الاستعلامات

### 2. إعداد Storage للصور

1. اذهب إلى **Storage** في Supabase
2. أنشئ bucket جديد باسم `charity-images`
3. اجعله **Public** للقراءة
4. أضف سياسات الرفع:

```sql
-- سياسة رفع الصور للمستخدمين المسجلين
CREATE POLICY "Authenticated users can upload charity images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'charity-images' AND 
        auth.role() = 'authenticated'
    );

-- سياسة قراءة الصور للجميع
CREATE POLICY "Anyone can view charity images" ON storage.objects
    FOR SELECT USING (bucket_id = 'charity-images');
```

### 3. اختبار النظام

#### إدراج بيانات تجريبية:
```sql
-- إدراج تبرع تجريبي
INSERT INTO charity_items (
    user_id, title, description, type, category, condition, 
    delivery_method, city, phone_number, is_urgent, is_anonymous
) VALUES (
    auth.uid(),
    'ملابس شتوية للأطفال',
    'مجموعة ملابس شتوية جديدة للأطفال من عمر 5-10 سنوات',
    'donation',
    'clothes',
    'new_item',
    'hand',
    'الرياض',
    '0501234567',
    false,
    false
);
```

## 🔐 الأمان والصلاحيات

### سياسات RLS المطبقة:

1. **charity_items:**
   - ✅ قراءة: الجميع (العناصر النشطة فقط)
   - ✅ إدراج: المستخدمون المسجلون
   - ✅ تحديث: المالك فقط
   - ✅ حذف: المالك فقط

2. **charity_interests:**
   - ✅ قراءة: المالك والمهتم
   - ✅ إدراج: المستخدمون المسجلون
   - ✅ حذف: المهتم فقط

3. **charity_reports:**
   - ✅ إدراج: المستخدمون المسجلون
   - ✅ قراءة: المبلغ فقط

## 📊 استعلامات مفيدة

### جلب التبرعات:
```sql
SELECT ci.*, p.full_name, p.avatar_url, p.is_verified
FROM charity_items ci
LEFT JOIN profiles p ON ci.user_id = p.id
WHERE ci.type = 'donation' 
  AND ci.is_active = true 
  AND ci.is_completed = false
ORDER BY ci.created_at DESC;
```

### جلب الحالات الطارئة:
```sql
SELECT ci.*, p.full_name, p.avatar_url, p.is_verified
FROM charity_items ci
LEFT JOIN profiles p ON ci.user_id = p.id
WHERE ci.is_urgent = true 
  AND ci.is_active = true 
  AND ci.is_completed = false
ORDER BY ci.created_at DESC;
```

### إحصائيات الصدقات:
```sql
SELECT 
    COUNT(*) FILTER (WHERE is_completed = true) as completed_count,
    COUNT(DISTINCT user_id) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as active_users_week,
    COUNT(*) FILTER (WHERE created_at >= NOW() - INTERVAL '7 days') as items_this_week
FROM charity_items;
```

## 🚀 الميزات المتاحة

### ✅ للمستخدمين العاديين:
- عرض جميع التبرعات والطلبات
- إبداء الاهتمام بالعناصر
- إضافة تبرعات أو طلبات مساعدة
- تصفية حسب المدينة والفئة
- الإبلاغ عن المحتوى المخالف

### ✅ لأصحاب العناصر:
- تحديث معلومات العنصر
- وضع علامة "تم الإكمال"
- حذف العنصر
- رؤية قائمة المهتمين

### ✅ الحماية والأمان:
- إخفاء الهوية (متبرع كريم)
- التحقق من المستخدمين الموثقين
- نظام الإبلاغ عن المخالفات
- حماية البيانات الشخصية

## ⚠️ ملاحظات مهمة

1. **تأكد من وجود جدول `profiles`** مع الحقول المطلوبة
2. **فعّل RLS** على جميع الجداول
3. **أنشئ bucket التخزين** للصور
4. **اختبر الصلاحيات** قبل النشر
5. **راقب الأداء** مع زيادة البيانات

## 📞 الدعم الفني

في حالة وجود مشاكل:
1. تحقق من logs في Supabase
2. تأكد من صحة سياسات RLS  
3. راجع صلاحيات المستخدمين
4. تحقق من إعدادات Storage
