// نموذج بيانات ملف الزواج الشرعي
class MarriageProfile {
  final String id;
  final String userId;
  final String name; // اسم أو اسم مستعار
  final Gender gender;
  final int age;
  final String city;
  final String country;
  final String profession;
  final MaritalStatus maritalStatus;
  final String description;
  final MarriageGoal goal;
  final String desiredPartnerSpecs;
  final String? profileImageUrl;
  final bool hideImageUntilApproval;
  final bool hideNameUntilApproval;
  final Map<String, String> contactMethods; // email, whatsapp, etc.
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool isActive;
  final bool isVerified;
  final int receivedRequestsCount;
  final int sentRequestsCount;

  MarriageProfile({
    required this.id,
    required this.userId,
    required this.name,
    required this.gender,
    required this.age,
    required this.city,
    required this.country,
    required this.profession,
    required this.maritalStatus,
    required this.description,
    required this.goal,
    required this.desiredPartnerSpecs,
    this.profileImageUrl,
    this.hideImageUntilApproval = true,
    this.hideNameUntilApproval = true,
    this.contactMethods = const {},
    required this.createdAt,
    this.updatedAt,
    this.isActive = true,
    this.isVerified = false,
    this.receivedRequestsCount = 0,
    this.sentRequestsCount = 0,
  });

  factory MarriageProfile.fromJson(Map<String, dynamic> json) {
    return MarriageProfile(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      name: json['name'] ?? '',
      gender: Gender.values.firstWhere(
        (e) => e.name == json['gender'],
        orElse: () => Gender.male,
      ),
      age: json['age'] ?? 0,
      city: json['city'] ?? '',
      country: json['country'] ?? '',
      profession: json['profession'] ?? '',
      maritalStatus: MaritalStatus.values.firstWhere(
        (e) => e.name == json['marital_status'],
        orElse: () => MaritalStatus.single,
      ),
      description: json['description'] ?? '',
      goal: MarriageGoal.values.firstWhere(
        (e) => e.name == json['goal'],
        orElse: () => MarriageGoal.marriage,
      ),
      desiredPartnerSpecs: json['desired_partner_specs'] ?? '',
      profileImageUrl: json['profile_image_url'],
      hideImageUntilApproval: json['hide_image_until_approval'] ?? true,
      hideNameUntilApproval: json['hide_name_until_approval'] ?? true,
      contactMethods: Map<String, String>.from(json['contact_methods'] ?? {}),
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      isActive: json['is_active'] ?? true,
      isVerified: json['is_verified'] ?? false,
      receivedRequestsCount: json['received_requests_count'] ?? 0,
      sentRequestsCount: json['sent_requests_count'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'name': name,
      'gender': gender.name,
      'age': age,
      'city': city,
      'country': country,
      'profession': profession,
      'marital_status': maritalStatus.name,
      'description': description,
      'goal': goal.name,
      'desired_partner_specs': desiredPartnerSpecs,
      'profile_image_url': profileImageUrl,
      'hide_image_until_approval': hideImageUntilApproval,
      'hide_name_until_approval': hideNameUntilApproval,
      'contact_methods': contactMethods,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'is_active': isActive,
      'is_verified': isVerified,
      'received_requests_count': receivedRequestsCount,
      'sent_requests_count': sentRequestsCount,
    };
  }

  MarriageProfile copyWith({
    String? name,
    int? age,
    String? city,
    String? country,
    String? profession,
    MaritalStatus? maritalStatus,
    String? description,
    MarriageGoal? goal,
    String? desiredPartnerSpecs,
    String? profileImageUrl,
    bool? hideImageUntilApproval,
    bool? hideNameUntilApproval,
    Map<String, String>? contactMethods,
    bool? isActive,
  }) {
    return MarriageProfile(
      id: id,
      userId: userId,
      name: name ?? this.name,
      gender: gender,
      age: age ?? this.age,
      city: city ?? this.city,
      country: country ?? this.country,
      profession: profession ?? this.profession,
      maritalStatus: maritalStatus ?? this.maritalStatus,
      description: description ?? this.description,
      goal: goal ?? this.goal,
      desiredPartnerSpecs: desiredPartnerSpecs ?? this.desiredPartnerSpecs,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      hideImageUntilApproval: hideImageUntilApproval ?? this.hideImageUntilApproval,
      hideNameUntilApproval: hideNameUntilApproval ?? this.hideNameUntilApproval,
      contactMethods: contactMethods ?? this.contactMethods,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
      isActive: isActive ?? this.isActive,
      isVerified: isVerified,
      receivedRequestsCount: receivedRequestsCount,
      sentRequestsCount: sentRequestsCount,
    );
  }

  // الحصول على النص المناسب للجنس
  String get genderText {
    switch (gender) {
      case Gender.male:
        return 'ذكر';
      case Gender.female:
        return 'أنثى';
    }
  }

  // الحصول على النص المناسب للحالة الاجتماعية
  String get maritalStatusText {
    switch (maritalStatus) {
      case MaritalStatus.single:
        return gender == Gender.male ? 'أعزب' : 'عزباء';
      case MaritalStatus.divorced:
        return gender == Gender.male ? 'مطلق' : 'مطلقة';
      case MaritalStatus.widowed:
        return gender == Gender.male ? 'أرمل' : 'أرملة';
    }
  }

  // الحصول على النص المناسب للهدف
  String get goalText {
    switch (goal) {
      case MarriageGoal.marriage:
        return 'زواج';
      case MarriageGoal.engagement:
        return 'خطبة';
      case MarriageGoal.seriousRelationship:
        return 'تعارف بنية الزواج';
    }
  }

  // الحصول على الاسم المناسب للعرض
  String getDisplayName(bool hasApproval) {
    if (hideNameUntilApproval && !hasApproval) {
      return '${genderText} من ${city}';
    }
    return name;
  }

  // الحصول على صورة الملف الشخصي
  String? getDisplayImage(bool hasApproval) {
    if (hideImageUntilApproval && !hasApproval) {
      return null;
    }
    return profileImageUrl;
  }
}

// الجنس
enum Gender {
  male,
  female,
}

// الحالة الاجتماعية
enum MaritalStatus {
  single,
  divorced,
  widowed,
}

// الهدف من التسجيل
enum MarriageGoal {
  marriage,
  engagement,
  seriousRelationship,
}

// نموذج طلب التواصل
class ContactRequest {
  final String id;
  final String senderId;
  final String receiverId;
  final String? message;
  final DateTime sentAt;
  final ContactRequestStatus status;
  final DateTime? respondedAt;

  ContactRequest({
    required this.id,
    required this.senderId,
    required this.receiverId,
    this.message,
    required this.sentAt,
    this.status = ContactRequestStatus.pending,
    this.respondedAt,
  });

  factory ContactRequest.fromJson(Map<String, dynamic> json) {
    return ContactRequest(
      id: json['id'] ?? '',
      senderId: json['sender_id'] ?? '',
      receiverId: json['receiver_id'] ?? '',
      message: json['message'],
      sentAt: DateTime.parse(json['sent_at']),
      status: ContactRequestStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ContactRequestStatus.pending,
      ),
      respondedAt: json['responded_at'] != null ? DateTime.parse(json['responded_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'sender_id': senderId,
      'receiver_id': receiverId,
      'message': message,
      'sent_at': sentAt.toIso8601String(),
      'status': status.name,
      'responded_at': respondedAt?.toIso8601String(),
    };
  }

  String get statusText {
    switch (status) {
      case ContactRequestStatus.pending:
        return 'قيد الانتظار';
      case ContactRequestStatus.accepted:
        return 'مقبول';
      case ContactRequestStatus.rejected:
        return 'مرفوض';
    }
  }
}

// حالات طلب التواصل
enum ContactRequestStatus {
  pending,
  accepted,
  rejected,
}

// نموذج الإبلاغ
class MarriageReport {
  final String id;
  final String reporterId;
  final String reportedProfileId;
  final String reason;
  final String? details;
  final DateTime createdAt;

  MarriageReport({
    required this.id,
    required this.reporterId,
    required this.reportedProfileId,
    required this.reason,
    this.details,
    required this.createdAt,
  });

  factory MarriageReport.fromJson(Map<String, dynamic> json) {
    return MarriageReport(
      id: json['id'] ?? '',
      reporterId: json['reporter_id'] ?? '',
      reportedProfileId: json['reported_profile_id'] ?? '',
      reason: json['reason'] ?? '',
      details: json['details'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'reporter_id': reporterId,
      'reported_profile_id': reportedProfileId,
      'reason': reason,
      'details': details,
      'created_at': createdAt.toIso8601String(),
    };
  }
}
