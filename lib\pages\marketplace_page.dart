import 'package:flutter/material.dart';
import '../models/marketplace_product.dart';
import '../models/product_category.dart';
import '../services/marketplace_service.dart';
import '../widgets/marketplace_product_card.dart';
import '../widgets/marketplace_search_bar.dart';
import '../widgets/marketplace_category_grid.dart';
import '../widgets/marketplace_filters.dart';
import 'add_product_page.dart';

class MarketplacePage extends StatefulWidget {
  const MarketplacePage({super.key});

  @override
  State<MarketplacePage> createState() => _MarketplacePageState();
}

class _MarketplacePageState extends State<MarketplacePage>
    with SingleTickerProviderStateMixin {
  final MarketplaceService _marketplaceService = MarketplaceService();
  final ScrollController _scrollController = ScrollController();
  
  late TabController _tabController;
  
  List<ProductCategory> _categories = [];
  List<MarketplaceProduct> _products = [];
  List<MarketplaceProduct> _featuredProducts = [];
  
  bool _isLoading = true;
  bool _isLoadingMore = false;
  String _searchQuery = '';
  String? _selectedCategoryId;
  String? _selectedCity;
  double? _minPrice;
  double? _maxPrice;
  String? _selectedCondition;
  String _sortBy = 'created_at';
  int _currentPage = 0;
  final int _pageSize = 20;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _scrollController.addListener(_onScroll);
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreProducts();
    }
  }

  Future<void> _loadInitialData() async {
    try {
      setState(() => _isLoading = true);
      
      final categories = await _marketplaceService.getMainCategoriesWithSubs();
      final products = await _marketplaceService.getProductsWithDetails(
        limit: _pageSize,
      );
      
      setState(() {
        _categories = categories;
        _products = products;
        _isLoading = false;
        _currentPage = 0;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showError('خطأ في تحميل البيانات: $e');
    }
  }

  Future<void> _loadMoreProducts() async {
    if (_isLoadingMore) return;
    
    try {
      setState(() => _isLoadingMore = true);
      
      final newProducts = await _marketplaceService.searchProducts(
        searchQuery: _searchQuery,
        categoryId: _selectedCategoryId,
        city: _selectedCity,
        minPrice: _minPrice,
        maxPrice: _maxPrice,
        condition: _selectedCondition,
        sortBy: _sortBy,
        limit: _pageSize,
        offset: (_currentPage + 1) * _pageSize,
      );
      
      if (newProducts.isNotEmpty) {
        setState(() {
          _products.addAll(newProducts);
          _currentPage++;
        });
      }
    } catch (e) {
      _showError('خطأ في تحميل المزيد: $e');
    } finally {
      setState(() => _isLoadingMore = false);
    }
  }

  Future<void> _searchProducts() async {
    try {
      setState(() => _isLoading = true);
      
      final products = await _marketplaceService.searchProducts(
        searchQuery: _searchQuery,
        categoryId: _selectedCategoryId,
        city: _selectedCity,
        minPrice: _minPrice,
        maxPrice: _maxPrice,
        condition: _selectedCondition,
        sortBy: _sortBy,
        limit: _pageSize,
      );
      
      setState(() {
        _products = products;
        _currentPage = 0;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showError('خطأ في البحث: $e');
    }
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _showFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => MarketplaceFilters(
        categories: _categories,
        selectedCategoryId: _selectedCategoryId,
        selectedCity: _selectedCity,
        minPrice: _minPrice,
        maxPrice: _maxPrice,
        selectedCondition: _selectedCondition,
        sortBy: _sortBy,
        onApply: (filters) {
          setState(() {
            _selectedCategoryId = filters['categoryId'];
            _selectedCity = filters['city'];
            _minPrice = filters['minPrice'];
            _maxPrice = filters['maxPrice'];
            _selectedCondition = filters['condition'];
            _sortBy = filters['sortBy'] ?? 'created_at';
          });
          _searchProducts();
        },
        onReset: () {
          setState(() {
            _selectedCategoryId = null;
            _selectedCity = null;
            _minPrice = null;
            _maxPrice = null;
            _selectedCondition = null;
            _sortBy = 'created_at';
          });
          _searchProducts();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'السوق',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.orange[600],
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'الرئيسية'),
            Tab(text: 'الفئات'),
            Tab(text: 'المفضلة'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilters,
            tooltip: 'الفلاتر',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث
          Container(
            color: Colors.orange[600],
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: MarketplaceSearchBar(
              onSearch: (query) {
                setState(() => _searchQuery = query);
                _searchProducts();
              },
              onFilterTap: _showFilters,
            ),
          ),
          
          // المحتوى
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildHomeTab(),
                _buildCategoriesTab(),
                _buildFavoritesTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (_) => const AddProductPage()),
          ).then((_) => _loadInitialData());
        },
        backgroundColor: Colors.orange[600],
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text(
          'إضافة منتج',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _buildHomeTab() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_products.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.shopping_bag_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد منتجات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'كن أول من يضيف منتج!',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadInitialData,
      child: ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.all(16),
        itemCount: _products.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _products.length) {
            return const Center(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: CircularProgressIndicator(),
              ),
            );
          }

          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: MarketplaceProductCard(
              product: _products[index],
              onTap: () {
                // Navigate to product details
              },
              onFavorite: () async {
                try {
                  final isFavorited = await _marketplaceService.toggleFavorite(_products[index].id);
                  setState(() {
                    _products[index] = _products[index].copyWith(isFavorited: isFavorited);
                  });
                } catch (e) {
                  _showError('خطأ في تحديث المفضلة: $e');
                }
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildCategoriesTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return MarketplaceCategoryGrid(
      categories: _categories,
      onCategoryTap: (category) {
        setState(() {
          _selectedCategoryId = category.id;
          _tabController.animateTo(0);
        });
        _searchProducts();
      },
    );
  }

  Widget _buildFavoritesTab() {
    return FutureBuilder<List<MarketplaceProduct>>(
      future: _marketplaceService.getUserFavorites(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Text('خطأ: ${snapshot.error}'),
          );
        }

        final favorites = snapshot.data ?? [];

        if (favorites.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.favorite_border,
                  size: 64,
                  color: Colors.grey[400],
                ),
                const SizedBox(height: 16),
                Text(
                  'لا توجد مفضلة',
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.grey[600],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: favorites.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: MarketplaceProductCard(
                product: favorites[index],
                onTap: () {
                  // Navigate to product details
                },
                onFavorite: () async {
                  try {
                    await _marketplaceService.toggleFavorite(favorites[index].id);
                    setState(() {}); // Refresh the favorites tab
                  } catch (e) {
                    _showError('خطأ في إزالة من المفضلة: $e');
                  }
                },
              ),
            );
          },
        );
      },
    );
  }
}
