import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../models/product.dart';
import '../models/comment.dart';
import '../models/reaction_type.dart';
import '../supabase_service.dart';
import 'dart:io';
import 'hierarchical_comment_widget.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class ProductCommentsSheet extends StatefulWidget {
  final Product product;
  final VoidCallback? onCommentAdded;

  const ProductCommentsSheet({super.key, required this.product, this.onCommentAdded});

  @override
  State<ProductCommentsSheet> createState() => _ProductCommentsSheetState();
}

class _ProductCommentsSheetState extends State<ProductCommentsSheet> {
  final TextEditingController _commentController = TextEditingController();
  String? _selectedMediaPath;
  CommentType _selectedMediaType = CommentType.text;
  late Stream<List<Comment>> _stream;
  final ImagePicker _picker = ImagePicker();
  final ScrollController _scrollController = ScrollController();
  bool _sending = false;
  List<Comment> _comments = [];
  Comment? _replyingTo;
  final FocusNode _textFieldFocus = FocusNode();

  @override
  void initState() {
    super.initState();
    _stream = SupabaseService().productCommentsStream(widget.product.id);

    _commentController.addListener(_onTextChanged);
  }

  List<Map<String, dynamic>> _following = [];

  void _onTextChanged() {
    final txt = _commentController.text;
    if (txt.isNotEmpty && txt.endsWith('@')) {
      _openMentionPicker();
    }
  }

  Future<void> _openMentionPicker() async {
    if (_following.isEmpty) {
      try {
        _following = await SupabaseService().fetchFollowingUsers();
      } catch (_) {}
    }
    if (!mounted) return;
    final selected = await showModalBottomSheet<Map<String, dynamic>>(
      context: context,
      builder: (ctx) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(width:40,height:4,margin:const EdgeInsets.symmetric(vertical:8),decoration:BoxDecoration(color:Colors.grey[300],borderRadius:BorderRadius.circular(2))),
              const Padding(
                padding: EdgeInsets.all(12.0),
                child: Text('إشارة إلى...', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
              ),
              Expanded(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: _following.length,
                  itemBuilder: (_, i) {
                    final u = _following[i];
                    return ListTile(
                      leading: CircleAvatar(backgroundImage: (u['avatar_url']??'').toString().isNotEmpty?NetworkImage(u['avatar_url']):null,child:(u['avatar_url']??'').toString().isEmpty?Text((u['name']??'')[0]):null),
                      title: Text(u['name']??''),
                      onTap: () => Navigator.pop(ctx, u),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
    if (selected != null) {
      final name = selected['name'] ?? '';
      final txt = _commentController.text;
      final idx = txt.lastIndexOf('@');
      final newText = txt.substring(0, idx + 1) + name + ' ';
      _commentController.text = newText;
      _commentController.selection = TextSelection.collapsed(offset: newText.length);
    } else {
      final txt2 = _commentController.text;
      if (txt2.endsWith('@')) {
        _commentController.text = txt2.substring(0, txt2.length - 1);
        _commentController.selection = TextSelection.collapsed(offset: _commentController.text.length);
      }
    }
  }

  @override
  void dispose() {
    _commentController.removeListener(_onTextChanged);
    _commentController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _pickMedia(bool isVideo) async {
    final XFile? file = isVideo
        ? await _picker.pickVideo(source: ImageSource.gallery)
        : await _picker.pickImage(source: ImageSource.gallery);

    if (file != null) {
      setState(() {
        _selectedMediaPath = file.path;
        _selectedMediaType = isVideo ? CommentType.video : CommentType.image;
      });
    }
  }

  void _submitComment() {
    if (_commentController.text.isNotEmpty || _selectedMediaPath != null) {
      _addComment();
    }
  }

  Future<void> _addComment() async {
    if (_sending) return;
    setState(() => _sending = true);
    String? mediaUrl;

    if (_selectedMediaPath != null) {
      final bytes = await File(_selectedMediaPath!).readAsBytes();
      final ext = _selectedMediaPath!.split('.').last;
      final storagePath = 'product_comments/${widget.product.id}/${DateTime.now().millisecondsSinceEpoch}.$ext';
      mediaUrl = await SupabaseService().uploadMedia(bytes, storagePath);
    }

    try {
      final uid = Supabase.instance.client.auth.currentUser!.id;
      await SupabaseService().createProductComment(
        productId: widget.product.id,
        parentId: _replyingTo?.id,
        content: _commentController.text,
        type: _selectedMediaType,
        mediaUrl: mediaUrl,
      );

      // تعليق تفاؤلي فى الواجهة
      final Comment newComment = Comment(
        id: 'temp_${DateTime.now().microsecondsSinceEpoch}',
        postId: widget.product.id,
        userId: uid,
        userName: 'أنت',
        userAvatar: '',
        content: _commentController.text,
        type: _selectedMediaType,
        mediaUrl: mediaUrl,
        createdAt: DateTime.now(),
        reactionCounts: const {},
        currentUserReaction: ReactionType.none,
        replies: const [],
        depth: _replyingTo!=null? _replyingTo!.depth+1 : 0,
        replyToUserName: _replyingTo?.userName,
      );
      setState(()=> _comments.insert(0, newComment));
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('فشل نشر التعليق: $e')));
      }
    }

    _commentController.clear();
    setState(() {
      _selectedMediaPath = null;
      _selectedMediaType = CommentType.text;
      _replyingTo = null;
    });

    if (mounted) setState(() => _sending = false);

    if (widget.onCommentAdded != null) widget.onCommentAdded!();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        children: [
          Expanded(
            child: StreamBuilder<List<Comment>>(
              stream: _stream,
              builder: (context, snapshot) {
                if (!snapshot.hasData) {
                  return const Center(child: CircularProgressIndicator());
                }
                _comments = snapshot.data!;
                if (_comments.isEmpty) {
                  return const Center(child: Text('لا توجد تعليقات'));
                }
                return ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(8),
                  itemCount: _comments.length,
                  itemBuilder: (ctx, i) => HierarchicalCommentWidget(
                    comment: _comments[i],
                    onReply: (c) {
                      setState(() => _replyingTo = c);
                      _textFieldFocus.requestFocus();
                    },
                    onReaction: (c, reaction) {
                      // تحديث فورى للواجهة
                      setState(() {
                        final idx = _comments.indexWhere((cm)=>cm.id==c.id);
                        if(idx!=-1){
                          final counts = Map<ReactionType,int>.from(_comments[idx].reactionCounts);
                          counts[reaction] = (counts[reaction]??0)+1;
                          _comments[idx] = _comments[idx].copyWith(
                            reactionCounts: counts,
                            currentUserReaction: reaction,
                          );
                        }
                      });
                      SupabaseService().toggleProductCommentReaction(commentId: c.id, type: reaction);
                    },
                  ),
                );
              },
            ),
          ),
          if (_selectedMediaPath != null)
            Container(
              height: 100,
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(color: Colors.grey[200], borderRadius: BorderRadius.circular(10)),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  if (_selectedMediaType == CommentType.image)
                    Image.file(File(_selectedMediaPath!), fit: BoxFit.cover)
                  else
                    const Icon(Icons.video_file, size: 50),
                  Positioned(
                    top: 4,
                    right: 4,
                    child: IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => setState(() {
                        _selectedMediaPath = null;
                        _selectedMediaType = CommentType.text;
                      }),
                    ),
                  ),
                ],
              ),
            ),
          SafeArea(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(color: Colors.white, boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 4)]),
              child: Row(
                children: [
                  const CircleAvatar(backgroundColor: Colors.blue, child: Text('م')),
                  const SizedBox(width: 8),
                  Expanded(
                    child: TextField(
                      controller: _commentController,
                      focusNode: _textFieldFocus,
                      decoration: InputDecoration(
                        hintText: 'اكتب تعليقاً...',
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(20), borderSide: BorderSide.none),
                        filled: true,
                        fillColor: Colors.grey[100],
                        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      ),
                      maxLines: null,
                    ),
                  ),
                  IconButton(icon: const Icon(Icons.image, color: Colors.blue), onPressed: () => _pickMedia(false)),
                  IconButton(icon: const Icon(Icons.videocam, color: Colors.blue), onPressed: () => _pickMedia(true)),
                  IconButton(icon: const Icon(Icons.send, color: Colors.blue), onPressed: _sending ? null : _submitComment),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
} 