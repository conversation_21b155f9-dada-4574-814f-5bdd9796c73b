-- =============================================================
--  تشخيص منشورات المجتمع
--  Debug Community Posts
-- =============================================================

-- 1) فحص جدول posts للمنشورات التي لها community_id
-- -------------------------------------------------------

SELECT 
  '🔍 POSTS WITH COMMUNITY_ID' as check_type,
  COUNT(*) as total_posts,
  COUNT(DISTINCT community_id) as communities_with_posts
FROM posts 
WHERE community_id IS NOT NULL;

-- 2) عرض منشورات المجتمع الموجودة
-- -------------------------------------------------------

SELECT 
  id,
  community_id,
  user_id,
  content,
  type,
  created_at,
  likes_count,
  comments_count
FROM posts 
WHERE community_id IS NOT NULL
ORDER BY created_at DESC
LIMIT 10;

-- 3) فحص المجتمعات الموجودة
-- -------------------------------------------------------

SELECT 
  '🔍 COMMUNITIES' as check_type,
  COUNT(*) as total_communities
FROM communities;

-- 4) عرض المجتمعات مع عدد المنشورات
-- -------------------------------------------------------

SELECT 
  c.id,
  c.name,
  COUNT(p.id) as posts_count
FROM communities c
LEFT JOIN posts p ON p.community_id = c.id
GROUP BY c.id, c.name
ORDER BY posts_count DESC;

-- 5) اختبار استعلام fetchCommunityPosts
-- -------------------------------------------------------

-- استعلام مشابه لما يستخدمه التطبيق
SELECT 
  p.id,
  p.user_id,
  p.content,
  p.created_at,
  p.type,
  p.media_url,
  p.link_url,
  p.link_meta,
  p.bg_color,
  p.posts_privacy,
  p.likes_count,
  p.dislikes_count,
  p.shares_count,
  p.comments_count,
  p.views_count,
  p.copies_count,
  p.shared_post_id,
  p.community_id,
  pr.name as profile_name,
  pr.avatar_url as profile_avatar
FROM posts p
LEFT JOIN profiles pr ON pr.id = p.user_id
WHERE p.community_id = (
  SELECT id FROM communities LIMIT 1
)
ORDER BY p.created_at DESC;

-- 6) فحص الصلاحيات على جدول posts
-- -------------------------------------------------------

SELECT 
  '🔍 POSTS TABLE RLS' as check_type,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM pg_tables 
      WHERE tablename = 'posts' 
      AND rowsecurity = false
    )
    THEN '✅ RLS DISABLED'
    ELSE '⚠️ RLS ENABLED'
  END as rls_status;

-- 7) النتيجة النهائية
-- -------------------------------------------------------

SELECT 
  '🎯 DIAGNOSIS RESULT' as check_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM posts WHERE community_id IS NOT NULL)
    THEN '✅ Community posts exist in database'
    ELSE '❌ No community posts found'
  END as status;

-- =============================================================
--  تعليمات التشخيص
-- =============================================================

/*

بعد تشغيل هذا السكريپت:

1. تحقق من وجود منشورات مع community_id
2. تحقق من أن الاستعلام يعمل بشكل صحيح
3. تحقق من صلاحيات RLS

إذا كانت المنشورات موجودة لكن لا تظهر في التطبيق،
فالمشكلة في كود fetchCommunityPosts

*/

-- =============================================================
--  انتهى تشخيص منشورات المجتمع
-- =============================================================
