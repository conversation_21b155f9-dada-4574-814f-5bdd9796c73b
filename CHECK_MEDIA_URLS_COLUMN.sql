-- التحقق من عمود media_urls في جدول posts
-- Check media_urls column in posts table

-- 1. التحقق من وجود عمود media_urls
SELECT 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'posts' AND column_name = 'media_urls';

-- 2. إنشاء العمود إذا لم يكن موجوداً
DO $$ 
BEGIN 
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'posts' AND column_name = 'media_urls'
    ) THEN
        ALTER TABLE posts ADD COLUMN media_urls TEXT[];
        RAISE NOTICE 'تم إضافة عمود media_urls لجدول posts بنجاح';
    ELSE
        RAISE NOTICE 'عمود media_urls موجود بالفعل في جدول posts';
    END IF;
END $$;

-- 3. إنشاء فهرس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_posts_media_urls ON posts USING GIN (media_urls);

-- 4. عرض المنشورات الأخيرة مع media_urls
SELECT 
    id,
    content,
    type,
    media_url,
    media_urls,
    array_length(media_urls, 1) as media_count,
    created_at,
    user_id
FROM posts 
ORDER BY created_at DESC 
LIMIT 10;

-- 5. إنشاء منشور اختبار مع media_urls
DO $$
DECLARE
    test_user_id UUID;
    test_post_id UUID;
BEGIN
    -- الحصول على أول مستخدم
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    
    IF test_user_id IS NULL THEN
        RAISE EXCEPTION 'لا يوجد مستخدمين في النظام!';
    END IF;
    
    RAISE NOTICE 'معرف المستخدم للاختبار: %', test_user_id;
    
    -- إنشاء منشور اختبار مع media_urls
    INSERT INTO posts (
        user_id,
        content,
        type,
        media_urls,
        created_at
    ) VALUES (
        test_user_id,
        'منشور اختبار للصور المتعددة',
        'image',
        ARRAY['https://example.com/test1.jpg', 'https://example.com/test2.jpg', 'https://example.com/test3.jpg'],
        NOW()
    ) RETURNING id INTO test_post_id;
    
    RAISE NOTICE 'تم إنشاء منشور اختبار بنجاح. معرف المنشور: %', test_post_id;
    
    -- التحقق من المنشور المحدث
    IF EXISTS (
        SELECT 1 FROM posts 
        WHERE id = test_post_id 
        AND media_urls IS NOT NULL 
        AND array_length(media_urls, 1) = 3
    ) THEN
        RAISE NOTICE 'المنشور تم حفظه بشكل صحيح مع media_urls';
    ELSE
        RAISE EXCEPTION 'فشل في حفظ المنشور مع media_urls!';
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'خطأ في إنشاء منشور الاختبار: %', SQLERRM;
END $$;

-- 6. رسالة نجاح
SELECT 'تم التحقق من عمود media_urls بنجاح!' as result; 