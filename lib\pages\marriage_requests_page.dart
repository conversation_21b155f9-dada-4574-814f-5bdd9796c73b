import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/marriage_profile.dart';
import '../models/marriage_chat.dart';
import '../services/marriage_service.dart';
import '../services/marriage_chat_service.dart';
import 'marriage_chat_page.dart';

class MarriageRequestsPage extends StatefulWidget {
  const MarriageRequestsPage({super.key});

  @override
  State<MarriageRequestsPage> createState() => _MarriageRequestsPageState();
}

class _MarriageRequestsPageState extends State<MarriageRequestsPage> with TickerProviderStateMixin {
  late TabController _tabController;
  List<ContactRequest> _sentRequests = [];
  List<ContactRequest> _receivedRequests = [];
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadRequests();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadRequests() async {
    setState(() => _loading = true);
    try {
      final sent = await MarriageService().getSentRequests();
      final received = await MarriageService().getReceivedRequests();
      
      setState(() {
        _sentRequests = sent;
        _receivedRequests = received;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // رسالة ترحيبية
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16),
          margin: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.pink[600]!, Colors.pink[800]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.message, color: Colors.white, size: 24),
                  const SizedBox(width: 8),
                  Text(
                    'طلبات التواصل',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'إدارة طلبات التواصل المرسلة والمستلمة',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
        
        // التبويبات
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(25),
          ),
          child: TabBar(
            controller: _tabController,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.grey[600],
            indicator: BoxDecoration(
              color: Colors.pink[600],
              borderRadius: BorderRadius.circular(25),
            ),
            tabs: [
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.send, size: 18),
                    const SizedBox(width: 8),
                    Text('المرسلة (${_sentRequests.length})'),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.inbox, size: 18),
                    const SizedBox(width: 8),
                    Text('المستلمة (${_receivedRequests.length})'),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        // محتوى التبويبات
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildSentRequestsTab(),
              _buildReceivedRequestsTab(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSentRequestsTab() {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_sentRequests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.send_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لم ترسل أي طلبات تواصل بعد',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'تصفح الملفات وأرسل طلبات التواصل',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadRequests,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _sentRequests.length,
        itemBuilder: (context, index) {
          final request = _sentRequests[index];
          return _buildRequestCard(request, isSent: true);
        },
      ),
    );
  }

  Widget _buildReceivedRequestsTab() {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_receivedRequests.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.inbox_outlined, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'لم تستلم أي طلبات تواصل بعد',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              'عندما يرسل لك أحد طلب تواصل ستجده هنا',
              style: TextStyle(fontSize: 14, color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadRequests,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _receivedRequests.length,
        itemBuilder: (context, index) {
          final request = _receivedRequests[index];
          return _buildRequestCard(request, isSent: false);
        },
      ),
    );
  }

  Widget _buildRequestCard(ContactRequest request, {required bool isSent}) {
    Color statusColor;
    IconData statusIcon;
    
    switch (request.status) {
      case ContactRequestStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.schedule;
        break;
      case ContactRequestStatus.accepted:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case ContactRequestStatus.rejected:
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [Colors.pink[400]!, Colors.pink[600]!],
                    ),
                  ),
                  child: Icon(
                    isSent ? Icons.send : Icons.person,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                
                const SizedBox(width: 12),
                
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        isSent ? 'طلب مرسل' : 'طلب مستلم',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _formatDate(request.sentAt),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                
                // حالة الطلب
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(statusIcon, size: 14, color: statusColor),
                      const SizedBox(width: 4),
                      Text(
                        request.statusText,
                        style: TextStyle(
                          color: statusColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            // رسالة الطلب (إذا وجدت)
            if (request.message != null && request.message!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[200]!),
                ),
                child: Text(
                  request.message!,
                  style: TextStyle(
                    color: Colors.grey[700],
                    fontSize: 14,
                  ),
                ),
              ),
            ],
            
            // أزرار الإجراءات للطلبات المستلمة المعلقة
            if (!isSent && request.status == ContactRequestStatus.pending) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _respondToRequest(request.id, ContactRequestStatus.rejected),
                      icon: const Icon(Icons.close, size: 18),
                      label: const Text('رفض'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red[700],
                        side: BorderSide(color: Colors.red[300]!),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _respondToRequest(request.id, ContactRequestStatus.accepted),
                      icon: const Icon(Icons.check, size: 18),
                      label: const Text('قبول'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green[600],
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                ],
              ),
            ],
            
            // أزرار الإجراءات للطلبات المرسلة المعلقة
            if (isSent && request.status == ContactRequestStatus.pending) ...[
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () => _deleteRequest(request.id),
                  icon: const Icon(Icons.delete_outline, size: 18),
                  label: const Text('حذف الطلب'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red[700],
                    side: BorderSide(color: Colors.red[300]!),
                  ),
                ),
              ),
            ],

            // معلومات إضافية للطلبات المقبولة
            if (request.status == ContactRequestStatus.accepted) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(Icons.check_circle, color: Colors.green[600], size: 20),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            'تم قبول الطلب! يمكنكم الآن التواصل.',
                            style: TextStyle(
                              color: Colors.green[700],
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => _openChat(request.id),
                        icon: const Icon(Icons.chat, size: 18),
                        label: const Text('فتح المحادثة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green[600],
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],

            // معلومات إضافية للطلبات المرفوضة
            if (request.status == ContactRequestStatus.rejected) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.red[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.red[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info_outline, color: Colors.red[600], size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        isSent
                            ? 'تم رفض طلبك. يمكنك المحاولة مع أشخاص آخرين.'
                            : 'تم رفض هذا الطلب.',
                        style: TextStyle(
                          color: Colors.red[700],
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }

  Future<void> _respondToRequest(String requestId, ContactRequestStatus status) async {
    try {
      await MarriageService().respondToContactRequest(requestId, status);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(status == ContactRequestStatus.accepted
                ? 'تم قبول الطلب بنجاح'
                : 'تم رفض الطلب'),
            backgroundColor: status == ContactRequestStatus.accepted
                ? Colors.green
                : Colors.orange,
          ),
        );

        _loadRequests(); // إعادة تحميل الطلبات
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في الرد على الطلب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteRequest(String requestId) async {
    // تأكيد الحذف
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف طلب التواصل'),
        content: const Text('هل أنت متأكد من حذف هذا الطلب؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      await MarriageService().deleteContactRequest(requestId);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف طلب التواصل بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        _loadRequests(); // إعادة تحميل الطلبات
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في حذف الطلب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _openChat(String requestId) async {
    try {
      final chatService = MarriageChatService();

      // محاولة العثور على المحادثة الموجودة
      final chats = await chatService.getUserChats();
      MarriageChat? chat;

      try {
        chat = chats.firstWhere((chat) => chat.contactRequestId == requestId);
      } catch (e) {
        // المحادثة غير موجودة، سنحاول إنشاؤها
        chat = null;
      }

      // إذا لم تكن المحادثة موجودة، أنشئها
      if (chat == null) {
        chat = await _createChatForRequest(requestId);
      }

      if (chat != null && mounted) {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (_) => MarriageChatPage(
              chatId: chat!.id,
              otherUserName: chat.otherUserName ?? 'مستخدم',
              otherUserAvatar: chat.otherUserAvatar,
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في فتح المحادثة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<MarriageChat?> _createChatForRequest(String requestId) async {
    try {
      // الحصول على تفاصيل طلب التواصل
      ContactRequest? request;

      // البحث في الطلبات المستلمة
      for (var r in _receivedRequests) {
        if (r.id == requestId) {
          request = r;
          break;
        }
      }

      // البحث في الطلبات المرسلة إذا لم نجدها
      if (request == null) {
        for (var r in _sentRequests) {
          if (r.id == requestId) {
            request = r;
            break;
          }
        }
      }

      if (request == null) {
        throw Exception('طلب التواصل غير موجود');
      }

      // إنشاء المحادثة يدوياً
      final response = await Supabase.instance.client
          .from('marriage_chats')
          .insert({
            'contact_request_id': requestId,
            'user1_id': request.senderId,
            'user2_id': request.receiverId,
          })
          .select()
          .single();

      // إرسال رسالة ترحيبية
      await Supabase.instance.client
          .from('marriage_messages')
          .insert({
            'chat_id': response['id'],
            'sender_id': request.receiverId,
            'content': 'تم قبول طلب التواصل! يمكنكم الآن التحدث بحرية. نتمنى لكم التوفيق! 💕',
            'message_type': 'text',
          });

      // الحصول على معلومات المستخدم الآخر
      final currentUserId = Supabase.instance.client.auth.currentUser?.id;
      final otherUserId = currentUserId == request.senderId ? request.receiverId : request.senderId;

      final otherUserProfile = await Supabase.instance.client
          .from('marriage_profiles')
          .select('name, profile_image_url')
          .eq('user_id', otherUserId)
          .maybeSingle();

      response['other_user_name'] = otherUserProfile?['name'] ?? 'مستخدم';
      response['other_user_avatar'] = otherUserProfile?['profile_image_url'];

      return MarriageChat.fromJson(response);
    } catch (e) {
      return null;
    }
  }
}
