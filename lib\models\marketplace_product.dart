class MarketplaceProduct {
  final String id;
  final String userId;
  final String categoryId;
  final String title;
  final String description;
  final double price;
  final String currency;
  final bool isNegotiable;
  final bool isFree;
  final bool isExchange;
  final String city;
  final String country;
  final double? latitude;
  final double? longitude;
  final ProductCondition condition;
  final ProductStatus status;
  final List<String> images;
  final String? videoUrl;
  final bool phoneEnabled;
  final bool whatsappEnabled;
  final bool chatEnabled;
  final String? phoneNumber;
  final String? whatsappNumber;
  final int viewsCount;
  final int favoritesCount;
  final int contactCount;
  final Map<String, dynamic> customFields;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime expiresAt;

  // معلومات إضافية (يتم جلبها من joins)
  final String? userName;
  final String? userAvatar;
  final String? categoryName;
  final String? categoryIcon;
  final bool? isFavorited;
  final double? sellerRating;
  final int? sellerReviewsCount;

  const MarketplaceProduct({
    required this.id,
    required this.userId,
    required this.categoryId,
    required this.title,
    required this.description,
    required this.price,
    this.currency = 'MAD',
    this.isNegotiable = false,
    this.isFree = false,
    this.isExchange = false,
    required this.city,
    this.country = 'Morocco',
    this.latitude,
    this.longitude,
    this.condition = ProductCondition.used,
    this.status = ProductStatus.active,
    this.images = const [],
    this.videoUrl,
    this.phoneEnabled = true,
    this.whatsappEnabled = true,
    this.chatEnabled = true,
    this.phoneNumber,
    this.whatsappNumber,
    this.viewsCount = 0,
    this.favoritesCount = 0,
    this.contactCount = 0,
    this.customFields = const {},
    required this.createdAt,
    required this.updatedAt,
    required this.expiresAt,
    this.userName,
    this.userAvatar,
    this.categoryName,
    this.categoryIcon,
    this.isFavorited,
    this.sellerRating,
    this.sellerReviewsCount,
  });

  // تحويل من Map
  factory MarketplaceProduct.fromMap(Map<String, dynamic> map) {
    return MarketplaceProduct(
      id: map['id'] ?? '',
      userId: map['user_id'] ?? '',
      categoryId: map['category_id'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      price: (map['price'] ?? 0).toDouble(),
      currency: map['currency'] ?? 'MAD',
      isNegotiable: map['is_negotiable'] ?? false,
      isFree: map['is_free'] ?? false,
      isExchange: map['is_exchange'] ?? false,
      city: map['city'] ?? '',
      country: map['country'] ?? 'Morocco',
      latitude: map['latitude']?.toDouble(),
      longitude: map['longitude']?.toDouble(),
      condition: ProductCondition.values.firstWhere(
        (e) => e.name == map['condition'],
        orElse: () => ProductCondition.used,
      ),
      status: ProductStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => ProductStatus.active,
      ),
      images: List<String>.from(map['images'] ?? []),
      videoUrl: map['video_url'],
      phoneEnabled: map['phone_enabled'] ?? true,
      whatsappEnabled: map['whatsapp_enabled'] ?? true,
      chatEnabled: map['chat_enabled'] ?? true,
      phoneNumber: map['phone_number'],
      whatsappNumber: map['whatsapp_number'],
      viewsCount: map['views_count'] ?? 0,
      favoritesCount: map['favorites_count'] ?? 0,
      contactCount: map['contact_count'] ?? 0,
      customFields: Map<String, dynamic>.from(map['custom_fields'] ?? {}),
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
      expiresAt: DateTime.parse(map['expires_at']),
      userName: map['user_name'],
      userAvatar: map['user_avatar'],
      categoryName: map['category_name'],
      categoryIcon: map['category_icon'],
      isFavorited: map['is_favorited'],
      sellerRating: map['seller_rating']?.toDouble(),
      sellerReviewsCount: map['seller_reviews_count'],
    );
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'user_id': userId,
      'category_id': categoryId,
      'title': title,
      'description': description,
      'price': price,
      'currency': currency,
      'is_negotiable': isNegotiable,
      'is_free': isFree,
      'is_exchange': isExchange,
      'city': city,
      'country': country,
      'latitude': latitude,
      'longitude': longitude,
      'condition': condition.name,
      'status': status.name,
      'images': images,
      'video_url': videoUrl,
      'phone_enabled': phoneEnabled,
      'whatsapp_enabled': whatsappEnabled,
      'chat_enabled': chatEnabled,
      'phone_number': phoneNumber,
      'whatsapp_number': whatsappNumber,
      'views_count': viewsCount,
      'favorites_count': favoritesCount,
      'contact_count': contactCount,
      'custom_fields': customFields,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'expires_at': expiresAt.toIso8601String(),
    };
  }

  // نسخ مع تعديل
  MarketplaceProduct copyWith({
    String? id,
    String? userId,
    String? categoryId,
    String? title,
    String? description,
    double? price,
    String? currency,
    bool? isNegotiable,
    bool? isFree,
    bool? isExchange,
    String? city,
    String? country,
    double? latitude,
    double? longitude,
    ProductCondition? condition,
    ProductStatus? status,
    List<String>? images,
    String? videoUrl,
    bool? phoneEnabled,
    bool? whatsappEnabled,
    bool? chatEnabled,
    String? phoneNumber,
    String? whatsappNumber,
    int? viewsCount,
    int? favoritesCount,
    int? contactCount,
    Map<String, dynamic>? customFields,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? expiresAt,
    String? userName,
    String? userAvatar,
    String? categoryName,
    String? categoryIcon,
    bool? isFavorited,
    double? sellerRating,
    int? sellerReviewsCount,
  }) {
    return MarketplaceProduct(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      categoryId: categoryId ?? this.categoryId,
      title: title ?? this.title,
      description: description ?? this.description,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      isNegotiable: isNegotiable ?? this.isNegotiable,
      isFree: isFree ?? this.isFree,
      isExchange: isExchange ?? this.isExchange,
      city: city ?? this.city,
      country: country ?? this.country,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      condition: condition ?? this.condition,
      status: status ?? this.status,
      images: images ?? this.images,
      videoUrl: videoUrl ?? this.videoUrl,
      phoneEnabled: phoneEnabled ?? this.phoneEnabled,
      whatsappEnabled: whatsappEnabled ?? this.whatsappEnabled,
      chatEnabled: chatEnabled ?? this.chatEnabled,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      whatsappNumber: whatsappNumber ?? this.whatsappNumber,
      viewsCount: viewsCount ?? this.viewsCount,
      favoritesCount: favoritesCount ?? this.favoritesCount,
      contactCount: contactCount ?? this.contactCount,
      customFields: customFields ?? this.customFields,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      userName: userName ?? this.userName,
      userAvatar: userAvatar ?? this.userAvatar,
      categoryName: categoryName ?? this.categoryName,
      categoryIcon: categoryIcon ?? this.categoryIcon,
      isFavorited: isFavorited ?? this.isFavorited,
      sellerRating: sellerRating ?? this.sellerRating,
      sellerReviewsCount: sellerReviewsCount ?? this.sellerReviewsCount,
    );
  }

  // الحصول على السعر المنسق
  String get formattedPrice {
    if (isFree) return 'مجاني';
    if (price == 0) return 'السعر عند الاتفاق';
    
    String priceStr = price.toStringAsFixed(price.truncateToDouble() == price ? 0 : 2);
    String negotiable = isNegotiable ? ' (قابل للتفاوض)' : '';
    return '$priceStr $currency$negotiable';
  }

  // الحصول على نص الحالة
  String get conditionText {
    switch (condition) {
      case ProductCondition.newCondition:
        return 'جديد';
      case ProductCondition.used:
        return 'مستعمل';
      case ProductCondition.refurbished:
        return 'مجدد';
    }
  }

  // الحصول على نص الحالة
  String get statusText {
    switch (status) {
      case ProductStatus.active:
        return 'نشط';
      case ProductStatus.sold:
        return 'تم البيع';
      case ProductStatus.reserved:
        return 'محجوز';
      case ProductStatus.inactive:
        return 'غير نشط';
    }
  }

  // التحقق من انتهاء الصلاحية
  bool get isExpired => DateTime.now().isAfter(expiresAt);

  // الحصول على الصورة الرئيسية
  String? get mainImage => images.isNotEmpty ? images.first : null;
}

// تعدادات
enum ProductCondition { newCondition, used, refurbished }
enum ProductStatus { active, sold, reserved, inactive }
