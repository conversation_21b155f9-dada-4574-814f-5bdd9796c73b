class ProductCategory {
  final String id;
  final String nameAr;
  final String nameEn;
  final String icon;
  final String color;
  final String? parentId;
  final bool isActive;
  final int sortOrder;
  final DateTime createdAt;
  final List<ProductCategory>? subcategories;

  const ProductCategory({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.icon,
    this.color = '#2196F3',
    this.parentId,
    this.isActive = true,
    this.sortOrder = 0,
    required this.createdAt,
    this.subcategories,
  });

  // تحويل من Map
  factory ProductCategory.fromMap(Map<String, dynamic> map) {
    return ProductCategory(
      id: map['id'] ?? '',
      nameAr: map['name_ar'] ?? '',
      nameEn: map['name_en'] ?? '',
      icon: map['icon'] ?? '',
      color: map['color'] ?? '#2196F3',
      parentId: map['parent_id'],
      isActive: map['is_active'] ?? true,
      sortOrder: map['sort_order'] ?? 0,
      createdAt: DateTime.parse(map['created_at']),
      subcategories: map['subcategories'] != null
          ? (map['subcategories'] as List)
              .map((sub) => ProductCategory.fromMap(sub))
              .toList()
          : null,
    );
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name_ar': nameAr,
      'name_en': nameEn,
      'icon': icon,
      'color': color,
      'parent_id': parentId,
      'is_active': isActive,
      'sort_order': sortOrder,
      'created_at': createdAt.toIso8601String(),
    };
  }

  // نسخ مع تعديل
  ProductCategory copyWith({
    String? id,
    String? nameAr,
    String? nameEn,
    String? icon,
    String? color,
    String? parentId,
    bool? isActive,
    int? sortOrder,
    DateTime? createdAt,
    List<ProductCategory>? subcategories,
  }) {
    return ProductCategory(
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      parentId: parentId ?? this.parentId,
      isActive: isActive ?? this.isActive,
      sortOrder: sortOrder ?? this.sortOrder,
      createdAt: createdAt ?? this.createdAt,
      subcategories: subcategories ?? this.subcategories,
    );
  }

  // التحقق من وجود فئات فرعية
  bool get hasSubcategories => subcategories != null && subcategories!.isNotEmpty;

  // التحقق من كونها فئة رئيسية
  bool get isMainCategory => parentId == null;

  // الحصول على الاسم حسب اللغة
  String getName([String locale = 'ar']) {
    return locale == 'ar' ? nameAr : nameEn;
  }
}

// الحقول المخصصة لكل فئة
class CategoryCustomFields {
  static Map<String, List<CustomField>> getCategoryFields(String categoryNameEn) {
    switch (categoryNameEn.toLowerCase()) {
      case 'clothing':
        return {
          'basic': [
            CustomField(
              key: 'size',
              label: 'المقاس',
              type: CustomFieldType.dropdown,
              options: ['XS', 'S', 'M', 'L', 'XL', 'XXL', '42', '44', '46', '48', '50'],
              required: true,
            ),
            CustomField(
              key: 'clothing_type',
              label: 'نوع الملابس',
              type: CustomFieldType.dropdown,
              options: ['قميص', 'سروال', 'عباءة', 'جلابة', 'فستان', 'تنورة', 'جاكيت'],
              required: true,
            ),
            CustomField(
              key: 'color',
              label: 'اللون',
              type: CustomFieldType.text,
              required: true,
            ),
            CustomField(
              key: 'material',
              label: 'المادة',
              type: CustomFieldType.dropdown,
              options: ['قطن', 'حرير', 'صوف', 'بوليستر', 'كتان', 'جينز'],
              required: false,
            ),
            CustomField(
              key: 'gender',
              label: 'الجنس',
              type: CustomFieldType.dropdown,
              options: ['رجالي', 'نسائي', 'أطفال'],
              required: true,
            ),
          ],
        };

      case 'shoes':
        return {
          'basic': [
            CustomField(
              key: 'shoe_size',
              label: 'المقاس',
              type: CustomFieldType.dropdown,
              options: List.generate(20, (i) => (35 + i).toString()),
              required: true,
            ),
            CustomField(
              key: 'shoe_type',
              label: 'نوع الحذاء',
              type: CustomFieldType.dropdown,
              options: ['رياضي', 'كلاسيك', 'كاجوال', 'رسمي', 'صندل', 'بوت'],
              required: true,
            ),
            CustomField(
              key: 'gender',
              label: 'الجنس',
              type: CustomFieldType.dropdown,
              options: ['رجالي', 'نسائي', 'أطفال'],
              required: true,
            ),
            CustomField(
              key: 'color',
              label: 'اللون',
              type: CustomFieldType.text,
              required: true,
            ),
          ],
        };

      case 'bags':
        return {
          'basic': [
            CustomField(
              key: 'bag_type',
              label: 'نوع الحقيبة',
              type: CustomFieldType.dropdown,
              options: ['حقيبة ظهر', 'حقيبة يد', 'حقيبة سفر', 'محفظة', 'حقيبة لابتوب'],
              required: true,
            ),
            CustomField(
              key: 'size',
              label: 'الحجم',
              type: CustomFieldType.dropdown,
              options: ['صغير', 'متوسط', 'كبير'],
              required: true,
            ),
            CustomField(
              key: 'material',
              label: 'المادة',
              type: CustomFieldType.dropdown,
              options: ['جلد طبيعي', 'جلد صناعي', 'قماش', 'نايلون'],
              required: false,
            ),
            CustomField(
              key: 'color',
              label: 'اللون',
              type: CustomFieldType.text,
              required: true,
            ),
            CustomField(
              key: 'gender',
              label: 'الجنس',
              type: CustomFieldType.dropdown,
              options: ['رجالي', 'نسائي', 'للجنسين'],
              required: true,
            ),
          ],
        };

      case 'phones':
        return {
          'basic': [
            CustomField(
              key: 'brand',
              label: 'الماركة',
              type: CustomFieldType.dropdown,
              options: ['iPhone', 'Samsung', 'Huawei', 'Xiaomi', 'Oppo', 'Vivo', 'OnePlus', 'أخرى'],
              required: true,
            ),
            CustomField(
              key: 'model',
              label: 'الموديل',
              type: CustomFieldType.text,
              required: true,
            ),
            CustomField(
              key: 'ram',
              label: 'الرام',
              type: CustomFieldType.dropdown,
              options: ['2GB', '3GB', '4GB', '6GB', '8GB', '12GB', '16GB'],
              required: false,
            ),
            CustomField(
              key: 'storage',
              label: 'التخزين',
              type: CustomFieldType.dropdown,
              options: ['16GB', '32GB', '64GB', '128GB', '256GB', '512GB', '1TB'],
              required: false,
            ),
            CustomField(
              key: 'battery',
              label: 'البطارية (mAh)',
              type: CustomFieldType.text,
              required: false,
            ),
            CustomField(
              key: 'has_box',
              label: 'يشمل العلبة والشاحن',
              type: CustomFieldType.boolean,
              required: false,
            ),
          ],
        };

      case 'computers':
        return {
          'basic': [
            CustomField(
              key: 'computer_type',
              label: 'النوع',
              type: CustomFieldType.dropdown,
              options: ['لابتوب', 'كمبيوتر مكتبي', 'تابلت'],
              required: true,
            ),
            CustomField(
              key: 'processor',
              label: 'المعالج',
              type: CustomFieldType.dropdown,
              options: ['Intel i3', 'Intel i5', 'Intel i7', 'Intel i9', 'AMD Ryzen 3', 'AMD Ryzen 5', 'AMD Ryzen 7'],
              required: false,
            ),
            CustomField(
              key: 'ram',
              label: 'الرام',
              type: CustomFieldType.dropdown,
              options: ['4GB', '8GB', '16GB', '32GB', '64GB'],
              required: false,
            ),
            CustomField(
              key: 'storage_type',
              label: 'نوع التخزين',
              type: CustomFieldType.dropdown,
              options: ['HDD', 'SSD', 'HDD + SSD'],
              required: false,
            ),
            CustomField(
              key: 'storage_size',
              label: 'حجم التخزين',
              type: CustomFieldType.dropdown,
              options: ['256GB', '512GB', '1TB', '2TB', '4TB'],
              required: false,
            ),
            CustomField(
              key: 'screen_size',
              label: 'حجم الشاشة',
              type: CustomFieldType.text,
              required: false,
            ),
          ],
        };

      case 'cars':
        return {
          'basic': [
            CustomField(
              key: 'brand',
              label: 'الماركة',
              type: CustomFieldType.dropdown,
              options: ['تويوتا', 'هوندا', 'نيسان', 'هيونداي', 'كيا', 'فولكسفاغن', 'رينو', 'بيجو', 'أخرى'],
              required: true,
            ),
            CustomField(
              key: 'model',
              label: 'الموديل',
              type: CustomFieldType.text,
              required: true,
            ),
            CustomField(
              key: 'year',
              label: 'سنة الصنع',
              type: CustomFieldType.dropdown,
              options: List.generate(30, (i) => (2024 - i).toString()),
              required: true,
            ),
            CustomField(
              key: 'mileage',
              label: 'الكيلومتر',
              type: CustomFieldType.text,
              required: true,
            ),
            CustomField(
              key: 'fuel_type',
              label: 'نوع الوقود',
              type: CustomFieldType.dropdown,
              options: ['بنزين', 'ديزل', 'هجين', 'كهربائي'],
              required: true,
            ),
            CustomField(
              key: 'transmission',
              label: 'ناقل الحركة',
              type: CustomFieldType.dropdown,
              options: ['يدوي', 'أوتوماتيك'],
              required: true,
            ),
            CustomField(
              key: 'color',
              label: 'اللون',
              type: CustomFieldType.text,
              required: true,
            ),
            CustomField(
              key: 'exchange_possible',
              label: 'متاح للتبديل',
              type: CustomFieldType.boolean,
              required: false,
            ),
          ],
        };

      default:
        return {
          'basic': [
            CustomField(
              key: 'additional_info',
              label: 'معلومات إضافية',
              type: CustomFieldType.textarea,
              required: false,
            ),
          ],
        };
    }
  }
}

// نموذج الحقل المخصص
class CustomField {
  final String key;
  final String label;
  final CustomFieldType type;
  final List<String>? options;
  final bool required;
  final String? placeholder;
  final String? defaultValue;

  const CustomField({
    required this.key,
    required this.label,
    required this.type,
    this.options,
    this.required = false,
    this.placeholder,
    this.defaultValue,
  });
}

// أنواع الحقول المخصصة
enum CustomFieldType {
  text,
  textarea,
  dropdown,
  boolean,
  number,
  date,
}
