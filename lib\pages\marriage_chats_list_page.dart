import 'package:flutter/material.dart';
import '../models/marriage_chat.dart';
import '../services/marriage_chat_service.dart';
import 'marriage_chat_page.dart';

class MarriageChatsListPage extends StatefulWidget {
  const MarriageChatsListPage({super.key});

  @override
  State<MarriageChatsListPage> createState() => _MarriageChatsListPageState();
}

class _MarriageChatsListPageState extends State<MarriageChatsListPage> {
  final MarriageChatService _chatService = MarriageChatService();
  List<MarriageChat> _chats = [];
  bool _loading = true;

  @override
  void initState() {
    super.initState();
    _loadChats();
  }

  Future<void> _loadChats() async {
    setState(() => _loading = true);
    try {
      final chats = await _chatService.getUserChats();
      setState(() {
        _chats = chats;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_chats.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(32),
              decoration: BoxDecoration(
                color: Colors.pink[50],
                shape: BoxShape.circle,
              ),
              child: Icon(Icons.chat_bubble_outline, size: 64, color: Colors.pink[600]),
            ),
            const SizedBox(height: 24),
            Text(
              'لا توجد محادثات بعد',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'عندما يتم قبول طلبات التواصل ستظهر المحادثات هنا',
              style: TextStyle(fontSize: 16, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadChats,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _chats.length,
        itemBuilder: (context, index) {
          final chat = _chats[index];
          return _buildChatCard(chat);
        },
      ),
    );
  }

  Widget _buildChatCard(MarriageChat chat) {
    final currentUserId = _chatService.client.auth.currentUser?.id ?? '';
    final otherUserName = chat.getOtherUserName(currentUserId);

    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _openChat(chat),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              // صورة المستخدم الآخر
              CircleAvatar(
                radius: 28,
                backgroundColor: Colors.pink[100],
                backgroundImage: chat.otherUserAvatar != null
                    ? NetworkImage(chat.otherUserAvatar!)
                    : null,
                child: chat.otherUserAvatar == null
                    ? Icon(Icons.person, color: Colors.pink[600], size: 28)
                    : null,
              ),
              
              const SizedBox(width: 16),
              
              // معلومات المحادثة
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // اسم المستخدم
                    Text(
                      otherUserName,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // آخر رسالة
                    Text(
                      chat.lastMessage ?? 'لا توجد رسائل بعد',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // نوع المحادثة
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.pink[50],
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(color: Colors.pink[200]!),
                      ),
                      child: Text(
                        'محادثة زواج شرعي',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.pink[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // وقت آخر رسالة
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    chat.getFormattedLastMessageTime(),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Icon(
                    Icons.chevron_right,
                    color: Colors.grey[400],
                    size: 20,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _openChat(MarriageChat chat) {
    final currentUserId = _chatService.client.auth.currentUser?.id ?? '';
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => MarriageChatPage(
          chatId: chat.id,
          otherUserName: chat.getOtherUserName(currentUserId),
          otherUserAvatar: chat.otherUserAvatar,
        ),
      ),
    ).then((_) => _loadChats()); // إعادة تحميل المحادثات عند العودة
  }
}
