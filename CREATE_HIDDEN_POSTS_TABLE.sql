-- إنشاء جدول hidden_posts لإخفاء المنشورات للمستخدمين
-- نفذ هذا السكريبت في Supabase SQL Editor

CREATE TABLE IF NOT EXISTS hidden_posts (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    post_id UUID REFERENCES posts(id) ON DELETE CASCADE,
    reason TEXT,
    hidden_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, post_id)
);

ALTER TABLE hidden_posts ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own hidden posts" ON hidden_posts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own hidden posts" ON hidden_posts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete their own hidden posts" ON hidden_posts
    FOR DELETE USING (auth.uid() = user_id);

CREATE INDEX IF NOT EXISTS idx_hidden_posts_user_id ON hidden_posts(user_id);
CREATE INDEX IF NOT EXISTS idx_hidden_posts_post_id ON hidden_posts(post_id);
CREATE INDEX IF NOT EXISTS idx_hidden_posts_hidden_at ON hidden_posts(hidden_at); 