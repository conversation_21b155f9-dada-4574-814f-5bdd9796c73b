import 'package:flutter/material.dart';
import '../models/product_category.dart';

class MarketplaceFilters extends StatefulWidget {
  final List<ProductCategory> categories;
  final String? selectedCategoryId;
  final String? selectedCity;
  final double? minPrice;
  final double? maxPrice;
  final String? selectedCondition;
  final String sortBy;
  final Function(Map<String, dynamic>) onApply;
  final VoidCallback onReset;

  const MarketplaceFilters({
    super.key,
    required this.categories,
    this.selectedCategoryId,
    this.selectedCity,
    this.minPrice,
    this.maxPrice,
    this.selectedCondition,
    this.sortBy = 'created_at',
    required this.onApply,
    required this.onReset,
  });

  @override
  State<MarketplaceFilters> createState() => _MarketplaceFiltersState();
}

class _MarketplaceFiltersState extends State<MarketplaceFilters> {
  late TextEditingController _cityController;
  late TextEditingController _minPriceController;
  late TextEditingController _maxPriceController;
  
  String? _selectedCategoryId;
  String? _selectedCondition;
  String _sortBy = 'created_at';
  RangeValues _priceRange = const RangeValues(0, 10000);

  final List<String> _cities = [
    'الرباط',
    'الدار البيضاء',
    'فاس',
    'مراكش',
    'أكادير',
    'طنجة',
    'مكناس',
    'وجدة',
    'القنيطرة',
    'تطوان',
    'سلا',
    'المحمدية',
    'خريبكة',
    'بني ملال',
    'الجديدة',
  ];

  final List<Map<String, String>> _conditions = [
    {'value': 'new', 'label': 'جديد'},
    {'value': 'used', 'label': 'مستعمل'},
    {'value': 'refurbished', 'label': 'مجدد'},
  ];

  final List<Map<String, String>> _sortOptions = [
    {'value': 'created_at', 'label': 'الأحدث'},
    {'value': 'price', 'label': 'السعر (من الأقل للأعلى)'},
    {'value': 'price DESC', 'label': 'السعر (من الأعلى للأقل)'},
    {'value': 'views_count', 'label': 'الأكثر مشاهدة'},
    {'value': 'favorites_count', 'label': 'الأكثر إعجاباً'},
  ];

  @override
  void initState() {
    super.initState();
    _cityController = TextEditingController(text: widget.selectedCity);
    _minPriceController = TextEditingController(
      text: widget.minPrice?.toString() ?? '',
    );
    _maxPriceController = TextEditingController(
      text: widget.maxPrice?.toString() ?? '',
    );
    
    _selectedCategoryId = widget.selectedCategoryId;
    _selectedCondition = widget.selectedCondition;
    _sortBy = widget.sortBy;
    
    if (widget.minPrice != null || widget.maxPrice != null) {
      _priceRange = RangeValues(
        widget.minPrice ?? 0,
        widget.maxPrice ?? 10000,
      );
    }
  }

  @override
  void dispose() {
    _cityController.dispose();
    _minPriceController.dispose();
    _maxPriceController.dispose();
    super.dispose();
  }

  void _applyFilters() {
    final filters = <String, dynamic>{
      'categoryId': _selectedCategoryId,
      'city': _cityController.text.trim().isEmpty ? null : _cityController.text.trim(),
      'minPrice': _priceRange.start > 0 ? _priceRange.start : null,
      'maxPrice': _priceRange.end < 10000 ? _priceRange.end : null,
      'condition': _selectedCondition,
      'sortBy': _sortBy,
    };

    widget.onApply(filters);
    Navigator.pop(context);
  }

  void _resetFilters() {
    setState(() {
      _selectedCategoryId = null;
      _selectedCondition = null;
      _sortBy = 'created_at';
      _priceRange = const RangeValues(0, 10000);
      _cityController.clear();
      _minPriceController.clear();
      _maxPriceController.clear();
    });
    
    widget.onReset();
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.9,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // المقبض
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // العنوان
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                const Text(
                  'الفلاتر',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _resetFilters,
                  child: const Text(
                    'إعادة تعيين',
                    style: TextStyle(color: Colors.red),
                  ),
                ),
              ],
            ),
          ),
          
          const Divider(),
          
          // المحتوى
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // الفئة
                  _buildSectionTitle('الفئة'),
                  const SizedBox(height: 8),
                  _buildCategoryDropdown(),
                  
                  const SizedBox(height: 24),
                  
                  // المدينة
                  _buildSectionTitle('المدينة'),
                  const SizedBox(height: 8),
                  _buildCityDropdown(),
                  
                  const SizedBox(height: 24),
                  
                  // نطاق السعر
                  _buildSectionTitle('نطاق السعر'),
                  const SizedBox(height: 8),
                  _buildPriceRange(),
                  
                  const SizedBox(height: 24),
                  
                  // الحالة
                  _buildSectionTitle('الحالة'),
                  const SizedBox(height: 8),
                  _buildConditionChips(),
                  
                  const SizedBox(height: 24),
                  
                  // الترتيب
                  _buildSectionTitle('ترتيب النتائج'),
                  const SizedBox(height: 8),
                  _buildSortOptions(),
                ],
              ),
            ),
          ),
          
          // أزرار العمل
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              border: Border(
                top: BorderSide(color: Colors.grey[200]!),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('إلغاء'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orange[600],
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text('تطبيق'),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildCategoryDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedCategoryId,
      decoration: const InputDecoration(
        hintText: 'اختر الفئة',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: [
        const DropdownMenuItem<String>(
          value: null,
          child: Text('جميع الفئات'),
        ),
        ...widget.categories.map((category) {
          return DropdownMenuItem<String>(
            value: category.id,
            child: Text(category.nameAr),
          );
        }),
      ],
      onChanged: (value) {
        setState(() => _selectedCategoryId = value);
      },
    );
  }

  Widget _buildCityDropdown() {
    return DropdownButtonFormField<String>(
      value: _cities.contains(_cityController.text) ? _cityController.text : null,
      decoration: const InputDecoration(
        hintText: 'اختر المدينة',
        border: OutlineInputBorder(),
        contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
      items: [
        const DropdownMenuItem<String>(
          value: null,
          child: Text('جميع المدن'),
        ),
        ..._cities.map((city) {
          return DropdownMenuItem<String>(
            value: city,
            child: Text(city),
          );
        }),
      ],
      onChanged: (value) {
        _cityController.text = value ?? '';
      },
    );
  }

  Widget _buildPriceRange() {
    return Column(
      children: [
        RangeSlider(
          values: _priceRange,
          min: 0,
          max: 10000,
          divisions: 100,
          labels: RangeLabels(
            '${_priceRange.start.round()} درهم',
            '${_priceRange.end.round()} درهم',
          ),
          onChanged: (values) {
            setState(() => _priceRange = values);
          },
        ),
        Row(
          children: [
            Text(
              'من ${_priceRange.start.round()} درهم',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
            const Spacer(),
            Text(
              'إلى ${_priceRange.end.round()} درهم',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildConditionChips() {
    return Wrap(
      spacing: 8,
      children: _conditions.map((condition) {
        final isSelected = _selectedCondition == condition['value'];
        return FilterChip(
          label: Text(condition['label']!),
          selected: isSelected,
          onSelected: (selected) {
            setState(() {
              _selectedCondition = selected ? condition['value'] : null;
            });
          },
          selectedColor: Colors.orange[100],
          checkmarkColor: Colors.orange[700],
        );
      }).toList(),
    );
  }

  Widget _buildSortOptions() {
    return Column(
      children: _sortOptions.map((option) {
        return RadioListTile<String>(
          title: Text(option['label']!),
          value: option['value']!,
          groupValue: _sortBy,
          onChanged: (value) {
            setState(() => _sortBy = value!);
          },
          activeColor: Colors.orange[600],
          contentPadding: EdgeInsets.zero,
        );
      }).toList(),
    );
  }
}
