import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/job_seeker.dart';

class JobSeekersService {
  final SupabaseClient _client = Supabase.instance.client;

  // الحصول على جميع الباحثين عن عمل
  Future<List<JobSeeker>> getAllJobSeekers({
    String? city,
    String? category,
    String? jobType,
    String? country,
  }) async {
    try {
      var query = _client
          .from('job_seekers')
          .select('*')
          .eq('is_active', true)
          .order('created_at', ascending: false);

      final response = await query;
      return (response as List).map((item) => JobSeeker.fromJson(item)).toList();
    } catch (e) {
      throw Exception('فشل في جلب الباحثين عن عمل: $e');
    }
  }

  // البحث في الباحثين عن عمل
  Future<List<JobSeeker>> searchJobSeekers({
    String? searchTerm,
    String? city,
    String? category,
    String? jobType,
    String? country,
  }) async {
    try {
      var queryBuilder = _client
          .from('job_seekers')
          .select('*')
          .eq('is_active', true);

      if (city != null && city != 'الكل') {
        queryBuilder = queryBuilder.eq('current_city', city);
      }

      if (category != null && category != 'الكل') {
        queryBuilder = queryBuilder.eq('category', category);
      }

      if (jobType != null && jobType != 'الكل') {
        queryBuilder = queryBuilder.eq('preferred_job_type', jobType);
      }

      if (country != null && country != 'الكل') {
        queryBuilder = queryBuilder.eq('current_country', country);
      }

      final response = await queryBuilder.order('created_at', ascending: false);
      List<JobSeeker> jobSeekers = (response as List)
          .map((item) => JobSeeker.fromJson(item))
          .toList();

      // تطبيق البحث النصي محلياً
      if (searchTerm != null && searchTerm.isNotEmpty) {
        jobSeekers = jobSeekers.where((seeker) {
          return seeker.fullName.toLowerCase().contains(searchTerm.toLowerCase()) ||
                 seeker.description.toLowerCase().contains(searchTerm.toLowerCase()) ||
                 seeker.skills.any((skill) =>
                     skill.toLowerCase().contains(searchTerm.toLowerCase()));
        }).toList();
      }

      return jobSeekers;
    } catch (e) {
      throw Exception('فشل في البحث: $e');
    }
  }

  // إضافة باحث عن عمل جديد
  Future<void> addJobSeeker(JobSeeker jobSeeker) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client.from('job_seekers').insert({
        'user_id': userId,
        'full_name': jobSeeker.fullName,
        'profile_image': jobSeeker.profileImage,
        'age': jobSeeker.age,
        'gender': jobSeeker.gender,
        'marital_status': jobSeeker.maritalStatus.name,
        'current_country': jobSeeker.currentCountry,
        'current_city': jobSeeker.currentCity,
        'nationality': jobSeeker.nationality,
        'category': jobSeeker.category.name,
        'skills': jobSeeker.skills,
        'languages': jobSeeker.languages,
        'experience_years': jobSeeker.experienceYears,
        'description': jobSeeker.description,
        'preferred_job_type': jobSeeker.preferredJobType.name,
        'preferred_location': jobSeeker.preferredLocation,
        'phone_number': jobSeeker.phoneNumber,
        'email': jobSeeker.email,
        'social_links': jobSeeker.socialLinks,
        'cv_url': jobSeeker.cvUrl,
        'portfolio_images': jobSeeker.portfolioImages,
      });
    } catch (e) {
      throw Exception('فشل في إضافة الملف المهني: $e');
    }
  }

  // تحديث ملف باحث عن عمل
  Future<void> updateJobSeeker(String id, Map<String, dynamic> updates) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('job_seekers')
          .update(updates)
          .eq('id', id)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في تحديث الملف المهني: $e');
    }
  }

  // حذف ملف باحث عن عمل
  Future<void> deleteJobSeeker(String id) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      await _client
          .from('job_seekers')
          .delete()
          .eq('id', id)
          .eq('user_id', userId);
    } catch (e) {
      throw Exception('فشل في حذف الملف المهني: $e');
    }
  }

  // الحصول على ملف المستخدم
  Future<JobSeeker?> getUserJobSeeker() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final response = await _client
          .from('job_seekers')
          .select('*')
          .eq('user_id', userId)
          .maybeSingle();

      if (response == null) return null;
      return JobSeeker.fromJson(response);
    } catch (e) {
      throw Exception('فشل في جلب الملف المهني: $e');
    }
  }

  // زيادة عدد المشاهدات
  Future<void> incrementViews(String id) async {
    try {
      await _client.rpc('increment_job_seeker_views', params: {'seeker_id': id});
    } catch (e) {
      // تجاهل الخطأ إذا لم تكن الدالة موجودة
      debugPrint('خطأ في زيادة المشاهدات: $e');
    }
  }

  // إضافة/إزالة الإعجاب
  Future<void> toggleLike(String seekerId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // التحقق من وجود إعجاب سابق
      final existing = await _client
          .from('job_seeker_likes')
          .select('id')
          .eq('seeker_id', seekerId)
          .eq('user_id', userId)
          .maybeSingle();

      if (existing != null) {
        // إزالة الإعجاب
        await _client
            .from('job_seeker_likes')
            .delete()
            .eq('seeker_id', seekerId)
            .eq('user_id', userId);
      } else {
        // إضافة الإعجاب
        await _client.from('job_seeker_likes').insert({
          'seeker_id': seekerId,
          'user_id': userId,
        });
      }
    } catch (e) {
      throw Exception('فشل في تحديث الإعجاب: $e');
    }
  }

  // التحقق من الإعجاب
  Future<bool> isLiked(String seekerId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return false;

      final response = await _client
          .from('job_seeker_likes')
          .select('id')
          .eq('seeker_id', seekerId)
          .eq('user_id', userId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      return false;
    }
  }

  // حفظ/إلغاء حفظ ملف
  Future<void> toggleSave(String seekerId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // التحقق من وجود حفظ سابق
      final existing = await _client
          .from('job_seeker_saves')
          .select('id')
          .eq('seeker_id', seekerId)
          .eq('user_id', userId)
          .maybeSingle();

      if (existing != null) {
        // إزالة الحفظ
        await _client
            .from('job_seeker_saves')
            .delete()
            .eq('seeker_id', seekerId)
            .eq('user_id', userId);
      } else {
        // إضافة الحفظ
        await _client.from('job_seeker_saves').insert({
          'seeker_id': seekerId,
          'user_id': userId,
        });
      }
    } catch (e) {
      throw Exception('فشل في تحديث الحفظ: $e');
    }
  }

  // التحقق من الحفظ
  Future<bool> isSaved(String seekerId) async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) return false;

      final response = await _client
          .from('job_seeker_saves')
          .select('id')
          .eq('seeker_id', seekerId)
          .eq('user_id', userId)
          .maybeSingle();

      return response != null;
    } catch (e) {
      return false;
    }
  }

  // الحصول على الملفات المحفوظة
  Future<List<JobSeeker>> getSavedJobSeekers() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // الحصول على معرفات الملفات المحفوظة
      final savesResponse = await _client
          .from('job_seeker_saves')
          .select('seeker_id')
          .eq('user_id', userId);

      if ((savesResponse as List).isEmpty) {
        return [];
      }

      final seekerIds = (savesResponse as List)
          .map((item) => item['seeker_id'])
          .toList();

      // الحصول على تفاصيل الملفات
      final seekersResponse = await _client
          .from('job_seekers')
          .select('*')
          .inFilter('id', seekerIds)
          .eq('is_active', true)
          .order('created_at', ascending: false);

      return (seekersResponse as List)
          .map((item) => JobSeeker.fromJson(item))
          .toList();
    } catch (e) {
      throw Exception('فشل في جلب الملفات المحفوظة: $e');
    }
  }

  // الحصول على إحصائيات
  Future<Map<String, int>> getJobSeekersStats() async {
    try {
      // عدد الباحثين عن عمل
      final totalResponse = await _client
          .from('job_seekers')
          .select('id')
          .eq('is_active', true);

      // عدد الباحثين الجدد هذا الأسبوع
      final weekAgo = DateTime.now().subtract(const Duration(days: 7));
      final newThisWeekResponse = await _client
          .from('job_seekers')
          .select('id')
          .eq('is_active', true)
          .gte('created_at', weekAgo.toIso8601String());

      // أكثر الفئات طلباً
      final categoriesResponse = await _client
          .from('job_seekers')
          .select('category')
          .eq('is_active', true);

      final categoryCounts = <String, int>{};
      for (final item in (categoriesResponse as List)) {
        final category = item['category'] as String;
        categoryCounts[category] = (categoryCounts[category] ?? 0) + 1;
      }

      final topCategory = categoryCounts.entries
          .fold<MapEntry<String, int>?>(null, (prev, curr) {
        if (prev == null || curr.value > prev.value) return curr;
        return prev;
      });

      return {
        'total': (totalResponse as List).length,
        'new_this_week': (newThisWeekResponse as List).length,
        'top_category_count': topCategory?.value ?? 0,
      };
    } catch (e) {
      return {
        'total': 0,
        'new_this_week': 0,
        'top_category_count': 0,
      };
    }
  }

  // حذف جميع البيانات المرتبطة بالمستخدم
  Future<void> deleteAllUserJobSeekerData() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // حذف جميع الإعجابات التي قام بها المستخدم
      await _client
          .from('job_seeker_likes')
          .delete()
          .eq('user_id', userId);

      // حذف جميع الحفظ التي قام بها المستخدم
      await _client
          .from('job_seeker_saves')
          .delete()
          .eq('user_id', userId);

      // حذف الملف المهني للمستخدم
      await _client
          .from('job_seekers')
          .delete()
          .eq('user_id', userId);

    } catch (e) {
      throw Exception('فشل في حذف البيانات: $e');
    }
  }

  // مسح البيانات المحفوظة فقط (الإعجابات والحفظ)
  Future<void> clearUserSavedData() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // حذف جميع الإعجابات
      await _client
          .from('job_seeker_likes')
          .delete()
          .eq('user_id', userId);

      // حذف جميع الحفظ
      await _client
          .from('job_seeker_saves')
          .delete()
          .eq('user_id', userId);

    } catch (e) {
      throw Exception('فشل في مسح البيانات المحفوظة: $e');
    }
  }

  // تفعيل/إلغاء تفعيل الملف المهني
  Future<void> toggleProfileVisibility() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // الحصول على الحالة الحالية
      final currentProfile = await getUserJobSeeker();
      if (currentProfile == null) throw Exception('لا يوجد ملف مهني');

      // تغيير الحالة
      await _client
          .from('job_seekers')
          .update({'is_active': !currentProfile.isActive})
          .eq('user_id', userId);

    } catch (e) {
      throw Exception('فشل في تغيير حالة الملف: $e');
    }
  }

  // تصدير بيانات المستخدم
  Future<Map<String, dynamic>> exportUserData() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      // الحصول على الملف المهني
      final profile = await getUserJobSeeker();

      // الحصول على الملفات المحفوظة
      final savedProfiles = await getSavedJobSeekers();

      // الحصول على الإعجابات
      final likesResponse = await _client
          .from('job_seeker_likes')
          .select('seeker_id, created_at')
          .eq('user_id', userId);

      return {
        'profile': profile?.toJson(),
        'saved_profiles': savedProfiles.map((p) => p.toJson()).toList(),
        'likes': likesResponse,
        'export_date': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('فشل في تصدير البيانات: $e');
    }
  }

  // الحصول على إحصائيات المستخدم
  Future<Map<String, dynamic>> getUserStats() async {
    try {
      final userId = _client.auth.currentUser?.id;
      if (userId == null) throw Exception('يجب تسجيل الدخول أولاً');

      final profile = await getUserJobSeeker();
      if (profile == null) {
        return {
          'has_profile': false,
          'profile_views': 0,
          'profile_likes': 0,
          'saved_profiles_count': 0,
          'given_likes_count': 0,
        };
      }

      // عدد الملفات المحفوظة
      final savedCount = await _client
          .from('job_seeker_saves')
          .select('id')
          .eq('user_id', userId);

      // عدد الإعجابات التي قام بها
      final likesGivenCount = await _client
          .from('job_seeker_likes')
          .select('id')
          .eq('user_id', userId);

      return {
        'has_profile': true,
        'profile_views': profile.viewsCount,
        'profile_likes': profile.likesCount,
        'saved_profiles_count': (savedCount as List).length,
        'given_likes_count': (likesGivenCount as List).length,
        'profile_created': profile.createdAt.toIso8601String(),
        'profile_updated': profile.updatedAt.toIso8601String(),
      };
    } catch (e) {
      throw Exception('فشل في جلب إحصائيات المستخدم: $e');
    }
  }
}
