import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/job.dart';
import '../services/jobs_service.dart';

class AddJobPage extends StatefulWidget {
  final Job? job; // للتعديل

  const AddJobPage({super.key, this.job});

  @override
  State<AddJobPage> createState() => _AddJobPageState();
}

class _AddJobPageState extends State<AddJobPage> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _companyController = TextEditingController();
  final _jobTitleController = TextEditingController();
  final _salaryController = TextEditingController();
  final _locationController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _skillsController = TextEditingController();
  final _applicationLinkController = TextEditingController();

  JobType _selectedJobType = JobType.fullTime;
  JobCategory _selectedCategory = JobCategory.technology;
  bool _isRemote = false;
  DateTime? _expiresAt;
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    if (widget.job != null) {
      _loadJobData();
    }
  }

  void _loadJobData() {
    final job = widget.job!;
    _titleController.text = job.title;
    _companyController.text = job.companyName;
    _jobTitleController.text = job.jobTitle;
    _salaryController.text = job.salary ?? '';
    _locationController.text = job.location;
    _descriptionController.text = job.description;
    _skillsController.text = job.requiredSkills.join(', ');
    _applicationLinkController.text = job.applicationLink ?? '';
    _selectedJobType = job.jobType;
    _selectedCategory = job.category;
    _isRemote = job.isRemote;
    _expiresAt = job.expiresAt;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _companyController.dispose();
    _jobTitleController.dispose();
    _salaryController.dispose();
    _locationController.dispose();
    _descriptionController.dispose();
    _skillsController.dispose();
    _applicationLinkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.job != null ? 'تعديل الوظيفة' : 'نشر وظيفة جديدة'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 1,
        actions: [
          TextButton(
            onPressed: _loading ? null : _saveJob,
            child: _loading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('نشر'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // عنوان الوظيفة
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'عنوان الوظيفة *',
                hintText: 'مثال: مطور تطبيقات موبايل',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال عنوان الوظيفة';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // اسم الشركة
            TextFormField(
              controller: _companyController,
              decoration: const InputDecoration(
                labelText: 'اسم الشركة أو المؤسسة *',
                hintText: 'مثال: شركة التقنية المتقدمة',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم الشركة';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // المسمى الوظيفي
            TextFormField(
              controller: _jobTitleController,
              decoration: const InputDecoration(
                labelText: 'المسمى الوظيفي *',
                hintText: 'مثال: مطور أول',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال المسمى الوظيفي';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // نوع العمل والفئة
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<JobType>(
                    value: _selectedJobType,
                    decoration: const InputDecoration(
                      labelText: 'نوع العمل *',
                      border: OutlineInputBorder(),
                    ),
                    items: JobType.values.map((type) => DropdownMenuItem(
                      value: type,
                      child: Text(_getJobTypeText(type)),
                    )).toList(),
                    onChanged: (value) => setState(() => _selectedJobType = value!),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: DropdownButtonFormField<JobCategory>(
                    value: _selectedCategory,
                    decoration: const InputDecoration(
                      labelText: 'الفئة *',
                      border: OutlineInputBorder(),
                    ),
                    items: JobCategory.values.map((category) => DropdownMenuItem(
                      value: category,
                      child: Text(_getCategoryText(category)),
                    )).toList(),
                    onChanged: (value) => setState(() => _selectedCategory = value!),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // الراتب (اختياري)
            TextFormField(
              controller: _salaryController,
              decoration: const InputDecoration(
                labelText: 'الراتب (اختياري)',
                hintText: 'مثال: 5000 - 8000 ريال',
                border: OutlineInputBorder(),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // العمل عن بعد
            CheckboxListTile(
              title: const Text('العمل عن بعد'),
              subtitle: const Text('هل يمكن أداء هذه الوظيفة عن بعد؟'),
              value: _isRemote,
              onChanged: (value) => setState(() => _isRemote = value!),
              controlAffinity: ListTileControlAffinity.leading,
            ),
            
            const SizedBox(height: 16),
            
            // الموقع
            if (!_isRemote)
              TextFormField(
                controller: _locationController,
                decoration: const InputDecoration(
                  labelText: 'الموقع *',
                  hintText: 'مثال: الرياض، السعودية',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (!_isRemote && (value == null || value.trim().isEmpty)) {
                    return 'يرجى إدخال الموقع';
                  }
                  return null;
                },
              ),
            
            if (!_isRemote) const SizedBox(height: 16),
            
            // وصف الوظيفة
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف الوظيفة *',
                hintText: 'اكتب وصفاً مفصلاً عن الوظيفة والمهام المطلوبة...',
                border: OutlineInputBorder(),
                alignLabelWithHint: true,
              ),
              maxLines: 5,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال وصف الوظيفة';
                }
                if (value.trim().length < 50) {
                  return 'يجب أن يكون الوصف 50 حرف على الأقل';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // المهارات المطلوبة
            TextFormField(
              controller: _skillsController,
              decoration: const InputDecoration(
                labelText: 'المهارات المطلوبة *',
                hintText: 'مثال: Flutter, Dart, Firebase (افصل بفاصلة)',
                border: OutlineInputBorder(),
                helperText: 'افصل المهارات بفاصلة',
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال المهارات المطلوبة';
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // رابط التقديم (اختياري)
            TextFormField(
              controller: _applicationLinkController,
              decoration: const InputDecoration(
                labelText: 'رابط التقديم (اختياري)',
                hintText: 'https://example.com/apply',
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value != null && value.isNotEmpty) {
                  final uri = Uri.tryParse(value);
                  if (uri == null || !(uri.hasAbsolutePath && uri.hasScheme)) {
                    return 'يرجى إدخال رابط صحيح';
                  }
                }
                return null;
              },
            ),
            
            const SizedBox(height: 16),
            
            // تاريخ انتهاء الوظيفة
            ListTile(
              title: const Text('تاريخ انتهاء الوظيفة (اختياري)'),
              subtitle: Text(_expiresAt != null 
                  ? 'تنتهي في: ${_formatDate(_expiresAt!)}'
                  : 'لا يوجد تاريخ انتهاء'),
              trailing: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (_expiresAt != null)
                    IconButton(
                      onPressed: () => setState(() => _expiresAt = null),
                      icon: const Icon(Icons.clear),
                    ),
                  IconButton(
                    onPressed: _selectExpiryDate,
                    icon: const Icon(Icons.calendar_today),
                  ),
                ],
              ),
              contentPadding: EdgeInsets.zero,
            ),
            
            const SizedBox(height: 32),
            
            // معلومات إضافية
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue[700]),
                      const SizedBox(width: 8),
                      Text(
                        'نصائح لوظيفة ناجحة',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue[700],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• اكتب عنواناً واضحاً ومحدداً\n'
                    '• أضف وصفاً مفصلاً للمهام والمتطلبات\n'
                    '• حدد المهارات المطلوبة بدقة\n'
                    '• اذكر الراتب إن أمكن لجذب المتقدمين',
                    style: TextStyle(
                      color: Colors.blue[700],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _selectExpiryDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _expiresAt ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (date != null) {
      setState(() => _expiresAt = date);
    }
  }

  Future<void> _saveJob() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _loading = true);

    try {
      final skills = _skillsController.text
          .split(',')
          .map((s) => s.trim())
          .where((s) => s.isNotEmpty)
          .toList();

      final job = Job(
        id: widget.job?.id ?? '',
        title: _titleController.text.trim(),
        companyName: _companyController.text.trim(),
        jobTitle: _jobTitleController.text.trim(),
        jobType: _selectedJobType,
        salary: _salaryController.text.trim().isEmpty ? null : _salaryController.text.trim(),
        location: _isRemote ? 'عن بعد' : _locationController.text.trim(),
        isRemote: _isRemote,
        description: _descriptionController.text.trim(),
        requiredSkills: skills,
        category: _selectedCategory,
        applicationLink: _applicationLinkController.text.trim().isEmpty 
            ? null : _applicationLinkController.text.trim(),
        createdAt: widget.job?.createdAt ?? DateTime.now(),
        expiresAt: _expiresAt,
        publisherId: Supabase.instance.client.auth.currentUser!.id,
        publisherName: '',
      );

      if (widget.job != null) {
        await JobsService().updateJob(widget.job!.id, job);
      } else {
        await JobsService().createJob(job);
      }

      if (mounted) {
        Navigator.pop(context, true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(widget.job != null ? 'تم تحديث الوظيفة بنجاح' : 'تم نشر الوظيفة بنجاح'),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'خطأ غير معروف';

        if (e.toString().contains('row level security')) {
          errorMessage = 'تحتاج إلى إعداد ملفك الشخصي أولاً';
        } else if (e.toString().contains('profiles')) {
          errorMessage = 'تحتاج إلى إنشاء جداول قاعدة البيانات أولاً';
        } else {
          errorMessage = e.toString().replaceAll('Exception: ', '');
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            action: errorMessage.contains('قاعدة البيانات')
                ? SnackBarAction(
                    label: 'معرفة المزيد',
                    textColor: Colors.white,
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('إعداد قاعدة البيانات'),
                          content: const Text(
                            'يرجى تشغيل ملف setup_profiles_and_jobs.sql في قاعدة البيانات أولاً.\n\n'
                            'هذا الملف سينشئ جميع الجداول والسياسات المطلوبة.'
                          ),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.pop(context),
                              child: const Text('حسناً'),
                            ),
                          ],
                        ),
                      );
                    },
                  )
                : null,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _loading = false);
      }
    }
  }

  String _getJobTypeText(JobType type) {
    switch (type) {
      case JobType.fullTime: return 'دوام كامل';
      case JobType.partTime: return 'دوام جزئي';
      case JobType.freelance: return 'عمل حر';
      case JobType.remote: return 'عن بعد';
      case JobType.contract: return 'عقد مؤقت';
      case JobType.internship: return 'تدريب';
    }
  }

  String _getCategoryText(JobCategory category) {
    switch (category) {
      case JobCategory.technology: return 'تكنولوجيا';
      case JobCategory.marketing: return 'تسويق';
      case JobCategory.education: return 'تعليم';
      case JobCategory.construction: return 'بناء';
      case JobCategory.restaurant: return 'مطاعم';
      case JobCategory.healthcare: return 'صحة';
      case JobCategory.finance: return 'مالية';
      case JobCategory.design: return 'تصميم';
      case JobCategory.sales: return 'مبيعات';
      case JobCategory.customerService: return 'خدمة عملاء';
      case JobCategory.other: return 'أخرى';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
