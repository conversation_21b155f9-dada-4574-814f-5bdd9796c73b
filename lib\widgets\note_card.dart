import 'package:flutter/material.dart';
import '../models/note.dart';

class NoteCard extends StatelessWidget {
  final Note note;
  final VoidCallback onTap;
  final VoidCallback onPin;
  final VoidCallback onArchive;
  final VoidCallback onDelete;
  final Function(TaskStatus) onTaskStatusChanged;

  const NoteCard({
    super.key,
    required this.note,
    required this.onTap,
    required this.onPin,
    required this.onArchive,
    required this.onDelete,
    required this.onTaskStatusChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: note.isPinned ? 4 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: note.isPinned 
            ? BorderSide(color: Colors.amber[600]!, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // الرأس
              Row(
                children: [
                  // أيقونة النوع
                  Container(
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: note.type.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      note.type.icon,
                      size: 16,
                      color: note.type.color,
                    ),
                  ),
                  
                  const SizedBox(width: 8),
                  
                  // العنوان
                  Expanded(
                    child: Text(
                      note.title,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        decoration: note.isTask && note.isCompleted 
                            ? TextDecoration.lineThrough 
                            : null,
                        color: note.isTask && note.isCompleted 
                            ? Colors.grey[600] 
                            : null,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  
                  // أيقونة التثبيت
                  if (note.isPinned)
                    Icon(
                      Icons.push_pin,
                      size: 16,
                      color: Colors.amber[600],
                    ),
                  
                  // قائمة الخيارات
                  PopupMenuButton<String>(
                    onSelected: _handleMenuAction,
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'pin',
                        child: Row(
                          children: [
                            Icon(
                              note.isPinned ? Icons.push_pin_outlined : Icons.push_pin,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(note.isPinned ? 'إلغاء التثبيت' : 'تثبيت'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'archive',
                        child: Row(
                          children: [
                            Icon(
                              note.isArchived ? Icons.unarchive : Icons.archive,
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(note.isArchived ? 'إلغاء الأرشفة' : 'أرشفة'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, size: 16, color: Colors.red),
                            SizedBox(width: 8),
                            Text('حذف', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              // المحتوى
              if (note.preview.isNotEmpty)
                Text(
                  note.preview,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                    height: 1.4,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              
              const SizedBox(height: 12),
              
              // معلومات إضافية
              Row(
                children: [
                  // الفئة
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: note.category.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          note.category.icon,
                          size: 12,
                          color: note.category.color,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          note.category.arabicName,
                          style: TextStyle(
                            fontSize: 10,
                            color: note.category.color,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(width: 8),
                  
                  // عدد الكلمات
                  if (note.wordCount > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        '${note.wordCount} كلمة',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.grey[600],
                        ),
                      ),
                    ),
                  
                  const Spacer(),
                  
                  // التاريخ
                  Text(
                    note.formattedUpdatedDate,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                ],
              ),
              
              // الوسوم
              if (note.tags.isNotEmpty) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  children: note.tags.take(3).map((tag) {
                    return Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Colors.blue[50],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.blue[200]!),
                      ),
                      child: Text(
                        '#$tag',
                        style: TextStyle(
                          fontSize: 10,
                          color: Colors.blue[700],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ],
              
              // حالة المهمة
              if (note.isTask) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      note.taskStatus?.icon ?? TaskStatus.pending.icon,
                      size: 16,
                      color: note.taskStatus?.color ?? TaskStatus.pending.color,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      note.taskStatus?.arabicName ?? TaskStatus.pending.arabicName,
                      style: TextStyle(
                        fontSize: 12,
                        color: note.taskStatus?.color ?? TaskStatus.pending.color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    
                    const Spacer(),
                    
                    // أزرار تغيير حالة المهمة
                    if (!note.isCompleted) ...[
                      InkWell(
                        onTap: () => onTaskStatusChanged(TaskStatus.inProgress),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Icon(
                            Icons.play_arrow,
                            size: 16,
                            color: Colors.blue[600],
                          ),
                        ),
                      ),
                      const SizedBox(width: 4),
                      InkWell(
                        onTap: () => onTaskStatusChanged(TaskStatus.completed),
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.green[50],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Icon(
                            Icons.check,
                            size: 16,
                            color: Colors.green[600],
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
              
              // التذكير أو الموعد النهائي
              if (note.hasReminder || note.hasDueDate) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    if (note.hasReminder) ...[
                      Icon(
                        Icons.alarm,
                        size: 14,
                        color: Colors.orange[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'تذكير: ${_formatDateTime(note.reminderDate!)}',
                        style: TextStyle(
                          fontSize: 11,
                          color: Colors.orange[600],
                        ),
                      ),
                    ],
                    
                    if (note.hasReminder && note.hasDueDate)
                      const SizedBox(width: 12),
                    
                    if (note.hasDueDate) ...[
                      Icon(
                        Icons.schedule,
                        size: 14,
                        color: note.isOverdue ? Colors.red[600] : Colors.blue[600],
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'موعد: ${_formatDateTime(note.dueDate!)}',
                        style: TextStyle(
                          fontSize: 11,
                          color: note.isOverdue ? Colors.red[600] : Colors.blue[600],
                          fontWeight: note.isOverdue ? FontWeight.bold : null,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'pin':
        onPin();
        break;
      case 'archive':
        onArchive();
        break;
      case 'delete':
        onDelete();
        break;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final date = DateTime(dateTime.year, dateTime.month, dateTime.day);
    
    if (date == today) {
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (date == today.add(const Duration(days: 1))) {
      return 'غداً ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (date == today.subtract(const Duration(days: 1))) {
      return 'أمس ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else {
      return '${dateTime.day}/${dateTime.month} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }
}
