import 'package:flutter/material.dart';

enum CharityType {
  donation,  // تبرع
  request,   // طلب مساعدة
  urgent,    // حالة طارئة
}

enum CharityCategory {
  food,         // طعام
  clothes,      // ملابس
  furniture,    // أثاث
  medicine,     // أدوية
  books,        // كتب
  electronics,  // إلكترونيات
  other,        // أخرى
}

enum CharityCondition {
  new_item,     // جديد
  used,         // مستعمل
  good,         // حالة جيدة
  urgent,       // طارئ
}

enum DeliveryMethod {
  hand,         // تسليم يدوي
  person,       // عن طريق شخص
  charity,      // عبر جمعية
}

extension CharityTypeExtension on CharityType {
  String get arabicName {
    switch (this) {
      case CharityType.donation:
        return 'تبرع';
      case CharityType.request:
        return 'طلب مساعدة';
      case CharityType.urgent:
        return 'حالة طارئة';
    }
  }

  String get description {
    switch (this) {
      case CharityType.donation:
        return 'أريد التبرع بشيء';
      case CharityType.request:
        return 'أحتاج للمساعدة';
      case CharityType.urgent:
        return 'حالة مستعجلة';
    }
  }
}

extension CharityCategoryExtension on CharityCategory {
  String get arabicName {
    switch (this) {
      case CharityCategory.food:
        return 'طعام';
      case CharityCategory.clothes:
        return 'ملابس';
      case CharityCategory.furniture:
        return 'أثاث';
      case CharityCategory.medicine:
        return 'أدوية';
      case CharityCategory.books:
        return 'كتب';
      case CharityCategory.electronics:
        return 'إلكترونيات';
      case CharityCategory.other:
        return 'أخرى';
    }
  }

  IconData get icon {
    switch (this) {
      case CharityCategory.food:
        return Icons.restaurant;
      case CharityCategory.clothes:
        return Icons.checkroom;
      case CharityCategory.furniture:
        return Icons.chair;
      case CharityCategory.medicine:
        return Icons.medical_services;
      case CharityCategory.books:
        return Icons.book;
      case CharityCategory.electronics:
        return Icons.devices;
      case CharityCategory.other:
        return Icons.category;
    }
  }

  Color get color {
    switch (this) {
      case CharityCategory.food:
        return Colors.orange;
      case CharityCategory.clothes:
        return Colors.purple;
      case CharityCategory.furniture:
        return Colors.brown;
      case CharityCategory.medicine:
        return Colors.red;
      case CharityCategory.books:
        return Colors.blue;
      case CharityCategory.electronics:
        return Colors.grey;
      case CharityCategory.other:
        return Colors.teal;
    }
  }
}

extension CharityConditionExtension on CharityCondition {
  String get arabicName {
    switch (this) {
      case CharityCondition.new_item:
        return 'جديد';
      case CharityCondition.used:
        return 'مستعمل';
      case CharityCondition.good:
        return 'حالة جيدة';
      case CharityCondition.urgent:
        return 'طارئ';
    }
  }

  Color get color {
    switch (this) {
      case CharityCondition.new_item:
        return Colors.green;
      case CharityCondition.used:
        return Colors.orange;
      case CharityCondition.good:
        return Colors.blue;
      case CharityCondition.urgent:
        return Colors.red;
    }
  }
}

extension DeliveryMethodExtension on DeliveryMethod {
  String get arabicName {
    switch (this) {
      case DeliveryMethod.hand:
        return 'تسليم يدوي';
      case DeliveryMethod.person:
        return 'عن طريق شخص';
      case DeliveryMethod.charity:
        return 'عبر جمعية';
    }
  }
}

class CharityItem {
  final String id;
  final String userId;
  final String userName;
  final String userAvatar;
  final bool isVerified;
  final bool isAnonymous;
  
  final String title;
  final String description;
  final CharityType type;
  final CharityCategory category;
  final CharityCondition condition;
  final DeliveryMethod deliveryMethod;
  
  final String city;
  final String country;
  final String? phoneNumber;
  final List<String> images;
  
  final bool isUrgent;
  final bool isCompleted;
  final bool isActive;
  
  final int interestCount;
  final DateTime createdAt;
  final DateTime? completedAt;

  const CharityItem({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userAvatar,
    required this.isVerified,
    required this.isAnonymous,
    required this.title,
    required this.description,
    required this.type,
    required this.category,
    required this.condition,
    required this.deliveryMethod,
    required this.city,
    required this.country,
    this.phoneNumber,
    required this.images,
    required this.isUrgent,
    required this.isCompleted,
    required this.isActive,
    required this.interestCount,
    required this.createdAt,
    this.completedAt,
  });

  factory CharityItem.fromJson(Map<String, dynamic> json) {
    return CharityItem(
      id: json['id'],
      userId: json['user_id'],
      userName: json['user_name'] ?? 'مستخدم',
      userAvatar: json['user_avatar'] ?? '',
      isVerified: json['is_verified'] ?? false,
      isAnonymous: json['is_anonymous'] ?? false,
      title: json['title'],
      description: json['description'],
      type: CharityType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => CharityType.donation,
      ),
      category: CharityCategory.values.firstWhere(
        (c) => c.name == json['category'],
        orElse: () => CharityCategory.other,
      ),
      condition: CharityCondition.values.firstWhere(
        (c) => c.name == json['condition'],
        orElse: () => CharityCondition.good,
      ),
      deliveryMethod: DeliveryMethod.values.firstWhere(
        (d) => d.name == json['delivery_method'],
        orElse: () => DeliveryMethod.hand,
      ),
      city: json['city'],
      country: json['country'],
      phoneNumber: json['phone_number'],
      images: List<String>.from(json['images'] ?? []),
      isUrgent: json['is_urgent'] ?? false,
      isCompleted: json['is_completed'] ?? false,
      isActive: json['is_active'] ?? true,
      interestCount: json['interest_count'] ?? 0,
      createdAt: DateTime.parse(json['created_at']),
      completedAt: json['completed_at'] != null 
          ? DateTime.parse(json['completed_at']) 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'user_name': userName,
      'user_avatar': userAvatar,
      'is_verified': isVerified,
      'is_anonymous': isAnonymous,
      'title': title,
      'description': description,
      'type': type.name,
      'category': category.name,
      'condition': condition.name,
      'delivery_method': deliveryMethod.name,
      'city': city,
      'country': country,
      'phone_number': phoneNumber,
      'images': images,
      'is_urgent': isUrgent,
      'is_completed': isCompleted,
      'is_active': isActive,
      'interest_count': interestCount,
      'created_at': createdAt.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
    };
  }

  String get displayName {
    return isAnonymous ? 'متبرع كريم' : userName;
  }

  String get formattedTime {
    final now = DateTime.now();
    final difference = now.difference(createdAt);

    if (difference.inDays > 0) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inMinutes > 0) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else {
      return 'الآن';
    }
  }
}
