-- إنشاء جميع buckets المطلوبة للتطبيق
-- Create all required buckets for the application

-- 1. إنشاء bucket media
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'media',
  'media', 
  true,
  52428800,  -- 50MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/avi', 'video/mov', 'video/quicktime']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 52428800,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/avi', 'video/mov', 'video/quicktime'];

-- 2. إنشاء bucket images (إذا كان التطبيق يستخدمه)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'images',
  'images', 
  true,
  52428800,  -- 50MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 52428800,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];

-- 3. إنشاء bucket files (للملفات العامة)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'files',
  'files', 
  true,
  104857600,  -- 100MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/avi', 'video/mov', 'video/quicktime', 'application/pdf', 'text/plain']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 104857600,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/avi', 'video/mov', 'video/quicktime', 'application/pdf', 'text/plain'];

-- 4. إنشاء bucket avatars (للصور الشخصية)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'avatars',
  'avatars', 
  true,
  10485760,  -- 10MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 10485760,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

-- 5. إنشاء bucket space-images (للمساحات)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'space-images',
  'space-images', 
  true,
  52428800,  -- 50MB
  ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/avi', 'video/mov']
)
ON CONFLICT (id) DO UPDATE SET
  public = true,
  file_size_limit = 52428800,
  allowed_mime_types = ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'video/mp4', 'video/avi', 'video/mov'];

-- 6. حذف السياسات القديمة
DROP POLICY IF EXISTS "media_select_policy" ON storage.objects;
DROP POLICY IF EXISTS "media_insert_policy" ON storage.objects;
DROP POLICY IF EXISTS "media_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "media_delete_policy" ON storage.objects;

DROP POLICY IF EXISTS "images_select_policy" ON storage.objects;
DROP POLICY IF EXISTS "images_insert_policy" ON storage.objects;
DROP POLICY IF EXISTS "images_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "images_delete_policy" ON storage.objects;

DROP POLICY IF EXISTS "files_select_policy" ON storage.objects;
DROP POLICY IF EXISTS "files_insert_policy" ON storage.objects;
DROP POLICY IF EXISTS "files_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "files_delete_policy" ON storage.objects;

DROP POLICY IF EXISTS "avatars_select_policy" ON storage.objects;
DROP POLICY IF EXISTS "avatars_insert_policy" ON storage.objects;
DROP POLICY IF EXISTS "avatars_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "avatars_delete_policy" ON storage.objects;

DROP POLICY IF EXISTS "space_images_select_policy" ON storage.objects;
DROP POLICY IF EXISTS "space_images_insert_policy" ON storage.objects;
DROP POLICY IF EXISTS "space_images_update_policy" ON storage.objects;
DROP POLICY IF EXISTS "space_images_delete_policy" ON storage.objects;

-- 7. إنشاء سياسات لجميع buckets
-- سياسات media
CREATE POLICY "media_select_policy" ON storage.objects FOR SELECT USING (bucket_id = 'media');
CREATE POLICY "media_insert_policy" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'media' AND auth.uid() IS NOT NULL);
CREATE POLICY "media_update_policy" ON storage.objects FOR UPDATE USING (bucket_id = 'media' AND auth.uid() IS NOT NULL);
CREATE POLICY "media_delete_policy" ON storage.objects FOR DELETE USING (bucket_id = 'media' AND auth.uid() IS NOT NULL);

-- سياسات images
CREATE POLICY "images_select_policy" ON storage.objects FOR SELECT USING (bucket_id = 'images');
CREATE POLICY "images_insert_policy" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'images' AND auth.uid() IS NOT NULL);
CREATE POLICY "images_update_policy" ON storage.objects FOR UPDATE USING (bucket_id = 'images' AND auth.uid() IS NOT NULL);
CREATE POLICY "images_delete_policy" ON storage.objects FOR DELETE USING (bucket_id = 'images' AND auth.uid() IS NOT NULL);

-- سياسات files
CREATE POLICY "files_select_policy" ON storage.objects FOR SELECT USING (bucket_id = 'files');
CREATE POLICY "files_insert_policy" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'files' AND auth.uid() IS NOT NULL);
CREATE POLICY "files_update_policy" ON storage.objects FOR UPDATE USING (bucket_id = 'files' AND auth.uid() IS NOT NULL);
CREATE POLICY "files_delete_policy" ON storage.objects FOR DELETE USING (bucket_id = 'files' AND auth.uid() IS NOT NULL);

-- سياسات avatars
CREATE POLICY "avatars_select_policy" ON storage.objects FOR SELECT USING (bucket_id = 'avatars');
CREATE POLICY "avatars_insert_policy" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'avatars' AND auth.uid() IS NOT NULL);
CREATE POLICY "avatars_update_policy" ON storage.objects FOR UPDATE USING (bucket_id = 'avatars' AND auth.uid() IS NOT NULL);
CREATE POLICY "avatars_delete_policy" ON storage.objects FOR DELETE USING (bucket_id = 'avatars' AND auth.uid() IS NOT NULL);

-- سياسات space-images
CREATE POLICY "space_images_select_policy" ON storage.objects FOR SELECT USING (bucket_id = 'space-images');
CREATE POLICY "space_images_insert_policy" ON storage.objects FOR INSERT WITH CHECK (bucket_id = 'space-images' AND auth.uid() IS NOT NULL);
CREATE POLICY "space_images_update_policy" ON storage.objects FOR UPDATE USING (bucket_id = 'space-images' AND auth.uid() IS NOT NULL);
CREATE POLICY "space_images_delete_policy" ON storage.objects FOR DELETE USING (bucket_id = 'space-images' AND auth.uid() IS NOT NULL);

-- 8. عرض جميع buckets المنشأة
SELECT 
    id, 
    name, 
    public, 
    file_size_limit,
    array_length(allowed_mime_types, 1) as mime_types_count
FROM storage.buckets 
ORDER BY id;

-- 9. عرض جميع السياسات
SELECT 
    policyname, 
    permissive, 
    cmd 
FROM pg_policies 
WHERE tablename = 'objects' 
AND schemaname = 'storage'
ORDER BY policyname;

-- 10. رسالة نجاح
SELECT 'تم إنشاء جميع buckets والسياسات بنجاح!' as result; 