import 'package:flutter/material.dart';
import '../models/post.dart';
import '../models/reaction_type.dart';
import '../supabase_service.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../widgets/comments_sheet.dart';
import '../widgets/share_post_sheet.dart';
import '../widgets/post_hide_options_sheet.dart';
import '../widgets/hidden_post_card.dart';
import '../widgets/interactive_verified_badge.dart';

class VideosPage extends StatefulWidget {
  const VideosPage({super.key});

  @override
  State<VideosPage> createState() => _VideosPageState();
}

class _VideosPageState extends State<VideosPage> {
  final PageController _pageController = PageController();
  int _current = 0;

  List<Post> _videos = [];
  bool _loading = true;
  RealtimeChannel? _channel;

  @override
  void initState() {
    super.initState();
    _loadVideos();
    _subscribeRealtime();
  }

  void _loadVideos() async {
    final list = await SupabaseService().fetchPosts();
    final vids = list.where((p) => p.type == PostType.video && p.mediaUrl != null).toList();
    if (mounted) {
      setState(() {
        _videos = vids;
        _loading = false;
      });
    }
  }

  void _subscribeRealtime() {
    final client = Supabase.instance.client;
    _channel = client.channel('public:posts');

    _channel!.onPostgresChanges(
      event: PostgresChangeEvent.insert,
      schema: 'public',
      table: 'posts',
      callback: (payload, [ref]) {
        final newRow = payload.newRecord as Map<String, dynamic>?;
        if (newRow != null && newRow['type'] == 'video') {
          _loadVideos();
        }
      },
    );

    _channel!.subscribe();
  }

  @override
  void dispose() {
    _pageController.dispose();
    if (_channel != null) {
      Supabase.instance.client.removeChannel(_channel!);
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_videos.isEmpty) {
      return const Center(child: Text('لا توجد فيديوهات بعد'));
    }
    return PageView.builder(
      controller: _pageController,
      scrollDirection: Axis.vertical,
      itemCount: _videos.length,
      onPageChanged: (i) => setState(() => _current = i),
      itemBuilder: (context, index) {
        final post = _videos[index];
        return _VideoItem(
          post: post,
          isActive: index == _current,
          onFinished: () {
            if (index < _videos.length - 1) {
              _pageController.animateToPage(index + 1, duration: const Duration(milliseconds: 300), curve: Curves.easeOut);
            }
          },
        );
      },
    );
  }
}

class _VideoItem extends StatefulWidget {
  final Post post;
  final bool isActive;
  final VoidCallback onFinished;
  const _VideoItem({required this.post, required this.isActive, required this.onFinished});

  @override
  State<_VideoItem> createState() => _VideoItemState();
}

class _VideoItemState extends State<_VideoItem> with AutomaticKeepAliveClientMixin {
  late VideoPlayerController _videoController;
  ChewieController? _chewie;
  bool _isFollowing = false;
  bool _loadingFollow = true;
  bool _triggeredNext = false;
  bool _isSelf = false; // track if current user is owner

  // Local mutable copy to reflect live counts
  late Post _post;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _post = widget.post;
    _videoController = VideoPlayerController.network(widget.post.mediaUrl!);
    _videoController.initialize().then((_) => setState(() {}));
    _chewie = ChewieController(
      videoPlayerController: _videoController,
      autoPlay: widget.isActive,
      looping: false,
      showControls: true,
      allowMuting: true,
      allowPlaybackSpeedChanging: true,
      playbackSpeeds: const [0.5, 1.0, 1.5, 2.0],
      optionsTranslation: OptionsTranslation(
        playbackSpeedButtonText: 'سرعة التشغيل',
        cancelButtonText: 'إلغاء',
      ),
    );

    // Determine if the video owner is the current logged-in user
    final myId = Supabase.instance.client.auth.currentUser?.id;
    _isSelf = myId == widget.post.userId;

    // Only fetch follow status when viewing another user's video
    if (!_isSelf) {
      SupabaseService().isFollowing(widget.post.userId).then((res) {
        if (mounted) setState(() {
          _isFollowing = res;
          _loadingFollow = false;
        });
      });
    } else {
      // No follow button for own videos
      _loadingFollow = false;
    }

    _videoController.addListener(() {
      if (widget.isActive) {
        // Reset flag when video rewinds / becomes active again
        if (!_videoController.value.isPlaying && _videoController.value.position == Duration.zero) {
          _triggeredNext = false;
        }

        final position = _videoController.value.position;
        final duration = _videoController.value.duration;
        if (duration != null && duration.inMilliseconds > 0) {
          final remaining = duration - position;

          // إذا عاد المستخدم بالزمن أو ما زال أمامه أكثر من 0.3 ثانية (الفيديو لم ينته بعد)
          if (remaining > const Duration(milliseconds: 300)) {
            _triggeredNext = false; // السماح بالتمرير مرة أخرى عند الاقتراب من نهاية الفيديو
          }

          // مرّر تلقائياً فقط عند الانتهاء (باقى ≤ 0.3 ثانية)
          if (!_triggeredNext && remaining <= const Duration(milliseconds: 300)) {
            _triggeredNext = true;
            widget.onFinished();
          }
        }
      } else {
        _triggeredNext = false; // reset when item becomes inactive
      }
    });
  }

  @override
  void didUpdateWidget(covariant _VideoItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    // When item becomes active again (user رجع للخلف)
    if (widget.isActive && !oldWidget.isActive) {
      // أعد الفيديو للبداية وأعد ضبط العلَم
      _videoController.seekTo(Duration.zero);
      _triggeredNext = false;
    }

    if (widget.isActive && !_videoController.value.isPlaying) {
      _videoController.play();
    } else if (!widget.isActive && _videoController.value.isPlaying) {
      _videoController.pause();
    }
  }

  @override
  void dispose() {
    _chewie?.dispose();
    _videoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    // إذا كان الفيديو مخفياً، عرض بطاقة الفيديو المخفي
    if (_post.isHidden) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Container(
            margin: const EdgeInsets.all(20),
            child: HiddenPostCard(
              post: _post,
              onUndo: _undoHideVideo,
            ),
          ),
        ),
      );
    }

    if (!_videoController.value.isInitialized) {
      return const Center(child: CircularProgressIndicator());
    }

    // حساب نسبة العرض إلى الارتفاع للفيديو مع فحوصات أمان
    final videoSize = _videoController.value.size;

    // التأكد من أن أبعاد الفيديو صحيحة
    bool isVerticalVideo = false;
    if (videoSize.width > 0 && videoSize.height > 0) {
      isVerticalVideo = videoSize.height > videoSize.width;
    }

    return Stack(
      children: [
        // للفيديوهات العمودية، استخدم Positioned.fill للامتداد الكامل
        // للفيديوهات الأفقية، استخدم Center مع AspectRatio للحفاظ على النسبة
        if (isVerticalVideo)
          Positioned.fill(child: Chewie(controller: _chewie!))
        else
          Center(
            child: videoSize.width > 0 && videoSize.height > 0
                ? AspectRatio(
                    aspectRatio: videoSize.width / videoSize.height,
                    child: Chewie(controller: _chewie!),
                  )
                : Chewie(controller: _chewie!), // fallback إذا كانت الأبعاد غير صحيحة
          ),

        // معلومات المستخدم وزر المتابعة
        Positioned(
          top: 40,
          right: 16,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircleAvatar(backgroundImage: NetworkImage(widget.post.userAvatar), radius: 18),
              const SizedBox(width: 8),
              Text(widget.post.userName, style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold)),
              if (widget.post.isVerified) ...[
                const SizedBox(width: 4),
                InteractiveVerifiedBadge(
                  size: 16,
                  userName: widget.post.userName,
                ),
              ],
              if (!_isSelf) ...[
                const SizedBox(width: 8),
                _loadingFollow
                    ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2))
                    : TextButton(
                        onPressed: () async {
                          setState(() => _loadingFollow = true);
                          final nowFollow = await SupabaseService().toggleFollow(widget.post.userId);
                          if (mounted) setState(() {
                            _isFollowing = nowFollow;
                            _loadingFollow = false;
                          });
                        },
                        style: TextButton.styleFrom(
                          backgroundColor: _isFollowing ? Colors.grey.withOpacity(0.6) : Colors.red,
                          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                        ),
                        child: Text(
                          _isFollowing ? 'متابَع' : 'متابعة',
                          style: const TextStyle(color: Colors.white),
                        ),
                      ),
              ],
            ],
          ),
        ),

        // زر إخفاء الفيديو (لا يظهر لصاحب الفيديو)
        if (!_isSelf && !_post.isHidden)
          Positioned(
            top: 40,
            left: 16,
            child: IconButton(
              onPressed: () => _showVideoHideOptions(),
              icon: const Icon(Icons.close, color: Colors.white, size: 24),
              tooltip: 'إخفاء الفيديو',
              style: IconButton.styleFrom(
                backgroundColor: Colors.black.withOpacity(0.5),
                shape: const CircleBorder(),
              ),
            ),
          ),

        // وصف الفيديو أسفل
        if (widget.post.content.isNotEmpty)
          Positioned(
            bottom: 70,
            right: 16,
            child: SizedBox(
              width: MediaQuery.of(context).size.width * 0.7,
              child: Text(widget.post.content, style: const TextStyle(color: Colors.white), maxLines: 3, overflow: TextOverflow.ellipsis),
            ),
          ),

        // أزرار الإعجاب والتعليق والمشاركة – على الجانب الأيمن مثل تيك توك
        Positioned(
          right: 8,
          bottom: 120,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _actionButton(
                icon: Icons.favorite,
                active: _post.currentUserReaction == ReactionType.like,
                activeColor: Colors.red,
                count: _post.likesCount,
                onTap: () => _react(ReactionType.like),
              ),
              const SizedBox(height: 16),
              _actionButton(
                icon: Icons.comment,
                active: false,
                count: _post.commentsCount,
                onTap: _openComments,
              ),
              const SizedBox(height: 16),
              _actionButton(
                icon: Icons.share,
                active: false,
                count: _post.sharesCount,
                onTap: _sharePost,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _actionButton({required IconData icon, required bool active, required int count, required VoidCallback onTap, Color? activeColor}) {
    return Column(
      children: [
        InkWell(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black45,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(icon, color: active ? (activeColor ?? Colors.white) : Colors.white, size: 28),
          ),
        ),
        const SizedBox(height: 4),
        Text(
          '$count',
          style: const TextStyle(color: Colors.white, fontSize: 12),
        ),
      ],
    );
  }

  Future<void> _react(ReactionType type) async {
    // Optimistic UI update
    setState(() {
      if (type == ReactionType.like) {
        if (_post.currentUserReaction == ReactionType.like) {
          _post = _post.copyWith(
            currentUserReaction: ReactionType.none,
            likesCount: _post.likesCount - 1,
          );
        } else {
          // لا يوجد تفاعل سلبى الآن
          _post = _post.copyWith(
            currentUserReaction: ReactionType.like,
            likesCount: _post.likesCount + 1,
          );
        }
      }
    });

    await SupabaseService().toggleReaction(postId: _post.id, reaction: type);
  }

  void _openComments() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (_) => CommentsSheet(
        postId: _post.id,
        postType: 'post',
        onCommentAdded: () async {
          // تحديث عدد التعليقات فوراً في الواجهة
          setState(() => _post = _post.copyWith(commentsCount: _post.commentsCount + 1));

          // إعادة تحميل عدد التعليقات الصحيح من قاعدة البيانات
          try {
            final actualCount = await SupabaseService().getPostCommentsCount(_post.id);
            if (mounted && actualCount != _post.commentsCount) {
              setState(() {
                _post = _post.copyWith(commentsCount: actualCount);
              });
            }
          } catch (e) {
            // في حالة الخطأ، نحتفظ بالعدد الحالي
          }
        },
      ),
    );
  }

  void _sharePost() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) => SharePostSheet(post: _post, onShared: () {
        setState(() => _post = _post.copyWith(sharesCount: _post.sharesCount + 1));
      }),
    );
  }

  // دالة إظهار خيارات إخفاء الفيديو
  void _showVideoHideOptions() {
    showPostHideOptions(
      context: context,
      post: _post,
      onHide: (reason) => _hideVideo(reason),
      onReport: () => _reportVideo(),
    );
  }

  // دالة إخفاء الفيديو
  void _hideVideo(HideReason reason) {
    setState(() {
      _post = _post.copyWith(
        isHidden: true,
        hideReason: reason.toString().split('.').last,
        hiddenAt: DateTime.now(),
      );
    });

    // إظهار رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('تم إخفاء الفيديو'),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: 'تراجع',
          textColor: Colors.white,
          onPressed: () => _undoHideVideo(),
        ),
      ),
    );
  }

  // دالة التراجع عن إخفاء الفيديو
  void _undoHideVideo() {
    setState(() {
      _post = _post.copyWith(
        isHidden: false,
        hideReason: null,
        hiddenAt: null,
      );
    });

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إظهار الفيديو مرة أخرى'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  // دالة الإبلاغ عن الفيديو
  void _reportVideo() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('الإبلاغ عن الفيديو'),
        content: const Text('هل تريد الإبلاغ عن هذا الفيديو كمحتوى مخالف؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('تم إرسال البلاغ، شكراً لك'),
                  backgroundColor: Colors.green,
                ),
              );
            },
            child: const Text('إبلاغ', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}