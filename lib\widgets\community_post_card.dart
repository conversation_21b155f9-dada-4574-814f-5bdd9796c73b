import 'package:flutter/material.dart';
import '../models/community_post.dart';
import '../supabase_service.dart';
import '../widgets/community_comments_sheet.dart';
import 'interactive_verified_badge.dart';

class CommunityPostCard extends StatefulWidget {
  final CommunityPost post;
  final VoidCallback onChanged;
  const CommunityPostCard({super.key, required this.post, required this.onChanged});

  @override
  State<CommunityPostCard> createState() => _CommunityPostCardState();
}

class _CommunityPostCardState extends State<CommunityPostCard> {
  late CommunityPost _p;
  @override
  void initState() {
    super.initState();
    _p = widget.post;
  }

  Future<void> _vote(int v) async {
    if (_p.userVote == v) v = 0; // toggle
    await SupabaseService().voteCommunityPost(_p.id, v);
    setState(() {
      int up = _p.upVotes;
      int down = _p.downVotes;
      int? prev = _p.userVote;
      if (prev == 1) up--;
      if (prev == -1) down--;
      if (v == 1) up++;
      if (v == -1) down++;
      _p = CommunityPost(
        id: _p.id,
        communityId: _p.communityId,
        userId: _p.userId,
        userName: _p.userName,
        userAvatar: _p.userAvatar,
        content: _p.content,
        mediaUrl: _p.mediaUrl,
        mediaType: _p.mediaType,
        createdAt: _p.createdAt,
        upVotes: up,
        downVotes: down,
        commentsCount: _p.commentsCount,
        userVote: v == 0 ? null : v,
      );
    });
    widget.onChanged();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(backgroundImage: _p.userAvatar.isNotEmpty ? NetworkImage(_p.userAvatar) : null),
                const SizedBox(width: 8),
                Text(_p.userName, style: const TextStyle(fontWeight: FontWeight.bold)),
                if (_p.isVerified) ...[
                  const SizedBox(width: 4),
                  InteractiveVerifiedBadge(
                    size: 16,
                    userName: _p.userName,
                  ),
                ],
                const Spacer(),
                Text('${_p.createdAt.difference(DateTime.now()).inHours.abs()}س'),
              ],
            ),
            const SizedBox(height: 8),
            Text(_p.content),
            const SizedBox(height: 8),
            Row(
              children: [
                IconButton(
                  icon: Icon(Icons.arrow_upward, color: _p.userVote == 1 ? Colors.orange : Colors.grey),
                  onPressed: () => _vote(1),
                ),
                Text('${_p.upVotes}'),
                IconButton(
                  icon: Icon(Icons.arrow_downward, color: _p.userVote == -1 ? Colors.blue : Colors.grey),
                  onPressed: () => _vote(-1),
                ),
                Text('${_p.downVotes}'),
                const Spacer(),
                InkWell(
                  onTap: () async {
                    await showModalBottomSheet(
                      context: context,
                      isScrollControlled: true,
                      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
                      builder: (_) => CommunityCommentsSheet(post: _p),
                    );
                    widget.onChanged();
                  },
                  child: Row(
                    children: [
                      const Icon(Icons.comment, size:16),
                      const SizedBox(width:4),
                      Text('${_p.commentsCount}')
                    ],
                  ),
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
} 