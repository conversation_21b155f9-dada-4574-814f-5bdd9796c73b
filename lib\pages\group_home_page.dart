import 'package:flutter/material.dart';
import '../models/group.dart';
import '../supabase_service.dart';
import '../models/post.dart';
import '../widgets/post_card.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'group_members_tab.dart';
import 'join_requests_page.dart';
import 'group_settings_page.dart';

class GroupHomePage extends StatefulWidget {
  final String groupId;
  const GroupHomePage({super.key, required this.groupId});

  @override
  State<GroupHomePage> createState() => _GroupHomePageState();
}

class _GroupHomePageState extends State<GroupHomePage> with SingleTickerProviderStateMixin {
  late Future<Group> _futureGroup;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _futureGroup = SupabaseService().fetchGroup(widget.groupId);
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<Group>(
      future: _futureGroup,
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Scaffold(
            appBar: AppBar(),
            body: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('حدث خطأ أثناء تحميل المجموعة', textAlign: TextAlign.center),
                  const SizedBox(height: 8),
                  ElevatedButton(
                    onPressed: () {
                      setState(() => _futureGroup = SupabaseService().fetchGroup(widget.groupId));
                    },
                    child: const Text('إعادة المحاولة'),
                  ),
                ],
              ),
            ),
          );
        }
        if (!snapshot.hasData) {
          return const Scaffold(body: Center(child: CircularProgressIndicator()));
        }
        final g = snapshot.data!;
        return Scaffold(
          body: NestedScrollView(
            headerSliverBuilder: (context, inner) => [
              SliverAppBar(
                expandedHeight: 180,
                pinned: true,
                flexibleSpace: FlexibleSpaceBar(
                  title: Text(g.name),
                  background: g.coverUrl.isNotEmpty
                      ? Image.network(g.coverUrl, fit: BoxFit.cover)
                      : Container(color: Colors.grey),
                ),
                actions: [
                  if (g.isAdmin || g.ownerId == Supabase.instance.client.auth.currentUser?.id)
                    PopupMenuButton<String>(
                      onSelected: (v) async {
                        if (v == 'requests') {
                          await Navigator.push(context, MaterialPageRoute(builder: (_) => JoinRequestsPage(groupId: g.id)));
                        }
                        if (v == 'settings') {
                          final changed = await Navigator.push(context, MaterialPageRoute(builder: (_) => GroupSettingsPage(group: g)));
                          if (changed == true) setState(() => _futureGroup = SupabaseService().fetchGroup(widget.groupId));
                        }
                      },
                      itemBuilder: (_) => const [
                        PopupMenuItem(value: 'requests', child: Text('طلبات الانضمام')),
                        PopupMenuItem(value: 'settings', child: Text('إعدادات')),
                      ],
                    ),
                  _buildJoinButton(g),
                ],
                bottom: TabBar(
                  controller: _tabController,
                  isScrollable: true,
                  tabs: const [
                    Tab(text: 'حول'),
                    Tab(text: 'منشورات'),
                    Tab(text: 'الأعضاء'),
                    Tab(text: 'الصور'),
                    Tab(text: 'الفيديوهات'),
                  ],
                ),
              ),
            ],
            body: TabBarView(
              controller: _tabController,
              children: [
                _AboutTab(group: g),
                _PostsTab(groupId: g.id),
                GroupMembersTab(groupId: g.id, isAdmin: g.isAdmin),
                _PostsTab(groupId: g.id, filter: PostType.image),
                _PostsTab(groupId: g.id, filter: PostType.video),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildJoinButton(Group g) {
    if (g.ownerId == Supabase.instance.client.auth.currentUser?.id) {
      return const SizedBox();
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8),
      child: ElevatedButton(
        onPressed: () async {
          await SupabaseService().toggleMembership(g.id);
          setState(() => _futureGroup = SupabaseService().fetchGroup(widget.groupId));
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: g.joined ? Colors.grey : Colors.red,
        ),
        child: Text(g.joined ? 'مغادرة' : 'انضمام'),
      ),
    );
  }
}

class _AboutTab extends StatelessWidget {
  final Group group;
  const _AboutTab({required this.group});

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Text(group.description),
        const SizedBox(height: 12),
        Row(children: [
          const Icon(Icons.person),
          const SizedBox(width: 4),
          Text('${group.membersCount} أعضاء'),
          const SizedBox(width: 16),
          const Icon(Icons.post_add),
          const SizedBox(width: 4),
          Text('${group.postsCount} منشور'),
        ]),
      ],
    );
  }
}

class _PostsTab extends StatefulWidget {
  final String groupId;
  final PostType? filter;
  const _PostsTab({required this.groupId, this.filter});

  @override
  State<_PostsTab> createState() => _PostsTabState();
}

class _PostsTabState extends State<_PostsTab> {
  late Future<List<Post>> _futurePosts;

  @override
  void initState() {
    super.initState();
    _load();
  }

  void _load() {
    _futurePosts = SupabaseService().fetchGroupPosts(widget.groupId, typeFilter: widget.filter);
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<List<Post>>(
      future: _futurePosts,
      builder: (context, snapshot) {
        if (!snapshot.hasData) return const Center(child: CircularProgressIndicator());
        final posts = snapshot.data!;
        if (posts.isEmpty) return const Center(child: Text('لا توجد منشورات بعد'));
        return RefreshIndicator(
          onRefresh: () async {
            setState(() => _load());
            await _futurePosts;
          },
          child: ListView.builder(
            itemCount: posts.length,
            itemBuilder: (ctx, i) => PostCard(post: posts[i]),
          ),
        );
      },
    );
  }
} 