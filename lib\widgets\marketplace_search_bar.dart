import 'package:flutter/material.dart';

class MarketplaceSearchBar extends StatefulWidget {
  final Function(String) onSearch;
  final VoidCallback? onFilterTap;
  final String? initialQuery;

  const MarketplaceSearchBar({
    super.key,
    required this.onSearch,
    this.onFilterTap,
    this.initialQuery,
  });

  @override
  State<MarketplaceSearchBar> createState() => _MarketplaceSearchBarState();
}

class _MarketplaceSearchBarState extends State<MarketplaceSearchBar> {
  late TextEditingController _controller;
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialQuery);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _performSearch() {
    final query = _controller.text.trim();
    if (query.isNotEmpty) {
      setState(() => _isSearching = true);
      widget.onSearch(query);
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) setState(() => _isSearching = false);
      });
    }
  }

  void _clearSearch() {
    _controller.clear();
    widget.onSearch('');
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // أيقونة البحث
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 12),
            child: _isSearching
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Colors.orange[600]!,
                      ),
                    ),
                  )
                : Icon(
                    Icons.search,
                    color: Colors.grey[600],
                    size: 20,
                  ),
          ),
          
          // حقل البحث
          Expanded(
            child: TextField(
              controller: _controller,
              textInputAction: TextInputAction.search,
              onSubmitted: (_) => _performSearch(),
              decoration: InputDecoration(
                hintText: 'ابحث عن منتج، خدمة، أو فئة...',
                hintStyle: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          
          // زر المسح
          if (_controller.text.isNotEmpty)
            IconButton(
              onPressed: _clearSearch,
              icon: Icon(
                Icons.clear,
                color: Colors.grey[600],
                size: 18,
              ),
              padding: const EdgeInsets.all(8),
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
            ),
          
          // زر الفلاتر
          if (widget.onFilterTap != null) ...[
            Container(
              width: 1,
              height: 24,
              color: Colors.grey[300],
              margin: const EdgeInsets.symmetric(horizontal: 8),
            ),
            IconButton(
              onPressed: widget.onFilterTap,
              icon: Icon(
                Icons.tune,
                color: Colors.orange[600],
                size: 20,
              ),
              padding: const EdgeInsets.all(8),
              constraints: const BoxConstraints(
                minWidth: 32,
                minHeight: 32,
              ),
              tooltip: 'الفلاتر',
            ),
            const SizedBox(width: 8),
          ],
        ],
      ),
    );
  }
}

// ويدجت البحث السريع مع الاقتراحات
class QuickSearchChips extends StatelessWidget {
  final List<String> suggestions;
  final Function(String) onChipTap;

  const QuickSearchChips({
    super.key,
    required this.suggestions,
    required this.onChipTap,
  });

  @override
  Widget build(BuildContext context) {
    if (suggestions.isEmpty) return const SizedBox.shrink();

    return Container(
      height: 40,
      margin: const EdgeInsets.only(top: 12),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: suggestions.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: EdgeInsets.only(
              right: index == 0 ? 0 : 8,
              left: index == suggestions.length - 1 ? 0 : 8,
            ),
            child: ActionChip(
              label: Text(
                suggestions[index],
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
              onPressed: () => onChipTap(suggestions[index]),
              backgroundColor: Colors.white,
              side: BorderSide(
                color: Colors.orange[200]!,
                width: 1,
              ),
              labelStyle: TextStyle(
                color: Colors.orange[700],
              ),
              padding: const EdgeInsets.symmetric(horizontal: 12),
            ),
          );
        },
      ),
    );
  }
}

// ويدجت البحث المتقدم
class AdvancedSearchBar extends StatefulWidget {
  final Function(Map<String, dynamic>) onSearch;
  final Map<String, dynamic>? initialFilters;

  const AdvancedSearchBar({
    super.key,
    required this.onSearch,
    this.initialFilters,
  });

  @override
  State<AdvancedSearchBar> createState() => _AdvancedSearchBarState();
}

class _AdvancedSearchBarState extends State<AdvancedSearchBar> {
  late TextEditingController _queryController;
  late TextEditingController _cityController;
  late TextEditingController _minPriceController;
  late TextEditingController _maxPriceController;
  
  String? _selectedCategory;
  String? _selectedCondition;

  @override
  void initState() {
    super.initState();
    _queryController = TextEditingController(
      text: widget.initialFilters?['query'] ?? '',
    );
    _cityController = TextEditingController(
      text: widget.initialFilters?['city'] ?? '',
    );
    _minPriceController = TextEditingController(
      text: widget.initialFilters?['minPrice']?.toString() ?? '',
    );
    _maxPriceController = TextEditingController(
      text: widget.initialFilters?['maxPrice']?.toString() ?? '',
    );
    _selectedCategory = widget.initialFilters?['category'];
    _selectedCondition = widget.initialFilters?['condition'];
  }

  @override
  void dispose() {
    _queryController.dispose();
    _cityController.dispose();
    _minPriceController.dispose();
    _maxPriceController.dispose();
    super.dispose();
  }

  void _performSearch() {
    final filters = <String, dynamic>{
      'query': _queryController.text.trim(),
      'city': _cityController.text.trim(),
      'category': _selectedCategory,
      'condition': _selectedCondition,
      'minPrice': double.tryParse(_minPriceController.text),
      'maxPrice': double.tryParse(_maxPriceController.text),
    };

    // إزالة القيم الفارغة
    filters.removeWhere((key, value) => 
        value == null || (value is String && value.isEmpty));

    widget.onSearch(filters);
  }

  void _resetFilters() {
    setState(() {
      _queryController.clear();
      _cityController.clear();
      _minPriceController.clear();
      _maxPriceController.clear();
      _selectedCategory = null;
      _selectedCondition = null;
    });
    widget.onSearch({});
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // العنوان
          Row(
            children: [
              const Text(
                'البحث المتقدم',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: _resetFilters,
                child: const Text('إعادة تعيين'),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // حقل البحث الرئيسي
          TextField(
            controller: _queryController,
            decoration: const InputDecoration(
              labelText: 'كلمة البحث',
              hintText: 'ابحث عن منتج أو خدمة...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // المدينة
          TextField(
            controller: _cityController,
            decoration: const InputDecoration(
              labelText: 'المدينة',
              hintText: 'اختر المدينة...',
              prefixIcon: Icon(Icons.location_city),
              border: OutlineInputBorder(),
            ),
          ),
          
          const SizedBox(height: 16),
          
          // نطاق السعر
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _minPriceController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'السعر الأدنى',
                    prefixIcon: Icon(Icons.attach_money),
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: TextField(
                  controller: _maxPriceController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(
                    labelText: 'السعر الأعلى',
                    prefixIcon: Icon(Icons.attach_money),
                    border: OutlineInputBorder(),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 24),
          
          // أزرار العمل
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('إلغاء'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    _performSearch();
                    Navigator.pop(context);
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange[600],
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('بحث'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
