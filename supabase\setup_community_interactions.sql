-- =============================================================
--  إعداد نظام التفاعل لمنشورات المجتمع
--  Setup Community Posts Interaction System
-- =============================================================

-- هذا السكريپت ينشئ جداول ودوال التفاعل لمنشورات المجتمع

-- 1) إضافة أعمدة الإحصائيات لجدول community_posts
-- -------------------------------------------------------

-- إضافة أعمدة العدادات
ALTER TABLE community_posts 
ADD COLUMN IF NOT EXISTS likes_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS dislikes_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS shares_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS views_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS copies_count INTEGER DEFAULT 0;

-- تحديث القيم الافتراضية للمنشورات الموجودة
UPDATE community_posts 
SET 
  likes_count = COALESCE(likes_count, 0),
  dislikes_count = COALESCE(dislikes_count, 0),
  shares_count = COALESCE(shares_count, 0),
  views_count = COALESCE(views_count, 0),
  copies_count = COALESCE(copies_count, 0)
WHERE likes_count IS NULL OR dislikes_count IS NULL OR shares_count IS NULL 
   OR views_count IS NULL OR copies_count IS NULL;

-- 2) إنشاء جداول التفاعل
-- -------------------------------------------------------

-- جدول تفاعلات منشورات المجتمع
CREATE TABLE IF NOT EXISTS community_post_reactions (
    id BIGSERIAL PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES community_posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    reaction TEXT NOT NULL CHECK (reaction IN ('like', 'dislike')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(post_id, user_id)
);

-- جدول مشاهدات منشورات المجتمع
CREATE TABLE IF NOT EXISTS community_post_views (
    id BIGSERIAL PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES community_posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    viewed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(post_id, user_id)
);

-- جدول مشاركات منشورات المجتمع
CREATE TABLE IF NOT EXISTS community_post_shares (
    id BIGSERIAL PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES community_posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    shared_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(post_id, user_id)
);

-- جدول نسخ منشورات المجتمع
CREATE TABLE IF NOT EXISTS community_post_copies (
    id BIGSERIAL PRIMARY KEY,
    post_id UUID NOT NULL REFERENCES community_posts(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    copied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(post_id, user_id)
);

-- 3) إنشاء الدوال لتحديث العدادات
-- -------------------------------------------------------

-- دالة تحديث عدادات التفاعل
CREATE OR REPLACE FUNCTION update_community_post_reactions(post_id_param UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE community_posts 
  SET 
    likes_count = (
      SELECT COUNT(*) FROM community_post_reactions 
      WHERE post_id = post_id_param AND reaction = 'like'
    ),
    dislikes_count = (
      SELECT COUNT(*) FROM community_post_reactions 
      WHERE post_id = post_id_param AND reaction = 'dislike'
    )
  WHERE id = post_id_param;
END;
$$ LANGUAGE plpgsql;

-- دالة زيادة عداد المشاهدات
CREATE OR REPLACE FUNCTION increment_community_post_views(post_id_param UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE community_posts 
  SET views_count = views_count + 1
  WHERE id = post_id_param;
END;
$$ LANGUAGE plpgsql;

-- دالة زيادة عداد المشاركات
CREATE OR REPLACE FUNCTION increment_community_post_shares(post_id_param UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE community_posts 
  SET shares_count = shares_count + 1
  WHERE id = post_id_param;
END;
$$ LANGUAGE plpgsql;

-- دالة زيادة عداد النسخ
CREATE OR REPLACE FUNCTION increment_community_post_copies(post_id_param UUID)
RETURNS VOID AS $$
BEGIN
  UPDATE community_posts 
  SET copies_count = copies_count + 1
  WHERE id = post_id_param;
END;
$$ LANGUAGE plpgsql;

-- 4) إنشاء triggers لتحديث العدادات تلقائياً
-- -------------------------------------------------------

-- trigger لتحديث عدادات التفاعل
CREATE OR REPLACE FUNCTION trigger_update_community_post_reactions()
RETURNS TRIGGER AS $$
BEGIN
  IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
    PERFORM update_community_post_reactions(NEW.post_id);
    RETURN NEW;
  ELSIF TG_OP = 'DELETE' THEN
    PERFORM update_community_post_reactions(OLD.post_id);
    RETURN OLD;
  END IF;
  RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- إنشاء trigger
DROP TRIGGER IF EXISTS community_post_reactions_trigger ON community_post_reactions;
CREATE TRIGGER community_post_reactions_trigger
  AFTER INSERT OR UPDATE OR DELETE ON community_post_reactions
  FOR EACH ROW EXECUTE FUNCTION trigger_update_community_post_reactions();

-- 5) تعطيل RLS على جداول التفاعل
-- -------------------------------------------------------

ALTER TABLE community_post_reactions DISABLE ROW LEVEL SECURITY;
ALTER TABLE community_post_views DISABLE ROW LEVEL SECURITY;
ALTER TABLE community_post_shares DISABLE ROW LEVEL SECURITY;
ALTER TABLE community_post_copies DISABLE ROW LEVEL SECURITY;

-- منح صلاحيات شاملة
GRANT ALL ON community_post_reactions TO authenticated, public;
GRANT ALL ON community_post_views TO authenticated, public;
GRANT ALL ON community_post_shares TO authenticated, public;
GRANT ALL ON community_post_copies TO authenticated, public;

-- 6) فحص النتائج
-- -------------------------------------------------------

SELECT 
  '🔍 TABLES CHECK' as check_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'community_post_reactions')
    AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'community_post_views')
    AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'community_post_shares')
    AND EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'community_post_copies')
    THEN '✅ SUCCESS: All interaction tables created'
    ELSE '❌ FAILED: Some tables are missing'
  END as status;

SELECT 
  '🔍 FUNCTIONS CHECK' as check_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'update_community_post_reactions')
    AND EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'increment_community_post_views')
    AND EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'increment_community_post_shares')
    AND EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'increment_community_post_copies')
    THEN '✅ SUCCESS: All functions created'
    ELSE '❌ FAILED: Some functions are missing'
  END as status;

SELECT 
  '🔍 COLUMNS CHECK' as check_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'community_posts' AND column_name = 'likes_count')
    AND EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'community_posts' AND column_name = 'views_count')
    THEN '✅ SUCCESS: All stat columns added'
    ELSE '❌ FAILED: Some columns are missing'
  END as status;

-- 7) النتيجة النهائية
-- -------------------------------------------------------

SELECT 
  '🎉 FINAL RESULT' as check_type,
  '✅ COMMUNITY INTERACTION SYSTEM READY!' as status,
  'Posts can now have likes, shares, views, copies like regular posts' as details;

-- =============================================================
--  تعليمات ما بعد التشغيل
-- =============================================================

/*

بعد تشغيل هذا السكريپت:

1. منشورات المجتمع ستدعم جميع أنواع التفاعل:
   - الإعجاب/عدم الإعجاب (like/dislike)
   - المشاهدات (views)
   - المشاركات (shares)
   - النسخ (copies)
   - التعليقات (comments)

2. العدادات ستتحدث تلقائياً عند التفاعل

3. نفس نظام التفاعل المستخدم في المنشورات العادية

4. جميع الإحصائيات ستظهر في التطبيق

إذا رأيت "✅ COMMUNITY INTERACTION SYSTEM READY!"
فهذا يعني أن النظام جاهز للاستخدام.

*/

-- =============================================================
--  انتهى إعداد نظام التفاعل لمنشورات المجتمع
-- =============================================================
