# 🚨 إصلاح سريع لخطأ قاعدة البيانات

## المشكلة
```
خطأ في تحميل البيانات : Exception فشل في جلب التبرعات:
PostgrestException: Could not find a relationship between 'charity_items' and 'profiles'
```

## ✅ الحل السريع

### 1. افتح Supabase Dashboard
- اذهب إلى مشروعك في Supabase
- اضغط على **SQL Editor**

### 2. انسخ والصق الكود التالي
انسخ محتوى ملف `quick_fix.sql` كاملاً والصقه في SQL Editor

### 3. اضغط Run
اضغط على زر **Run** لتنفيذ جميع الاستعلامات

### 4. تحقق من النتيجة
يجب أن ترى رسالة: `تم إنشاء جداول الصدقات بنجاح!`

## 🔍 ما يفعله الإصلاح

### ✅ إنشاء الجداول المطلوبة:
- `profiles` - معلومات المستخدمين
- `charity_items` - عناصر الصدقات
- `charity_interests` - الاهتمامات
- `charity_reports` - البلاغات

### ✅ إعداد الصلاحيات:
- Row Level Security (RLS)
- سياسات الأمان
- صلاحيات القراءة والكتابة

### ✅ إنشاء الفهارس:
- فهارس لتحسين الأداء
- فهارس على الحقول المهمة

### ✅ الدوال التلقائية:
- تحديث عداد الاهتمامات
- تحديث وقت التعديل

### ✅ بيانات تجريبية:
- عنصر تبرع تجريبي
- طلب مساعدة طارئ

## 🧪 اختبار النظام

بعد تنفيذ الإصلاح:

1. **افتح التطبيق**
2. **اذهب لقسم الصدقات**
3. **يجب أن ترى البيانات التجريبية**
4. **جرب إضافة عنصر جديد**

## ⚠️ ملاحظات مهمة

### إذا استمر الخطأ:
1. تأكد من تسجيل الدخول في التطبيق
2. تحقق من إعدادات Supabase
3. راجع صلاحيات المستخدم

### للتحقق من الجداول:
```sql
-- التحقق من وجود الجداول
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name LIKE '%charity%';

-- التحقق من البيانات
SELECT COUNT(*) as total_items FROM charity_items;
```

### للتحقق من الصلاحيات:
```sql
-- التحقق من RLS
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE tablename LIKE '%charity%';
```

## 📞 إذا احتجت مساعدة

1. **تحقق من logs** في Supabase
2. **راجع Authentication** settings
3. **تأكد من API keys** في التطبيق

## 🎯 النتيجة المتوقعة

بعد الإصلاح:
- ✅ قسم الصدقات يعمل بدون أخطاء
- ✅ يمكن عرض التبرعات والطلبات
- ✅ يمكن إضافة عناصر جديدة
- ✅ جميع الوظائف تعمل بشكل طبيعي
