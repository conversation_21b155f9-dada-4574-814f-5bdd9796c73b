import 'package:flutter/material.dart';
import 'dart:async';
import '../supabase_service.dart';
import '../models/story.dart';
import 'package:video_player/video_player.dart';
import 'package:chewie/chewie.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/post.dart';
import '../models/reaction_type.dart';
import 'chat_page.dart';

class StoryViewerPage extends StatefulWidget {
  final String initialUserId;
  const StoryViewerPage({super.key, required this.initialUserId});

  @override
  State<StoryViewerPage> createState() => _StoryViewerPageState();
}

class _StoryViewerPageState extends State<StoryViewerPage> with TickerProviderStateMixin {
  List<Story> _stories = [];
  int _index = 0;
  bool _liked = false;
  int _viewsCount = 0;
  int _likesCount = 0;
  
  // متغيرات شريط التقدم
  late AnimationController _progressController;
  late Animation<double> _progressAnimation;
  Timer? _storyTimer;
  
  // مدة العرض لكل نوع قصة
  static const int _textStoryDuration = 180; // 3 دقائق للنصوص
  static const int _imageStoryDuration = 180; // 3 دقائق للصور
  static const int _videoStoryDuration = 180; // 3 دقائق للفيديو (أو مدة الفيديو الفعلية إذا كانت أقل)

  Future<void> _updateLikedState() async {
    if (_stories.isEmpty) return;
    final story = _stories[_index];
    final uid = Supabase.instance.client.auth.currentUser?.id;
    if (uid == null) return;
    final rows = await Supabase.instance.client
        .from('story_reactions')
        .select('type')
        .eq('story_id', story.id)
        .eq('user_id', uid)
        .maybeSingle();
    if (!mounted) return;
    setState(() => _liked = rows != null && rows['type'] == 'like');
  }

  Future<void> _refreshStats() async {
    if (_stories.isEmpty) return;
    final story = _stories[_index];
    
    try {
      // زيادة عداد المشاهدات
      await SupabaseService().incrementStoryView(story.id);
      
      // جلب الإحصائيات المحدثة
      final stats = await SupabaseService().getStoryStats(story.id);
      
      if (!mounted) return;
      
      setState(() {
        _viewsCount = stats['views'] ?? 0;
        _likesCount = stats['likes'] ?? 0;
      });
      
      print('📊 إحصائيات القصة: المشاهدات: $_viewsCount، الإعجابات: $_likesCount');
    } catch (e) {
      print('❌ خطأ في تحديث إحصائيات القصة: $e');
    }
  }

  @override
  void initState() {
    super.initState();
    _initializeProgressController();
    Future.microtask(() async {
      final list = await SupabaseService().storiesStream().first;
      if (!mounted) return;
      setState(() {
        _stories = list.where((s) => s.userId == widget.initialUserId).toList();
      });
      await _updateLikedState();
      await _refreshStats();
      _startStoryTimer();
    });
  }

  void _initializeProgressController() {
    _progressController = AnimationController(
      duration: Duration(seconds: _getStoryDuration()),
      vsync: this,
    );
    _progressAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(_progressController);
    
    _progressController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _next();
      }
    });
  }

  int _getStoryDuration() {
    if (_stories.isEmpty || _index >= _stories.length) return _textStoryDuration;
    
    final story = _stories[_index];
    switch (story.type) {
      case StoryType.text:
        return _textStoryDuration;
      case StoryType.image:
        return _imageStoryDuration;
      case StoryType.video:
        // للفيديو، استخدم مدة الفيديو الفعلية أو 20 ثانية كحد أقصى
        // سيتم تحديث هذا في _VideoPlayer
        return _videoStoryDuration;
      default:
        return _textStoryDuration;
    }
  }

  void _startStoryTimer() {
    _storyTimer?.cancel();
    _progressController.reset();
    _progressController.duration = Duration(seconds: _getStoryDuration());
    
    // تأخير قصير قبل بدء شريط التقدم
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        _progressController.forward();
        print('⏱️ بدء عرض القصة لمدة: ${_getStoryDuration()} ثانية');
      }
    });
  }

  void _pauseStoryTimer() {
    _storyTimer?.cancel();
    _progressController.stop();
    print('⏸️ إيقاف مؤقت للقصة');
  }

  void _resumeStoryTimer() {
    _progressController.forward();
    print('▶️ استئناف القصة');
  }

  void _next() {
    if (_index < _stories.length - 1) {
      setState(() {
        _index++;
      });
      _updateLikedState();
      _refreshStats();
      _startStoryTimer(); // إعادة تشغيل شريط التقدم للقصة التالية
    } else {
      Navigator.pop(context);
    }
  }

  @override
  void dispose() {
    _storyTimer?.cancel();
    _progressController.dispose();
    super.dispose();
  }

  Future<void> _toggleLike() async {
    final story = _stories[_index];
    
    try {
      await SupabaseService().toggleStoryReaction(storyId: story.id, type: ReactionType.like);
      
      // تحديث حالة الإعجاب
      setState(() {
        _liked = !_liked;
      });
      
      // جلب الإحصائيات المحدثة من قاعدة البيانات
      final stats = await SupabaseService().getStoryStats(story.id);
      
      if (!mounted) return;
      
      setState(() {
        _likesCount = stats['likes'] ?? 0;
      });
      
      print('❤️ تم تحديث الإعجاب: $_liked، العدد الجديد: $_likesCount');
    } catch (e) {
      print('❌ خطأ في تحديث الإعجاب: $e');
    }
  }

  Future<void> _replyStory() async {
    final story = _stories[_index];
    final controller = TextEditingController();
    final msg = await showDialog<String>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: const Text('رد على القصة'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(hintText: 'اكتب ردك هنا'),
          autofocus: true,
        ),
        actions: [
          TextButton(onPressed: () => Navigator.pop(ctx), child: const Text('إلغاء')),
          TextButton(onPressed: () => Navigator.pop(ctx, controller.text.trim()), child: const Text('إرسال')),
        ],
      ),
    );
    if (msg != null && msg.isNotEmpty) {
      final chatId = await SupabaseService().getOrCreateChat(story.userId);
      await SupabaseService().sendMessage(chatId: chatId, content: msg);
      if (!mounted) return;
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (_) => ChatPage(
            chatId: chatId,
            otherId: story.userId,
            username: story.userName,
            avatarUrl: story.userAvatar,
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_stories.isEmpty) {
      return const Scaffold(body: Center(child: CircularProgressIndicator()));
    }
    final story = _stories[_index];
    Widget content;
    switch (story.type) {
      case StoryType.text:
        content = Center(
          child: Text(story.text ?? '', style: const TextStyle(fontSize: 28, color: Colors.white)),
        );
        break;
      case StoryType.image:
        content = Image.network(story.mediaUrl ?? '', fit: BoxFit.contain);
        break;
      case StoryType.video:
        content = _VideoPlayer(
          url: story.mediaUrl ?? '',
          maxDuration: _videoStoryDuration,
          onDurationChanged: (int actualDuration) {
            // تحديث مدة الفيديو الفعلية
            if (mounted) {
              setState(() {
                // إعادة تهيئة شريط التقدم بالمدة الجديدة
                _progressController.duration = Duration(seconds: actualDuration);
                _progressController.reset();
                _progressController.forward();
              });
            }
          },
        );
        break;
    }

    return GestureDetector(
      onTap: () {
        // إيقاف مؤقت عند النقر العادي
        if (_progressController.status == AnimationStatus.forward) {
          _pauseStoryTimer();
        } else {
          _resumeStoryTimer();
        }
      },
      onDoubleTap: _next, // الانتقال للقصة التالية عند النقر المزدوج
      onLongPressStart: (_) => _pauseStoryTimer(),
      onLongPressEnd: (_) => _resumeStoryTimer(),
      child: Scaffold(
        backgroundColor: Colors.black,
        body: SafeArea(child: Stack(children: [
          Center(child: content),
          
          // شريط التقدم في الأعلى
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              height: 4,
              margin: const EdgeInsets.all(16),
              child: Row(
                children: List.generate(_stories.length, (index) {
                  return Expanded(
                    child: Container(
                      margin: const EdgeInsets.symmetric(horizontal: 2),
                      child: AnimatedBuilder(
                        animation: index == _index ? _progressAnimation : const AlwaysStoppedAnimation(0.0),
                        builder: (context, child) {
                          return LinearProgressIndicator(
                            value: index < _index ? 1.0 : (index == _index ? _progressAnimation.value : 0.0),
                            backgroundColor: Colors.white.withOpacity(0.3),
                            valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                          );
                        },
                      ),
                    ),
                  );
                }),
              ),
            ),
          ),
          // أعلى يسار - أزرار الإغلاق والحذف
          Positioned(
            top: 16,
            left: 16,
            child: Row(
              children: [
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Navigator.pop(context),
                ),
                if (Supabase.instance.client.auth.currentUser?.id == story.userId)
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.redAccent),
                    onPressed: () async {
                      final ok = await showDialog<bool>(
                        context: context,
                        builder: (ctx) => AlertDialog(
                          title: const Text('حذف القصة؟'),
                          content: const Text('لن تتمكن من استعادتها بعد الحذف'),
                          actions: [
                            TextButton(onPressed: ()=>Navigator.pop(ctx,false), child: const Text('إلغاء')),
                            TextButton(onPressed: ()=>Navigator.pop(ctx,true), child: const Text('حذف', style: TextStyle(color: Colors.red)))
                          ],
                        ),
                      );
                      if (ok==true){
                        await SupabaseService().deleteStory(story.id);
                        if (mounted){
                          Navigator.pop(context);
                        }
                      }
                    },
                  ),
              ],
            ),
          ),
          
          // أعلى يمين - إحصائيات المشاهدات والإعجابات
          Positioned(
            top: 16,
            right: 16,
            child: Row(
              children: [
                // إحصائيات المشاهدات
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.remove_red_eye, color: Colors.white, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '$_viewsCount',
                        style: const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                // إحصائيات الإعجابات
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.5),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.favorite, color: Colors.redAccent, size: 16),
                      const SizedBox(width: 4),
                      Text(
                        '$_likesCount',
                        style: const TextStyle(color: Colors.white, fontSize: 12),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          Positioned(
            right: 16,
            bottom: 100,
            child: Column(
              children: [
                IconButton(
                  iconSize: 36,
                  onPressed: _toggleLike,
                  icon: Icon(_liked ? Icons.favorite : Icons.favorite_border, color: Colors.redAccent),
                ),
                const SizedBox(height: 8),
                IconButton(
                  iconSize: 34,
                  onPressed: _replyStory,
                  icon: const Icon(Icons.reply, color: Colors.white),
                ),
              ],
            ),
          ),
        ])),
      ),
    );
  }
}

class _VideoPlayer extends StatefulWidget {
  final String url;
  final int maxDuration; // المدة القصوى بالثواني
  final Function(int)? onDurationChanged; // callback لتحديث مدة الفيديو
  const _VideoPlayer({
    required this.url, 
    this.maxDuration = 180, // 3 دقائق كحد أقصى
    this.onDurationChanged,
  });
  @override
  State<_VideoPlayer> createState() => _VideoPlayerState();
}

class _VideoPlayerState extends State<_VideoPlayer> {
  late VideoPlayerController _controller;
  ChewieController? _chewie;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeVideo();
  }

  Future<void> _initializeVideo() async {
    try {
      _controller = VideoPlayerController.network(widget.url);
      await _controller.initialize();
      
      // التحقق من مدة الفيديو
      final duration = _controller.value.duration;
      final maxDuration = Duration(seconds: widget.maxDuration);
      
      // استخدام مدة الفيديو الفعلية أو المدة القصوى
      final actualDuration = duration < maxDuration ? duration : maxDuration;
      
      // إخطار الوالد بمدة الفيديو الفعلية
      if (widget.onDurationChanged != null) {
        widget.onDurationChanged!(actualDuration.inSeconds);
      }
      
      print('🎬 مدة الفيديو: ${duration.inSeconds} ثانية، المدة المستخدمة: ${actualDuration.inSeconds} ثانية');
      
      _chewie = ChewieController(
        videoPlayerController: _controller,
        autoPlay: true,
        looping: false,
        allowPlaybackSpeedChanging: false, // إخفاء خيارات السرعة للقصص
        showControls: false, // إخفاء عناصر التحكم
        maxScale: 1.0, // منع التكبير
        aspectRatio: _controller.value.aspectRatio,
        errorBuilder: (context, errorMessage) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error, color: Colors.white, size: 48),
                const SizedBox(height: 16),
                Text(
                  'خطأ في تحميل الفيديو',
                  style: const TextStyle(color: Colors.white, fontSize: 16),
                ),
              ],
            ),
          );
        },
      );
      
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    } catch (e) {
      print('❌ خطأ في تحميل الفيديو: $e');
      if (mounted) {
        setState(() {
          _isInitialized = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _chewie?.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized || _chewie == null) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
        ),
      );
    }

    return Chewie(controller: _chewie!);
  }
} 