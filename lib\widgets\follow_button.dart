import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../supabase_service.dart';

class FollowButton extends StatefulWidget {
  final String userId;
  final String userName;
  final double? height;
  final double? fontSize;
  final EdgeInsets? padding;
  final bool compact;

  const FollowButton({
    super.key,
    required this.userId,
    required this.userName,
    this.height,
    this.fontSize,
    this.padding,
    this.compact = false,
  });

  @override
  State<FollowButton> createState() => _FollowButtonState();
}

class _FollowButtonState extends State<FollowButton> {
  bool? _isFollowing;
  bool _loading = false;

  @override
  void initState() {
    super.initState();
    _checkFollowStatus();
  }

  Future<void> _checkFollowStatus() async {
    final currentUserId = Supabase.instance.client.auth.currentUser?.id;
    if (currentUserId == null || currentUserId == widget.userId) {
      setState(() => _isFollowing = null); // لا يمكن متابعة نفسك
      return;
    }

    try {
      final isFollowing = await SupabaseService().isFollowing(widget.userId);
      if (mounted) {
        setState(() => _isFollowing = isFollowing);
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isFollowing = false);
      }
    }
  }

  Future<void> _toggleFollow() async {
    if (_loading || _isFollowing == null) return;

    setState(() => _loading = true);
    
    try {
      final newFollowStatus = await SupabaseService().toggleFollow(widget.userId);
      if (mounted) {
        setState(() {
          _isFollowing = newFollowStatus;
          _loading = false;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(newFollowStatus ? 'تم متابعة ${widget.userName}' : 'تم إلغاء متابعة ${widget.userName}'),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() => _loading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في ${_isFollowing! ? 'إلغاء المتابعة' : 'المتابعة'}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isFollowing == null) return const SizedBox.shrink();

    final height = widget.height ?? (widget.compact ? 20 : 24);
    final fontSize = widget.fontSize ?? (widget.compact ? 9 : 11);
    final padding = widget.padding ?? EdgeInsets.symmetric(
      horizontal: widget.compact ? 8 : 12,
      vertical: widget.compact ? 2 : 4,
    );

    return Container(
      height: height,
      padding: padding,
      decoration: BoxDecoration(
        color: _isFollowing! ? Colors.grey.shade200 : Colors.blue,
        borderRadius: BorderRadius.circular(height / 2),
        border: _isFollowing! ? Border.all(color: Colors.grey.shade400) : null,
      ),
      child: InkWell(
        onTap: _loading ? null : _toggleFollow,
        borderRadius: BorderRadius.circular(height / 2),
        child: Center(
          child: _loading
              ? SizedBox(
                  width: height * 0.5,
                  height: height * 0.5,
                  child: CircularProgressIndicator(
                    strokeWidth: 1.5,
                    color: _isFollowing! ? Colors.grey.shade600 : Colors.white,
                  ),
                )
              : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _isFollowing! ? Icons.check : Icons.add,
                      size: fontSize + 2,
                      color: _isFollowing! ? Colors.grey.shade700 : Colors.white,
                    ),
                    SizedBox(width: widget.compact ? 2 : 4),
                    Text(
                      _isFollowing! ? 'متابَع' : 'متابعة',
                      style: TextStyle(
                        fontSize: fontSize,
                        fontWeight: FontWeight.w600,
                        color: _isFollowing! ? Colors.grey.shade700 : Colors.white,
                      ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }
}
