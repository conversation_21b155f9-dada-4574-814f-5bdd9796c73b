[{"merged": "dev.fluttercommunity.plus.wakelock.wakelock_plus-release-4:/drawable/notification_bg.xml", "source": "dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13:/drawable/notification_bg.xml"}, {"merged": "dev.fluttercommunity.plus.wakelock.wakelock_plus-release-4:/drawable/notification_tile_bg.xml", "source": "dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13:/drawable/notification_tile_bg.xml"}, {"merged": "dev.fluttercommunity.plus.wakelock.wakelock_plus-release-4:/drawable/notification_icon_background.xml", "source": "dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13:/drawable/notification_icon_background.xml"}, {"merged": "dev.fluttercommunity.plus.wakelock.wakelock_plus-release-4:/drawable/notification_bg_low.xml", "source": "dev.fluttercommunity.plus.wakelock.wakelock_plus-core-1.13.1-13:/drawable/notification_bg_low.xml"}]