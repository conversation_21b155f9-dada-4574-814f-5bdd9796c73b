import 'package:flutter/material.dart';
import '../models/real_estate_property.dart';
import '../services/real_estate_service.dart';

class EditPropertyPage extends StatefulWidget {
  final RealEstateProperty property;

  const EditPropertyPage({
    super.key,
    required this.property,
  });

  @override
  State<EditPropertyPage> createState() => _EditPropertyPageState();
}

class _EditPropertyPageState extends State<EditPropertyPage> {
  final _formKey = GlobalKey<FormState>();
  final RealEstateService _realEstateService = RealEstateService();
  
  // Controllers
  late final TextEditingController _titleController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _priceController;
  late final TextEditingController _areaController;
  late final TextEditingController _phoneController;
  late final TextEditingController _whatsappController;
  
  // Form values
  late PropertyType _selectedType;
  late PropertyPurpose _selectedPurpose;
  late PropertyCategory _selectedCategory;
  late Currency _selectedCurrency;
  
  late int _bedrooms;
  late int _bathrooms;
  late int _floors;
  late int _parkingSpaces;
  
  late List<String> _selectedFeatures;
  late bool _allowAppMessages;
  bool _loading = false;

  // قوائم الميزات
  final List<String> _availableFeatures = [
    'مصعد', 'حديقة', 'مسبح', 'أمن وحراسة', 'موقف سيارات',
    'شرفة', 'تكييف مركزي', 'تدفئة مركزية', 'إنترنت', 'كاميرات مراقبة'
  ];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  void _initializeControllers() {
    final property = widget.property;
    
    _titleController = TextEditingController(text: property.title);
    _descriptionController = TextEditingController(text: property.description);
    _priceController = TextEditingController(text: property.price.toString());
    _areaController = TextEditingController(text: property.area?.toString() ?? '');
    _phoneController = TextEditingController(text: property.contactPhone ?? '');
    _whatsappController = TextEditingController(text: property.contactWhatsapp ?? '');
    
    _selectedType = property.propertyType;
    _selectedPurpose = property.purpose;
    _selectedCategory = property.category;
    _selectedCurrency = property.currency;
    
    _bedrooms = property.bedrooms;
    _bathrooms = property.bathrooms;
    _floors = property.floors;
    _parkingSpaces = property.parkingSpaces;
    
    _selectedFeatures = List.from(property.features);
    _allowAppMessages = property.allowAppMessages;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _areaController.dispose();
    _phoneController.dispose();
    _whatsappController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تعديل العقار'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _loading ? null : _saveChanges,
            child: _loading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(color: Colors.white, strokeWidth: 2),
                  )
                : const Text('حفظ', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // معلومات أساسية
            _buildSection(
              'المعلومات الأساسية',
              Icons.info_outline,
              [
                _buildTextField(
                  controller: _titleController,
                  label: 'عنوان الإعلان',
                  validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
                ),
                
                const SizedBox(height: 16),
                
                _buildTextField(
                  controller: _descriptionController,
                  label: 'وصف العقار',
                  maxLines: 4,
                  validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // نوع العقار والغرض
            _buildSection(
              'نوع العقار والغرض',
              Icons.home_work,
              [
                _buildDropdown<PropertyType>(
                  label: 'نوع العقار',
                  value: _selectedType,
                  items: PropertyType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Row(
                        children: [
                          Text(type.icon),
                          const SizedBox(width: 8),
                          Text(type.arabicName),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) => setState(() => _selectedType = value!),
                ),
                
                const SizedBox(height: 16),
                
                _buildDropdown<PropertyPurpose>(
                  label: 'الغرض',
                  value: _selectedPurpose,
                  items: PropertyPurpose.values.map((purpose) {
                    return DropdownMenuItem(
                      value: purpose,
                      child: Row(
                        children: [
                          Text(purpose.icon),
                          const SizedBox(width: 8),
                          Text(purpose.arabicName),
                        ],
                      ),
                    );
                  }).toList(),
                  onChanged: (value) => setState(() => _selectedPurpose = value!),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // السعر والمساحة
            _buildSection(
              'السعر والمساحة',
              Icons.attach_money,
              [
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: _buildTextField(
                        controller: _priceController,
                        label: 'السعر',
                        keyboardType: TextInputType.number,
                        validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: _buildDropdown<Currency>(
                        label: 'العملة',
                        value: _selectedCurrency,
                        items: Currency.values.map((currency) {
                          return DropdownMenuItem(
                            value: currency,
                            child: Text(currency.symbol),
                          );
                        }).toList(),
                        onChanged: (value) => setState(() => _selectedCurrency = value!),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 16),
                
                _buildTextField(
                  controller: _areaController,
                  label: 'المساحة (متر مربع)',
                  keyboardType: TextInputType.number,
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // تفاصيل العقار
            if (_selectedCategory == PropertyCategory.residential) ...[
              _buildSection(
                'تفاصيل العقار',
                Icons.bed,
                [
                  Row(
                    children: [
                      Expanded(
                        child: _buildNumberField(
                          label: 'عدد الغرف',
                          value: _bedrooms,
                          onChanged: (value) => setState(() => _bedrooms = value),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildNumberField(
                          label: 'عدد الحمامات',
                          value: _bathrooms,
                          onChanged: (value) => setState(() => _bathrooms = value),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 16),
                  
                  Row(
                    children: [
                      Expanded(
                        child: _buildNumberField(
                          label: 'عدد الطوابق',
                          value: _floors,
                          onChanged: (value) => setState(() => _floors = value),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildNumberField(
                          label: 'مواقف السيارات',
                          value: _parkingSpaces,
                          onChanged: (value) => setState(() => _parkingSpaces = value),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: 24),
            ],
            
            // الميزات
            _buildSection(
              'الميزات',
              Icons.star,
              [
                _buildMultiSelectChips(
                  'ميزات العقار',
                  _availableFeatures,
                  _selectedFeatures,
                  (features) => setState(() => _selectedFeatures = features),
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            
            // معلومات التواصل
            _buildSection(
              'معلومات التواصل',
              Icons.contact_phone,
              [
                _buildTextField(
                  controller: _phoneController,
                  label: 'رقم الهاتف',
                  keyboardType: TextInputType.phone,
                ),
                
                const SizedBox(height: 16),
                
                _buildTextField(
                  controller: _whatsappController,
                  label: 'رقم الواتساب',
                  keyboardType: TextInputType.phone,
                ),
                
                const SizedBox(height: 16),
                
                SwitchListTile(
                  title: const Text('السماح بالرسائل داخل التطبيق'),
                  value: _allowAppMessages,
                  onChanged: (value) => setState(() => _allowAppMessages = value),
                  activeColor: Colors.blue[600],
                ),
              ],
            ),
            
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  Widget _buildSection(String title, IconData icon, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: Colors.blue[600]),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.blue[600],
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ...children,
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    int maxLines = 1,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.blue[600]!),
        ),
      ),
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
    );
  }

  Widget _buildDropdown<T>({
    required String label,
    required T value,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.blue[600]!),
        ),
      ),
      items: items,
      onChanged: onChanged,
    );
  }

  Widget _buildNumberField({
    required String label,
    required int value,
    required void Function(int) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            IconButton(
              onPressed: value > 0 ? () => onChanged(value - 1) : null,
              icon: const Icon(Icons.remove),
              style: IconButton.styleFrom(
                backgroundColor: Colors.grey[200],
                foregroundColor: Colors.grey[700],
              ),
            ),
            Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  value.toString(),
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                ),
              ),
            ),
            IconButton(
              onPressed: () => onChanged(value + 1),
              icon: const Icon(Icons.add),
              style: IconButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMultiSelectChips(
    String title,
    List<String> options,
    List<String> selected,
    void Function(List<String>) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: options.map((option) {
            final isSelected = selected.contains(option);
            return FilterChip(
              label: Text(option),
              selected: isSelected,
              onSelected: (isSelected) {
                final newSelected = List<String>.from(selected);
                if (isSelected) {
                  newSelected.add(option);
                } else {
                  newSelected.remove(option);
                }
                onChanged(newSelected);
              },
              selectedColor: Colors.blue[100],
              checkmarkColor: Colors.blue[600],
            );
          }).toList(),
        ),
      ],
    );
  }

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _loading = true);

    try {
      final updatedProperty = RealEstateProperty(
        id: widget.property.id,
        userId: widget.property.userId,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        propertyType: _selectedType,
        purpose: _selectedPurpose,
        category: _selectedCategory,
        country: widget.property.country,
        city: widget.property.city,
        district: widget.property.district,
        address: widget.property.address,
        latitude: widget.property.latitude,
        longitude: widget.property.longitude,
        price: double.parse(_priceController.text.trim()),
        currency: _selectedCurrency,
        area: _areaController.text.trim().isNotEmpty ? double.parse(_areaController.text.trim()) : null,
        bedrooms: _bedrooms,
        bathrooms: _bathrooms,
        floors: _floors,
        parkingSpaces: _parkingSpaces,
        features: _selectedFeatures,
        amenities: widget.property.amenities,
        contactPhone: _phoneController.text.trim().isNotEmpty ? _phoneController.text.trim() : null,
        contactWhatsapp: _whatsappController.text.trim().isNotEmpty ? _whatsappController.text.trim() : null,
        allowAppMessages: _allowAppMessages,
        isActive: widget.property.isActive,
        isFeatured: widget.property.isFeatured,
        isVerified: widget.property.isVerified,
        viewsCount: widget.property.viewsCount,
        createdAt: widget.property.createdAt,
        updatedAt: DateTime.now(),
        expiresAt: widget.property.expiresAt,
        images: widget.property.images,
        isFavorite: widget.property.isFavorite,
        ownerName: widget.property.ownerName,
        ownerAvatar: widget.property.ownerAvatar,
      );

      await _realEstateService.updateProperty(widget.property.id, updatedProperty);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث العقار بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.pop(context, true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث العقار: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() => _loading = false);
    }
  }
}
