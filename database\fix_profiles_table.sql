-- إصلاح جدول profiles لنظام التصويتات

-- التحقق من وجود جدول profiles وإنشاؤه إذا لم يكن موجوداً
CREATE TABLE IF NOT EXISTS profiles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT UNIQUE,
    full_name TEXT,
    avatar_url TEXT,
    username TEXT UNIQUE,
    bio TEXT,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إضافة الأعمدة المطلوبة إذا لم تكن موجودة
DO $$
BEGIN
    -- إضافة full_name إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'full_name'
    ) THEN
        ALTER TABLE profiles ADD COLUMN full_name TEXT;
    END IF;

    -- إضافة avatar_url إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'avatar_url'
    ) THEN
        ALTER TABLE profiles ADD COLUMN avatar_url TEXT;
    END IF;

    -- إضافة username إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'username'
    ) THEN
        ALTER TABLE profiles ADD COLUMN username TEXT UNIQUE;
    END IF;

    -- إضافة bio إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'bio'
    ) THEN
        ALTER TABLE profiles ADD COLUMN bio TEXT;
    END IF;

    -- إضافة is_verified إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'is_verified'
    ) THEN
        ALTER TABLE profiles ADD COLUMN is_verified BOOLEAN DEFAULT false;
    END IF;

    -- إضافة created_at إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'created_at'
    ) THEN
        ALTER TABLE profiles ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;

    -- إضافة updated_at إذا لم يكن موجوداً
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'profiles' AND column_name = 'updated_at'
    ) THEN
        ALTER TABLE profiles ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;
END $$;

-- إنشاء فهارس للأداء
CREATE INDEX IF NOT EXISTS idx_profiles_email ON profiles(email);
CREATE INDEX IF NOT EXISTS idx_profiles_username ON profiles(username);
CREATE INDEX IF NOT EXISTS idx_profiles_created_at ON profiles(created_at);

-- دالة تحديث updated_at للـ profiles
CREATE OR REPLACE FUNCTION update_profiles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- إضافة trigger لتحديث updated_at
DROP TRIGGER IF EXISTS update_profiles_updated_at_trigger ON profiles;
CREATE TRIGGER update_profiles_updated_at_trigger
    BEFORE UPDATE ON profiles
    FOR EACH ROW
    EXECUTE FUNCTION update_profiles_updated_at();

-- تفعيل Row Level Security للـ profiles
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للـ profiles
DROP POLICY IF EXISTS "profiles_select_policy" ON profiles;
CREATE POLICY "profiles_select_policy" ON profiles
    FOR SELECT USING (true); -- يمكن للجميع قراءة البروفايلات العامة

DROP POLICY IF EXISTS "profiles_insert_policy" ON profiles;
CREATE POLICY "profiles_insert_policy" ON profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

DROP POLICY IF EXISTS "profiles_update_policy" ON profiles;
CREATE POLICY "profiles_update_policy" ON profiles
    FOR UPDATE USING (auth.uid() = id);

-- منح الصلاحيات
GRANT ALL ON profiles TO authenticated;
GRANT EXECUTE ON FUNCTION update_profiles_updated_at() TO authenticated;

-- إدراج مستخدمين تجريبيين بناءً على المستخدمين الموجودين في auth.users
DO $$
DECLARE
    user_record RECORD;
    user_count INTEGER := 0;
BEGIN
    -- التحقق من وجود مستخدمين في auth.users
    SELECT COUNT(*) INTO user_count FROM auth.users;

    IF user_count = 0 THEN
        RAISE NOTICE 'لا يوجد مستخدمين في auth.users. يجب تسجيل مستخدمين أولاً.';
        RETURN;
    END IF;

    -- إضافة بيانات للمستخدمين الموجودين
    FOR user_record IN
        SELECT id, email FROM auth.users LIMIT 5
    LOOP
        INSERT INTO profiles (id, email, full_name, username, bio, is_verified)
        VALUES (
            user_record.id,
            user_record.email,
            COALESCE(SPLIT_PART(user_record.email, '@', 1), 'مستخدم'),
            LOWER(REPLACE(SPLIT_PART(user_record.email, '@', 1), '.', '_')),
            'مستخدم في تطبيق أرزاوو',
            false
        )
        ON CONFLICT (id) DO UPDATE SET
            email = EXCLUDED.email,
            full_name = COALESCE(profiles.full_name, EXCLUDED.full_name),
            username = COALESCE(profiles.username, EXCLUDED.username),
            bio = COALESCE(profiles.bio, EXCLUDED.bio);

        user_count := user_count + 1;
    END LOOP;

    RAISE NOTICE 'تم تحديث % مستخدم في جدول profiles', user_count;
END $$;

-- التحقق من النتائج
SELECT 
    COUNT(*) as total_profiles,
    COUNT(CASE WHEN full_name IS NOT NULL THEN 1 END) as profiles_with_names,
    COUNT(CASE WHEN is_verified = true THEN 1 END) as verified_profiles
FROM profiles;

-- عرض المستخدمين المضافين
SELECT id, email, full_name, username, is_verified, created_at 
FROM profiles 
ORDER BY created_at DESC 
LIMIT 10;

-- رسالة نجاح
SELECT 'تم إصلاح جدول profiles وإضافة مستخدمين تجريبيين!' as message;
