import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'reaction_type.dart';

enum PostType {
  text,
  image,
  video,
  audio,
  voice,
  link,
  shared,
}

enum PostPrivacy { everyone, followers }

class Post {
  final String id;
  final String userId;
  final String userName;
  final String userAvatar;
  final String content;
  final DateTime createdAt;
  final PostType type;
  final String? mediaUrl;
  final List<String>? mediaUrls; // للصور والفيديوهات المتعددة
  final String? linkUrl;
  final Map<String, dynamic>? linkMeta;
  final Post? originalPost; // للمنشورات المعاد نشرها
  int commentsCount;
  int likesCount;
  int dislikesCount;
  int sharesCount;
  int copiesCount; // NEW: عدد نسخ الرابط
  int viewsCount;
  Map<ReactionType, int> reactionCounts;
  final String? bgColor; // hex color string like #FF0000
  ReactionType currentUserReaction;
  bool isSaved;
  final PostPrivacy privacy;
  final bool isCommunityPost; // للتمييز بين منشورات المجتمع والمنشورات العادية
  final String? communityId; // معرف المجتمع إذا كان منشور مجتمع
  final bool isSpacePost; // للتمييز بين منشورات المساحات والمنشورات العادية
  final String? spaceId; // معرف المساحة إذا كان منشور مساحة
  final String? spaceName; // اسم المساحة
  bool isHidden; // حالة إخفاء المنشور
  String? hideReason; // سبب الإخفاء
  DateTime? hiddenAt; // وقت الإخفاء
  final bool isVerified; // حالة توثيق المستخدم

  Post({
    required this.id,
    required this.userId,
    required this.userName,
    required this.userAvatar,
    required this.content,
    required this.createdAt,
    required this.type,
    this.mediaUrl,
    this.mediaUrls,
    this.linkUrl,
    this.linkMeta,
    this.originalPost,
    this.commentsCount = 0,
    this.likesCount = 0,
    this.dislikesCount = 0,
    this.sharesCount = 0,
    this.copiesCount = 0,
    this.viewsCount = 0,
    Map<ReactionType, int>? reactionCounts,
    this.bgColor,
    this.currentUserReaction = ReactionType.none,
    this.privacy = PostPrivacy.everyone,
    this.isSaved = false,
    this.isCommunityPost = false,
    this.communityId,
    this.isSpacePost = false,
    this.spaceId,
    this.spaceName,
    this.isHidden = false,
    this.hideReason,
    this.hiddenAt,
    this.isVerified = false,
  }) : reactionCounts = reactionCounts ?? {};

  Post copyWith({
    Post? originalPost,
    int? likesCount,
    int? dislikesCount,
    int? sharesCount,
    int? copiesCount,
    int? commentsCount,
    ReactionType? currentUserReaction,
    String? mediaUrl,
    List<String>? mediaUrls,
    String? linkUrl,
    Map<String, dynamic>? linkMeta,
    bool? isSaved,
    int? viewsCount,
    Map<ReactionType, int>? reactionCounts,
    String? bgColor,
    PostPrivacy? privacy,
    bool? isCommunityPost,
    String? communityId,
    bool? isSpacePost,
    String? spaceId,
    String? spaceName,
    bool? isHidden,
    String? hideReason,
    DateTime? hiddenAt,
    bool? isVerified,
  }) {
    return Post(
      id: id,
      userId: userId,
      userName: userName,
      userAvatar: userAvatar,
      content: content,
      createdAt: createdAt,
      type: type,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      mediaUrls: mediaUrls ?? this.mediaUrls,
      linkUrl: linkUrl ?? this.linkUrl,
      linkMeta: linkMeta ?? this.linkMeta,
      originalPost: originalPost ?? this.originalPost,
      likesCount: likesCount ?? this.likesCount,
      dislikesCount: dislikesCount ?? this.dislikesCount,
      sharesCount: sharesCount ?? this.sharesCount,
      copiesCount: copiesCount ?? this.copiesCount,
      commentsCount: commentsCount ?? this.commentsCount,
      currentUserReaction: currentUserReaction ?? this.currentUserReaction,
      isSaved: isSaved ?? this.isSaved,
      viewsCount: viewsCount ?? this.viewsCount,
      reactionCounts: reactionCounts ?? this.reactionCounts,
      bgColor: bgColor ?? this.bgColor,
      privacy: privacy ?? this.privacy,
      isCommunityPost: isCommunityPost ?? this.isCommunityPost,
      communityId: communityId ?? this.communityId,
      isSpacePost: isSpacePost ?? this.isSpacePost,
      spaceId: spaceId ?? this.spaceId,
      spaceName: spaceName ?? this.spaceName,
      isHidden: isHidden ?? this.isHidden,
      hideReason: hideReason ?? this.hideReason,
      hiddenAt: hiddenAt ?? this.hiddenAt,
      isVerified: isVerified ?? this.isVerified,
    );
  }

  // ---------------- Serialisation ---------------- //
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'userAvatar': userAvatar,
      'content': content,
      'createdAt': createdAt.toIso8601String(),
      'type': type.name,
      'mediaUrl': mediaUrl,
      'mediaUrls': mediaUrls,
      'linkUrl': linkUrl,
      'linkMeta': linkMeta,
      'originalPost': originalPost?.toMap(),
      'commentsCount': commentsCount,
      'likesCount': likesCount,
      'dislikesCount': dislikesCount,
      'sharesCount': sharesCount,
      'copiesCount': copiesCount,
      'viewsCount': viewsCount,
      'reactionCounts': reactionCounts.map((k, v) => MapEntry(k.value, v)),
      'bgColor': bgColor,
      'currentUserReaction': currentUserReaction.value,
      'isSaved': isSaved,
      'privacy': privacy.name,
      'isVerified': isVerified,
      'isVerified': isVerified,
    };
  }

  factory Post.fromMap(Map<String, dynamic> map) {
    PostType _parseType(String? v) {
      switch (v) {
        case 'image':
          return PostType.image;
        case 'video':
          return PostType.video;
        case 'audio':
          return PostType.audio;
        case 'voice':
          return PostType.voice;
        case 'link':
          return PostType.link;
        case 'shared':
          return PostType.shared;
        default:
          return PostType.text;
      }
    }

    Map<ReactionType, int> _parseReactions(Map<String, dynamic>? src) {
      if (src == null) return {};
      return src.map((k, v) => MapEntry(ReactionType.fromString(k), v as int));
    }

    PostPrivacy _parsePrivacy(String? v) => v == 'followers' ? PostPrivacy.followers : PostPrivacy.everyone;

    return Post(
      id: map['id'] ?? '',
      userId: map['userId'] ?? '',
      userName: map['userName'] ?? 'مستخدم',
      userAvatar: map['userAvatar'] ?? '',
      content: map['content'] ?? '',
      createdAt: DateTime.parse(map['createdAt']),
      type: _parseType(map['type']),
      mediaUrl: map['mediaUrl'],
      mediaUrls: map['mediaUrls'] != null ? List<String>.from(map['mediaUrls']) : null,
      linkUrl: map['linkUrl'],
      linkMeta: map['linkMeta'] != null ? Map<String, dynamic>.from(map['linkMeta']) : null,
      originalPost: map['originalPost'] != null ? Post.fromMap(Map<String, dynamic>.from(map['originalPost'])) : null,
      commentsCount: map['commentsCount'] ?? 0,
      likesCount: map['likesCount'] ?? 0,
      dislikesCount: map['dislikesCount'] ?? 0,
      sharesCount: map['sharesCount'] ?? 0,
      copiesCount: map['copiesCount'] ?? 0,
      viewsCount: map['viewsCount'] ?? 0,
      reactionCounts: _parseReactions(map['reactionCounts'] != null ? Map<String, dynamic>.from(map['reactionCounts']) : null),
      bgColor: map['bgColor'],
      currentUserReaction: ReactionType.fromString(map['currentUserReaction']),
      isSaved: map['isSaved'] ?? false,
      privacy: _parsePrivacy(map['privacy']),
      isCommunityPost: map['isCommunityPost'] ?? false,
      communityId: map['communityId'],
      isSpacePost: map['isSpacePost'] ?? false,
      spaceId: map['spaceId'],
      spaceName: map['spaceName'],
      isHidden: map['isHidden'] ?? false,
      hideReason: map['hideReason'],
      hiddenAt: map['hiddenAt'] != null ? DateTime.parse(map['hiddenAt']) : null,
    );
  }

  // التحقق من أن المنشور للمستخدم الحالي
  bool get isCurrentUserPost {
    try {
      // استخدام Supabase للحصول على المستخدم الحالي
      final currentUserId = Supabase.instance.client.auth.currentUser?.id;
      return currentUserId != null && userId == currentUserId;
    } catch (e) {
      return false; // في حالة عدم وجود مستخدم مسجل دخول
    }
  }
}