# حل مشكلة الصور المتعددة في المنشورات العادية

## 🚨 المشكلة
عند اختيار 4 صور للنشر في الصفحة الرئيسية، يظهر منشور فارغ بدون الصور.

## ✅ الحل الشامل

### الخطوة 1: التحقق من قاعدة البيانات

قم بتنفيذ `CHECK_POSTS_MEDIA_URLS.sql` في Supabase SQL Editor:

```sql
-- هذا الملف سيتحقق من وجود عمود media_urls في جدول posts
-- وإضافته إذا لم يكن موجوداً
```

### الخطوة 2: التحقق من الكود

الكود يعمل بشكل صحيح:

#### ✅ `lib/widgets/new_post_sheet.dart`
- يدعم اختيار الصور المتعددة (حتى 4 صور)
- يرفع الصور إلى Supabase Storage
- يرسل `mediaUrls` إلى `createPost`

#### ✅ `lib/supabase_service.dart`
- دالة `createPost` تدعم `mediaUrls`
- يحفظ الصور في عمود `media_urls` في قاعدة البيانات

#### ✅ `lib/widgets/post_card.dart`
- يعرض الصور المتعددة باستخدام `FeedMultiImage`
- يدعم التمرير بين الصور

#### ✅ `lib/widgets/feed_media.dart`
- `FeedMultiImage` يعرض الصور بشكل جميل
- يدعم 1-4 صور بتخطيطات مختلفة

### الخطوة 3: اختبار الحل

1. **نفذ SQL في Supabase:**
   ```sql
   -- نفذ CHECK_POSTS_MEDIA_URLS.sql
   ```

2. **أعد بناء التطبيق:**
   ```bash
   flutter clean
   flutter pub get
   flutter build apk --release
   ```

3. **اختبر الوظيفة:**
   - افتح تطبيق Arzawo
   - اضغط على زر "+" في الصفحة الرئيسية
   - اختر 4 صور
   - اكتب محتوى المنشور
   - اضغط "نشر"
   - تأكد من ظهور الصور في المنشور

### الخطوة 4: استكشاف الأخطاء

إذا لم تظهر الصور:

1. **تحقق من قاعدة البيانات:**
   ```sql
   SELECT column_name, data_type 
   FROM information_schema.columns 
   WHERE table_name = 'posts' AND column_name = 'media_urls';
   ```

2. **تحقق من المنشورات الجديدة:**
   ```sql
   SELECT id, content, media_urls, created_at 
   FROM posts 
   WHERE media_urls IS NOT NULL 
   ORDER BY created_at DESC 
   LIMIT 5;
   ```

3. **تحقق من bucket التخزين:**
   ```sql
   SELECT * FROM storage.buckets WHERE id = 'media';
   ```

### الخطوة 5: إصلاح محتمل

إذا كان عمود `media_urls` غير موجود:

```sql
-- إضافة عمود media_urls
ALTER TABLE posts ADD COLUMN media_urls TEXT[];

-- إنشاء فهرس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_posts_media_urls ON posts USING GIN (media_urls);
```

## 🎉 الميزات المدعومة

- ✅ دعم حتى 4 صور في منشور واحد
- ✅ معاينة الصور قبل النشر
- ✅ إمكانية حذف الصور قبل النشر
- ✅ عرض الصور في تخطيطات جميلة (1-4 صور)
- ✅ دعم التمرير بين الصور
- ✅ تحسين الأداء مع فهارس قاعدة البيانات

## 📁 الملفات المهمة

1. `CHECK_POSTS_MEDIA_URLS.sql` - التحقق من قاعدة البيانات
2. `lib/widgets/new_post_sheet.dart` - واجهة إنشاء المنشور
3. `lib/supabase_service.dart` - خدمة رفع الصور
4. `lib/widgets/post_card.dart` - عرض المنشورات
5. `lib/widgets/feed_media.dart` - عرض الوسائط

## 🚀 النتيجة

بعد تطبيق هذه التحديثات، ستتمكن من:
- اختيار حتى 4 صور للنشر في الصفحة الرئيسية
- رؤية معاينة الصور قبل النشر
- عرض الصور بشكل جميل في المنشورات
- التمرير بين الصور المتعددة
- حفظ الصور في قاعدة البيانات بشكل صحيح 