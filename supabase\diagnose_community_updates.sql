-- =============================================================
--  تشخيص مشكلة عدم حفظ معلومات المجتمع
--  Diagnose Community Update Issues
-- =============================================================

-- هذا السكريپت يفحص مشاكل تحديث معلومات المجتمعات

-- 1) فحص جدول المجتمعات
-- -------------------------------------------------------

SELECT 
  '🔍 COMMUNITIES TABLE' as check_type,
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'communities')
    THEN '✅ EXISTS: Communities table found'
    ELSE '❌ MISSING: Communities table not found'
  END as status,
  'Basic table existence check' as details;

-- 2) فحص أعمدة الجدول
-- -------------------------------------------------------

SELECT 
  '🔍 TABLE COLUMNS' as check_type,
  '✅ COLUMNS: ' || STRING_AGG(column_name, ', ') as status,
  'Available columns in communities table' as details
FROM information_schema.columns 
WHERE table_name = 'communities' 
AND table_schema = 'public';

-- 3) فحص RLS على جدول المجتمعات
-- -------------------------------------------------------

SELECT 
  '🔍 RLS STATUS' as check_type,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'public' 
      AND c.relname = 'communities'
      AND c.relrowsecurity = false
    )
    THEN '✅ GOOD: RLS disabled on communities'
    WHEN EXISTS (
      SELECT 1 FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'public' 
      AND c.relname = 'communities'
      AND c.relrowsecurity = true
    )
    THEN '⚠️ WARNING: RLS enabled - may block updates'
    ELSE '❓ UNKNOWN: Cannot determine RLS status'
  END as status,
  'Row Level Security check' as details;

-- 4) فحص السياسات على جدول المجتمعات
-- -------------------------------------------------------

SELECT 
  '🔍 POLICIES CHECK' as check_type,
  CASE 
    WHEN NOT EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE tablename = 'communities' 
      AND schemaname = 'public'
    )
    THEN '✅ GOOD: No policies blocking updates'
    ELSE '⚠️ WARNING: Policies exist - may block updates'
  END as status,
  COALESCE(
    (SELECT STRING_AGG(policyname || ' (' || cmd || ')', ', ')
     FROM pg_policies 
     WHERE tablename = 'communities' AND schemaname = 'public'),
    'No policies'
  ) as details;

-- 5) اختبار تحديث مجتمع وهمي
-- -------------------------------------------------------

DO $$
DECLARE
  test_community_id UUID;
  update_result INTEGER;
  test_status TEXT := '❌ FAILED';
  test_details TEXT := 'Update test failed';
BEGIN
  -- البحث عن مجتمع موجود للاختبار
  SELECT id INTO test_community_id
  FROM communities
  LIMIT 1;

  IF test_community_id IS NOT NULL THEN
    -- محاولة تحديث وهمي (نفس البيانات)
    UPDATE communities
    SET updated_at = NOW()
    WHERE id = test_community_id;

    GET DIAGNOSTICS update_result = ROW_COUNT;

    IF update_result > 0 THEN
      test_status := '✅ SUCCESS';
      test_details := 'Update operation works - ' || update_result || ' row(s) affected';
    ELSE
      test_status := '⚠️ WARNING';
      test_details := 'Update executed but no rows affected';
    END IF;
  ELSE
    test_status := '⚠️ NO DATA';
    test_details := 'No communities found to test';
  END IF;
  
  -- حفظ النتيجة
  CREATE TEMP TABLE IF NOT EXISTS update_test (
    check_type TEXT,
    status TEXT,
    details TEXT
  );
  
  INSERT INTO update_test VALUES (
    '🔍 UPDATE TEST',
    test_status,
    test_details
  );
END $$;

-- عرض نتيجة اختبار التحديث
SELECT check_type, status, details FROM update_test;

-- 6) فحص الصلاحيات على الجدول
-- -------------------------------------------------------

SELECT 
  '🔍 PERMISSIONS' as check_type,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.table_privileges 
      WHERE table_schema = 'public' 
      AND table_name = 'communities'
      AND grantee IN ('public', 'authenticated')
      AND privilege_type = 'UPDATE'
    )
    THEN '✅ GOOD: UPDATE permissions granted'
    ELSE '⚠️ WARNING: No explicit UPDATE permissions'
  END as status,
  COALESCE(
    (SELECT STRING_AGG(DISTINCT CONCAT(grantee, ':', privilege_type), ', ')
     FROM information_schema.table_privileges 
     WHERE table_schema = 'public' 
     AND table_name = 'communities'
     AND grantee IN ('public', 'authenticated')),
    'No permissions found'
  ) as details;

-- 7) فحص المجتمعات الموجودة
-- -------------------------------------------------------

SELECT 
  '🔍 DATA CHECK' as check_type,
  '📊 FOUND: ' || COUNT(*)::text || ' communities' as status,
  'Total communities in database' as details
FROM communities;

-- 8) فحص آخر التحديثات
-- -------------------------------------------------------

SELECT 
  '🔍 RECENT UPDATES' as check_type,
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM communities 
      WHERE updated_at > NOW() - INTERVAL '1 hour'
    )
    THEN '✅ ACTIVE: Recent updates found'
    ELSE '⚠️ STALE: No recent updates'
  END as status,
  COALESCE(
    (SELECT 'Last update: ' || MAX(updated_at)::text 
     FROM communities),
    'No update timestamps'
  ) as details;

-- 9) التوصية النهائية
-- -------------------------------------------------------

SELECT 
  '💡 RECOMMENDATION' as check_type,
  CASE 
    WHEN NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'communities')
    THEN '🔧 CRITICAL: Create communities table'
    WHEN EXISTS (
      SELECT 1 FROM pg_class c
      JOIN pg_namespace n ON n.oid = c.relnamespace
      WHERE n.nspname = 'public' 
      AND c.relname = 'communities'
      AND c.relrowsecurity = true
    ) AND EXISTS (
      SELECT 1 FROM pg_policies 
      WHERE tablename = 'communities' 
      AND schemaname = 'public'
      AND cmd = 'UPDATE'
    )
    THEN '🔧 FIX: Disable RLS or create permissive UPDATE policy'
    WHEN NOT EXISTS (
      SELECT 1 FROM information_schema.table_privileges 
      WHERE table_schema = 'public' 
      AND table_name = 'communities'
      AND privilege_type = 'UPDATE'
    )
    THEN '🔧 FIX: Grant UPDATE permissions on communities table'
    ELSE '🎉 GOOD: Database setup looks correct - check app code'
  END as status,
  'Follow this recommendation to fix update issues' as details;

-- تنظيف
DROP TABLE IF EXISTS update_test;

-- =============================================================
--  تفسير النتائج
-- =============================================================

/*

كيفية قراءة النتائج:

✅ GOOD/SUCCESS/EXISTS = ممتاز، يعمل
⚠️ WARNING/STALE = تحذير، قد يسبب مشاكل  
❌ FAILED/MISSING/CRITICAL = مشكلة حرجة، يحتاج إصلاح
📊 FOUND/ACTIVE = معلومات إحصائية

إذا رأيت "🎉 GOOD: Database setup looks correct - check app code"
فهذا يعني أن قاعدة البيانات سليمة والمشكلة في كود التطبيق.

إذا رأيت أي توصية أخرى، اتبعها لإصلاح مشكلة قاعدة البيانات.

*/
