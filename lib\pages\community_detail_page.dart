import 'package:flutter/material.dart';
import '../models/community.dart';
import '../supabase_service.dart';
import '../widgets/post_card.dart';
import '../models/post.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'community_settings_page.dart';
import '../widgets/new_post_sheet.dart';
import 'invite_followers_page.dart';

class CommunityDetailPage extends StatefulWidget {
  final Community community;
  const CommunityDetailPage({super.key, required this.community});

  @override
  State<CommunityDetailPage> createState() => _CommunityDetailPageState();
}

class _CommunityDetailPageState extends State<CommunityDetailPage> {
  late Community _comm;
  bool _joining = false;
  bool _member = false;
  late Future<List<Post>> _postsFuture;

  @override
  void initState() {
    super.initState();
    _comm = widget.community;
    _checkMember();
    _refreshHeader();
    _postsFuture = SupabaseService().fetchCommunityPosts(_comm.id);
  }

  Future<void> _checkMember() async {
    final isMem = await SupabaseService().isMemberOfCommunity(_comm.id);
    if (mounted) setState(() => _member = isMem);
  }

  Future<void> _refreshHeader() async {
    final data = await SupabaseService().getCommunity(_comm.id);
    if (data != null && mounted) setState(() => _comm = data);
  }

  Future<void> _refreshAll() async {
    await _refreshHeader();
    setState(() {
      _postsFuture = SupabaseService().fetchCommunityPosts(_comm.id);
    });
  }

  void _openCreatePost() async {
    final res = await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
      builder: (_) => NewPostSheet(communityId: _comm.id),
    );
    if (res == true) _refreshAll();
  }

  Future<void> _toggleJoin() async {
    setState(() => _joining = true);
    try {
      if (_member) {
        await SupabaseService().leaveCommunity(_comm.id);
      } else {
        await SupabaseService().joinCommunity(_comm.id);
      }
      await _checkMember();
      await _refreshHeader();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('$e')));
      }
    }
    if (mounted) setState(() => _joining = false);
  }

  @override
  Widget build(BuildContext context) {
    final currentUserId = Supabase.instance.client.auth.currentUser?.id ?? '';
    final isOwner = currentUserId == _comm.ownerId;
    return Scaffold(
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 300,
              pinned: true,
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
              title: Text(_comm.name),
              actions: [
                if (_member)
                  IconButton(
                    icon: const Icon(Icons.person_add),
                    onPressed: _openInvite,
                  ),
                if (isOwner)
                  IconButton(
                    icon: const Icon(Icons.settings),
                    onPressed: () async {
                      final changed = await Navigator.push(
                        context,
                        MaterialPageRoute(builder: (_) => CommunitySettingsPage(community: _comm)),
                      );
                      if (changed == true) _refreshHeader();
                    },
                  ),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: Stack(
                  fit: StackFit.expand,
                  children: [
                    if (_comm.coverUrl != null && _comm.coverUrl!.isNotEmpty)
                      Hero(
                        tag: 'community_cover_${_comm.id}',
                        child: Image.network(_comm.coverUrl!, fit: BoxFit.cover),
                      )
                    else
                      Container(color: Colors.red[600]),
                    
                    Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [Colors.transparent, Colors.black.withValues(alpha: 0.7)],
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                CircleAvatar(
                                  radius: 25,
                                  backgroundImage: _comm.avatarUrl != null && _comm.avatarUrl!.isNotEmpty
                                      ? NetworkImage(_comm.avatarUrl!)
                                      : null,
                                  child: (_comm.avatarUrl == null || _comm.avatarUrl!.isEmpty)
                                      ? Text(_comm.name.substring(0, 1), style: const TextStyle(fontSize: 20))
                                      : null,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(_comm.name, style: const TextStyle(color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold)),
                                      Text('${_comm.membersCount} عضو', style: const TextStyle(color: Colors.white70)),
                                    ],
                                  ),
                                ),
                                ElevatedButton(
                                  onPressed: _joining ? null : _toggleJoin,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: _member ? Colors.grey[300] : Colors.red[600],
                                    foregroundColor: _member ? Colors.black87 : Colors.white,
                                  ),
                                  child: _joining
                                      ? const SizedBox(height: 16, width: 16, child: CircularProgressIndicator(strokeWidth: 2))
                                      : Text(_member ? 'مغادرة' : 'انضمام'),
                                ),
                              ],
                            ),
                            if (_member) ...[
                              const SizedBox(height: 12),
                              Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton.icon(
                                      icon: const Icon(Icons.add),
                                      label: const Text('منشور'),
                                      onPressed: _openCreatePost,
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.red[600],
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  OutlinedButton.icon(
                                    icon: const Icon(Icons.person_add_alt),
                                    label: const Text('دعوة'),
                                    onPressed: _openInvite,
                                    style: OutlinedButton.styleFrom(
                                      foregroundColor: Colors.white,
                                      side: const BorderSide(color: Colors.white),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                            if (_comm.description != null && _comm.description!.isNotEmpty) ...[
                              const SizedBox(height: 8),
                              Text(_comm.description!, style: const TextStyle(color: Colors.white70)),
                            ],
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ];
        },
        body: RefreshIndicator(
          onRefresh: _refreshAll,
          child: FutureBuilder<List<Post>>(
            future: _postsFuture,
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.waiting) {
                return const Center(child: CircularProgressIndicator());
              }

              if (snapshot.hasError) {
                return Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.error, color: Colors.red, size: 48),
                      const SizedBox(height: 16),
                      const Text('خطأ في تحميل المنشورات:', style: TextStyle(fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      Text(snapshot.error.toString(), style: const TextStyle(color: Colors.red), textAlign: TextAlign.center),
                      const SizedBox(height: 16),
                      ElevatedButton(onPressed: _refreshAll, child: const Text('إعادة المحاولة')),
                    ],
                  ),
                );
              }

              final list = snapshot.data ?? [];
              if (list.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.post_add, size: 64, color: Colors.grey),
                      const SizedBox(height: 16),
                      const Text('لا توجد منشورات بعد', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      Text('معرف المجتمع: ${_comm.id}'),
                      const SizedBox(height: 16),
                      ElevatedButton(onPressed: _refreshAll, child: const Text('تحديث')),
                    ],
                  ),
                );
              }
              return ListView.builder(
                padding: const EdgeInsets.only(bottom: 100),
                itemCount: list.length,
                itemBuilder: (context, i) {
                  final post = list[i];
                  return PostCard(post: post, onRefresh: _refreshAll);
                },
              );
            },
          ),
        ),
      ),
    );
  }

  void _openInvite() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => InviteFollowersPage(
          communityId: _comm.id,
          communityName: _comm.name,
        ),
      ),
    );
  }
}
