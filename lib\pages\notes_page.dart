import 'package:flutter/material.dart';
import '../models/note.dart';
import '../services/notes_service.dart';
import '../widgets/note_card.dart';
import 'create_note_page.dart';
import 'note_details_page.dart';
import 'notes_search_page.dart';
import 'notes_settings_page.dart';

class NotesPage extends StatefulWidget {
  const NotesPage({super.key});

  @override
  State<NotesPage> createState() => _NotesPageState();
}

class _NotesPageState extends State<NotesPage> with TickerProviderStateMixin {
  late TabController _tabController;
  final NotesService _notesService = NotesService();
  
  List<Note> _allNotes = [];
  List<Note> _filteredNotes = [];
  Map<String, int> _stats = {};
  
  bool _loading = true;
  String _selectedFilter = 'all';
  NoteCategory? _selectedCategory;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadNotes();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadNotes() async {
    setState(() => _loading = true);
    try {
      final notes = await _notesService.getUserNotes();
      final stats = await _notesService.getNotesStats();
      
      setState(() {
        _allNotes = notes;
        _filteredNotes = notes;
        _stats = stats;
        _loading = false;
      });
    } catch (e) {
      setState(() => _loading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل المذكرات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _filterNotes(String filter, {NoteCategory? category}) {
    setState(() {
      _selectedFilter = filter;
      _selectedCategory = category;
      
      switch (filter) {
        case 'all':
          _filteredNotes = _allNotes;
          break;
        case 'pinned':
          _filteredNotes = _allNotes.where((note) => note.isPinned).toList();
          break;
        case 'tasks':
          _filteredNotes = _allNotes.where((note) => note.isTask).toList();
          break;
        case 'reminders':
          _filteredNotes = _allNotes.where((note) => note.hasReminder).toList();
          break;
        case 'category':
          if (category != null) {
            _filteredNotes = _allNotes.where((note) => note.category == category).toList();
          }
          break;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'المذكرة',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Colors.deepPurple[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _openSearch(),
            tooltip: 'البحث',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _openSettings(),
            tooltip: 'الإعدادات',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(
              icon: Icon(Icons.note),
              text: 'الكل',
            ),
            Tab(
              icon: Icon(Icons.task_alt),
              text: 'المهام',
            ),
            Tab(
              icon: Icon(Icons.alarm),
              text: 'التذكيرات',
            ),
            Tab(
              icon: Icon(Icons.archive),
              text: 'الأرشيف',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllNotesTab(),
          _buildTasksTab(),
          _buildRemindersTab(),
          _buildArchiveTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _createNote(),
        backgroundColor: Colors.deepPurple[600],
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text(
          'مذكرة جديدة',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _buildAllNotesTab() {
    return RefreshIndicator(
      onRefresh: _loadNotes,
      child: Column(
        children: [
          // شريط الإحصائيات
          _buildStatsBar(),
          
          // شريط التصفية
          _buildFilterBar(),
          
          // قائمة المذكرات
          Expanded(
            child: _buildNotesList(_filteredNotes),
          ),
        ],
      ),
    );
  }

  Widget _buildTasksTab() {
    final tasks = _allNotes.where((note) => note.isTask).toList();
    
    return RefreshIndicator(
      onRefresh: _loadNotes,
      child: Column(
        children: [
          // إحصائيات المهام
          Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.1),
                  spreadRadius: 1,
                  blurRadius: 4,
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  Icons.task_alt,
                  'المجموع',
                  '${_stats['total_tasks'] ?? 0}',
                  Colors.blue,
                ),
                _buildStatItem(
                  Icons.check_circle,
                  'مكتملة',
                  '${_stats['completed_tasks'] ?? 0}',
                  Colors.green,
                ),
                _buildStatItem(
                  Icons.schedule,
                  'معلقة',
                  '${_stats['pending_tasks'] ?? 0}',
                  Colors.orange,
                ),
              ],
            ),
          ),
          
          Expanded(
            child: _buildNotesList(tasks),
          ),
        ],
      ),
    );
  }

  Widget _buildRemindersTab() {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    return FutureBuilder<List<Note>>(
      future: _notesService.getUpcomingReminders(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 64, color: Colors.red[300]),
                const SizedBox(height: 16),
                Text(
                  'خطأ في تحميل التذكيرات',
                  style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                ),
              ],
            ),
          );
        }

        final reminders = snapshot.data ?? [];
        
        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: _buildNotesList(reminders),
        );
      },
    );
  }

  Widget _buildArchiveTab() {
    return FutureBuilder<List<Note>>(
      future: _notesService.getUserNotes(isArchived: true),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (snapshot.hasError) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.error, size: 64, color: Colors.red[300]),
                const SizedBox(height: 16),
                Text(
                  'خطأ في تحميل الأرشيف',
                  style: TextStyle(fontSize: 18, color: Colors.grey[600]),
                ),
              ],
            ),
          );
        }

        final archivedNotes = snapshot.data ?? [];
        
        return RefreshIndicator(
          onRefresh: () async {
            setState(() {});
          },
          child: _buildNotesList(archivedNotes),
        );
      },
    );
  }

  Widget _buildStatsBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 4,
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            Icons.note,
            'المذكرات',
            '${_stats['active_notes'] ?? 0}',
            Colors.deepPurple,
          ),
          _buildStatItem(
            Icons.task_alt,
            'المهام',
            '${_stats['total_tasks'] ?? 0}',
            Colors.blue,
          ),
          _buildStatItem(
            Icons.archive,
            'الأرشيف',
            '${_stats['archived_notes'] ?? 0}',
            Colors.grey,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String label, String value, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildFilterBar() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip('الكل', 'all'),
          _buildFilterChip('المثبتة', 'pinned'),
          _buildFilterChip('المهام', 'tasks'),
          _buildFilterChip('التذكيرات', 'reminders'),
          const SizedBox(width: 8),
          ...NoteCategory.values.map((category) => 
            _buildCategoryChip(category)),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, String filter) {
    final isSelected = _selectedFilter == filter;
    
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) => _filterNotes(filter),
        selectedColor: Colors.deepPurple[100],
        checkmarkColor: Colors.deepPurple[600],
      ),
    );
  }

  Widget _buildCategoryChip(NoteCategory category) {
    final isSelected = _selectedFilter == 'category' && _selectedCategory == category;
    
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(category.icon, size: 16, color: category.color),
            const SizedBox(width: 4),
            Text(category.arabicName),
          ],
        ),
        selected: isSelected,
        onSelected: (selected) => _filterNotes('category', category: category),
        selectedColor: category.color.withOpacity(0.2),
        checkmarkColor: category.color,
      ),
    );
  }

  Widget _buildNotesList(List<Note> notes) {
    if (_loading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (notes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.note_add,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد مذكرات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'اضغط على "مذكرة جديدة" لإنشاء أول مذكرة',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: notes.length,
      itemBuilder: (context, index) {
        final note = notes[index];
        return NoteCard(
          note: note,
          onTap: () => _openNote(note),
          onPin: () => _togglePin(note),
          onArchive: () => _toggleArchive(note),
          onDelete: () => _deleteNote(note),
          onTaskStatusChanged: (status) => _updateTaskStatus(note, status),
        );
      },
    );
  }

  void _createNote() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const CreateNotePage(),
      ),
    ).then((_) => _loadNotes());
  }

  void _openNote(Note note) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NoteDetailsPage(note: note),
      ),
    ).then((_) => _loadNotes());
  }

  void _openSearch() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const NotesSearchPage(),
      ),
    );
  }

  void _openSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const NotesSettingsPage(),
      ),
    ).then((_) => _loadNotes());
  }

  void _togglePin(Note note) async {
    try {
      await _notesService.togglePin(note.id);
      _loadNotes();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(note.isPinned ? 'تم إلغاء التثبيت' : 'تم التثبيت'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _toggleArchive(Note note) async {
    try {
      await _notesService.toggleArchive(note.id);
      _loadNotes();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(note.isArchived ? 'تم إلغاء الأرشفة' : 'تم الأرشفة'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _deleteNote(Note note) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('حذف المذكرة'),
        content: Text('هل تريد حذف "${note.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _notesService.deleteNote(note.id);
                _loadNotes();
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم حذف المذكرة'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('خطأ في الحذف: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _updateTaskStatus(Note note, TaskStatus status) async {
    try {
      await _notesService.toggleTaskStatus(note.id, status);
      _loadNotes();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث المهمة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
