-- إنشاء جداول قسم الوظائف

-- جدول الوظائف الرئيسي
CREATE TABLE IF NOT EXISTS jobs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    company_name TEXT NOT NULL,
    job_title TEXT NOT NULL,
    job_type TEXT NOT NULL CHECK (job_type IN ('fullTime', 'partTime', 'freelance', 'remote', 'contract', 'internship')),
    salary TEXT,
    location TEXT NOT NULL,
    is_remote BOOLEAN DEFAULT FALSE,
    description TEXT NOT NULL,
    required_skills TEXT[] DEFAULT '{}',
    category TEXT NOT NULL CHECK (category IN ('technology', 'marketing', 'education', 'construction', 'restaurant', 'healthcare', 'finance', 'design', 'sales', 'customerService', 'other')),
    application_link TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    publisher_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    space_id UUID REFERENCES spaces(id) ON DELETE SET NULL,
    is_active BOOLEAN DEFAULT TRUE,
    applications_count INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT FALSE
);

-- جدول طلبات التقديم
CREATE TABLE IF NOT EXISTS job_applications (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    applicant_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    resume_url TEXT,
    cover_letter TEXT,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'rejected', 'interviewed')),
    UNIQUE(job_id, applicant_id)
);

-- جدول الوظائف المحفوظة
CREATE TABLE IF NOT EXISTS saved_jobs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, job_id)
);

-- جدول الإبلاغ عن الوظائف
CREATE TABLE IF NOT EXISTS job_reports (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    reporter_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    reason TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء الفهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_jobs_publisher_id ON jobs(publisher_id);
CREATE INDEX IF NOT EXISTS idx_jobs_category ON jobs(category);
CREATE INDEX IF NOT EXISTS idx_jobs_job_type ON jobs(job_type);
CREATE INDEX IF NOT EXISTS idx_jobs_location ON jobs(location);
CREATE INDEX IF NOT EXISTS idx_jobs_is_active ON jobs(is_active);
CREATE INDEX IF NOT EXISTS idx_jobs_created_at ON jobs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_jobs_expires_at ON jobs(expires_at);
CREATE INDEX IF NOT EXISTS idx_jobs_space_id ON jobs(space_id);

CREATE INDEX IF NOT EXISTS idx_job_applications_job_id ON job_applications(job_id);
CREATE INDEX IF NOT EXISTS idx_job_applications_applicant_id ON job_applications(applicant_id);
CREATE INDEX IF NOT EXISTS idx_job_applications_status ON job_applications(status);

CREATE INDEX IF NOT EXISTS idx_saved_jobs_user_id ON saved_jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_saved_jobs_job_id ON saved_jobs(job_id);

-- دالة لزيادة عدد المتقدمين
CREATE OR REPLACE FUNCTION increment_applications_count(job_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE jobs 
    SET applications_count = applications_count + 1 
    WHERE id = job_id;
END;
$$ LANGUAGE plpgsql;

-- دالة لتقليل عدد المتقدمين
CREATE OR REPLACE FUNCTION decrement_applications_count(job_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE jobs 
    SET applications_count = GREATEST(applications_count - 1, 0) 
    WHERE id = job_id;
END;
$$ LANGUAGE plpgsql;

-- تريجر لزيادة عدد المتقدمين عند إضافة طلب جديد
CREATE OR REPLACE FUNCTION trigger_increment_applications()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM increment_applications_count(NEW.job_id);
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_job_application_insert
    AFTER INSERT ON job_applications
    FOR EACH ROW
    EXECUTE FUNCTION trigger_increment_applications();

-- تريجر لتقليل عدد المتقدمين عند حذف طلب
CREATE OR REPLACE FUNCTION trigger_decrement_applications()
RETURNS TRIGGER AS $$
BEGIN
    PERFORM decrement_applications_count(OLD.job_id);
    RETURN OLD;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_job_application_delete
    AFTER DELETE ON job_applications
    FOR EACH ROW
    EXECUTE FUNCTION trigger_decrement_applications();

-- إعداد Row Level Security (RLS)
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE saved_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_reports ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للوظائف
CREATE POLICY "الجميع يمكنهم قراءة الوظائف النشطة" ON jobs
    FOR SELECT USING (is_active = true);

CREATE POLICY "المستخدمون المسجلون يمكنهم إنشاء وظائف" ON jobs
    FOR INSERT WITH CHECK (auth.uid() = publisher_id);

CREATE POLICY "الناشر يمكنه تحديث وظائفه" ON jobs
    FOR UPDATE USING (auth.uid() = publisher_id);

CREATE POLICY "الناشر يمكنه حذف وظائفه" ON jobs
    FOR DELETE USING (auth.uid() = publisher_id);

-- سياسات الأمان لطلبات التقديم
CREATE POLICY "المتقدم يمكنه قراءة طلباته" ON job_applications
    FOR SELECT USING (auth.uid() = applicant_id);

CREATE POLICY "صاحب الوظيفة يمكنه قراءة طلبات وظائفه" ON job_applications
    FOR SELECT USING (
        auth.uid() IN (
            SELECT publisher_id FROM jobs WHERE id = job_applications.job_id
        )
    );

CREATE POLICY "المستخدمون المسجلون يمكنهم التقديم" ON job_applications
    FOR INSERT WITH CHECK (auth.uid() = applicant_id);

CREATE POLICY "المتقدم يمكنه تحديث طلبه" ON job_applications
    FOR UPDATE USING (auth.uid() = applicant_id);

CREATE POLICY "صاحب الوظيفة يمكنه تحديث حالة الطلبات" ON job_applications
    FOR UPDATE USING (
        auth.uid() IN (
            SELECT publisher_id FROM jobs WHERE id = job_applications.job_id
        )
    );

CREATE POLICY "المتقدم يمكنه حذف طلبه" ON job_applications
    FOR DELETE USING (auth.uid() = applicant_id);

-- سياسات الأمان للوظائف المحفوظة
CREATE POLICY "المستخدم يمكنه قراءة وظائفه المحفوظة" ON saved_jobs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه حفظ الوظائف" ON saved_jobs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "المستخدم يمكنه إلغاء حفظ الوظائف" ON saved_jobs
    FOR DELETE USING (auth.uid() = user_id);

-- سياسات الأمان للإبلاغات
CREATE POLICY "المستخدم يمكنه قراءة إبلاغاته" ON job_reports
    FOR SELECT USING (auth.uid() = reporter_id);

CREATE POLICY "المستخدمون المسجلون يمكنهم الإبلاغ" ON job_reports
    FOR INSERT WITH CHECK (auth.uid() = reporter_id);

-- الجداول جاهزة للاستخدام مع البيانات الحقيقية
-- لا توجد بيانات وهمية - سيقوم المستخدمون بإضافة الوظائف الحقيقية

-- منح الصلاحيات
GRANT ALL ON jobs TO authenticated;
GRANT ALL ON job_applications TO authenticated;
GRANT ALL ON saved_jobs TO authenticated;
GRANT ALL ON job_reports TO authenticated;

GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON FUNCTION increment_applications_count(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION decrement_applications_count(UUID) TO authenticated;
