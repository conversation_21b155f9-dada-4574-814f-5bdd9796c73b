import 'package:flutter/material.dart';
import '../models/group.dart';
import '../supabase_service.dart';
import '../widgets/profile_avatar.dart';

class GroupSettingsPage extends StatefulWidget {
  final Group group;
  const GroupSettingsPage({super.key, required this.group});

  @override
  State<GroupSettingsPage> createState() => _GroupSettingsPageState();
}

class _GroupSettingsPageState extends State<GroupSettingsPage> {
  late Group _grp;
  final _nameCtrl = TextEditingController();
  final _descCtrl = TextEditingController();
  bool _saving = false;
  List<Map<String, dynamic>> _members = [];

  @override
  void initState() {
    super.initState();
    _grp = widget.group;
    _nameCtrl.text = _grp.name;
    _descCtrl.text = _grp.description;
    _loadMembers();
  }

  Future<void> _loadMembers() async {
    _members = await SupabaseService().fetchGroupMembers(_grp.id);
    if (mounted) setState(() {});
  }

  Future<void> _save() async {
    setState(() => _saving = true);
    await SupabaseService().updateGroupInfo(groupId: _grp.id, name: _nameCtrl.text.trim(), description: _descCtrl.text.trim());
    if (mounted) {
      setState(() => _saving = false);
      Navigator.pop(context, true);
    }
  }

  Future<void> _remove(String uid) async {
    await SupabaseService().kickMember(_grp.id, uid);
    _loadMembers();
  }

  Future<void> _archive(bool v) async {
    await SupabaseService().archiveGroup(_grp.id, v);
    if (mounted) ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(v ? 'تم الأرشفة' : 'تم إلغاء الأرشفة')));
  }

  Future<void> _delete() async {
    await SupabaseService().deleteGroup(_grp.id);
    if (!mounted) return;
    Navigator.popUntil(context, (r) => r.isFirst);
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم حذف المجموعة')));
  }

  Future<void> _schedule() async {
    final date = await showDatePicker(context: context, initialDate: DateTime.now().add(const Duration(days:7)), firstDate: DateTime.now().add(const Duration(days:1)), lastDate: DateTime.now().add(const Duration(days:365)));
    if (date!=null){
      await SupabaseService().scheduleGroupDeletion(_grp.id, date);
      if(mounted) ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تمت جدولة الحذف')));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(actions:[TextButton(onPressed:_saving?null:_save, child:_saving?const CircularProgressIndicator():const Text('حفظ'))], title: const Text('إعدادات المجموعة')),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextField(controller: _nameCtrl, decoration: const InputDecoration(labelText:'الاسم')),
            const SizedBox(height:12),
            TextField(controller: _descCtrl, decoration: const InputDecoration(labelText:'الوصف'), maxLines:3),
            const SizedBox(height:24),
            const Text('الأعضاء', style: TextStyle(fontWeight: FontWeight.bold)),
            ..._members.map((m)=> ListTile(leading: ProfileAvatar(userId: m['id']), title: Text(m['name']??''), subtitle: Text(m['role']??''), trailing: m['role']=='admin'?null: IconButton(icon: const Icon(Icons.remove_circle, color: Colors.red), onPressed: ()=>_remove(m['id'])))),
            const SizedBox(height:32),
            const Divider(),
            const Text('منطقة الخطر', style: TextStyle(color: Colors.red, fontWeight: FontWeight.bold)),
            const SizedBox(height:12),
            ElevatedButton.icon(icon:const Icon(Icons.archive), label:const Text('أرشفة/إلغاء أرشفة'), onPressed: ()=>_archive(true), style:ElevatedButton.styleFrom(backgroundColor: Colors.orange)),
            const SizedBox(height:8),
            ElevatedButton.icon(icon:const Icon(Icons.delete), label:const Text('حذف نهائى'), onPressed: _delete, style:ElevatedButton.styleFrom(backgroundColor: Colors.red)),
            const SizedBox(height:8),
            ElevatedButton.icon(icon:const Icon(Icons.schedule), label:const Text('جدولة حذف'), onPressed: _schedule),
          ],
        ),
      ),
    );
  }
} 