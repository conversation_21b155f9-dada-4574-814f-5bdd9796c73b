-- التحقق من وجود جدول space_posts وإنشاؤه إذا لم يكن موجوداً
DO $$
BEGIN
    -- التحقق من وجود الجدول
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'space_posts') THEN
        -- إنشاء جدول space_posts
        CREATE TABLE space_posts (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            space_id UUID NOT NULL REFERENCES spaces(id) ON DELETE CASCADE,
            author_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
            author_name TEXT NOT NULL,
            content TEXT NOT NULL,
            media_urls TEXT[] DEFAULT '{}',
            link_url TEXT,
            link_title TEXT,
            link_description TEXT,
            link_image TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );

        -- إنشاء فهارس
        CREATE INDEX idx_space_posts_space_id ON space_posts(space_id);
        CREATE INDEX idx_space_posts_author_id ON space_posts(author_id);
        CREATE INDEX idx_space_posts_created_at ON space_posts(created_at);

        -- إنشاء RLS policies
        ALTER TABLE space_posts ENABLE ROW LEVEL SECURITY;

        CREATE POLICY "Allow read access to space_posts" ON space_posts
            FOR SELECT USING (true);

        CREATE POLICY "Allow insert access to space_posts" ON space_posts
            FOR INSERT WITH CHECK (auth.uid() = author_id);

        CREATE POLICY "Allow update access to space_posts" ON space_posts
            FOR UPDATE USING (auth.uid() = author_id);

        CREATE POLICY "Allow delete access to space_posts" ON space_posts
            FOR DELETE USING (auth.uid() = author_id);

        RAISE NOTICE 'تم إنشاء جدول space_posts بنجاح';
    ELSE
        RAISE NOTICE 'جدول space_posts موجود بالفعل';
    END IF;
END $$;

-- إنشاء دالة لزيادة عداد المنشورات في المساحة
CREATE OR REPLACE FUNCTION increment_space_posts(space_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE spaces 
    SET posts_count = COALESCE(posts_count, 0) + 1,
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- إنشاء دالة لإنقاص عداد المنشورات في المساحة
CREATE OR REPLACE FUNCTION decrement_space_posts(space_id UUID)
RETURNS VOID AS $$
BEGIN
    UPDATE spaces 
    SET posts_count = GREATEST(COALESCE(posts_count, 0) - 1, 0),
        updated_at = NOW()
    WHERE id = space_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- منح الصلاحيات
GRANT EXECUTE ON FUNCTION increment_space_posts TO authenticated;
GRANT EXECUTE ON FUNCTION decrement_space_posts TO authenticated;
GRANT USAGE ON SCHEMA public TO authenticated;

-- عرض عدد المنشورات في كل مساحة بعد الإصلاح
SELECT 
    s.name as space_name,
    s.id as space_id,
    COUNT(sp.id) as posts_count
FROM spaces s
LEFT JOIN space_posts sp ON s.id = sp.space_id
GROUP BY s.id, s.name
ORDER BY posts_count DESC; 