import 'package:flutter/material.dart';
import '../models/story.dart';
import '../supabase_service.dart';

class StoryArchivePage extends StatefulWidget {
  const StoryArchivePage({super.key});

  @override
  State<StoryArchivePage> createState() => _StoryArchivePageState();
}

class _StoryArchivePageState extends State<StoryArchivePage> {
  late Future<List<Story>> _future;

  @override
  void initState() {
    super.initState();
    final uid = SupabaseService().getCurrentUserId();
    _future = SupabaseService().fetchUserStoriesArchive(uid!);
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('أرشيف القصص'),
          bottom: const TabBar(tabs: [
            Tab(text: 'الصور'),
            Tab(text: 'الفيديو'),
          ]),
        ),
        body: FutureBuilder<List<Story>>(
        future: _future,
        builder: (context, snap) {
          if (snap.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snap.hasError) {
            return Center(child: Text('خطأ: ${snap.error}'));
          }
          final allStories = snap.data ?? [];
          final imageStories = allStories.where((s)=>s.type==StoryType.image).toList();
          final videoStories = allStories.where((s)=>s.type==StoryType.video).toList();

          Widget buildGrid(List<Story> stories){
            if(stories.isEmpty){
              return const Center(child: Text('لا يوجد محتوى'));
            }
            return GridView.builder(
              padding: const EdgeInsets.all(8),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount:3,crossAxisSpacing:4,mainAxisSpacing:4),
              itemCount: stories.length,
              itemBuilder: (c,i){
                final s=stories[i];
                final thumbnail=Image.network(
                  s.mediaUrl ?? '',
                  fit: BoxFit.cover,
                  errorBuilder: (_, __, ___) => Container(color: Colors.grey.shade200),
                );
                return GestureDetector(
                  onTap: () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (_) => _ArchiveStoryViewer(stories: stories, initialIndex: i),
                      ),
                    );
                  },
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: Stack(
                      fit: StackFit.expand,
                      children: [
                        thumbnail,
                        Positioned(
                          bottom: 4,
                          left: 4,
                          child: Text(
                            _formatDate(s.createdAt),
                            style: const TextStyle(color: Colors.white, fontSize: 10, shadows: [Shadow(color: Colors.black, blurRadius: 2)]),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              });
          }

          return TabBarView(children:[
            buildGrid(imageStories),
            buildGrid(videoStories),
          ]);
        },
      ),
    ),
  );
  }

  String _formatDate(DateTime dt) {
    return '${dt.day}/${dt.month}/${dt.year}';
  }
}

class _ArchiveStoryViewer extends StatefulWidget {
  final List<Story> stories;
  final int initialIndex;
  const _ArchiveStoryViewer({required this.stories, required this.initialIndex});

  @override
  State<_ArchiveStoryViewer> createState() => _ArchiveStoryViewerState();
}

class _ArchiveStoryViewerState extends State<_ArchiveStoryViewer> {
  late int _index;

  @override
  void initState() {
    super.initState();
    _index = widget.initialIndex;
  }

  void _next() {
    if (_index < widget.stories.length - 1) {
      setState(() => _index++);
    } else {
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    final story = widget.stories[_index];
    Widget content;
    switch (story.type) {
      case StoryType.text:
        content = Center(
          child: Text(story.text ?? '', style: const TextStyle(fontSize: 28, color: Colors.white)),
        );
        break;
      case StoryType.image:
        content = Image.network(story.mediaUrl ?? '', fit: BoxFit.contain);
        break;
      case StoryType.video:
        content = Center(child: Text('فيديو غير مدعوم فى الأرشيف حالياً', style: const TextStyle(color: Colors.white)));
        break;
    }

    return GestureDetector(
      onTap: _next,
      child: Scaffold(
        backgroundColor: Colors.black,
        appBar: AppBar(backgroundColor: Colors.transparent),
        body: Center(child: content),
      ),
    );
  }
} 