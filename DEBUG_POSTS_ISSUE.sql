-- التحقق من مشكلة المنشورات الفارغة
-- Debug posts issue

-- 1. التحقق من وجود عمود media_urls
SELECT 
    column_name, 
    data_type, 
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'posts' AND column_name = 'media_urls';

-- 2. التحقق من المنشورات الأخيرة
SELECT 
    id,
    content,
    type,
    media_url,
    media_urls,
    created_at,
    user_id
FROM posts 
ORDER BY created_at DESC 
LIMIT 10;

-- 3. التحقق من bucket التخزين
SELECT * FROM storage.buckets WHERE id = 'media';

-- 4. التحقق من سياسات التخزين
SELECT policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE tablename = 'objects' AND schemaname = 'storage'
AND policyname LIKE '%media%';

-- 5. إنشاء منشور اختبار
INSERT INTO posts (
    user_id,
    content,
    type,
    media_urls,
    created_at
) VALUES (
    (SELECT id FROM auth.users LIMIT 1),
    'منشور اختبار للتحقق من المشكلة',
    'image',
    ARRAY['https://example.com/test1.jpg', 'https://example.com/test2.jpg'],
    NOW()
) RETURNING id, content, media_urls;

-- 6. التحقق من المنشور الجديد
SELECT 
    id,
    content,
    type,
    media_url,
    media_urls,
    created_at
FROM posts 
WHERE content LIKE '%اختبار%'
ORDER BY created_at DESC 
LIMIT 5; 