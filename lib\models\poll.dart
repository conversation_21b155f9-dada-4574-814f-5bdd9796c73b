import 'package:flutter/material.dart';

// نوع التصويت
enum PollType {
  public('عام', Icons.public),
  private('خاص', Icons.lock);

  const PollType(this.arabicName, this.icon);
  final String arabicName;
  final IconData icon;
}

// مدة التصويت
enum PollDuration {
  oneHour('ساعة واحدة', Duration(hours: 1)),
  sixHours('6 ساعات', Duration(hours: 6)),
  twelveHours('12 ساعة', Duration(hours: 12)),
  oneDay('24 ساعة', Duration(days: 1)),
  threeDays('3 أيام', Duration(days: 3)),
  oneWeek('أسبوع', Duration(days: 7)),
  unlimited('غير محدود', null);

  const PollDuration(this.arabicName, this.duration);
  final String arabicName;
  final Duration? duration;
}

// فئة التصويت
enum PollCategory {
  general('عام', Icons.public, Colors.blue),
  sports('رياضة', Icons.sports_soccer, Colors.green),
  community('مجتمع', Icons.people, Colors.orange),
  religion('دين', Icons.mosque, Colors.purple),
  entertainment('ترفيه', Icons.movie, Colors.red),
  technology('تقنية', Icons.computer, Colors.cyan),
  health('صحة', Icons.health_and_safety, Colors.pink),
  education('تعليم', Icons.school, Colors.indigo),
  business('أعمال', Icons.business, Colors.brown),
  politics('سياسة', Icons.account_balance, Colors.grey);

  const PollCategory(this.arabicName, this.icon, this.color);
  final String arabicName;
  final IconData icon;
  final Color color;
}

// خيار التصويت
class PollOption {
  final String id;
  final String text;
  final int votes;
  final double percentage;

  PollOption({
    required this.id,
    required this.text,
    required this.votes,
    required this.percentage,
  });

  factory PollOption.fromJson(Map<String, dynamic> json) {
    return PollOption(
      id: json['id'] ?? '',
      text: json['text'] ?? '',
      votes: json['votes'] ?? 0,
      percentage: (json['percentage'] ?? 0.0).toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'votes': votes,
      'percentage': percentage,
    };
  }

  PollOption copyWith({
    String? id,
    String? text,
    int? votes,
    double? percentage,
  }) {
    return PollOption(
      id: id ?? this.id,
      text: text ?? this.text,
      votes: votes ?? this.votes,
      percentage: percentage ?? this.percentage,
    );
  }
}

// التصويت الرئيسي
class Poll {
  final String id;
  final String userId;
  final String question;
  final List<PollOption> options;
  final PollType type;
  final PollCategory category;
  final PollDuration duration;
  final bool allowComments;
  final bool allowRevote;
  final bool isActive;
  final int totalVotes;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final String? userVote; // ID الخيار الذي صوت عليه المستخدم
  final String? authorName;
  final String? authorAvatar;
  final bool isVerified;

  Poll({
    required this.id,
    required this.userId,
    required this.question,
    required this.options,
    required this.type,
    required this.category,
    required this.duration,
    required this.allowComments,
    required this.allowRevote,
    required this.isActive,
    required this.totalVotes,
    required this.createdAt,
    this.expiresAt,
    this.userVote,
    this.authorName,
    this.authorAvatar,
    this.isVerified = false,
  });

  factory Poll.fromJson(Map<String, dynamic> json) {
    return Poll(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      question: json['question'] ?? '',
      options: (json['options'] as List?)
          ?.map((option) => PollOption.fromJson(option))
          .toList() ?? [],
      type: PollType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => PollType.public,
      ),
      category: PollCategory.values.firstWhere(
        (c) => c.name == json['category'],
        orElse: () => PollCategory.general,
      ),
      duration: PollDuration.values.firstWhere(
        (d) => d.name == json['duration'],
        orElse: () => PollDuration.unlimited,
      ),
      allowComments: json['allow_comments'] ?? true,
      allowRevote: json['allow_revote'] ?? false,
      isActive: json['is_active'] ?? true,
      totalVotes: json['total_votes'] ?? 0,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      expiresAt: json['expires_at'] != null ? DateTime.parse(json['expires_at']) : null,
      userVote: json['user_vote'],
      authorName: json['author_name'],
      authorAvatar: json['author_avatar'],
      isVerified: json['is_verified'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'question': question,
      'options': options.map((option) => option.toJson()).toList(),
      'type': type.name,
      'category': category.name,
      'duration': duration.name,
      'allow_comments': allowComments,
      'allow_revote': allowRevote,
      'is_active': isActive,
      'total_votes': totalVotes,
      'created_at': createdAt.toIso8601String(),
      'expires_at': expiresAt?.toIso8601String(),
      'user_vote': userVote,
      'author_name': authorName,
      'author_avatar': authorAvatar,
      'is_verified': isVerified,
    };
  }

  // التحقق من انتهاء صلاحية التصويت
  bool get isExpired {
    if (expiresAt == null) return false;
    return DateTime.now().isAfter(expiresAt!);
  }

  // التحقق من إمكانية التصويت
  bool get canVote {
    return isActive && !isExpired;
  }

  // الحصول على الوقت المتبقي
  Duration? get timeRemaining {
    if (expiresAt == null) return null;
    final now = DateTime.now();
    if (now.isAfter(expiresAt!)) return Duration.zero;
    return expiresAt!.difference(now);
  }

  // تنسيق الوقت المتبقي
  String get formattedTimeRemaining {
    final remaining = timeRemaining;
    if (remaining == null) return 'غير محدود';
    if (remaining == Duration.zero) return 'انتهى';
    
    if (remaining.inDays > 0) {
      return '${remaining.inDays} يوم';
    } else if (remaining.inHours > 0) {
      return '${remaining.inHours} ساعة';
    } else if (remaining.inMinutes > 0) {
      return '${remaining.inMinutes} دقيقة';
    } else {
      return 'أقل من دقيقة';
    }
  }

  // الحصول على الخيار الفائز
  PollOption? get winningOption {
    if (options.isEmpty) return null;
    return options.reduce((a, b) => a.votes > b.votes ? a : b);
  }

  Poll copyWith({
    String? id,
    String? userId,
    String? question,
    List<PollOption>? options,
    PollType? type,
    PollCategory? category,
    PollDuration? duration,
    bool? allowComments,
    bool? allowRevote,
    bool? isActive,
    int? totalVotes,
    DateTime? createdAt,
    DateTime? expiresAt,
    String? userVote,
    String? authorName,
    String? authorAvatar,
    bool? isVerified,
  }) {
    return Poll(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      question: question ?? this.question,
      options: options ?? this.options,
      type: type ?? this.type,
      category: category ?? this.category,
      duration: duration ?? this.duration,
      allowComments: allowComments ?? this.allowComments,
      allowRevote: allowRevote ?? this.allowRevote,
      isActive: isActive ?? this.isActive,
      totalVotes: totalVotes ?? this.totalVotes,
      createdAt: createdAt ?? this.createdAt,
      expiresAt: expiresAt ?? this.expiresAt,
      userVote: userVote ?? this.userVote,
      authorName: authorName ?? this.authorName,
      authorAvatar: authorAvatar ?? this.authorAvatar,
      isVerified: isVerified ?? this.isVerified,
    );
  }
}
